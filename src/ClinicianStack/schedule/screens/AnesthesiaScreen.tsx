import React from "react";
import { Text, View } from "react-native";
import { useFocusEffect } from "@react-navigation/native";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch } from "../../../store";
import { useNavigation } from "@react-navigation/native";
import {
  findAnesthesiaDiff,
  useAnesthesiaDetails,
  useAnesthesiaUserDetails,
  useLoaderAndError,
} from "../hooks/anesthesiaHooks";
import { setAnesthesiaUserDetails } from "../../../store/clinician/ScheduleStack/anesthesia";
import ScreenWrapper from "../../../components/ScreenWrapper";
import CustomCard from "../../../components/CustomCard";
import Heading from "../../../components/Heading";
import LineSeperator from "../../../components/LineSeperator";
import OptionSelector from "../../../components/OptionSelector";
import {
  fetchAnesthesiaDetails,
  putAnesthesiaDetails,
} from "../../../store/clinician/ScheduleStack/anesthesia/thunk";
import { useClinicianSelectedPatient } from "../hooks/schedulesHooks";
import Loader from "../../../components/Loader";
import globalStyles from "../../../styles/GlobalStyles";
import SearchablePicker from "../../../components/SearchablePicker";
import CustomInput from "../../../components/CustomTextInput";
import { formatAnesthesiaOptions } from "../../../utils";
import SaveActionButton from "../../../components/SaveActionButton";
import { parseIntInput } from "../../../utils";
import PopupModal from "../../../components/Popup";
import CustomText from "../../../components/CustomText";
import { simpleAutoSave } from "../../../services/simpleAutoSave";
import { setLastSavedData, setSaveStatus } from "../../../store/services";
import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons";

const AnesthesiaScreen: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();

  const selectedPatient = useClinicianSelectedPatient();

  const diff = findAnesthesiaDiff();

  const { loader, error } = useLoaderAndError();

  const userDetails = useAnesthesiaUserDetails();
  const anesthesiaDetails = useAnesthesiaDetails();

  const [modalVisible, setModalVisible] = React.useState(false);
  const navigation = useNavigation();
  const [popupMsg, setPopupMsg] = React.useState<string[]>([]);

  const [popupMsgType, setPopupMsgType] = React.useState("");

  const {
    anesthesiaType,
    anesthesiaTypeSelected,
    procedureImageType,
    procedureImageTypeSelected,
    catheterType,
    catheterTypeSelected,
    fluidBolusType,
    fluidBolusTypeSelected,
    laaPressure,
    procedureImageTypeSelectedName,
  } = formatAnesthesiaOptions(userDetails);

  const validator = () => {
    setPopupMsg([]);

    var temp = false;
    // if (
    //   laaPressure === 0 ||
    //   laaPressure === null ||
    //   laaPressure === undefined
    // ) {
    //   setPopupMsg((prev) => [...prev, "Please Enter a Valid Pressure Value"]);
    //   temp = true;
    // }

    if (
      (procedureImageTypeSelectedName?.toLowerCase() === "ice" &&
        catheterTypeSelected === null) ||
      catheterTypeSelected === undefined ||
      catheterTypeSelected === ""
    ) {
      setPopupMsg((prev) => [
        ...prev,
        "Please Enter a Valid ICE Catheter Value",
      ]);
      temp = true;
    }

    if (temp) {
      setModalVisible(true);
      return false;
    } else {
      return true;
    }
  };

  const saveDetails = async (): Promise<void> => {
    try {
      const isValid = validator();

      if (!isValid) {
        dispatch(
          setSaveStatus({
            screenId: "anesthesia",
            status: "validation_failed",
          })
        );
        setLocalSaveStatus("validation_failed");
        throw new Error("Validation failed");
      }

      const dataCopy = { ...diff.currentValues };

      const res = await dispatch(
        putAnesthesiaDetails({
          case_details_id: userDetails?.case_detail_id,
          payload: dataCopy,
        })
      );

      if (res.payload && selectedPatient?.case_id) {
        const refetchRes = await dispatch(
          fetchAnesthesiaDetails({ case_id: selectedPatient.case_id })
        );

        if (refetchRes.payload) {
          dispatch(setAnesthesiaUserDetails(refetchRes.payload));
          // navigation.navigate("Transseptal Puncture");
        }
      }
    } catch (err) {
      throw err;
    }
  };

  const handleCancel = () => {
    dispatch(
      setAnesthesiaUserDetails({
        ...userDetails,
        ...anesthesiaDetails,
      })
    );
  };

  useFocusEffect(
    React.useCallback(() => {
      const fetchDetails = async () => {
        if (selectedPatient?.case_id) {
          const res = await dispatch(
            fetchAnesthesiaDetails({ case_id: selectedPatient.case_id })
          );

          if (res.payload) {
            dispatch(setAnesthesiaUserDetails(res.payload));
          }
        }
      };
      fetchDetails();
    }, [dispatch, selectedPatient?.case_id])
  );

  const saveStatus = useSelector(
    (state: any) => state.common?.autoSave?.["anesthesia"]?.status
  );

  const [localSaveStatus, setLocalSaveStatus] = React.useState<string>("");

  const effectiveSaveStatus = saveStatus || localSaveStatus;

  // Track if user has made changes to prevent auto-navigation on initial load
  const [hasUserMadeChanges, setHasUserMadeChanges] = React.useState(false);

  // Track when user makes changes to any field
  React.useEffect(() => {
    if (diff.hasDiff && !hasUserMadeChanges) {
      setHasUserMadeChanges(true);
    }
  }, [diff.hasDiff, hasUserMadeChanges]);

  // Add effect to navigate to Transseptal Puncture screen when save is successful
  // React.useEffect(() => {
  //   if (effectiveSaveStatus === "saved" && hasUserMadeChanges) {
  //     // Add a small delay to ensure the user sees the "saved" message briefly
  //     const timer = setTimeout(() => {
  //       (navigation as any).navigate("Transseptal Puncture");
  //     }, 1000);

  //     return () => clearTimeout(timer);
  //   }
  // }, [effectiveSaveStatus, navigation, hasUserMadeChanges]);

  /**
   * Derive the save message and styling based on the save status
   */
  const { saveMessage, messageStyle, iconName, iconColor } =
    React.useMemo(() => {
      let message = "";
      let style = "text-primaryPurple italic";
      let icon = "alert-circle-outline";
      let color = "#8143d9";

      switch (effectiveSaveStatus) {
        case "changes_detected":
          message = "Changes detected, saving soon...";
          style = "text-primaryBlack italic";
          icon = "alert-circle-outline";
          color = "#f5c542"; // yellow
          break;
        case "saving":
          message = "Saving... Please don't close the app";
          style = "text-primaryPurple font-bold italic";
          icon = "alert-circle-outline";
          color = "#8143d9"; // purple
          break;
        case "saved":
          message = "All changes saved";
          style = "text-green-3 italic";
          icon = "check-circle-outline";
          color = "#28a745"; // green
          break;
        case "validation_failed":
          message = "Validation failed. Please check your inputs.";
          style = "text-red-3 font-bold italic";
          icon = "alert-circle-outline";
          color = "#d93025"; // red
          break;
        case "error":
          message = "Error saving changes. Please try again.";
          style = "text-red-3 font-bold italic";
          icon = "alert-circle-outline";
          color = "#d93025"; // red
          break;
      }

      return {
        saveMessage: message,
        messageStyle: style,
        iconName: icon,
        iconColor: color,
      };
    }, [effectiveSaveStatus]);
  /**
   * Ref to track previous diff values to prevent unnecessary saves
   */
  const prevDiffRef = React.useRef<string>("");

  /**
   * Ref to track if initial data has been loaded
   * This prevents autosave from triggering before data is fetched
   */
  const initialDataLoadedRef = React.useRef<boolean>(false);

  /**
   * Effect to set initialDataLoaded when data is fetched
   */
  React.useEffect(() => {
    // Check if we have valid data
    const hasValidData = userDetails?.case_detail_id !== undefined;

    if (hasValidData && !initialDataLoadedRef.current) {
      initialDataLoadedRef.current = true;
    }
  }, [userDetails]);

  /**
   * Effect to trigger autosave when data changes
   * This uses our new simpleAutoSave service with local state fallback
   */
  React.useEffect(() => {
    // Only proceed if initial data has been loaded and there are actual differences
    if (!initialDataLoadedRef.current) {
      return;
    }

    // Check if case_detail_id exists to prevent API calls with undefined ID
    if (!userDetails?.case_detail_id) {
      return;
    }

    // Only proceed if there are actual differences
    if (diff.hasDiff) {
      // Check if we have any undefined values in critical fields
      const hasUndefinedValues = Object.values(diff.currentValues).every(
        (val) => val === undefined
      );
      if (hasUndefinedValues) {
        return;
      }

      // Convert current values to string for comparison
      const currentDiffString = JSON.stringify(diff.currentValues);

      // Only trigger save if the values have actually changed
      if (currentDiffString !== prevDiffRef.current) {
        // Update the ref with current values
        prevDiffRef.current = currentDiffString;

        // Create a custom wrapper for setSaveStatus that updates both Redux and local state
        const setStatusWithFallback = (payload: {
          screenId: string;
          status: string;
        }) => {
          // Update local state as a fallback
          setLocalSaveStatus(payload.status);
          // Also dispatch to Redux
          return dispatch(setSaveStatus(payload));
        };

        // Trigger the simple autosave with the current values
        simpleAutoSave(
          "anesthesia", // Screen ID
          diff.currentValues, // Data to save
          saveDetails, // Save function
          4000, // 4 second delay before saving
          dispatch, // Redux dispatch
          setStatusWithFallback, // Custom action to set save status with fallback
          setLastSavedData // Action to set last saved data
        );
      } else {
        console.log(
          "No actual changes in anesthesia diff values, skipping autosave"
        );
      }
    }
  }, [
    diff.hasDiff,
    diff.currentValues,
    dispatch,
    setLocalSaveStatus,
    userDetails,
  ]);

  const handleRefresh = async () => {
    if (!selectedPatient?.case_id) return;
    const refetchRes = await dispatch(
      fetchAnesthesiaDetails({ case_id: selectedPatient.case_id })
    );
    if (refetchRes.payload) {
      dispatch(setAnesthesiaUserDetails(refetchRes.payload));
    }
  };

  if (loader) {
    return <Loader />;
  }

  return (
    <ScreenWrapper direction="column" onRefresh={handleRefresh}>
      <CustomCard>
        {saveMessage && (
          <View
            style={{
              flexDirection: "row",
              justifyContent: "flex-end",
              alignItems: "center",
              marginVertical: 4,
            }}
          >
            <MaterialCommunityIcons
              name={iconName}
              color={iconColor}
              size={20}
            />
            <Text
              className={`text-sm ${messageStyle}`}
              style={{ marginLeft: 6 }}
            >
              {saveMessage}
            </Text>
          </View>
        )}

        <View className="mt-2 gap-3">
          <Heading
            text="Sedation Type"
            size="sub-heading"
            showSeperator={false}
          />
          <View className="p-3 rounded-md bg-primaryBg">
            <OptionSelector
              options={anesthesiaType || []}
              selected={anesthesiaTypeSelected}
              onSelect={(option) =>
                dispatch(
                  setAnesthesiaUserDetails({
                    anesthesia: {
                      ...userDetails.anesthesia,
                      selected: {
                        id: option.value,
                        name: option.label,
                      },
                    },
                  })
                )
              }
            />
          </View>
        </View>

        <LineSeperator extraStyle={globalStyles.lineseperator} />

        <View>
          <View className="gap-3">
            <Heading
              text="Procedure Imaging Type"
              size="sub-heading"
              showSeperator={false}
            />
            <View
              className={`p-3 bg-primaryBg ${
                procedureImageTypeSelectedName?.toLocaleLowerCase() === "ice"
                  ? "rounded-t-md"
                  : "rounded-md"
              } `}
            >
              <OptionSelector
                options={procedureImageType || []}
                selected={procedureImageTypeSelected}
                onSelect={(option) =>
                  dispatch(
                    setAnesthesiaUserDetails({
                      imaging: {
                        ...userDetails.imaging,
                        selected: {
                          id: option.value,
                          name: option.label,
                        },
                      },
                    })
                  )
                }
              />
            </View>
          </View>

          {procedureImageTypeSelectedName?.toLocaleLowerCase() === "ice" && (
            <View className="rounded-md bg-primaryBg px-3 pb-5">
              <CustomText
                value={"ICE Catheter Type"}
                className="my-2 font-bold"
              />
              <SearchablePicker
                items={catheterType}
                placeholder="ICE Catheter Type"
                value={catheterTypeSelected}
                error={
                  catheterTypeSelected === null ||
                  catheterTypeSelected === undefined ||
                  catheterTypeSelected === ""
                }
                onValueChange={(option) =>
                  dispatch(
                    setAnesthesiaUserDetails({
                      catheter: {
                        ...userDetails.catheter,
                        selected: {
                          id: option.value,
                          name: option.label,
                        },
                      },
                    })
                  )
                }
              />
            </View>
          )}
        </View>

        {/* <LineSeperator extraStyle={globalStyles.lineseperator} />

        <View>
          <Heading
            text="Left Atrial Pressure (mmHg)"
            size="sub-heading"
            showSeperator={false}
            extraStyle="mb-3"
          />
          <CustomInput
            inputValue={laaPressure?.toString()}
            placeholder="Left Atrial Pressure"
            keyboardType="numeric"
            error={
              laaPressure === 0 ||
              laaPressure === null ||
              laaPressure === undefined
            }
            onInputChange={(value) =>
              parseIntInput(value, (updatedValue) => {
                dispatch(
                  setAnesthesiaUserDetails({
                    la_pressure: updatedValue,
                  })
                );
              })
            }
          />
        </View>

        <LineSeperator extraStyle={globalStyles.lineseperator} />

        <View>
          <Heading
            text="Fluid Bolus (mL)"
            size="sub-heading"
            showSeperator={false}
            extraStyle="mb-2"
          />

          <SearchablePicker
            items={fluidBolusType?.sort(
              (a: { label: number }, b: { label: number }) => a.label - b.label
            )}
            placeholder="Select"
            value={fluidBolusTypeSelected}
            onValueChange={(option) =>
              dispatch(
                setAnesthesiaUserDetails({
                  fluid_bolus: {
                    ...userDetails.fluid_bolus,
                    selected: {
                      id: option.value,
                      name: option.label,
                    },
                  },
                })
              )
            }
            disableError
          />
        </View> */}
        <LineSeperator extraStyle="my-5" />

        {/* <View className="flex-row items-center justify-center">
          <SaveActionButton
            disabled={!diff.hasDiff}
            onPress={saveDetails}
            onCancel={handleCancel}
          />
        </View> */}

        <View className="mt-3"></View>
      </CustomCard>
      <View className="mt-9"></View>

      <PopupModal
        show={modalVisible}
        msg={popupMsg}
        status="warning"
        onClose={() => setModalVisible(false)}
      />
    </ScreenWrapper>
  );
};

export default AnesthesiaScreen;
