import React, { useState } from "react";
import { <PERSON>, Button, StyleSheet } from "react-native";
import DatePicker from "react-native-date-picker";
import SearchablePicker from "../../../components/SearchablePicker";
import { useDispatch } from "react-redux";
import { AppDispatch } from "../../../store";
import Loader from "../../../components/Loader";
import {
  useSiteList,
  usePatientsList,
  useLoadersState,
  useAddCaseModal,
} from "../hooks/schedulesHooks";
import {
  fetchSiteList,
  fetchPatientsList,
  putRepCase,
} from "../../../store/clinician/ScheduleStack/schedules/thunk";

const AddCaseBottomSheet = () => {
  const dispatch = useDispatch<AppDispatch>();
  const modalVisible = useAddCaseModal();
  const [date, setDate] = useState(new Date());

  // State variables
  const [siteList, setSiteList] = useState<{ label: string; value: number }[]>(
    []
  );
  const [siteSelected, setSiteSelected] = useState<number>(0);
  const [patients, setPatients] = useState<{ label: string; value: number }[]>(
    []
  );
  const [selectedPatient, setSelectedPatient] = useState<number>(0);

  const siteListData = useSiteList();
  const patientsList = usePatientsList();
  const { patientLoader } = useLoadersState();

  // Helper functions
  const unMount = () => {
    setSiteList([]);
    setSiteSelected(0);
    setPatients([]);
    setSelectedPatient(0);
  };

  const cleanState = () => {
    setSiteSelected(0);
    setPatients([]);
    setSelectedPatient(0);
  };

  const fetchPatientList = async () => {
    try {
      const response = await dispatch(
        fetchPatientsList({
          site_id: siteSelected,
          case_date: date.toISOString().split("T")[0],
        })
      );

      if (response.payload && Array.isArray(response.payload)) {
        setPatients(
          response.payload.map((patient) => ({
            label: patient.patient.name,
            value: patient.case_id,
          }))
        );
      }
    } catch (error) {
      console.error("Error fetching patient list:", error);
    }
  };

  const handleAddCase = async () => {
    try {
      await dispatch(putRepCase({ case_id: selectedPatient }));
      cleanState();
    } catch (error) {
      console.error("Error adding case:", error);
    }
  };

  React.useEffect(() => {
    if (modalVisible) {
      dispatch(fetchSiteList());
    }

    return () => {
      unMount();
    };
  }, [dispatch, modalVisible]);

  React.useEffect(() => {
    if (siteListData && Array.isArray(siteListData)) {
      setSiteList(
        siteListData.map((site) => ({
          label: site.name,
          value: site.id,
        }))
      );
    }
  }, [siteListData]);

  React.useEffect(() => {
    if (siteSelected) {
      fetchPatientList();
    }
  }, [siteSelected, date]);

  return (
    <View style={styles.container}>
      {/* Styled DatePicker */}
      <View style={styles.datePickerContainer}>
        <DatePicker
          date={date}
          onDateChange={setDate}
          mode="date"
          theme="light"
        />
      </View>

      <View style={styles.picker}>
        <SearchablePicker
          placeholder={"Select site"}
          items={siteList}
          value={siteSelected}
          onValueChange={(value) => setSiteSelected(value.value)}
        />
      </View>

      {siteSelected > 0 && (
        <View style={styles.picker}>
          {patientLoader ? (
            <Loader />
          ) : (
            <SearchablePicker
              placeholder={"Select patient"}
              items={patients}
              value={selectedPatient}
              onValueChange={(value) => setSelectedPatient(value.value)}
            />
          )}
        </View>
      )}

      {selectedPatient > 0 && (
        <View style={styles.addButton}>
          <Button title="Add Case" onPress={handleAddCase} />
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // backgroundColor: "#121212", // Background color for dark mode
    padding: 16,
  },
  datePickerContainer: {
    // backgroundColor: "#f8f6ff", // Dark mode background
    borderRadius: 8,
    padding: 8,
    marginBottom: 16,
  },
  datePicker: {
    color: "black",
    // width: "100%",
  },
  picker: {
    marginVertical: 8,
  },
  addButton: {
    marginTop: 16,
  },
});

export default AddCaseBottomSheet;
