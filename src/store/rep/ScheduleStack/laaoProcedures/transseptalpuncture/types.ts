interface TypeOption {
  id: number;
  name: string;
  display_name: string;
}

interface AnesthesiaDetails {
  id: number;
  case_id: number;
  anesthesia: number;
  imaging: number;
  catheter: number;
  la_pressure: number;
  fluid_bolus: number;
  anesthesia_type: TypeOption[];
  imaging_type: TypeOption[];
  catheter_type: TypeOption[];
  fluid_bolus_type: TypeOption[];
}

interface IinitialState {
  anesthesiaDetails: AnesthesiaDetails;
  userDetails: any;
  loading: boolean;
  error: any;
}

export type { IinitialState };
