import { useSelector } from "react-redux";
import { RootState } from "../../../store";
import { IPatientCase } from "../../../store/rep/ScheduleStack/patientDetails/types";

const usePreopDetails = (): IPatientCase => {
  const { preopDetails } = useSelector((state: RootState) => state.preop);
  return preopDetails;
};

const useUserDetails = (): IPatientCase => {
  const { userDetails } = useSelector((state: RootState) => state.preop);
  return userDetails;
};

const useGroinAccessDetails = () => {
  const groinAccess = useSelector(
    (state: RootState) => state.preop.groinAccess
  );
  return groinAccess;
};

const useLoaderAndError = () => {
  const loader = useSelector((state: RootState) => state.preop.loading);
  const error = useSelector((state: RootState) => state.preop.error);
  return { loader, error };
};

const useMedData = () => {
  const med = useSelector((state: RootState) => state.preop.medication);

  return med;
};

const useApiMedData = () => {
  const data = useSelector((state: RootState) => state.preop.apiMedication);
  return data;
};

const useMedicationData = (medicineId?: string) => {
  const userDetails = useUserDetails();

  const anticoagulationOptions =
    userDetails?.patient?.anticoagulation?.options?.map((item) => ({
      label: item.name,
      value: item.id,
    }));

  const med_data = useMedData();

  const med_selected_to_edit = med_data?.find((med) => med.id === medicineId);

  const dosageOptions = userDetails?.patient?.anticoagulation?.options
    ?.find((item) => item.id === med_selected_to_edit?.med_id)
    ?.quantity?.map((quantity) => ({
      label: quantity?.toString(),
      value: quantity?.toString(),
    }));

  const findDosageOptions = (medicationId: string) => {
    return userDetails?.patient?.anticoagulation?.options
      ?.find((item) => item.id === medicationId)
      ?.quantity?.map((quantity) => ({
        label: quantity?.toString(),
        value: quantity?.toString(),
      }));
  };

  const frequency_options =
    userDetails?.patient?.anticoagulation?.frequency_options?.map((item) => ({
      label: item.name,
      value: item.id,
    }));

  return {
    anticoagulationOptions,
    med_data,
    med_selected_to_edit,
    dosageOptions,
    frequency_options,
    findDosageOptions,
  };
};

const formatPreOpDetails = (data: IPatientCase) => {
  // Return empty object if data is undefined or null
  if (!data) {
    return {
      case_id: undefined,
      first_name: undefined,
      last_name: undefined,
      patient_id: undefined,
      patient_age: undefined,
      patient_sex: undefined,
      patient_dob: undefined,
      prior_ablation_options: [],
      prior_ablation_selected_id: undefined,
      prior_ablation_selected_name: undefined,
      other_selected_value: undefined,
      anticoagulation_options: [],
      anticoagulation_selected: [],
      cha2ds2_vasc: undefined,
      cha2ds2_vasc_score: undefined,
      has_bled: undefined,
      has_bled_score: undefined,
    };
  }

  const case_id = data?.case_id;
  const first_name = data?.patient?.first_name;
  const last_name = data?.patient?.last_name;
  const middle_name = data?.patient?.middle_name;
  const patient_id = data?.patient?.id;
  const patient_age = data?.patient?.age;
  const patient_sex = data?.patient?.sex;
  const patient_dob = data?.patient?.dob;
  const prior_ablation_options =
    data?.patient?.prior_ablation?.options?.map((item: any) => ({
      label: item.name,
      value: item.id,
    })) || [];
  const prior_ablation_selected_id =
    data?.patient?.prior_ablation?.selected?.id;
  const prior_ablation_selected_name =
    data?.patient?.prior_ablation?.selected?.name;
  const other_selected_value = data?.patient?.prior_ablation?.selected?.other;
  const anticoagulation_options =
    data?.patient?.anticoagulation?.options?.map((option: any) => ({
      label: option.name,
      value: option.id,
    })) || [];
  const anticoagulation_selected =
    data?.patient?.anticoagulation?.selected?.map(
      (selected: any) => selected.id
    ) || [];
  const cha2ds2_vasc = data?.patient?.cha2ds2_vasc?.calculation;

  const cha2ds2_vasc_score = data?.patient?.cha2ds2_vasc?.score;
  const has_bled = data?.patient?.has_bled_score?.calculation;
  const has_bled_score = data?.patient?.has_bled_score?.score;
  const referring_provider_options =
    data?.patient?.referring_provider?.options?.map((option: any) => ({
      label: option.name,
      value: option.id,
    }));
  const referring_provider_selected_id =
    data?.patient?.referring_provider?.selected?.id;
  const pcp_options = data?.patient?.pcp?.options?.map((option: any) => ({
    label: option.name,
    value: option.id,
  }));
  let pcp_selected_id = data?.patient?.pcp?.selected?.id;
  const rationale_options = data?.patient?.rationale?.options?.map(
    (option: any) => ({
      label: option.name,
      value: option.id,
    })
  );
  const rationale_selected_id = data?.patient?.rationale?.selected?.id;
  const rationale_selected_name = data?.patient?.rationale?.selected?.name;
  const rationale_other = data?.patient?.rationale?.selected?.other || "";

  const procedure_date = data?.procedure_date;
  const procedure_time = data?.procedure_time;

  let implanting_physician_selected_id =
    data?.implanting_physician?.selected?.id;
  const implanting_physician_options = data?.implanting_physician?.options?.map(
    (option: any) => ({
      label: option.name,
      value: option.id,
    })
  );
  const site_options = data?.site?.options?.map((option: any) => ({
    label: option.name,
    value: option.id,
  }));
  const site_selected_id = data?.site?.selected?.id;
  const tee_status = data?.patient?.tee;
  const truplan_upload_status = data?.patient?.truplan_upload_status;
  const cta_status = data?.patient?.cta;
  const study = data?.patient?.study;
  const pre_op_imaging = {
    cta: cta_status,
    tee: tee_status,
  };

  return {
    case_id,
    first_name,
    last_name,
    middle_name,
    patient_id,
    patient_age,
    patient_sex,
    patient_dob,
    prior_ablation_options,
    prior_ablation_selected_id,
    prior_ablation_selected_name,
    other_selected_value,
    anticoagulation_options,
    anticoagulation_selected,
    cha2ds2_vasc,
    cha2ds2_vasc_score,
    has_bled,
    has_bled_score,
    rationale_options,
    rationale_selected_id,
    rationale_selected_name,
    rationale_other,
    referring_provider_options,
    referring_provider_selected_id,
    pcp_options,
    pcp_selected_id,
    procedure_date,
    procedure_time,
    implanting_physician_options,
    implanting_physician_selected_id,
    site_options,
    site_selected_id,
    tee_status,
    truplan_upload_status,
    cta_status,
    study,
    pre_op_imaging,
  };
};

const findPatientDemographicsDiff = () => {
  const preopDetails = usePreopDetails();
  const userDetails = useUserDetails();
  const med = useMedData();
  const apiMed = useApiMedData();

  const diff = {
    anticoagulant: JSON.stringify(med) === JSON.stringify(apiMed),
    implanting_physician_id:
      preopDetails?.implanting_physician?.selected?.id ===
      userDetails?.implanting_physician?.selected?.id,
    prior_ablation:
      preopDetails?.patient?.prior_ablation?.selected?.id ===
      userDetails?.patient?.prior_ablation?.selected?.id,
    rationale:
      preopDetails?.patient?.rationale?.selected?.id ===
      userDetails?.patient?.rationale?.selected?.id,
    procedure_date:
      preopDetails?.procedure_date === userDetails?.procedure_date,
    procedure_time:
      preopDetails?.procedure_time === userDetails?.procedure_time,
    site_id:
      preopDetails?.site?.selected?.id === userDetails?.site?.selected?.id,
    patient: {
      first_name:
        preopDetails?.patient?.first_name === userDetails?.patient?.first_name,
      middle_name:
        preopDetails?.patient?.middle_name ===
        userDetails?.patient?.middle_name,
      last_name:
        preopDetails?.patient?.last_name === userDetails?.patient?.last_name,
      dob: preopDetails?.patient?.dob === userDetails?.patient?.dob,
      sex: preopDetails?.patient?.sex === userDetails?.patient?.sex,
      pcp:
        JSON.stringify(preopDetails?.patient?.pcp?.selected?.id) ===
        JSON.stringify(userDetails?.patient?.pcp?.selected?.id),
      referring_providers:
        JSON.stringify(
          preopDetails?.patient?.referring_provider?.selected?.id
        ) ===
        JSON.stringify(userDetails?.patient?.referring_provider?.selected?.id),
    },
  };

  const hasDiff = (obj) => {
    for (const key in obj) {
      if (typeof obj[key] === "object" && obj[key] !== null) {
        if (hasDiff(obj[key])) return true;
      } else if (obj[key] === false) {
        return true;
      }
    }
    return false;
  };

  const currentValues = {
    implanting_physician_id: userDetails?.implanting_physician?.selected?.id,
    anticoagulant: med
      ? med?.map((item) => ({
          medicine: item.med_id,
          quantity: item.med_dose,
          dosing_frequency: item.dosing_frequency_id,
        }))
      : [],
    rationale: userDetails?.patient?.rationale?.selected?.id,
    rationale_other: userDetails?.patient?.rationale?.selected?.other,
    prior_ablation: userDetails?.patient?.prior_ablation?.selected?.id,
    prior_ablation_other: userDetails?.patient?.prior_ablation?.selected?.other,
    procedure_date: userDetails?.procedure_date,
    procedure_time: userDetails?.procedure_time,
    site_id: userDetails?.site?.selected?.id,
    patient: {
      first_name: userDetails?.patient?.first_name,
      middle_name: userDetails?.patient?.middle_name,
      last_name: userDetails?.patient?.last_name,
      dob: userDetails?.patient?.dob,
      sex: userDetails?.patient?.sex,
      pcp: [userDetails?.patient?.pcp?.selected?.id],
      referring_providers: [
        userDetails?.patient?.referring_provider?.selected?.id,
      ],
    },
  };

  return {
    hasDiff: hasDiff(diff),
    currentValues: currentValues,
  };
};

export {
  usePreopDetails,
  useUserDetails,
  useLoaderAndError,
  useGroinAccessDetails,
  findPatientDemographicsDiff,
  formatPreOpDetails,
  useMedicationData,
};
