import { createSlice, PayloadAction, createAsyncThunk } from "@reduxjs/toolkit";
import {
  fetchSchedules,
  fetchSiteList,
  fetchPatientsList,
  putRepCase,
  putChadScoreDetails,
  putHasbledScoreDetails,
} from "./thunk";
import initialState from "./initialstate";

const scheduleSlice = createSlice({
  name: "schedules",
  initialState,
  reducers: {
    setSiteSelectedDate: (state, action: PayloadAction<string>) => {
      state.selectedDate = action.payload;
    },
    showAddCaseModal: (state, action: PayloadAction<any>) => {
      state.showAddCaseModal = action.payload;
    },
    showAddPatientModal: (state, action: PayloadAction<any>) => {
      state.showAddPatientModal = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchSchedules.pending, (state) => {
        state.loaders.agendaLoader = true;
        state.errors.scheduleError = null;
      })
      .addCase(fetchSchedules.fulfilled, (state, action) => {
        state.loaders.agendaLoader = false;
        state.schedules = action.payload;
      })
      .addCase(fetchSchedules.rejected, (state, action) => {
        state.loaders.agendaLoader = false;
        state.errors.scheduleError = action.payload as string;
        state.schedules = [];
      })

      .addCase(fetchSiteList.pending, (state) => {
        state.loaders.siteLoader = true;
        state.errors.siteListError = null;
      })
      .addCase(fetchSiteList.fulfilled, (state, action) => {
        state.loaders.siteLoader = false;
        state.siteList = action.payload;
      })
      .addCase(fetchSiteList.rejected, (state, action) => {
        state.loaders.siteLoader = false;
        state.errors.siteListError = action.payload as string;
        state.siteList = [];
      })

      .addCase(fetchPatientsList.pending, (state) => {
        state.loaders.patientLoader = true;
        state.errors.siteListError = null;
      })
      .addCase(fetchPatientsList.fulfilled, (state, action) => {
        state.loaders.patientLoader = false;
        state.patientList = action.payload;
      })
      .addCase(fetchPatientsList.rejected, (state, action) => {
        state.loaders.patientLoader = false;
        state.errors.patientListError = action.payload as string;
        state.siteList = [];
      })

      .addCase(putRepCase.pending, (state) => {
        state.loaders.patientLoader = true;
        state.errors.siteListError = null;
      })
      .addCase(putRepCase.fulfilled, (state, action) => {
        state.loaders.patientLoader = false;
      })
      .addCase(putRepCase.rejected, (state, action) => {
        state.loaders.patientLoader = false;
        state.errors.patientListError = action.payload as string;
        state.siteList = [];
      });

    builder
      .addCase(putChadScoreDetails.pending, (state) => {
        state.loaders.agendaLoader = true;
        state.errors.scheduleError = null;
      })
      .addCase(putChadScoreDetails.fulfilled, (state, action) => {
        state.loaders.agendaLoader = false;
        state.chadScore = action.payload;
      })
      .addCase(putChadScoreDetails.rejected, (state, action) => {
        state.loaders.agendaLoader = false;
        state.errors.scheduleError = action.payload as string;
        state.chadScore = initialState?.chadScore;
      });

    builder
      .addCase(putHasbledScoreDetails.pending, (state) => {
        state.loaders.agendaLoader = true;
        state.errors.scheduleError = null;
      })
      .addCase(putHasbledScoreDetails.fulfilled, (state, action) => {
        state.loaders.agendaLoader = false;
        state.chadScore = action.payload;
      })
      .addCase(putHasbledScoreDetails.rejected, (state, action) => {
        state.loaders.agendaLoader = false;
        state.errors.scheduleError = action.payload as string;
        state.chadScore = initialState?.chadScore;
      });
  },
});

export const { setSiteSelectedDate, showAddCaseModal, showAddPatientModal } =
  scheduleSlice.actions;

export default scheduleSlice.reducer;
