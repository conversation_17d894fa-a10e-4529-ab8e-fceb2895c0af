import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { ReferringProvider, RepReferringProvider } from "./types";
import {
  fetchReferringProviders,
  fetchReferringProviderById,
  createReferringProvider,
  updateReferringProvider,
} from "./thunk";
import initialState from "./initialstate";
import { fetchCredentialOptions } from "./thunk";

const referringProvidersSlice = createSlice({
  name: "referringProviders",
  initialState,
  reducers: {
    setProviderDetails: (state, action) => {
      state.userDetails = {
        ...state.userDetails,
        ...action.payload,
      };
    },
    resetProviderDetails: (state) => {
      state.userDetails = null;
    },
    setActiveTab: (
      state,
      action: PayloadAction<"referring-providers" | "pcp-providers">
    ) => {
      state.activeTab = action.payload;
    },
    showAddProviderModal: (state, action: PayloadAction<boolean>) => {
      state.showAddProviderModal = action.payload;
    },
    setSelectedProvider: (state, action: PayloadAction<string>) => {
      state.selectedProvider = action.payload;
    },
  },
  extraReducers: (builder) => {
    // Fetch all referring providers - FIXED: Only update providerListDetails
    builder.addCase(fetchReferringProviders.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(
      fetchReferringProviders.fulfilled,
      (state, action: PayloadAction<ReferringProvider[]>) => {
        // FIXED: Store list of providers separately, don't overwrite userDetails
        state.providerListDetails = action.payload;
        state.loading = false;
      }
    );
    builder.addCase(fetchReferringProviders.rejected, (state, action) => {
      state.loading = false;
      state.error =
        action.error.message || "Failed to fetch referring providers";
    });

    // Fetch specific provider - FIXED: Only update selected provider details
    builder.addCase(fetchReferringProviderById.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(
      fetchReferringProviderById.fulfilled,
      (state, action: PayloadAction<ReferringProvider>) => {
        // FIXED: Only update providerDetails and userDetails for selected provider
        state.providerDetails = action.payload;
        state.userDetails = action.payload;
        state.loading = false;
      }
    );
    builder.addCase(fetchReferringProviderById.rejected, (state, action) => {
      state.loading = false;
      state.error =
        action.error.message || "Failed to fetch referring provider";
    });

    // Update provider - FIXED: Only update the specific provider
    builder.addCase(updateReferringProvider.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(
      updateReferringProvider.fulfilled,
      (state, action: PayloadAction<ReferringProvider>) => {
        // FIXED: Update both the provider details and the list
        state.providerDetails = action.payload;
        state.userDetails = action.payload;

        // Update the provider in the list as well
        if (state.providerListDetails) {
          const index = state.providerListDetails.findIndex(
            (provider) => provider.id === action.payload.id
          );
          if (index !== -1) {
            state.providerListDetails[index] = action.payload;
          }
        }

        state.loading = false;
      }
    );
    builder.addCase(updateReferringProvider.rejected, (state, action) => {
      state.loading = false;
      state.error = action.error.message || "Failed to update provider";
    });

    // Add provider - FIXED: Add to list instead of overwriting
    builder.addCase(createReferringProvider.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(
      createReferringProvider.fulfilled,
      (state, action: PayloadAction<ReferringProvider>) => {
        // FIXED: Add new provider to the list
        if (state.providerListDetails) {
          state.providerListDetails.push(action.payload);
        } else {
          state.providerListDetails = [action.payload];
        }

        state.providerDetails = action.payload;
        state.userDetails = action.payload;
        state.selectedProvider = action.payload.id;
        state.loading = false;
      }
    );
    builder.addCase(createReferringProvider.rejected, (state, action) => {
      state.loading = false;
      state.error = action.error.message || "Failed to add provider";
    });

    // builder.addCase(fetchCredentialOptions.pending, (state) => {
    //   state.loading = true;
    //   state.error = null;
    // });
    // builder.addCase(
    //   fetchCredentialOptions.fulfilled,
    //   (state, action: PayloadAction<RepReferringProvider[]>) => {
    //     state.credentialOptions = action.payload;
    //     state.loading = false;
    //   }
    // );
    // builder.addCase(fetchCredentialOptions.rejected, (state, action) => {
    //   state.loading = false;
    //   state.error =
    //     action.error.message || "Failed to fetch credential options";
    // });
    // Fetch credential options
    builder.addCase(fetchCredentialOptions.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(
      fetchCredentialOptions.fulfilled,
      (state, action: PayloadAction<string[]>) => {
        state.credentialOptions = action.payload;
        state.loading = false;
      }
    );
    builder.addCase(fetchCredentialOptions.rejected, (state, action) => {
      state.loading = false;
      state.error =
        action.error.message || "Failed to fetch credential options";
    });
  },
});

export const {
  setProviderDetails,
  resetProviderDetails,
  setActiveTab,
  showAddProviderModal,
  setSelectedProvider,
} = referringProvidersSlice.actions;

export default referringProvidersSlice.reducer;
