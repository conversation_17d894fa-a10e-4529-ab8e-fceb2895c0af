import * as React from "react";
import { createStackNavigator } from "@react-navigation/stack";
import HomeScreen from "./screens/HomeScreen";

interface IHomeScreenStackProps {}

const HomeScreenStack: React.FunctionComponent<IHomeScreenStackProps> = (
  props
) => {
  const HomeStack = createStackNavigator();

  return (
    <HomeStack.Navigator>
      <HomeStack.Screen name="Dashboard" component={HomeScreen} />
    </HomeStack.Navigator>
  );
};

export default HomeScreenStack;
