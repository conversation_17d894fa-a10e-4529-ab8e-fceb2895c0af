import { useSelector } from "react-redux";
import { RootState } from "../../../store";
import { IProcedureDetails } from "../../../store/coordinator/ScheduleStack/procedure/types";

const useLoaderAndError = () => {
  const loader = useSelector(
    (state: RootState) => state.coordinator.schedule.procedureDetails.loading
  );
  const error = useSelector(
    (state: RootState) => state.coordinator.schedule.procedureDetails.error
  );
  return { loader, error };
};

const useProcedureDetailsUserDetails = (): IProcedureDetails | null => {
  const { userDetails } = useSelector(
    (state: RootState) => state.coordinator.schedule.procedureDetails
  );
  return userDetails;
};

const useProcedureDetails = () => {
  const { procedureDetails } = useSelector(
    (state: RootState) => state.coordinator.schedule.procedureDetails
  );
  return procedureDetails;
};

const formatProcedureDetails = (data: IProcedureDetails | null) => {
  const procedure_date = data?.procedure_date || null;

  const procedure_time = data?.procedure_time || null;

  const implanting_physician = data?.implanting_physician?.id || null;
  const implanting_physician_name = data?.implanting_physician?.name || null;
  const selectDeviceValue = data?.device?.selected?.device_size;

  const selectedDevice = data?.device?.selected.id;
  const deviceType = data?.device?.options?.map((option: any) => ({
    label: option.name,
    value: option.id,
    option: option.device_size?.map((size: any) => ({
      label: size?.toString(),
      value: size?.toString(),
    })),
  }));

  const anesthesiaType = data?.anesthesia?.options?.map((option: any) => ({
    label: option.name,
    value: option.id,
  }));
  const anesthesiaTypeSelected = data?.anesthesia?.selected?.id;
  const complications_toggler =
    data?.complication?.selected?.complication_present === true ? "Yes" : "No";

  const complications_type = data?.complication?.options?.map(
    (option: any) => ({
      label: option.name,
      value: option.id,
    })
  );
  const complication_selected_id = data?.complication?.selected?.id;
  const complication_selected_name = data?.complication?.selected?.name;

  const complication_other = data?.complication?.selected?.complication_other;
  const residualLeak = data?.leak === true ? "Yes" : "No";
  const residualLeakValue = data?.leak_value;
  const ablationCase = data?.afib_ablation === true ? "Yes" : "No";

  return {
    procedure_date,
    implanting_physician,
    implanting_physician_name,
    selectedDevice,
    selectDeviceValue,
    deviceType,
    anesthesiaType,
    anesthesiaTypeSelected,
    residualLeak,
    ablationCase,
    residualLeakValue,
    complications_type,
    complication_selected_id,
    complication_selected_name,
    complication_other,
    complications_toggler,
    procedure_time,
  };
};

const findProcedureDetailsDiff = () => {
  const userDetails = useProcedureDetailsUserDetails();
  const procedureDetails = useProcedureDetails();

  const diff = JSON.stringify(userDetails) !== JSON.stringify(procedureDetails);

  const currentValues = {
    implanting_physician_id: userDetails?.implanting_physician?.id,
    procedure_detail: {
      leak: userDetails?.leak,
      leak_value: userDetails?.leak_value,
      device_size: userDetails?.device?.selected?.device_size,
      device_type: userDetails?.device?.selected?.id,
      anesthesia_type: userDetails?.anesthesia?.selected?.id,
      complication_id: userDetails?.complication?.selected?.id,
      complication_other:
        userDetails?.complication?.selected?.complication_other,
      complication_present:
        userDetails?.complication?.selected?.complication_present,
    },

    procedure_date: userDetails?.procedure_date,
    procedure_time: userDetails?.procedure_time,
    afib_ablation: userDetails?.afib_ablation,
  };

  return {
    hasDiff: diff,
    currentValues: currentValues,
  };
};

export {
  useLoaderAndError,
  useProcedureDetailsUserDetails,
  useProcedureDetails,
  findProcedureDetailsDiff,
  formatProcedureDetails,
};
