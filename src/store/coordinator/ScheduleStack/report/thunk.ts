import { createAsyncThunk } from "@reduxjs/toolkit";
import api from "../../../../api/api";

const fetchcaseSynopsis = createAsyncThunk(
  "schedule/fetchcaseSynopsis",
  async ({ case_id }: { case_id: string }, thunkAPI) => {
    try {
      const response = await api.private.get(
        api.routes.privateRoutes.rep.caseSynopsis_get(case_id)
      );

      if (response.data && response.data.result) {
        return response.data.result;
      } else {
        return thunkAPI.rejectWithValue(
          'Invalid response format: Missing "result" field'
        );
      }
    } catch (error) {
      console.error("Error caught during API call:", error);
      return thunkAPI.rejectWithValue("Failed to fetch case synopsis details");
    }
  }
);

export { fetchcaseSynopsis };
