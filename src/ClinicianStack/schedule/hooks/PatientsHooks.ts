import { useSelector } from "react-redux";
import { RootState } from "../../../store";
import { AppDispatch } from "../../../store";
import { setSelectedPatient } from "../../../store/rep/ScheduleStack/patients";

// const dispatch = useDispatch<AppDispatch>();

const useSiteDetails = () => {
  const selectedDate = useSelector(
    (state: RootState) => state.patients.selectedDate
  );
  const selectedHospitalName = useSelector(
    (state: RootState) => state.patients.selectedHospitalName
  );
  const selectedHospitalId = useSelector(
    (state: RootState) => state.patients.selectedHospitalId
  );
  const implantingPhysicianName = useSelector(
    (state: RootState) => state.patients.implantingPhysicianName
  );

  const implantingPhysicianImage = useSelector(
    (state: RootState) => state.patients.implantingPhysicianImage
  );

  const hospitalImage = useSelector(
    (state: RootState) => state.patients.hospitalImage
  );

  return {
    selectedDate,
    selectedHospitalName,
    selectedHospitalId,
    implantingPhysicianName,
    implantingPhysicianImage,
    hospitalImage,
  };
};

const usePatientCases = () => {
  const patientCases = useSelector(
    (state: RootState) => state.patients.patients
  );
  return patientCases;
};

// const useSelectedPatient = () => {
//   const selectedPatient = useSelector(
//     (state: RootState) => state.clinician.schedules.selectedPatient
//   );

//   return selectedPatient;
//   return {
//     case_id: "6756dd69a403e4d2cde949e8",
//     patient: {
//       id: "6756cd8eb6ce792aed7c6e7b",
//       name: "Melissa Grey",
//       image_url: null,
//       age: 71,
//       sex: "F",
//       cta: true,
//       tee: false,
//       anticoagulation: null,
//       rationale: "Prior GI bleed",
//       cha2ds2_vasc: {
//         score: 6,
//         calculation: {
//           congestive_heart_failure: 0,
//           hypertension: 0,
//           age: 1,
//           diabetes_mellitus: 1,
//           stroke_tia_thromboembolic_event: 2,
//           vascular_disease: 1,
//           sex: 1,
//         },
//       },
//       referring_provider: {
//         id: "6756eb47b7420b80d0e94973",
//         name: "David Taylor, MD",
//       },
//       afib_ablation: false,
//       address: "Salem, WA",
//       anticipated: "Overnight Stay",
//     },
//     procedure_date: "2025-01-06",
//     procedure_time: "10:00:00",
//     anticoagulant: [
//       {
//         id: "675824cccb6878d48be9496c",
//         name: "Aspirin",
//       },
//       {
//         id: "675824cccb6878d48be94971",
//         name: "Rivaroxaban",
//       },
//     ],
//   };
// };

// const useCoordinatorSelectedPatient = () => {
//   const selectedPatient = useSelector(
//     (state: RootState) => state.clinician.schedules.selectedPatient
//   );

//   return selectedPatient;
//   return {
//     case_id: "6756dd69a403e4d2cde949e8",
//     patient: {
//       id: "6756cd8eb6ce792aed7c6e7b",
//       name: "Melissa Grey",
//       image_url: null,
//       age: 71,
//       sex: "F",
//       cta: true,
//       tee: false,
//       anticoagulation: null,
//       rationale: "Prior GI bleed",
//       cha2ds2_vasc: {
//         score: 6,
//         calculation: {
//           congestive_heart_failure: 0,
//           hypertension: 0,
//           age: 1,
//           diabetes_mellitus: 1,
//           stroke_tia_thromboembolic_event: 2,
//           vascular_disease: 1,
//           sex: 1,
//         },
//       },
//       referring_provider: {
//         id: "6756eb47b7420b80d0e94973",
//         name: "David Taylor, MD",
//       },
//       afib_ablation: false,
//       address: "Salem, WA",
//       anticipated: "Overnight Stay",
//     },
//     procedure_date: "2025-01-06",
//     procedure_time: "10:00:00",
//     anticoagulant: [
//       {
//         id: "675824cccb6878d48be9496c",
//         name: "Aspirin",
//       },
//       {
//         id: "675824cccb6878d48be94971",
//         name: "Rivaroxaban",
//       },
//     ],
//   };
// };

export {
  useSiteDetails,
  usePatientCases,
  // useSelectedPatient,
  // useCoordinatorSelectedPatient,
};
