import { createSlice } from "@reduxjs/toolkit";
import initialState from "./initialstate";
import { fetchTEEDetails, putTEEDetails } from "./thunk";

const teeSlice = createSlice({
  name: "tee",
  initialState,
  reducers: {
    setTEEUserDetails: (state, action) => {
      state.userDetails = {
        ...state.userDetails,
        ...action.payload,
      };
    },

    resetLaaAnatomyState: (state) => {
      state.tee = null;
      state.userDetails = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchTEEDetails.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchTEEDetails.fulfilled, (state, action) => {
        state.loading = false;
        state.tee = action.payload;
        state.userDetails = action.payload;
      })
      .addCase(fetchTEEDetails.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });

    builder
      .addCase(putTEEDetails.pending, (state) => {
        state.loading = true;
      })
      .addCase(putTEEDetails.fulfilled, (state, action) => {
        state.loading = false;
        state.error = null;
      })
      .addCase(putTEEDetails.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const { setTEEUserDetails, resetLaaAnatomyState } = teeSlice.actions;

export default teeSlice.reducer;
