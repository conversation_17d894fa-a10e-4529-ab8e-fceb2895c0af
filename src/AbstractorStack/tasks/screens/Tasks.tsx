import * as React from "react";
import { View, FlatList } from "react-native";
import CustomText from "../../../components/CustomText";
import CustomInput from "../../../components/CustomTextInput";
import RepTaskCard from "../Components/TaskCard";
import CustomTabView from "../../../components/CustomTabView";
import { useCoordinatorTasks, useLoaderAndError } from "../hooks/tasksHooks";
import { useFocusEffect } from "@react-navigation/native";
import Loader from "../../../components/Loader";
import Error from "../../../components/Error";
import { fetchCoordinatorTasks } from "../../../store/coordinator/Tasks/tasks/thunk";
import { useDispatch } from "react-redux";
import { AppDispatch } from "../../../store";
import { ITask } from "../../../store/rep/Tasks/tasks/types";

interface ITasksProps {}

const Tasks: React.FunctionComponent<ITasksProps> = (props) => {
  const dispatch = useDispatch<AppDispatch>();

  useFocusEffect(
    React.useCallback(() => {
      const fetchTasks = async () => {
        try {
          const response = await dispatch(fetchCoordinatorTasks());

          if (response.payload) {
          }
        } catch (error) {
          console.error("Error fetching tasks:", error);
        }
      };
      fetchTasks();
    }, [])
  );

  const { error, loader } = useLoaderAndError();

  const coordinatorTasks = useCoordinatorTasks();

  const filterTopics = ["All", "Due", "in_progress", "Completed"];

  const [selectedTopic, setSelectedTopic] = React.useState(filterTopics[0]);
  const [searchQuery, setSearchQuery] = React.useState("");

  const tabs = [
    { label: "All", value: "All" },
    { label: "Due", value: "Due" },
    { label: "In Progress", value: "in_progress" },
    { label: "Completed", value: "Completed" },
  ];

  const filterTasks = (): ITask[] | null => {
    if (!coordinatorTasks) {
      return null;
    }

    let filteredTasks: ITask[] = coordinatorTasks;

    if (selectedTopic === "Due") {
      filteredTasks = coordinatorTasks.filter(
        (task) => task.task_status.toLowerCase() === "pending"
      );
    } else if (selectedTopic === "in_progress") {
      filteredTasks = coordinatorTasks.filter(
        (task) => task.task_status.toLowerCase() === "in_progress"
      );
    } else if (selectedTopic === "Completed") {
      filteredTasks = coordinatorTasks.filter(
        (task) => task.task_status.toLowerCase() === "completed"
      );
    }

    if (searchQuery.trim()) {
      filteredTasks = filteredTasks.filter((task) =>
        task.patient.name.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    return filteredTasks;
  };

  if (loader) {
    return <Loader />;
  }

  if (error) {
    return <Error message={error} />;
  }

  return (
    <View className="flex-1 p-4 bg-primaryBg">
      <CustomInput
        placeholder="Search Patient"
        inputValue={searchQuery}
        onInputChange={setSearchQuery}
        searchIcon={{
          icon: true,
          iconPress: () => {},
        }}
      />
      <View className="mt-3">
        <CustomTabView
          options={tabs}
          onSelect={(option) => setSelectedTopic(option)}
        />
      </View>

      <FlatList
        data={filterTasks()}
        // keyExtractor={genUUID()}
        showsVerticalScrollIndicator={false}
        renderItem={({ item }) => (
          <View className="mb-3">
            <RepTaskCard info={item} />
          </View>
        )}
        ListEmptyComponent={() => {
          return (
            <View className="flex-1 justify-center items-center h-[250px] ">
              <CustomText value="No tasks found" />
            </View>
          );
        }}
      />
    </View>
  );
};

export default Tasks;
