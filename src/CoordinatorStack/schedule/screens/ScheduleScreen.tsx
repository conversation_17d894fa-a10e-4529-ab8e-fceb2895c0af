import React, { useState, useEffect, useRef } from "react";
import {
  Text,
  View,
  ActivityIndicator,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
} from "react-native";
import { MatIcon } from "../../../utils";
import { CalendarProvider, ExpandableCalendar } from "react-native-calendars";
import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons";
import { useDispatch } from "react-redux";
import { AppDispatch } from "../../../store";
import {
  fetchCoordinatorSchedules,
  putChadScoreDetails,
} from "../../../store/coordinator/ScheduleStack/schedule/thunk";
import PatientCard from "../components/PatientCard";
import moment from "moment";
import CustomTabView from "../../../components/CustomTabView";
import { useLoadersState } from "../hooks/schedulesHooks";
import CustomSearchBar from "../../../components/CustomSearchBar";
import ScreenWrapper from "../../../components/ScreenWrapper";
import BottomSheetComponent, {
  BottomSheetRefProps,
} from "../../../components/BottomSheetComponent";
import AddPatient from "./AddPatient";
import {
  useCoordinatorSchedules,
  useCoordinatorSchedulesUserDetails,
} from "../hooks/schedulesHooks";
import { useTruplanLink } from "../hooks/ctaHooks";
import CHADCard from "../../../components/CHADCard";
import { useFocusEffect, useNavigation } from "@react-navigation/native";
import { StackNavigationProp } from "@react-navigation/stack";
import DatePicker from "react-native-date-picker";
import CustomText from "../../../components/CustomText";
import Heading from "../../../components/Heading";
import { resetPatientDetails } from "../../../store/coordinator/ScheduleStack/addpatient";
import { setChadScore } from "../../../store/coordinator/ScheduleStack/schedule";

// Define the navigation param list for the Coordinator stack
type CoordinatorStackParamList = {
  ScheduleScreen: undefined;
  SettingScreen: { url: string };
  // Add other screens as needed
};

type CoordinatorNavigationProp = StackNavigationProp<CoordinatorStackParamList>;

interface IScheduleScreenProps {}

const ScheduleScreen: React.FunctionComponent<IScheduleScreenProps> = () => {
  // NEW START
  const navigation = useNavigation();
  const coordinatorSchedule = useCoordinatorSchedules();
  const truplanLink = useTruplanLink();

  const filterTopics = ["pre-op", "procedure", "completed"];

  const [selectedTopic, setSelectedTopic] = React.useState(filterTopics[1]);

  const bottomSheetRef = useRef<BottomSheetRefProps>(null);
  const [bottomSheetContent, setBottomSheetContent] =
    useState<React.ReactNode>(null);

  const handleClose = () => {
    bottomSheetRef.current?.close();
    dispatch(resetPatientDetails());
  };

  // For CHAD press update: clear custom content so CHADCard displays.
  const handleChadPress = (patient: any) => {
    setSelectedPatientId(patient?.case_id);
    setChad(patient?.patient?.cha2ds2_vasc?.calculation);
    setBottomSheetContent(null);
    bottomSheetRef.current?.open();
  };
  const openBottomSheet = (content: React.ReactNode) => {
    setBottomSheetContent(content);
    bottomSheetRef.current?.open();
  };

  const [date, setDate] = useState(new Date().toISOString().split("T")[0]);
  const [open, setOpen] = useState(false);
  const [fromDate, setFromDate] = React.useState(() => {
    const defaultFromDate = new Date();
    defaultFromDate.setDate(defaultFromDate.getDate() - 30);
    return defaultFromDate;
  });
  const [toDate, setToDate] = React.useState(new Date());

  const [dateModalFrom, setDateModalFrom] = React.useState(false);
  const [dateModalTo, setDateModalTo] = React.useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [selectedPatientId, setSelectedPatientId] = useState(null);
  const dispatch = useDispatch<AppDispatch>();
  const { scheduleLoader } = useLoadersState();
  const [chad, setChad] = useState<any>({});

  const fetchEvents = async (selectedDate: Date) => {
    if (selectedTopic === "completed") {
      // Unchanged: uses custom date selectors for completed topics.
      const res = await dispatch(
        fetchCoordinatorSchedules({
          case_date: moment(fromDate).format("YYYY-MM-DD"),
          to_date: moment(toDate).format("YYYY-MM-DD"),
          scheduleType: selectedTopic,
        })
      );
    } else {
      // NEW: Use week boundaries instead of fixed offset days
      const weekStart = moment(selectedDate)
        .startOf("isoWeek")
        .format("YYYY-MM-DD");
      const weekEnd = moment(selectedDate)
        .endOf("isoWeek")
        .format("YYYY-MM-DD");
      const res = await dispatch(
        fetchCoordinatorSchedules({
          case_date: weekStart,
          to_date: weekEnd,
          scheduleType: selectedTopic,
        })
      );
    }
  };

  useFocusEffect(
    React.useCallback(() => {
      fetchEvents(date);
    }, [date, selectedTopic, fromDate, toDate])
  );

  const tabs = [
    { label: "Pre-Op", value: "pre-op" },
    { label: "Scheduled", value: "procedure" },
    { label: "Completed", value: "completed" },
  ];

  const filteredSections = () => {
    if (coordinatorSchedule.length > 0) {
      if (selectedTopic === "procedure") {
        return coordinatorSchedule
          ?.filter((item) => {
            const patientName = item.patient?.name?.toLowerCase() || "";
            return (
              patientName.includes(searchQuery.toLowerCase()) &&
              item.procedure_date === date
            );
          })
          .map((item) => ({
            ...item,
            preop: selectedTopic === "pre-op" ? true : false,
          }));
      } else {
        return coordinatorSchedule
          ?.filter((item) => {
            const patientName = item.patient?.name?.toLowerCase() || "";
            return patientName.includes(searchQuery.toLowerCase());
          })
          .map((item) => ({
            ...item,
            preop: selectedTopic === "pre-op" ? true : false,
          }));
      }
    } else {
      return null;
    }
  };

  const filteredData = React.useMemo(
    () => filteredSections(),
    [filteredSections]
  );

  const saveDetails = async (updatedValues: any) => {
    handleClose();
    try {
      const res = await dispatch(
        putChadScoreDetails({
          case_id: selectedPatientId,
          payload: updatedValues,
        })
      );
      if (res) {
        await fetchEvents(date);
      }
    } catch (error) {
      console.error("Error updating CHAD Score:", error);
    }
  };

  const handleRefresh = async () => {
    await fetchEvents(date);
  };

  const marked = coordinatorSchedule?.reduce((acc: any, item: any) => {
    acc[item.procedure_date] = { marked: true, dotColor: "#8143d9" };
    return acc;
  }, {});

  return (
    <>
      <View className="bg-primaryBg">
        <View className="px-3 mt-4 ml-[2px] flex-row items-center justify-between mb-2 mr-1">
          <View className="flex-1">
            <CustomSearchBar
              placeholder="Search Patient Name"
              value={searchQuery}
              onChangeText={setSearchQuery}
            />
          </View>

          <TouchableOpacity
            onPress={() => {
              navigation.navigate("SettingsScreen", {
                // url: truplanLink,
              });
            }}
            className="ml-2"
          >
            {MatIcon("office-building-cog", "#8143d9", 27)}
          </TouchableOpacity>

          <TouchableOpacity
            onPress={() =>
              openBottomSheet(
                <AddPatient
                  onClose={handleClose}
                  onSave={handleRefresh}
                  bottomSheetRef={bottomSheetRef}
                />
              )
            }
            className="ml-2"
          >
            <MaterialCommunityIcons
              name={showFilters ? "account-plus-outline" : "account-plus"}
              color="#8143d9"
              size={27}
            />
          </TouchableOpacity>
        </View>
        <View className="px-3">
          <CustomTabView
            options={tabs}
            onSelect={(option) => setSelectedTopic(option)}
          />
        </View>
      </View>

      <CalendarProvider
        date={date.toString()}
        onDateChanged={(date) => setDate(date)}
        style={styles.calendarProvider}
      >
        {selectedTopic === "pre-op" || selectedTopic === "completed" ? null : (
          <ExpandableCalendar
            firstDay={1}
            markedDates={marked}
            onDayPress={(day) => setDate(day.dateString)}
            theme={styles.calendarTheme}
          />
        )}

        {selectedTopic === "completed" && (
          <View className="flex-row justify-between px-4">
            <View>
              <Heading
                text="From Date"
                size="label"
                color={"black"}
                extraStyle="pb-2"
                showSeperator={false}
              />

              <TouchableOpacity
                onPress={() => {
                  setDateModalFrom(true);
                }}
                className="border border-primaryPurple p-4 rounded-lg bg-primaryWhite"
              >
                <CustomText
                  value={moment(fromDate).format("MM-DD-YYYY")}
                  className="text-primaryBlack text-md"
                />
              </TouchableOpacity>
            </View>

            <View>
              <Heading
                text="To Date"
                size="label"
                color={"black"}
                extraStyle="pb-2"
                showSeperator={false}
              />

              <TouchableOpacity
                onPress={() => {
                  setDateModalTo(true);
                }}
                className="border border-primaryPurple p-4 rounded-lg bg-primaryWhite"
              >
                <CustomText
                  value={moment(toDate).format("MM-DD-YYYY")}
                  className="text-primaryBlack text-md"
                />
              </TouchableOpacity>
            </View>
          </View>
        )}
        <ScreenWrapper direction="column" onRefresh={handleRefresh}>
          {scheduleLoader ? (
            <View className="mt-48 flex-1 justify-center items-center">
              <ActivityIndicator size="large" color="#8143d9" />
            </View>
          ) : (
            <View>
              {filteredData && filteredData.length > 0 ? (
                filteredData.map((item) => (
                  <View key={item.case_id} className="mb-4">
                    <PatientCard
                      patientCase={item}
                      chadPress={() => handleChadPress(item)}
                      showAnticipatedAndDate={
                        selectedTopic === "pre-op" ? false : true
                      }
                    />
                  </View>
                ))
              ) : (
                <View className="mt-4 flex-1 justify-center items-center p-4">
                  <CustomText value="None" className="text-gray-500 text-lg" />
                </View>
              )}
            </View>
          )}
        </ScreenWrapper>
      </CalendarProvider>

      <BottomSheetComponent
        ref={bottomSheetRef}
        snapPoints={["50%", "90%"]}
        backgroundColor="#FFFFFF"
        onClose={handleClose}
      >
        {bottomSheetContent
          ? bottomSheetContent
          : chad && <CHADCard chad={chad} onSave={saveDetails} />}
      </BottomSheetComponent>

      <DatePicker
        modal
        open={dateModalFrom}
        date={fromDate}
        onConfirm={(date) => {
          setDateModalFrom(false);
          setFromDate(date);
        }}
        onCancel={() => {
          setDateModalFrom(false);
        }}
        mode={"date"}
        maximumDate={new Date()}
      />

      <DatePicker
        modal
        open={dateModalTo}
        date={toDate}
        onConfirm={(date) => {
          setDateModalTo(false);
          setToDate(date);
        }}
        onCancel={() => {
          setDateModalTo(false);
        }}
        mode={"date"}
        minimumDate={
          fromDate ? moment(fromDate).add(1, "days").toDate() : new Date()
        }
        maximumDate={new Date()}
      />
    </>
  );
};

export default ScheduleScreen;

const styles = StyleSheet.create({
  calendarProvider: {
    backgroundColor: "#f8f6ff",
  },
  calendarTheme: {
    selectedDayBackgroundColor: "#8143d9",
    todayTextColor: "#8143d9",
    arrowColor: "#8143d9",
    monthTextColor: "#8143d9",
    textDayFontWeight: "500",
    textMonthFontWeight: "bold",
    textDayHeaderFontWeight: "500",
  },
  section: {
    backgroundColor: "#f8f6ff",
  },
  loader: {
    position: "absolute",
    top: 0,
    left: 0,
    width: "100%",
    height: "100%",
    backgroundColor: "white",
    justifyContent: "center",
    alignItems: "center",
  },
  emptyItem: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 10,
  },
  emptyText: {
    color: "#999",
    fontSize: 16,
  },
});
