import { useState, useEffect, useRef, useCallback } from "react";

interface AutosaveOptions {
  delay: number;
  dependencies: any[];
  isDataLoaded?: boolean; // Optional flag to indicate if data has been loaded from API
  validator?: () => boolean; // Optional validator function that returns true if data is valid
  skipDependenciesCheck?: boolean; // Optional flag to skip dependencies check for specific updates
  screenId?: string; // Optional unique identifier for the screen using this hook
}

// Custom deep comparison function
const deepEqual = (obj1: any, obj2: any): boolean => {
  if (obj1 === obj2) return true; // Handle simple equality and same reference

  // If one of them is not an object or is null, return false
  if (
    typeof obj1 !== "object" ||
    obj1 === null ||
    typeof obj2 !== "object" ||
    obj2 === null
  ) {
    return false;
  }

  // Compare the number of keys (length)
  const keys1 = Object.keys(obj1);
  const keys2 = Object.keys(obj2);

  if (keys1.length !== keys2.length) return false;

  // Compare each key and its value recursively
  for (let key of keys1) {
    if (!keys2.includes(key)) return false; // Key mismatch

    if (!deepEqual(obj1[key], obj2[key])) return false; // Value mismatch
  }

  return true; // All checks passed
};

// Generate a hash for an object to track changes
const generateHash = (obj: any): string => {
  try {
    return JSON.stringify(obj);
  } catch (e) {
    console.error("Error generating hash:", e);
    return Math.random().toString(); // Fallback to random hash if stringify fails
  }
};

// Global store to track save operations across different screens
const globalSaveOperations: { [screenId: string]: boolean } = {};

// Global store to track pending changes across different screens
const globalPendingChanges: { [screenId: string]: boolean } = {};

// Global store to track save messages across different screens
const globalSaveMessages: { [screenId: string]: string } = {};

// Global store to track validation status across different screens
const globalValidationStatus: { [screenId: string]: boolean } = {};

// Global store to track last saved hash for each screen
const globalLastSavedHashes: { [screenId: string]: string } = {};

// Global store to track unsaved changes status for each screen
const globalUnsavedChanges: { [screenId: string]: boolean } = {};

// Queue for save operations
interface SaveOperation {
  screenId: string;
  saveFunction: () => Promise<any>;
  timestamp: number;
}

let saveQueue: SaveOperation[] = [];
let isProcessingQueue = false;

// Process the save queue
const processSaveQueue = async () => {
  if (isProcessingQueue || saveQueue.length === 0) return;

  isProcessingQueue = true;

  // Sort the queue by timestamp (oldest first)
  saveQueue.sort((a, b) => a.timestamp - b.timestamp);

  // Get the next operation
  const nextOperation = saveQueue.shift();

  if (nextOperation) {
    try {
      await nextOperation.saveFunction();
    } catch (error) {
      console.error(
        `Error processing save operation for screen ${nextOperation.screenId}:`,
        error
      );
    } finally {
      // Mark this screen's operation as complete
      globalSaveOperations[nextOperation.screenId] = false;

      // If there are pending changes for this screen, add a new operation to the queue
      if (globalPendingChanges[nextOperation.screenId]) {
        globalPendingChanges[nextOperation.screenId] = false;
      }

      isProcessingQueue = false;

      // Process the next operation in the queue
      processSaveQueue();
    }
  } else {
    isProcessingQueue = false;
  }
};

export const useAutosave = (
  saveDetails: () => Promise<any>,
  options: AutosaveOptions
) => {
  const {
    delay,
    dependencies,
    isDataLoaded = true,
    validator,
    skipDependenciesCheck = false,
    screenId = "default", // Default screen ID if none provided
  } = options;

  // Initialize global state for this screen if not already set
  if (globalSaveMessages[screenId] === undefined) {
    globalSaveMessages[screenId] = "";
  }
  if (globalValidationStatus[screenId] === undefined) {
    globalValidationStatus[screenId] = false;
  }
  if (globalUnsavedChanges[screenId] === undefined) {
    globalUnsavedChanges[screenId] = false;
  }
  if (globalSaveOperations[screenId] === undefined) {
    globalSaveOperations[screenId] = false;
  }

  // Use state with initializers that check the global stores
  const [isSaving, setIsSaving] = useState(
    () => globalSaveOperations[screenId] || false
  );
  const [saveMessage, setSaveMessage] = useState(
    () => globalSaveMessages[screenId] || ""
  );
  const [validationFailed, setValidationFailed] = useState(
    () => globalValidationStatus[screenId] || false
  );

  // Sync local state with global state when screenId changes
  useEffect(() => {
    setIsSaving(globalSaveOperations[screenId] || false);
    setSaveMessage(globalSaveMessages[screenId] || "");
    setValidationFailed(globalValidationStatus[screenId] || false);
  }, [screenId]);

  // Custom setSaveMessage function that updates both local state and global store
  const updateSaveMessage = useCallback(
    (message: string) => {
      setSaveMessage(message);
      globalSaveMessages[screenId] = message;
    },
    [screenId]
  );

  // Custom setValidationFailed function that updates both local state and global store
  const updateValidationFailed = useCallback(
    (failed: boolean) => {
      setValidationFailed(failed);
      globalValidationStatus[screenId] = failed;
    },
    [screenId]
  );

  // Custom setIsSaving function that updates both local state and global store
  const updateIsSaving = useCallback(
    (saving: boolean) => {
      setIsSaving(saving);
      globalSaveOperations[screenId] = saving;
    },
    [screenId]
  );

  const previousValues = useRef<any>(JSON.parse(JSON.stringify(dependencies)));
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const pendingChangesRef = useRef(false);
  const isFirstRender = useRef(true);
  const dataInitialized = useRef(false);
  const wasDataLoaded = useRef(isDataLoaded);
  const lastSavedHash = useRef<string>(""); // Track the last saved data hash
  const saveInProgress = useRef<boolean>(false); // Track if a save is currently in progress
  const isRefetching = useRef<boolean>(false); // Track if we're in a refetch operation
  const currentScreenId = useRef<string>(screenId); // Store the screen ID
  const globalTimeoutRefs: { [screenId: string]: NodeJS.Timeout | null } = {};
  // Update previous values when dependencies change, data is loaded, or screen changes
  useEffect(() => {
    // Update the screenId reference when it changes
    const screenChanged = currentScreenId.current !== screenId;
    if (screenChanged) {
      // Store the current screen ID
      currentScreenId.current = screenId;

      // Initialize or retrieve the last saved hash for this screen
      if (globalLastSavedHashes[screenId]) {
        lastSavedHash.current = globalLastSavedHashes[screenId];
      } else {
        // If no hash exists, initialize it
        const initialHash = generateHash(dependencies);
        lastSavedHash.current = initialHash;
        globalLastSavedHashes[screenId] = initialHash;
      }
    }

    // On first render, data load, or refetch operation
    // just update the reference values without triggering a save
    if (
      isFirstRender.current ||
      !dataInitialized.current ||
      isDataLoaded !== wasDataLoaded.current ||
      isRefetching.current
    ) {
      const currentData = JSON.parse(JSON.stringify(dependencies));
      previousValues.current = currentData;

      // Only initialize the hash if it's not already set for this screen
      if (!lastSavedHash.current) {
        const newHash = generateHash(currentData);
        lastSavedHash.current = newHash;
        globalLastSavedHashes[screenId] = newHash;
      }

      isFirstRender.current = false;
      dataInitialized.current = true;
      wasDataLoaded.current = isDataLoaded;
      isRefetching.current = false; // Reset refetch flag

      // Only reset validation status on first load, not on screen change
      if (!dataInitialized.current) {
        updateValidationFailed(false);
        globalUnsavedChanges[screenId] = false;
      }
    }
  }, [dependencies, isDataLoaded, screenId, updateValidationFailed]);

  // Function to perform the actual save
  const performSave = useCallback(async () => {
    try {
      // Check if this screen already has a save operation in progress
      if (globalSaveOperations[screenId]) {
        globalPendingChanges[screenId] = true;
        pendingChangesRef.current = true;
        return;
      }

      // Local check as well
      if (saveInProgress.current) {
        pendingChangesRef.current = true;
        return;
      }

      // Generate hash of current data to compare with last saved data
      const currentHash = generateHash(dependencies);

      // If the data is the same as what was last saved, don't save again
      if (currentHash === lastSavedHash.current) {
        return;
      }

      // Run validator if provided
      if (typeof validator === "function") {
        const isValid = validator();
        if (!isValid) {
          updateValidationFailed(true);
          updateSaveMessage("Validation failed");
          return;
        } else {
          if (validationFailed) {
            updateValidationFailed(false);
          }
        }
      }

      // Create a save function that will be added to the queue
      const saveFunction = async () => {
        try {
          globalSaveOperations[screenId] = true;
          saveInProgress.current = true;
          updateIsSaving(true);
          updateSaveMessage("Saving...");
          globalUnsavedChanges[screenId] = false;
          isRefetching.current = true;

          await saveDetails();

          const currentData = JSON.parse(JSON.stringify(dependencies));
          previousValues.current = currentData;
          const newHash = generateHash(currentData);

          lastSavedHash.current = newHash;
          globalLastSavedHashes[screenId] = newHash;

          updateIsSaving(false);
          updateSaveMessage("Saved successfully");

          return true;
        } catch (error) {
          updateIsSaving(false);
          updateSaveMessage("Save failed");
          throw error;
        } finally {
          saveInProgress.current = false;
          isRefetching.current = false;
        }
      };

      // Add the save operation to the queue
      saveQueue.push({
        screenId: screenId,
        saveFunction,
        timestamp: Date.now(),
      });

      // Start processing the queue
      processSaveQueue();
    } catch (error) {
      updateIsSaving(false);
      updateSaveMessage("Save failed");
      isRefetching.current = false;
    } finally {
      saveInProgress.current = false;
    }
  }, [
    dependencies,
    saveDetails,
    validationFailed,
    screenId,
    updateIsSaving,
    updateSaveMessage,
    updateValidationFailed,
  ]);

  useEffect(() => {
    if (isFirstRender.current || !dataInitialized.current) {
      return;
    }

    if (isRefetching.current) {
      return;
    }

    const hasChanges = !deepEqual(previousValues.current, dependencies);

    if (hasChanges) {
      if (!globalUnsavedChanges[screenId] && !isSaving) {
        updateSaveMessage("Changes detected...");
        globalUnsavedChanges[screenId] = true;
      }

      if (globalTimeoutRefs[screenId]) {
        clearTimeout(globalTimeoutRefs[screenId]);
        globalTimeoutRefs[screenId] = null;
      }

      if (saveInProgress.current) {
        pendingChangesRef.current = true;
        return;
      }

      globalTimeoutRefs[screenId] = setTimeout(() => {
        if (isRefetching.current) {
          return;
        }

        const currentHash = generateHash(dependencies);
        if (currentHash !== lastSavedHash.current) {
          performSave();
        }
      }, delay);
    } else {
      if (saveMessage === "Changes detected..." && !saveInProgress.current) {
        globalUnsavedChanges[screenId] = false;
        updateSaveMessage("");
      }
    }

    return () => {
      if (globalTimeoutRefs[screenId]) {
        clearTimeout(globalTimeoutRefs[screenId]);
        globalTimeoutRefs[screenId] = null;
      }
    };
  }, [
    dependencies,
    delay,
    isSaving,
    performSave,
    saveMessage,
    skipDependenciesCheck,
    screenId,
    updateSaveMessage,
  ]);

  return { isSaving, saveMessage, validationFailed };
};
