import React, { useState } from "react";
import { View, Text, TouchableOpacity } from "react-native";
import CustomCard from "../../../components/CustomCard";
import CustomButton from "../../../components/CustomButton";
import CustomInput from "../../../components/CustomTextInput";

interface IPreOptTestingCardProps {}

const PreOptTestingCard: React.FunctionComponent<
  IPreOptTestingCardProps
> = () => {
  const dummyData = {
    hgb: "14.2", // Hemoglobin in mg/dL
    plts: "250", // Platelets in uL
    cr: "1.1", // Creatinine in mg/dL
    egfr: "90", // Estimated Glomerular Filtration Rate (mL/min)
    rhythm: "Regular", // EKG Rhythm
    ventricularRate: "75", // Ventricular Rate in bpm
  };

  const [hgb, setHgb] = useState<string>(dummyData.hgb);
  const [plts, setPlts] = useState<string>(dummyData.plts);
  const [cr, setCr] = useState<string>(dummyData.cr);
  const [egfr, setEgfr] = useState<string>(dummyData.egfr);
  const [rhythm, setRhythm] = useState<string>(dummyData.rhythm);
  const [ventricularRate, setVentricularRate] = useState<string>(
    dummyData.ventricularRate
  );

  const handleSave = () => {
    // Logic for saving data
  };

  const handleCancel = () => {
    // Reset fields
    setHgb(dummyData.hgb);
    setPlts(dummyData.plts);
    setCr(dummyData.cr);
    setEgfr(dummyData.egfr);
    setRhythm(dummyData.rhythm);
    setVentricularRate(dummyData.ventricularRate);
  };

  return (
    <CustomCard>
      <View className="p-3 rounded-md bg-primaryBg">
        {/* Labs */}
        <Text className="text-primaryBlack font-medium mb-3">Labs</Text>
        <View className="flex-row items-center justify-between mb-3">
          <Text className="text-primaryBlack font-medium w-[40%]">
            Hgb (hemoglobin)
          </Text>
          <View className="flex-1">
            <CustomInput
              inputValue={hgb}
              onInputChange={setHgb}
              placeholder="mg/dL"
            />
          </View>
        </View>
        <View className="flex-row items-center justify-between mb-3">
          <Text className="text-primaryBlack font-medium w-[40%]">
            Plts (platelets)
          </Text>
          <View className="flex-1">
            <CustomInput
              inputValue={plts}
              onInputChange={setPlts}
              placeholder="uL"
            />
          </View>
        </View>
        <View className="flex-row items-center justify-between mb-3">
          <Text className="text-primaryBlack font-medium w-[40%]">
            Cr (creatinine)
          </Text>
          <View className="flex-1">
            <CustomInput
              inputValue={cr}
              onInputChange={setCr}
              placeholder="mg/dL"
            />
          </View>
        </View>
        <View className="flex-row items-center justify-between mb-3">
          <Text className="text-primaryBlack font-medium w-[40%]">eGFR</Text>
          <View className="flex-1">
            <CustomInput
              inputValue={egfr}
              onInputChange={setEgfr}
              placeholder="mL/min"
            />
          </View>
        </View>

        {/* EKG */}
        <Text className="text-primaryBlack font-medium mb-3">EKG</Text>
        <View className="flex-row items-center justify-between mb-3">
          <Text className="text-primaryBlack font-medium w-[40%]">Rhythm</Text>
          <View className="flex-1">
            <CustomInput
              inputValue={rhythm}
              onInputChange={setRhythm}
              placeholder="Regular/Irregular"
            />
          </View>
        </View>
        <View className="flex-row items-center justify-between mb-3">
          <Text className="text-primaryBlack font-medium w-[40%]">
            Ventricular Rate
          </Text>
          <View className="flex-1">
            <CustomInput
              inputValue={ventricularRate}
              onInputChange={setVentricularRate}
              placeholder="bpm"
            />
          </View>
        </View>

        <View className="flex-row justify-center gap-4 mt-6">
          <TouchableOpacity
            onPress={handleSave}
            className="border border-primaryPurple px-6 py-3 rounded-full bg-primaryPurple"
          >
            <Text className="text-primaryWhite font-semibold">Save</Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={handleCancel}
            className="border border-primaryPurple px-6 py-3 rounded-full bg-primaryBg"
          >
            <Text className="text-primaryPurple">Cancel</Text>
          </TouchableOpacity>
        </View>
      </View>
    </CustomCard>
  );
};

export default PreOptTestingCard;
