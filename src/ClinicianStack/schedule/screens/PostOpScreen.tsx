import React, { useState, useRef } from "react";
import { useNavigation } from "@react-navigation/native";
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  StyleSheet,
  Button,
  FlatList,
} from "react-native";
import { useFocusEffect } from "@react-navigation/native";
import Heading from "../../../components/Heading";
import ScreenWrapper from "../../../components/ScreenWrapper";
import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons";
import moment from "moment";
import ToggleButton from "../../../components/ToggleButton";
import CustomInput from "../../../components/CustomTextInput";
import CustomCard from "../../../components/CustomCard";
import LineSeperator from "../../../components/LineSeperator";
import DatePicker from "react-native-date-picker";
import SearchablePicker from "../../../components/SearchablePicker";
import CustomText from "../../../components/CustomText";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import { parseIntInput } from "../../../utils";
import SaveActionButton from "../../../components/SaveActionButton";
import {
  useLoaderAndError,
  usePostOpUserDetails,
  usePostOpDetails,
  findPostOpDiff,
  formatPostOpData,
} from "../hooks/postopHooks";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../../store";
import {
  fetchPostOpDetails,
  putPostOpDetails,
} from "../../../store/clinician/ScheduleStack/postop/thunk";
import { AppDispatch } from "../../../store";
import { get } from "react-native/Libraries/TurboModule/TurboModuleRegistry";
import { setPostOpDetails } from "../../../store/rep/ScheduleStack/postop";
import Loader from "../../../components/Loader";
import Error from "../../../components/Error";
import OptionSelector from "../../../components/OptionSelector";
import { Linking } from "react-native";
import { useMedicationData } from "../hooks/postopHooks";
import BottomSheetComponent, {
  BottomSheetRefProps,
} from "../../../components/BottomSheetComponent";
import MedicationBottomSheetComponent from "../components/MedicationBottomSheetComponent";
import { deleteMedicationById } from "../../../store/coordinator/ScheduleStack/postop";
import { useClinicianSelectedPatient } from "../hooks/schedulesHooks";
import PopupModal from "../../../components/Popup";
import { simpleAutoSave } from "../../../services/simpleAutoSave";
import { setSaveStatus, setLastSavedData } from "../../../store/services";

interface PostOpProps {
  heading: string;
}

const PostOpScreen: React.FunctionComponent<PostOpProps> = () => {
  const dispatch = useDispatch<AppDispatch>();

  const [medicationKey, setMedicationKey] = useState<any>(Date.now());

  const selectedPatient = useClinicianSelectedPatient();
  const { error, loader } = useLoaderAndError();
  const navigation = useNavigation();

  const userDetails = usePostOpUserDetails();
  const postOpDetails = usePostOpDetails();
  // NEW START

  const {
    anticoagulation_selected,
    anticoagulation_options,
    discharge_plan_selected,
    discharge_plan_options,
    anticipated_45_days_follow_up_date,
    follow_ups_45_days,
    follow_ups_6_months,
  } = formatPostOpData(userDetails);

  // Track selected medications

  const [medicationSelectedId, setMedicationSelectedId] = useState<
    string | null
  >("");

  const bottomRef = useRef<BottomSheetRefProps>(null);
  const [selected, setSelected] = useState([]);

  const [medDeleted, setMedDeleted] = useState(false);

  const [initialUpdate, setInitialUpdate] = useState(false);

  const [medDiff, setMedDiff] = useState(false);
  const saveInProgressRef = React.useRef(false);
  // Create a ref to track the last saved data hash
  const lastSavedDataHashRef = React.useRef("");
  const [deleteConfirmationVisible, setDeleteConfirmationVisible] =
    useState(false);
  const [medToDelete, setMedToDelete] = useState<string | null>(null);

  const { med_data } = useMedicationData();

  const medicationBottomSheetRef = useRef(null);

  const handleClose = () => {
    if (medicationBottomSheetRef.current) {
      medicationBottomSheetRef.current.stateCleanUp();
    }
  };

  const handleMedOpen = (id?: string) => {
    setMedicationKey(Date.now());
    if (id) {
      setMedicationSelectedId(id);
    } else {
      setMedicationSelectedId(null);
    }
    bottomRef.current?.open();
  };

  const handleMedClose = () => {
    bottomRef.current?.close();
  };

  React.useEffect(() => {
    if (
      anticoagulation_selected &&
      selected.length === 0 &&
      !medDeleted &&
      !initialUpdate
    ) {
      setSelected(anticoagulation_selected);
      setInitialUpdate(true);
    }
  }, [anticoagulation_selected, medDeleted]);

  const handleAddMedication = () => {
    handleMedOpen();
  };

  const resetState = () => {
    setMedDeleted(false);
    setInitialUpdate(false);
    setMedDiff(false);
    setSelected([]);
    setMedDiff(false);
  };

  // NEW END

  useFocusEffect(
    React.useCallback(() => {
      const fetchDetails = async () => {
        if (selectedPatient?.case_id) {
          // reset the med local state
          resetState();
          const res = await dispatch(
            fetchPostOpDetails({ caseId: selectedPatient?.case_id })
          );
        }
      };

      fetchDetails();
    }, [dispatch, selectedPatient?.case_id])
  );

  const diff = findPostOpDiff();

  const saveDetails = async (dataToSave: any): Promise<void> => {
    try {
      // // Run validation
      // const isValid = validator();

      // // If validation fails, update status and throw error
      // if (!isValid) {
      //   dispatch(
      //     setSaveStatus({
      //       screenId: "postOp",
      //       status: "validation_failed",
      //     })
      //   );
      //   setLocalSaveStatus("validation_failed");
      //   throw new Error("Validation failed");
      // }

      // Avoid saving if already in progress
      if (saveInProgressRef.current) {
        return;
      }

      const dataCopy = { ...(dataToSave || diff.currentValues) };
      const currentDataHash = JSON.stringify(dataCopy);

      if (currentDataHash === lastSavedDataHashRef.current) {
        return;
      }

      saveInProgressRef.current = true;

      if (!selectedPatient?.case_id) {
        saveInProgressRef.current = false;
        throw new Error("Missing case_id");
      }

      await dispatch(
        putPostOpDetails({
          caseId: selectedPatient.case_id,
          payload: dataCopy,
        })
      );

      lastSavedDataHashRef.current = currentDataHash;

      const refetchRes = await dispatch(
        fetchPostOpDetails({ caseId: selectedPatient.case_id })
      );

      if (refetchRes.payload) {
        resetState();
      }

      saveInProgressRef.current = false;
    } catch (err) {
      saveInProgressRef.current = false;
      throw err;
    }
  };

  const handleCancel = () => {
    resetState();
    dispatch(
      setPostOpDetails({
        ...userDetails,
        ...postOpDetails,
      })
    );
    // reset meddata
  };

  const saveStatus = useSelector(
    (state: any) => state.common?.autoSave?.["postOp"]?.status
  );

  const [localSaveStatus, setLocalSaveStatus] = React.useState<string>("");

  const effectiveSaveStatus = saveStatus || localSaveStatus;

  // Track if user has made changes to prevent auto-navigation on initial load
  const [hasUserMadeChanges, setHasUserMadeChanges] = React.useState(false);

  // Track when user makes changes to any field
  React.useEffect(() => {
    if (diff.hasDiff && !hasUserMadeChanges) {
      setHasUserMadeChanges(true);
    }
  }, [diff.hasDiff, hasUserMadeChanges]);

  // Add effect to navigate to Case Synopsis tab when save is successful
  // React.useEffect(() => {
  //   if (effectiveSaveStatus === "saved" && hasUserMadeChanges) {
  //     // Add a small delay to ensure the user sees the "saved" message briefly
  //     const timer = setTimeout(() => {
  //       // Navigate to the Case Synopsis main tab
  //       (navigation as any).navigate("Case Synopsis");
  //     }, 1000);

  //     return () => clearTimeout(timer);
  //   }
  // }, [effectiveSaveStatus, navigation, hasUserMadeChanges]);

  /**
   * Derive the save message and styling based on the save status
   */
  const { saveMessage, messageStyle, iconName, iconColor } =
    React.useMemo(() => {
      let message = "";
      let style = "text-primaryPurple italic";
      let icon = "alert-circle-outline";
      let color = "#8143d9";

      switch (effectiveSaveStatus) {
        case "changes_detected":
          message = "Changes detected, saving soon...";
          style = "text-primaryBlack italic";
          icon = "alert-circle-outline";
          color = "#f5c542"; // yellow
          break;
        case "saving":
          message = "Saving... Please don't close the app";
          style = "text-primaryPurple font-bold italic";
          icon = "alert-circle-outline";
          color = "#8143d9"; // purple
          break;
        case "saved":
          message = "All changes saved";
          style = "text-green-3 italic";
          icon = "check-circle-outline";
          color = "#28a745"; // green
          break;
        case "validation_failed":
          message = "Validation failed. Please check your inputs.";
          style = "text-red-3 font-bold italic";
          icon = "alert-circle-outline";
          color = "#d93025"; // red
          break;
        case "error":
          message = "Error saving changes. Please try again.";
          style = "text-red-3 font-bold italic";
          icon = "alert-circle-outline";
          color = "#d93025"; // red
          break;
      }

      return {
        saveMessage: message,
        messageStyle: style,
        iconName: icon,
        iconColor: color,
      };
    }, [effectiveSaveStatus]);
  /**
   * Ref to track previous diff values to prevent unnecessary saves
   */
  const prevDiffRef = React.useRef<string>("");

  /**
   * Ref to track if initial data has been loaded
   * This prevents autosave from triggering before data is fetched
   */
  const initialDataLoadedRef = React.useRef<boolean>(false);

  /**
   * Effect to set initialDataLoaded when data is fetched
   */
  React.useEffect(() => {
    // Check if we have valid data
    const hasValidData =
      selectedPatient?.case_id !== undefined && userDetails !== null;

    if (hasValidData && !initialDataLoadedRef.current) {
      initialDataLoadedRef.current = true;
    }
  }, [userDetails, selectedPatient]);

  /**
   * Effect to trigger autosave when data changes
   * This uses our new simpleAutoSave service with local state fallback
   */
  React.useEffect(() => {
    // Only proceed if initial data has been loaded and there are actual differences
    if (!initialDataLoadedRef.current) {
      return;
    }

    // Check if case_detail_id exists to prevent API calls with undefined ID
    if (!userDetails?.case_id) {
      return;
    }

    // Only proceed if there are actual differences
    if (diff.hasDiff) {
      // Check if we have any undefined values in critical fields
      const hasUndefinedValues = Object.values(diff.currentValues).every(
        (val) => val === undefined
      );
      if (hasUndefinedValues) {
        return;
      }

      // Convert current values to string for comparison
      const currentDiffString = JSON.stringify(diff.currentValues);

      // Only trigger save if the values have actually changed
      if (currentDiffString !== prevDiffRef.current) {
        // Update the ref with current values
        prevDiffRef.current = currentDiffString;

        // Create a custom wrapper for setSaveStatus that updates both Redux and local state
        const setStatusWithFallback = (payload: {
          screenId: string;
          status: string;
        }) => {
          // Update local state as a fallback
          setLocalSaveStatus(payload.status);
          // Also dispatch to Redux
          return dispatch(setSaveStatus(payload));
        };

        // Trigger the simple autosave with the current values
        simpleAutoSave(
          "postOp", // Screen ID
          diff.currentValues, // Data to save
          saveDetails, // Save function
          4000, // 4 second delay before saving
          dispatch, // Redux dispatch
          setStatusWithFallback, // Custom action to set save status with fallback
          setLastSavedData // Action to set last saved data
        );
      } else {
        console.log("No actual changes in diff values, skipping autosave");
      }
    }
  }, [
    diff.hasDiff,
    diff.currentValues,
    dispatch,
    setLocalSaveStatus,
    userDetails,
  ]);

  const [anticoagulationDateModal, setAnticoagulationDateModal] =
    useState(false);
  const [date45DaysModal, setDate45DaysModal] = useState(false);
  const [date1YearModal, setDate1YearModal] = useState(false);

  const renderMed = ({ item, index }) => {
    return (
      <>
        <View className="flex-row justify-between items-center p-2">
          <View className="flex-row items-center">
            <Text className="text-md font-bold text-primaryPurple">
              {item.med_name}
            </Text>
            <Text className="text-sm text-primaryBlack">
              {" - "}
              {item.med_dose} {item.dosing_frequency}
              {item.period?.toLowerCase() !== "indefinitely" ? " x" : " -"}{" "}
              {item.period_count} {item.period}
            </Text>
          </View>

          <View className="flex-row space-x-4 items-center">
            {/* Edit Button */}
            <TouchableOpacity
              onPress={() => {
                handleMedOpen(item.id);
              }}
              className="px-4 py-2"
            >
              <MaterialCommunityIcons name="pencil" size={20} color="#3b82f6" />
            </TouchableOpacity>

            {/* Delete Button */}
            <TouchableOpacity
              onPress={() => {
                setMedToDelete(item.id);
                setDeleteConfirmationVisible(true);
              }}
              className="px-4 py-2"
            >
              <MaterialCommunityIcons
                name="trash-can"
                size={20}
                color="#ef4444"
              />
            </TouchableOpacity>
          </View>
        </View>

        {/* Add LineSeperator except for the last item */}
        {index < med_data.length - 1 && <LineSeperator color="primaryWhite" />}
      </>
    );
  };

  const renderEmpty = () => {
    return (
      <View className="flex-row justify-center">
        <CustomText value={"None"} />
      </View>
    );
  };

  const handleRefresh = async () => {
    resetState();
    if (!selectedPatient?.case_id) return;
    const refetchRes = await dispatch(
      fetchPostOpDetails({ caseId: selectedPatient?.case_id })
    );
  };

  if (loader) {
    return <Loader />;
  }

  if (error) {
    return <Error message={error} />;
  }

  return (
    <>
      <ScreenWrapper direction="column" onRefresh={handleRefresh}>
        <CustomCard>
          {/* <SaveActionButton
            disabled={!(medDiff || diff.hasDiff)}
            onPress={saveDetails}
          /> */}
          {saveMessage && (
            <View
              style={{
                flexDirection: "row",
                justifyContent: "flex-end",
                alignItems: "center",
                marginVertical: 4,
              }}
            >
              <MaterialCommunityIcons
                name={iconName}
                color={iconColor}
                size={20}
              />
              <Text
                className={`text-sm ${messageStyle}`}
                style={{ marginLeft: 6 }}
              >
                {saveMessage}
              </Text>
            </View>
          )}
          <View className="gap-4 mt-3">
            <View className="gap-6">
              <View className="gap-2">
                <View className="flex-row justify-between">
                  <Heading
                    text="Anticoagulation/Antiplatelet Plan"
                    size="sub-heading"
                    extraStyle="mb-2 flex-1"
                    showSeperator={false}
                  />
                  <MaterialCommunityIcons
                    name="plus-circle-outline"
                    color="#8143d9"
                    size={25}
                    onPress={handleAddMedication}
                  />
                </View>

                {/* <MedicationPlan
                  apiData={apiData}
                  selected={selected}
                  onMedicationChange={handleMedicationChange}
                  onQuantityChange={handleQuantityChange}
                  onAddMedication={handleAddMedication}
                  onDeleteMedication={handleDeleteMedication}
                /> */}

                <View className="bg-primaryBg rounded-md ">
                  <FlatList
                    data={med_data}
                    renderItem={renderMed}
                    keyExtractor={(item) => item.id}
                    // ListEmptyComponent={renderEmpty}
                    scrollEnabled={false}
                  />
                </View>
              </View>
            </View>

            <LineSeperator extraStyle="mt-2" />
            <Heading
              text="Discharge Plan"
              size="sub-heading"
              color="black"
              showSeperator={false}
            />

            <View className="bg-primaryBg rounded-md p-3">
              <OptionSelector
                options={discharge_plan_options?.map((option) => ({
                  label: option.label,
                  value: option.value,
                  icon:
                    option.label === "Same day discharge"
                      ? "clock-outline"
                      : "bed",
                }))}
                selected={discharge_plan_selected}
                onSelect={(option) => {
                  dispatch(
                    setPostOpDetails({
                      discharge_plan: {
                        ...userDetails?.discharge_plan,
                        selected: {
                          id: option?.value,
                          name: option?.label,
                        },
                      },
                    })
                  );
                }}
              />
            </View>
            <LineSeperator extraStyle="mt-2" />

            <View className="gap-2 ">
              <Heading
                text="Anticipated 45 Days Follow Up"
                size="sub-heading"
                extraStyle="mb-2"
                showSeperator={false}
              />

              <TouchableOpacity
                disabled
                onPress={() => setAnticoagulationDateModal(true)}
                className="border border-primaryGray p-4 rounded-lg"
              >
                <CustomText
                  value={
                    anticipated_45_days_follow_up_date
                      ? moment(anticipated_45_days_follow_up_date).format(
                          "MM-DD-YYYY"
                        )
                      : "Date not selected"
                  }
                  className="text-primaryBlack text-md"
                  color={`${
                    anticipated_45_days_follow_up_date
                      ? "text-primaryBlack"
                      : "text-primaryGray"
                  }`}
                />
              </TouchableOpacity>
            </View>

            <LineSeperator extraStyle="mt-2 mb-1" />

            <View className="gap-2 rounded-lg p-2 bg-primaryBg ">
              <Heading
                text="45 Day Follow Up Imaging"
                size="sub-heading"
                color="black"
                showSeperator={false}
                extraStyle="bg-primaryBg pb-2 mt-3 rounded"
              />

              <View className="flex-row justify-between rounded-md px-3 pb-3">
                <TouchableOpacity
                  className={`rounded items-center w-[40%] p-2 ${
                    follow_ups_45_days?.cta_link
                      ? `bg-green-2`
                      : `bg-secondaryGray`
                  } shadow-sm`}
                  disabled={follow_ups_45_days?.cta_link ? false : true}
                  // onPress={() => openLink(follow_ups_45_days?.cta_link)}
                  onPress={() =>
                    navigation.navigate("WebViewer", {
                      link: follow_ups_45_days?.cta_link,
                    })
                  }
                >
                  <Text
                    className={`
                    ${
                      follow_ups_45_days?.cta_link
                        ? `text-green-3`
                        : `text-primaryWhite`
                    }
                    `}
                  >
                    {"Post-Op CTA"}
                  </Text>
                </TouchableOpacity>

                {/* TEE Button */}
                <TouchableOpacity
                  className={`rounded items-center w-[40%] p-2 ${
                    follow_ups_45_days?.tee_link
                      ? `bg-green-2`
                      : `bg-secondaryGray`
                  } shadow-sm`}
                  disabled={follow_ups_45_days?.tee_link ? false : true}
                  onPress={() =>
                    navigation.navigate("WebViewer", {
                      link: follow_ups_45_days?.tee_link,
                    })
                  }
                >
                  <Text
                    className={`
                    ${
                      follow_ups_45_days?.tee_link
                        ? `text-green-3`
                        : `text-primaryWhite`
                    }
                    `}
                  >
                    {"Post-Op TEE"}
                  </Text>
                </TouchableOpacity>
              </View>

              {follow_ups_45_days?.cta_link && follow_ups_45_days?.tee_link && (
                <>
                  <LineSeperator extraStyle="my-2" color="primaryWhite" />
                  <View className="bg-primaryBg rounded-lg">
                    <View className="flex-row justify-between items-center">
                      <CustomText
                        value="Completed"
                        className="text-primaryBlack text-md font-semibold"
                      />
                      <ToggleButton
                        messages={["Yes", "No"]}
                        selected={follow_ups_45_days.completed}
                        setSelected={(value) => {
                          dispatch(
                            setPostOpDetails({
                              follow_ups_45_days: {
                                ...userDetails?.follow_ups_45_days,
                                completed: value === "Yes" ? true : false,
                              },
                            })
                          );
                        }}
                      />
                    </View>
                    <LineSeperator extraStyle="my-2" color="primaryWhite" />

                    {follow_ups_45_days.completed == "Yes" && (
                      <>
                        <View className="flex-row justify-between items-center">
                          <View className="flex-1">
                            <CustomText
                              value="Date"
                              className="text-primaryBlack text-md font-semibold"
                            />
                          </View>
                          <View className="flex-1">
                            <TouchableOpacity
                              onPress={() => setDate45DaysModal(true)}
                              className="border border-primaryPurple p-4 rounded-lg bg-primaryWhite"
                            >
                              <CustomText
                                value={
                                  follow_ups_45_days?.date
                                    ? moment(follow_ups_45_days?.date).format(
                                        "MM-DD-YYYY"
                                      )
                                    : "Select Date"
                                }
                                className="text-primaryBlack text-md"
                              />
                            </TouchableOpacity>
                          </View>
                        </View>
                        <LineSeperator extraStyle="my-2" color="primaryWhite" />
                      </>
                    )}

                    <View className="flex-row justify-between items-center">
                      <CustomText
                        value="Peri device leak"
                        className="text-primaryBlack text-md font-semibold"
                      />
                      <ToggleButton
                        messages={["Yes", "No"]}
                        selected={follow_ups_45_days.peri_device_leak}
                        invertColor
                        setSelected={(value) => {
                          dispatch(
                            setPostOpDetails({
                              follow_ups_45_days: {
                                ...userDetails?.follow_ups_45_days,
                                peri_device_leak:
                                  value === "Yes" ? true : false,
                                width:
                                  value === "No"
                                    ? null
                                    : follow_ups_45_days.width,
                              },
                            })
                          );
                        }}
                      />
                    </View>
                    <LineSeperator extraStyle="my-2" color="primaryWhite" />

                    {follow_ups_45_days.peri_device_leak === "Yes" && (
                      <>
                        <View className="flex-row justify-between items-center">
                          <View className="flex-1">
                            <CustomText
                              value="Width (mm)"
                              className="text-primaryBlack text-md font-semibold"
                            />
                          </View>
                          <View className="flex-1">
                            <CustomInput
                              inputValue={
                                follow_ups_45_days.width?.toString() || ""
                              } // Convert number to string for the input field
                              onInputChange={(value) =>
                                parseIntInput(value, (updatedValue) => {
                                  dispatch(
                                    setPostOpDetails({
                                      follow_ups_45_days: {
                                        ...userDetails?.follow_ups_45_days,
                                        width: updatedValue,
                                      },
                                    })
                                  );
                                })
                              }
                              placeholder="in mm"
                              keyboardType="numeric"
                            />
                          </View>
                        </View>
                        <LineSeperator extraStyle="my-2" color="primaryWhite" />
                      </>
                    )}

                    <View className="flex-row justify-between items-center mb-3">
                      <CustomText
                        value="Device related thrombus"
                        className="text-primaryBlack text-md font-semibold"
                      />
                      <ToggleButton
                        messages={["Yes", "No"]}
                        selected={follow_ups_45_days.thrombus}
                        invertColor
                        setSelected={(value) => {
                          dispatch(
                            setPostOpDetails({
                              follow_ups_45_days: {
                                ...userDetails?.follow_ups_45_days,
                                thrombus: value === "Yes" ? true : false,
                              },
                            })
                          );
                        }}
                      />
                    </View>
                  </View>
                </>
              )}
            </View>

            <LineSeperator extraStyle="mt-2 mb-1" />

            <View className="gap-2 rounded-lg p-2 bg-primaryBg">
              <Heading
                text="6 Month Follow Up Imaging"
                size="sub-heading"
                color="black"
                showSeperator={false}
                extraStyle="bg-primaryBg pb-2 mt-3 rounded"
              />

              <View className="flex-row justify-between rounded-md px-3 pb-3">
                <TouchableOpacity
                  className={`rounded items-center w-[40%] p-2 ${
                    follow_ups_6_months?.cta_link
                      ? `bg-green-2`
                      : `bg-secondaryGray`
                  } shadow-sm`}
                  disabled={follow_ups_6_months?.cta_link ? false : true}
                  onPress={() =>
                    navigation.navigate("WebViewer", {
                      link: follow_ups_6_months?.cta_link,
                    })
                  }
                >
                  <Text
                    className={`
                    ${
                      follow_ups_45_days?.cta
                        ? `text-green-3`
                        : `text-primaryWhite`
                    }
                    `}
                  >
                    {"Post-Op CTA"}
                  </Text>
                </TouchableOpacity>

                {/* TEE Button */}
                <TouchableOpacity
                  className={`rounded items-center w-[40%] p-2 ${
                    follow_ups_6_months?.tee_link
                      ? `bg-green-2`
                      : `bg-secondaryGray`
                  } shadow-sm`}
                  disabled={follow_ups_6_months?.tee_link ? false : true}
                  // onPress={() => openLink(follow_ups_6_months?.tee_link)}
                  onPress={() =>
                    navigation.navigate("WebViewer", {
                      link: follow_ups_6_months?.tee_link,
                    })
                  }
                >
                  <Text
                    className={`
                    ${
                      follow_ups_6_months?.tee_link
                        ? `text-green-3`
                        : `text-primaryWhite`
                    }
                    `}
                  >
                    {"Post-Op TEE"}
                  </Text>
                </TouchableOpacity>
              </View>

              {follow_ups_6_months?.cta_link &&
                follow_ups_6_months?.tee_link && (
                  <>
                    <LineSeperator extraStyle="my-2" color="primaryWhite" />
                    <View className="bg-primaryBg  rounded-lg">
                      <View className="flex-row justify-between items-center">
                        <CustomText
                          value="Completed"
                          className="text-primaryBlack text-md font-semibold"
                        />
                        <ToggleButton
                          messages={["Yes", "No"]}
                          selected={follow_ups_6_months.completed}
                          setSelected={(value) => {
                            dispatch(
                              setPostOpDetails({
                                follow_ups_6_months: {
                                  ...userDetails?.follow_ups_6_months,
                                  completed: value === "Yes" ? true : false,
                                },
                              })
                            );
                          }}
                        />
                      </View>
                      <LineSeperator extraStyle="my-2" color="primaryWhite" />

                      {follow_ups_6_months.completed === "Yes" && (
                        <>
                          <View className="flex-row justify-between items-center">
                            <View className="flex-1">
                              <CustomText
                                value="Date"
                                className="text-primaryBlack text-md font-semibold"
                              />
                            </View>
                            <View className="flex-1">
                              <TouchableOpacity
                                onPress={() => setDate1YearModal(true)}
                                className="border border-primaryPurple bg-primaryWhite p-4 rounded-lg"
                              >
                                <CustomText
                                  value={
                                    follow_ups_6_months?.date
                                      ? moment(follow_ups_6_months.date).format(
                                          "MM-DD-YYYY"
                                        )
                                      : "Select Date"
                                  }
                                  className="text-primaryBlack text-md"
                                />
                              </TouchableOpacity>
                            </View>
                          </View>
                          <LineSeperator
                            extraStyle="my-2"
                            color="primaryWhite"
                          />
                        </>
                      )}

                      <View className="flex-row justify-between items-center">
                        <CustomText
                          value="Peri device leak"
                          className="text-primaryBlack text-md font-semibold"
                        />
                        <ToggleButton
                          messages={["Yes", "No"]}
                          selected={follow_ups_6_months.peri_device_leak}
                          invertColor
                          setSelected={(value) => {
                            dispatch(
                              setPostOpDetails({
                                follow_ups_6_months: {
                                  ...userDetails?.follow_ups_6_months,
                                  peri_device_leak:
                                    value === "Yes" ? true : false,
                                  width:
                                    value === "No"
                                      ? null
                                      : follow_ups_6_months.width,
                                },
                              })
                            );
                          }}
                        />
                      </View>
                      <LineSeperator extraStyle="my-2" color="primaryWhite" />

                      {follow_ups_6_months.peri_device_leak === "Yes" && (
                        <>
                          <View className="flex-row justify-between items-center">
                            <View className="flex-1">
                              <CustomText
                                value="Width (mm)"
                                className="text-primaryBlack text-md font-semibold"
                              />
                            </View>
                            <View className="flex-1">
                              <CustomInput
                                inputValue={
                                  follow_ups_6_months.width?.toString() || ""
                                } // Convert number to string for the input field
                                onInputChange={(value) =>
                                  parseIntInput(value, (updatedValue) => {
                                    dispatch(
                                      setPostOpDetails({
                                        follow_ups_6_months: {
                                          ...userDetails?.follow_ups_6_months,
                                          width: updatedValue,
                                        },
                                      })
                                    );
                                  })
                                }
                                placeholder="in mm"
                                keyboardType="numeric"
                              />
                            </View>
                          </View>
                          <LineSeperator
                            extraStyle="my-2"
                            color="primaryWhite"
                          />
                        </>
                      )}

                      <View className="flex-row justify-between items-center mb-3">
                        <CustomText
                          value="Device related thrombus"
                          className="text-primaryBlack text-md font-semibold"
                        />
                        <ToggleButton
                          messages={["Yes", "No"]}
                          selected={follow_ups_6_months.thrombus}
                          invertColor
                          setSelected={(value) => {
                            dispatch(
                              setPostOpDetails({
                                follow_ups_6_months: {
                                  ...userDetails?.follow_ups_6_months,
                                  thrombus: value === "Yes" ? true : false,
                                },
                              })
                            );
                          }}
                        />
                      </View>
                    </View>
                  </>
                )}
            </View>
          </View>
          <LineSeperator extraStyle="my-5" />
          <View className="flex-row items-center justify-center">
            {/* <TouchableOpacity
              onPress={saveDetails}
              className={`border 
                          ${
                            !diff.hasDiff
                              ? `bg-primaryGray border-primaryGray`
                              : `bg-primaryPurple border-primaryPurple`
                          }
                           px-6 py-3 rounded-full`}
              disabled={!diff.hasDiff}
            >
              <Text className="text-primaryWhite font-semibold">Save</Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={handleCancel}
              className="border border-primaryPurple px-6 py-3 rounded-full bg-primaryBg"
            >
              <Text className="text-primaryPurple">Cancel</Text>
            </TouchableOpacity> */}

            {/* <SaveActionButton
              disabled={!diff.hasDiff}
              onPress={saveDetails}
              onCancel={handleCancel}
            /> */}
          </View>
          <View className="mt-3"></View>
        </CustomCard>

        <View className="mt-9"></View>
        {/*4 anticoagulation follow up*/}
        <DatePicker
          modal
          open={anticoagulationDateModal}
          date={
            anticipated_45_days_follow_up_date &&
            moment(
              anticipated_45_days_follow_up_date,
              "YYYY-MM-DD",
              true
            ).isValid()
              ? moment(
                  anticipated_45_days_follow_up_date,
                  "YYYY-MM-DD"
                ).toDate()
              : new Date()
          }
          onConfirm={(date) => {
            setAnticoagulationDateModal(false);
            dispatch(
              setPostOpDetails({
                anticipated_45_days_follow_up_date:
                  moment(date).format("YYYY-MM-DD"),
              })
            );
          }}
          onCancel={() => {
            setAnticoagulationDateModal(false);
          }}
          mode={"date"}
        />

        {/* 45 days follow up */}
        <DatePicker
          modal
          open={date45DaysModal}
          date={
            follow_ups_45_days?.date
              ? moment(follow_ups_45_days?.date, "YYYY-MM-DD")?.toDate()
              : new Date()
          }
          onConfirm={(date) => {
            setDate45DaysModal(false);

            dispatch(
              setPostOpDetails({
                follow_ups_45_days: {
                  ...userDetails?.follow_ups_45_days,
                  date: moment(date).format("YYYY-MM-DD"),
                },
              })
            );
          }}
          onCancel={() => {
            setDate45DaysModal(false);
          }}
          mode={"date"}
        />

        {/* 1 yr follow up */}
        <DatePicker
          modal
          open={date1YearModal}
          date={
            follow_ups_6_months?.date
              ? moment(follow_ups_6_months.date, "YYYY-MM-DD").toDate()
              : new Date()
          }
          onConfirm={(date) => {
            setDate1YearModal(false);

            dispatch(
              setPostOpDetails({
                follow_ups_6_months: {
                  ...userDetails?.follow_ups_6_months,
                  date: moment(date).format("YYYY-MM-DD"),
                },
              })
            );
          }}
          onCancel={() => {
            setDate1YearModal(false);
          }}
          mode={"date"}
        />
      </ScreenWrapper>

      <BottomSheetComponent
        ref={bottomRef}
        snapPoints={["75%", "85%"]}
        backgroundColor="white"
        onClose={() => handleClose()}
      >
        {
          <View className="flex-1">
            <MedicationBottomSheetComponent
              ref={medicationBottomSheetRef}
              medicineId={medicationSelectedId || ""}
              bottomSheetClose={handleMedClose}
              medKey={medicationKey}
            />
          </View>
        }
      </BottomSheetComponent>
      <PopupModal
        show={deleteConfirmationVisible}
        msg={["Are you sure you want to delete this medication?"]}
        status="warning"
        onClose={() => setDeleteConfirmationVisible(false)}
      >
        <View className="flex-row justify-end mt-4">
          <TouchableOpacity
            onPress={() => setDeleteConfirmationVisible(false)}
            style={{
              paddingHorizontal: 16,
              paddingVertical: 8,
              backgroundColor: "#D1D5DB",
              borderRadius: 6,
              marginRight: 8,
            }}
          >
            <Text style={{ color: "black" }}>Cancel</Text>
          </TouchableOpacity>

          <TouchableOpacity
            onPress={() => {
              if (medToDelete) {
                dispatch(deleteMedicationById(medToDelete));
                setDeleteConfirmationVisible(false);
                setMedToDelete(null);
                setMedDeleted(true);
              }
            }}
            style={{
              paddingHorizontal: 16,
              paddingVertical: 8,
              backgroundColor: "#EF4444",
              borderRadius: 6,
            }}
          >
            <Text style={{ color: "white" }}>Delete</Text>
          </TouchableOpacity>
        </View>
      </PopupModal>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
  },
  row: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
  },
  picker: {
    flex: 1,
    marginRight: 10,
  },
});

export default PostOpScreen;
