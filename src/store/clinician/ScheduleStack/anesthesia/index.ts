import { createSlice } from "@reduxjs/toolkit";
import initialState from "./initialstate";
import { fetchAnesthesiaDetails, putAnesthesiaDetails } from "./thunk";

const anesthesiaSlice = createSlice({
  name: "anesthesia",
  initialState,
  reducers: {
    setAnesthesiaUserDetails: (state, action) => {
      state.userDetails = {
        ...state.userDetails,
        ...action.payload,
      };
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchAnesthesiaDetails.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchAnesthesiaDetails.fulfilled, (state, action) => {
        state.loading = false;
        state.anesthesiaDetails = action.payload;
      })
      .addCase(fetchAnesthesiaDetails.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });

    // builder
    //   .addCase(putAnesthesiaDetails.pending, (state) => {
    //     state.loading = true;
    //   })
    //   .addCase(putAnesthesiaDetails.fulfilled, (state, action) => {
    //     state.loading = false;
    //     state.error = null;
    //   })
    //   .addCase(putAnesthesiaDetails.rejected, (state, action) => {
    //     state.loading = false;
    //     state.error = action.payload as string;
    //   });
  },
});

export const { setAnesthesiaUserDetails } = anesthesiaSlice.actions;

export default anesthesiaSlice.reducer;
