// thunk.ts
import { createAsyncThunk } from "@reduxjs/toolkit";
import api from "../../../../api/api";
import { ITask } from "./types";

const fetchRepTasks = createAsyncThunk(
  "tasks/fetchRepTasks",
  async (_, thunkAPI) => {
    try {
      const response = await api.private.get(
        api.routes.privateRoutes.rep.task_get
      );

      if (response.data && response.data.result) {
        if (response.data.result.length > 0) {
          return response.data.result;
        } else {
          return null as ITask[] | null;
        }
      } else {
        return thunkAPI.rejectWithValue(
          'Invalid response format: Missing "result" field'
        );
      }
    } catch (error: any) {
      console.error("Error caught during API call:", error.message || error);
      return thunkAPI.rejectWithValue(
        error.response?.data?.message || "Failed to fetch Rep Tasks"
      );
    }
  }
);

export { fetchRepTasks };
