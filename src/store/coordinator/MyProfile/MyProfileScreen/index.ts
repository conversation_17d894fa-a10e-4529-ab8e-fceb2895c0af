import { createSlice } from "@reduxjs/toolkit";
import initialState from "./initialstate";
import { fetchProfile } from "./thunk";

const profileSlice = createSlice({
  name: "profile",
  initialState,
  reducers: {
    setProfileDetails: (state, action) => {
      state.profileDetails = {
        ...state.profileDetails,
        ...action.payload,
      };
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchProfile.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchProfile.fulfilled, (state, action) => {
        state.loading = false;
        state.profileDetails = action.payload;
      })
      .addCase(fetchProfile.rejected, (state, action) => {
        state.loading = false;
        state.profileDetails = null;
        state.error = action.payload as string;
      });
  },
});

export const { setProfileDetails } = profileSlice.actions;
export default profileSlice.reducer;
