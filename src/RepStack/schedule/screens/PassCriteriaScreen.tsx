import * as React from "react";
import { Text, View, TouchableOpacity, Image, TextInput } from "react-native";
import ScreenWrapper from "../../../components/ScreenWrapper";
import { useNavigation } from "@react-navigation/native";
import { MatIcon } from "../../../utils";
import SearchablePicker from "../../../components/SearchablePicker";
import ToggleButton from "../../../components/ToggleButton";
import Heading from "../../../components/Heading";
import CustomInput from "../../../components/CustomTextInput";
import CompressionCalculator from "../components/CompressionCalculator";
import OptionSelector from "../../../components/OptionSelector";
import { getDeviceSizeOptions } from "../../../utils";
import CustomCard from "../../../components/CustomCard";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import LineSeperator from "../../../components/LineSeperator";
import { formatPassCriteriaOptions } from "../../../utils";
import {
  fetchPassCriteriaDetails,
  putPassCriteriaDetails,
} from "../../../store/rep/ScheduleStack/laaoProcedures/passcriteria/thunk";
import { useSelectedPatient } from "../hooks/PatientsHooks";
import { useDispatch } from "react-redux";
import { AppDispatch } from "../../../store";
import SaveActionButton from "../../../components/SaveActionButton";
import Loader from "../../../components/Loader";
import {
  useLoaderAndError,
  usePassCriteriaUserDetails,
  usePassCriteriaDetails,
} from "../hooks/passcriteriaHooks";
import { useFocusEffect } from "@react-navigation/native";
import {
  setPassCriteriaUserDetails,
  setSelectedDeviceSize,
} from "../../../store/rep/ScheduleStack/laaoProcedures/passcriteria";
import { optimalSizesArray } from "../hooks/passcriteriaHooks";
import { findPassCriteriaDiff } from "../hooks/passcriteriaHooks";
import { parseIntInput } from "../../../utils";
import PopupModal from "../../../components/Popup";
import CustomCheckbox from "../../../components/CustomCheckboxComponent";

interface IPassCriteriaScreenProps {}

const PassCriteriaScreen: React.FunctionComponent<
  IPassCriteriaScreenProps
> = () => {
  const [modalVisible, setModalVisible] = React.useState(false);

  const [popupMsg, setPopupMsg] = React.useState<string[]>([]);
  const navigation = useNavigation();
  const userDetails = usePassCriteriaUserDetails();
  const passCriteriaDetails = usePassCriteriaDetails();
  const {
    device_sizes,
    size_selected,
    width0,
    width45,
    width90,
    width135,
    position,
    anchor,
    leak,
    leak_value,
  } = formatPassCriteriaOptions(userDetails);

  const validator = () => {
    setPopupMsg([]);
    var temp = false;

    if (width0 === null || width0 === undefined || width0 === 0) {
      setPopupMsg((prev) => [...prev, "Width 0 Cannot be Empty"]);
      temp = true;
    }

    if (width45 === null || width45 === undefined || width45 === 0) {
      setPopupMsg((prev) => [...prev, "Width 45 Cannot be Empty"]);
      temp = true;
    }

    if (width90 === null || width90 === undefined || width90 === 0) {
      setPopupMsg((prev) => [...prev, "Width 90 Cannot be Empty"]);
      temp = true;
    }

    if (width135 === null || width135 === undefined || width135 === 0) {
      setPopupMsg((prev) => [...prev, "Width 135 Cannot be Empty"]);
      temp = true;
    }

    if (
      (leak && leak_value === null) ||
      leak_value === undefined ||
      leak_value === 0
    ) {
      setPopupMsg((prev) => [...prev, "Leak Value cannot be empty"]);
      temp = true;
    }

    if (temp) {
      setModalVisible(true);
      return false;
    } else {
      return true;
    }
  };
  const sealValues = [
    {
      label: "No Leak",
      value: "No Leak",
    },
    {
      label: "Leak",
      value: "Leak",
    },
  ];

  interface SizeSelectorProps {
    deviceSizes: number[];
    conditionArray: number[];
    selectedSize: number | null;
    onPress: (size: any) => void;
  }

  const SizeSelector: React.FC<SizeSelectorProps> = ({
    deviceSizes,
    conditionArray,
    selectedSize,
    onPress,
  }) => {
    return (
      <View className="flex-row flex-wrap justify-between bg-primaryBg">
        {deviceSizes?.length > 0 &&
          deviceSizes.map((item, index) => {
            const isInConditionArray = conditionArray.includes(item);
            const isSelected = selectedSize === item;

            return (
              <TouchableOpacity
                key={index}
                onPress={() => onPress(item)}
                className={`
                  
                  ${
                    isSelected
                      ? "border-2 border-primaryPurple bg-green-3"
                      : "border-2 border-primaryBg"
                  }
                  px-3
                  py-2
                  rounded-md
                  `}
              >
                <Text
                  // style={isSelected ? { textDecorationLine: "underline" } : {}}
                  className={`
                    text-center
                    ${
                      isSelected
                        ? "font-bold text-primaryWhite"
                        : "text-primaryBlack"
                    }
                   
                    text-lg
                 `}
                >
                  {item}
                </Text>
              </TouchableOpacity>
            );
          })}
      </View>
    );
  };

  const dispatch = useDispatch<AppDispatch>();
  const selectedPatient = useSelectedPatient();
  const { loader, error } = useLoaderAndError();

  const optimal_sizes = optimalSizesArray();

  const diff = findPassCriteriaDiff();

  const saveDetails = async () => {
    try {
      if (validator()) {
        const res = await dispatch(
          putPassCriteriaDetails({
            case_details_id: userDetails?.case_detail_id,
            payload: diff.currentValues,
          })
        );
        if (res.payload) {
          if (selectedPatient?.case_id) {
            const refetchRes = await dispatch(
              fetchPassCriteriaDetails({ case_id: selectedPatient.case_id })
            );

            if (refetchRes.payload) {
              dispatch(setPassCriteriaUserDetails(refetchRes.payload));
              navigation.navigate("Fluoroscopy & Contrast");
            }
          }
        }
      }
    } catch (err) {
      console.error(err);
    }
  };

  const handleCancel = () => {
    dispatch(
      setPassCriteriaUserDetails({
        ...userDetails,
        ...passCriteriaDetails,
      })
    );
  };

  useFocusEffect(
    React.useCallback(() => {
      const fetchDetails = async () => {
        if (selectedPatient?.case_id) {
          const res = await dispatch(
            fetchPassCriteriaDetails({ case_id: selectedPatient.case_id })
          );

          if (res.payload) {
            dispatch(setPassCriteriaUserDetails(res.payload));
          }
        }
      };
      fetchDetails();
    }, [dispatch, selectedPatient?.case_id])
  );

  const handleRefresh = async () => {
    if (!selectedPatient?.case_id) return;
    const refetchRes = await dispatch(
      fetchPassCriteriaDetails({ case_id: selectedPatient.case_id })
    );
    if (refetchRes.payload) {
      dispatch(setPassCriteriaUserDetails(refetchRes.payload));
    }
  };

  if (loader) {
    return <Loader />;
  }

  return (
    <ScreenWrapper direction="column" onRefresh={handleRefresh}>
      <KeyboardAwareScrollView></KeyboardAwareScrollView>
      <CustomCard>
        <Heading
          text="Device"
          color="black"
          size="sub-heading"
          showSeperator={false}
          extraStyle="mt-2 pb-2"
        />

        <View className="bg-primaryBg px-4 py-3 rounded-md">
          <SizeSelector
            deviceSizes={device_sizes}
            conditionArray={[]}
            selectedSize={size_selected}
            onPress={(value) => dispatch(setSelectedDeviceSize(value))}
          />
        </View>

        <LineSeperator extraStyle="mt-4 mb-3" />

        <View className="flex-row justify-between items-center">
          <Heading
            text="Position"
            color="black"
            size="sub-heading"
            showSeperator={false}
          />
          <ToggleButton
            messages={["Passed", "Failed"]}
            selected={position}
            setSelected={(value) =>
              dispatch(
                setPassCriteriaUserDetails({
                  position: value === "Passed" ? true : false,
                })
              )
            }
          />

          {/* <CustomCheckbox
            value={position}
            onChange={(val) => {
              dispatch(
                setPassCriteriaUserDetails({
                  position: val,
                })
              );
            }}
          /> */}
        </View>

        <LineSeperator extraStyle="mt-4 mb-3" />

        <View className="flex-row justify-between items-center">
          <Heading
            text="Anchor"
            color="black"
            size="sub-heading"
            showSeperator={false}
          />
          <ToggleButton
            messages={["Passed", "Failed"]}
            selected={anchor}
            setSelected={(value) =>
              dispatch(
                setPassCriteriaUserDetails({
                  anchor: value === "Passed" ? true : false,
                })
              )
            }
          />

          {/* <CustomCheckbox
            value={anchor}
            onChange={(val) => {
              dispatch(
                setPassCriteriaUserDetails({
                  anchor: val,
                })
              );
            }}
          /> */}
        </View>

        <LineSeperator extraStyle="mt-4 mb-3" />

        <View>
          <Heading
            text="Size"
            color="black"
            size="sub-heading"
            showSeperator={false}
            extraStyle="pb-1"
          />
          <View className="rounded-md py-1">
            <CompressionCalculator
              deviceSizes={device_sizes}
              selectedSize={size_selected}
              width0={width0}
              width45={width45}
              width90={width90}
              width135={width135}
            />
          </View>
        </View>

        <LineSeperator extraStyle="mt-4 mb-3" />

        <View>
          <Heading
            text="Seal"
            color="black"
            size="sub-heading"
            showSeperator={false}
            extraStyle="pb-2"
          />
          <View className="rounded-md p-3 bg-primaryBg">
            <OptionSelector
              options={sealValues}
              selected={leak ? "Leak" : "No Leak"}
              onSelect={(value) => {
                dispatch(
                  setPassCriteriaUserDetails({
                    leak: value.value === "Leak" ? true : false,
                  })
                );
              }}
              customSelectedColor="bg-orange-3"
              customSelectedValue="Leak"
            />
            {leak === true ? (
              <View className="flex-column items-center justify-center">
                <View className="mt-4 flex-row items-center pb-4">
                  <View className="mr-4">
                    <Heading
                      text="Leak Value (mm)"
                      size="label"
                      color="black"
                      showSeperator={false}
                    />
                  </View>
                  <CustomInput
                    inputValue={leak_value?.toString()}
                    error={leak_value === 0}
                    onInputChange={(value) =>
                      parseIntInput(value, (updatedValue) => {
                        dispatch(
                          setPassCriteriaUserDetails({
                            leak_value: updatedValue,
                          })
                        );
                      })
                    }
                    keyboardType="numeric"
                    placeholder="(mm)"
                    width="20%"
                    maxLength={2}
                  />
                </View>
                <View
                  className={`
                    ${leak_value >= 5 ? "bg-red-3" : "bg-yellow-3"} 
                    flex-row 
                    items-center 
                    justify-center
                    p-3 
                    rounded
                    w-full
                  `}
                >
                  <Text
                    className={`
                      text-primaryWhite
                    text-md
                    font-bold
                    `}
                  >
                    {leak_value >= 5 || leak_value === 0 ? "FAILED" : "PASSED"}
                  </Text>
                </View>
              </View>
            ) : (
              <View
                className={`
                bg-green-3
                flex-row 
                items-center 
                justify-center
                p-3 
                rounded
                w-full
              `}
              >
                <Text className="text-primaryWhite text-md font-bold">
                  PASSED
                </Text>
              </View>
            )}
          </View>
        </View>
        <LineSeperator extraStyle="my-5" />

        <View className="flex-row items-center justify-center">
          <SaveActionButton
            disabled={!diff.hasDiff}
            onPress={saveDetails}
            onCancel={handleCancel}
          />
        </View>
        <View className="mb-3 "></View>
      </CustomCard>
      <View className="mt-12"></View>
      <PopupModal
        show={modalVisible}
        msg={popupMsg}
        status="warning"
        onClose={() => setModalVisible(false)}
      />
    </ScreenWrapper>
  );
};

export default PassCriteriaScreen;
