import * as React from "react";
import { Text, View } from "react-native";
import CustomInput from "../../../components/CustomTextInput";
import { useDispatch } from "react-redux";
import { AppDispatch } from "../../../store";
import {
  setOptimalSizesArray,
  setCompressionValueForAngle0,
  setCompressionValueForAngle45,
  setCompressionValueForAngle90,
  setCompressionValueForAngle135,
  setPassedFailed,
  setCompressionForAngle0,
  setCompressionForAngle45,
  setCompressionForAngle90,
  setCompressionForAngle135,
} from "../../../store/rep/ScheduleStack/laaoProcedures/passcriteria";
import { parseFloatInput } from "../../../utils";
import LineSeperator from "../../../components/LineSeperator";
import { KeyboardAwareScrollView } from "react-native-ui-lib";

interface ICompressionCalculatorProps {
  deviceSizes: number[];
  selectedSize: number;
  width0: number;
  width45: number;
  width90: number;
  width135: number;
}

const CompressionCalculator: React.FunctionComponent<
  ICompressionCalculatorProps
> = (props) => {
  const dispatch = useDispatch<AppDispatch>();
  const { deviceSizes, selectedSize, width0, width45, width90, width135 } =
    props;

  // Total number of input fields for autofocus functionality.
  const totalFields = 4;
  // Create a ref array to hold references of each CustomInput.
  const fieldRefs = React.useRef<Array<CustomInput | null>>(
    Array(totalFields).fill(null)
  );

  // Handle input change and autofocus to the next input when appropriate.
  const handleInputChange = (
    fieldIndex: number,
    text: string,
    onChange: (value: string) => void
  ) => {
    const rawLength = text.length; // Original length before modifications.
    let newText = text.replace(/^0+/, ""); // Remove leading zeros.

    // Reset empty input to "0".
    if (newText === "") {
      newText = "0";
    }

    onChange(newText);

    // Auto focus logic for decimal inputs
    if (fieldIndex < totalFields - 1) {
      if (text.includes(".")) {
        // If user is typing a decimal, wait until they complete 2 decimal places
        const decimalPart = text.split(".")[1];
        if (decimalPart && decimalPart.length === 2) {
          fieldRefs.current[fieldIndex + 1]?.focus();
        }
      } else if (rawLength === 2) {
        // For whole numbers, add a delay to allow user to type decimal point
        setTimeout(() => {
          // Check if the field still has the same value and no decimal was added
          const currentInput = fieldRefs.current[fieldIndex];
          if (
            currentInput &&
            currentInput.props?.inputValue === newText &&
            !newText.includes(".")
          ) {
            fieldRefs.current[fieldIndex + 1]?.focus();
          }
        }, 800); // 800ms delay to allow decimal input
      }
    }
  };

  const calculateCompression = (deviceSize: number, width: number) => {
    const compression = ((deviceSize - width) / deviceSize) * 100;
    return Math.round(compression);
  };

  const findOptimalSize = React.useCallback(() => {
    let optimalSize: number[] = [];
    deviceSizes?.forEach((size) => {
      let result = {
        width0: calculateCompression(size, width0),
        width45: calculateCompression(size, width45),
        width90: calculateCompression(size, width90),
        width135: calculateCompression(size, width135),
      };

      const allPass = Object.values(result).every(
        (value) => value >= 10 && value <= 30
      );

      if (allPass) {
        optimalSize.push(size);
        dispatch(setPassedFailed(true));
      } else {
        dispatch(setPassedFailed(false));
      }
    });

    if (optimalSize.length > 0) {
      dispatch(setOptimalSizesArray(optimalSize));
      dispatch(setPassedFailed(true));
    } else {
      dispatch(setOptimalSizesArray([]));
      dispatch(setPassedFailed(false));
    }
  }, [deviceSizes, width0, width45, width90, width135, dispatch]);

  // Find the lowest compression (largest measurement) for green circle highlighting
  const findLowestCompression = React.useMemo(() => {
    if (selectedSize === 0) return null;

    const compressions = [
      {
        angle: "0",
        compression: calculateCompression(selectedSize, width0),
        width: width0,
      },
      {
        angle: "45",
        compression: calculateCompression(selectedSize, width45),
        width: width45,
      },
      {
        angle: "90",
        compression: calculateCompression(selectedSize, width90),
        width: width90,
      },
      {
        angle: "135",
        compression: calculateCompression(selectedSize, width135),
        width: width135,
      },
    ];

    // Find the minimum compression (which corresponds to the largest width measurement)
    const minCompression = Math.min(...compressions.map((c) => c.compression));
    return compressions.find((c) => c.compression === minCompression);
  }, [selectedSize, width0, width45, width90, width135]);

  const showPercentage = (selectedSize: number, width: number) => {
    let color = "red";
    const result = calculateCompression(selectedSize, width);
    const isLowestCompression =
      findLowestCompression && findLowestCompression.width === width;

    if (result >= 10 && result <= 30) {
      color = "green";
    } else if (result < 10) {
      color = "red";
    } else if (result > 30 && result <= 40) {
      color = "orange";
    } else {
      color = "red";
    }

    if (selectedSize === 0) {
      return (
        <View style={{ flex: 1, alignItems: "flex-start" }}>
          <Text className="text-red-3 text-left">0%</Text>
        </View>
      );
    }
    return (
      <View style={{ width: "20%", alignItems: "center" }}>
        <View
          className={`
            ${
              isLowestCompression
                ? "border-4 border-green-3 bg-green-2"
                : result < 10
                ? "border-4 border-red-3"
                : result >= 10
                ? "border-4 border-green-3"
                : ""
            }
            rounded-full
            px-2 py-1
            min-w-[50px]
            items-center
            justify-center
          `}
        >
          <Text
            className={`
                   text-[16px]
                   text-center
                   ${
                     color === "green"
                       ? "text-green-3"
                       : color === "orange"
                       ? "text-[#F97316]"
                       : "text-red-3"
                   }
                   ${isLowestCompression ? "font-bold" : ""}
                 `}
          >
            {result}%
          </Text>
        </View>
        {isLowestCompression && (
          <Text className="text-xs text-green-3 mt-1 font-semibold">
            Lowest
          </Text>
        )}
      </View>
    );
  };

  const [deviceType, setDeviceType] = React.useState<string | undefined>(
    "Watchman FLX Pro"
  );

  const showPassFail = () => {
    let result = {
      width0: calculateCompression(selectedSize, width0),
      width45: calculateCompression(selectedSize, width45),
      width90: calculateCompression(selectedSize, width90),
      width135: calculateCompression(selectedSize, width135),
    };

    const allPass = Object.values(result).every(
      (value) => value >= 10 && value <= 30
    );

    return (
      <View
        className={`
        flex 
        items-center
        justify-center
        ${allPass ? "bg-green-3" : "bg-red-3"}
        p-3
        rounded 
        `}
      >
        <Text className="text-primaryWhite text-md font-bold">
          {allPass ? "PASSED" : "FAILED"}
        </Text>
      </View>
    );
  };

  React.useEffect(() => {
    const compression0 = calculateCompression(selectedSize, width0);
    const compression45 = calculateCompression(selectedSize, width45);
    const compression90 = calculateCompression(selectedSize, width90);
    const compression135 = calculateCompression(selectedSize, width135);

    dispatch(setCompressionForAngle0(compression0));
    dispatch(setCompressionForAngle45(compression45));
    dispatch(setCompressionForAngle90(compression90));
    dispatch(setCompressionForAngle135(compression135));
  }, [selectedSize, width0, width45, width90, width135, dispatch]);

  React.useEffect(() => {
    findOptimalSize();
  }, [width0, width45, width90, width135, findOptimalSize]);

  React.useEffect(() => {
    findOptimalSize();
  }, [deviceType, findOptimalSize]);

  // Define the input UI fields with onChange modifications integrating autofocus.
  const inputUIFields = {
    row1: {
      "0deg": {
        label: "0°",
        width: width0,
        onChange: (value: string) => {
          if (Number(value) > 50) return;
          parseFloatInput(value, (updatedValue) => {
            const compression = calculateCompression(
              selectedSize,
              updatedValue
            );
            dispatch(
              setCompressionValueForAngle0({
                updatedValue,
                compression,
              })
            );
          });
        },
      },
      "45deg": {
        label: "45°",
        width: width45,
        onChange: (value: string) => {
          if (Number(value) > 50) return;
          parseFloatInput(value, (updatedValue) => {
            const compression = calculateCompression(
              selectedSize,
              updatedValue
            );
            dispatch(
              setCompressionValueForAngle45({
                updatedValue,
                compression,
              })
            );
          });
        },
      },
    },
    row2: {
      "90deg": {
        label: "90°",
        width: width90,
        onChange: (value: string) => {
          if (Number(value) > 50) return;
          parseFloatInput(value, (updatedValue) => {
            const compression = calculateCompression(
              selectedSize,
              updatedValue
            );
            dispatch(
              setCompressionValueForAngle90({
                updatedValue,
                compression,
              })
            );
          });
        },
      },
      "135deg": {
        label: "135°",
        width: width135,
        onChange: (value: string) => {
          if (Number(value) > 50) return;
          parseFloatInput(value, (updatedValue) => {
            const compression = calculateCompression(
              selectedSize,
              updatedValue
            );
            dispatch(
              setCompressionValueForAngle135({
                updatedValue,
                compression,
              })
            );
          });
        },
      },
    },
  };

  // A mutable counter to assign a field index for autofocus.
  let fieldIndex = 0;

  return (
    <KeyboardAwareScrollView className="">
      <View className="flex-row justify-evenly items-center py-2 bg-lightGray rounded-md mb-2">
        <Text className="text-primaryBlack font-bold text-lg w-[20%] text-center">
          Angle
        </Text>
        <Text className="text-primaryBlack font-bold text-lg w-[30%] text-center ml-3">
          W in (mm)
        </Text>
        <Text className="text-primaryBlack font-bold text-lg  text-center">
          Compression(%)
        </Text>
      </View>

      {Object.entries(inputUIFields).map(([rowKey, fields]) => (
        <View
          key={rowKey}
          className="flex-column justify-between w-full gap-y-2 mb-2"
        >
          {Object.entries(fields).map(([key, { label, width, onChange }]) => {
            const currentFieldIndex = fieldIndex;
            fieldIndex++;
            return (
              <View key={key} className="bg-primaryBg rounded-md">
                <View className="flex-row justify-evenly items-center my-3 gap-3">
                  <View className="w-[20%]">
                    <Text className="text-primaryBlack text-md">{label}</Text>
                  </View>
                  <View className="w-[20%] items-start mr-12">
                    <CustomInput
                      ref={(ref) =>
                        (fieldRefs.current[currentFieldIndex] = ref)
                      }
                      inputValue={width.toString()}
                      error={!width}
                      onInputChange={(text: string) =>
                        handleInputChange(currentFieldIndex, text, onChange)
                      }
                      placeholder={label}
                      keyboardType="numeric"
                      width="100%"
                      maxLength={5}
                    />
                  </View>
                  {showPercentage(selectedSize, width)}
                </View>
              </View>
            );
          })}
        </View>
      ))}
      {showPassFail()}
    </KeyboardAwareScrollView>
  );
};

export default CompressionCalculator;
