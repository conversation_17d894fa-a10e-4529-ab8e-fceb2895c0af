import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import AsyncStorage from "@react-native-async-storage/async-storage";
import api from "../../api/api";

interface ToasterState {
  show: boolean;
  message: string;
  icon: "info" | "success" | "error"; // Icon type
  duration?: number; // Animation duration
}

const initialState: ToasterState = {
  show: false,
  message: "",
  icon: "info", // Icon type
  duration: 3000, // Animation duration
};

const ToasterSlicer = createSlice({
  name: "auth",
  initialState,
  reducers: {
    showToaster: (state, action: PayloadAction<ToasterState>) => {
      const { duration, icon, message, show } = action.payload;
      state.duration = duration;
      state.icon = icon;
      state.message = message;
      state.show = show;
    },
    hideToaster: (state) => {
      state.show = false;
    },
  },
});

export const { showToaster, hideToaster } = ToasterSlicer.actions;

export default ToasterSlicer.reducer;
