import { ReferringProvider, RepReferringProvider } from "./types";

const initialState = {
  providerListDetails: [] as ReferringProvider[], // For dropdown options
  providerDetails: null as ReferringProvider | null, // Selected provider's original details
  userDetails: null as ReferringProvider | null, // For form editing (current state)
  // credentialOptions: [] as RepReferringProvider[],
  credentialOptions: [] as string[],
  activeTab: "referring-providers" as
    | "referring-providers"
    | "pcp-providers"
    | "sites"
    | "implanting-physicians",
  showAddProviderModal: false,
  selectedProvider: "", // Stores the ID of the selected provider
  loading: false,
  error: null as string | null,
};

export default initialState;
