import React, { useState } from "react";
import { View, Text, TouchableOpacity } from "react-native";
import CustomCard from "../../../components/CustomCard";
import CustomInput from "../../../components/CustomTextInput";
import SearchablePicker from "../../../components/SearchablePicker";
import ToggleButton from "../../../components/ToggleButton";
import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons";
import {
  useHistory,
  useUserDetails,
  useHistoryDiff,
  usePreopDetails,
  useMedicationData,
} from "../hooks/preopHooks";
import {
  setPreopUserDetails,
  deleteMedicationById,
} from "../../../store/clinician/ScheduleStack/preop";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch } from "../../../store";
import CustomText from "../../../components/CustomText";
import {
  fetchPreopDetails,
  putPreopHistoryDetails,
} from "../../../store/clinician/ScheduleStack/preop/thunk";
import { useClinicianSelectedPatient } from "../hooks/schedulesHooks";
import { parseIntInput, replaceUndefinedWithNull } from "../../../utils";
import LineSeperator from "../../../components/LineSeperator";
import { FlatList } from "react-native";
import PopupModal from "../../../components/Popup";
import SaveActionButton from "../../../components/SaveActionButton";
import { simpleAutoSave } from "../../../services/simpleAutoSave";
import { setSaveStatus, setLastSavedData } from "../../../store/services";

interface HistoryCardProps {
  onMedOpen: (id?: string) => void;
  onMedClose: () => void;
  medicationBottomSheetRef: React.RefObject<any>;
  chadBottomSheetRef: React.RefObject<any>;
  hasBledBottomSheetRef: React.RefObject<any>;
}

const HistoryCard: React.FC<HistoryCardProps> = ({
  onMedOpen,
  onMedClose,
  medicationBottomSheetRef,
  chadBottomSheetRef,
  hasBledBottomSheetRef,
}) => {
  const dispatch = useDispatch<AppDispatch>();
  const userDetails = useUserDetails();
  const preopDetails = usePreopDetails();
  const selectedPatient = useClinicianSelectedPatient();
  const diff = useHistoryDiff();

  const [medDeleted, setMedDeleted] = useState(false);
  const [selected, setSelected] = useState([]);
  // Create a ref to track if a save is in progress
  const saveInProgressRef = React.useRef(false);

  // Create a ref to track the last saved data hash
  const lastSavedDataHashRef = React.useRef("");
  const [initialUpdate, setInitialUpdate] = useState(false);

  const [modalVisible, setModalVisible] = React.useState(false);

  const [popupMsg, setPopupMsg] = React.useState<string[]>([]);

  const [deleteConfirmationVisible, setDeleteConfirmationVisible] =
    useState(false);
  const [medToDelete, setMedToDelete] = useState<string | null>(null);

  const {
    chad_score,
    hasbled,
    hasbled_score,
    afib_classification_options,
    afib_classification_selected_id,
    primary_rationale_options,
    primary_rationale_selected_id,
    secondary_rationale_options,
    secondary_rationale_selected_id,
    anticoagulation_options,
    anticoagulation_selected,
    distance,
    has_social_support,
    home_address,
    lives_independent,
    prior_ablation_options,
    prior_ablation_selected_id,
    prior_ablation_selected_name,
    other_selected_value,
    secondary_rationale_selected_name,
    primary_rationale_selected_name,
    primary_rationale_other,
    secondary_rationale_other,
  } = useHistory();

  const { med_data } = useMedicationData();

  const validator = () => {
    setPopupMsg([]);

    var temp = false;
    // if (
    //   primary_rationale_selected_id === "" ||
    //   primary_rationale_selected_id === null ||
    //   primary_rationale_selected_id === undefined
    // ) {
    //   setPopupMsg((prev) => [...prev, "Please Enter Primary Rationale"]);
    //   temp = true;
    // }

    if (
      primary_rationale_selected_name?.toLowerCase() === "other" &&
      !primary_rationale_other
    ) {
      setPopupMsg((prev) => [
        ...prev,
        "Please Enter Other for Primary Rationale",
      ]);
      temp = true;
    }

    // if (
    //   secondary_rationale_selected_name?.toLowerCase() === "other" &&
    //   !secondary_rationale_other
    // ) {
    //   setPopupMsg((prev) => [
    //     ...prev,
    //     "Please Enter Other for Secondary Rationale",
    //   ]);
    // }

    if (
      afib_classification_selected_id === null ||
      afib_classification_selected_id === undefined ||
      afib_classification_selected_id === ""
    ) {
      setPopupMsg((prev) => [
        ...prev,
        "Please Select AFIB Classification Type",
      ]);
      temp = true;
    }

    if (
      prior_ablation_selected_name?.toLowerCase() === "other" &&
      !other_selected_value
    ) {
      setPopupMsg((prev) => [...prev, "Please Enter Other for Prior Ablation"]);
      temp = true;
    }

    // if (
    //   prior_ablation_selected_id === null ||
    //   prior_ablation_selected_id === undefined ||
    //   prior_ablation_selected_id === ""
    // ) {
    //   setPopupMsg((prev) => [...prev, "Please Enter Prior Ablation"]);
    //   temp = true;
    // }

    // if (
    //   home_address === null ||
    //   home_address === undefined ||
    //   home_address === ""
    // ) {
    //   setPopupMsg((prev) => [...prev, "Please Enter Home Address"]);
    //   temp = true;
    // }

    // if (
    //   distance === null ||
    //   distance === undefined ||
    //   distance?.toString() === ""
    // ) {
    //   setPopupMsg((prev) => [...prev, "Please Enter Distance From Hospital"]);
    //   temp = true;
    // }

    if (temp) {
      setModalVisible(true);
      return false;
    } else {
      return true;
    }
  };

  const saveDetails = async (): Promise<void> => {
    try {
      // Run validation
      const isValid = validator();

      if (!isValid) {
        dispatch(
          setSaveStatus({
            screenId: "preopHistory", // adjust if needed
            status: "validation_failed",
          })
        );
        setLocalSaveStatus("validation_failed");
        throw new Error("Validation failed");
      }

      // Avoid saving if already in progress
      if (saveInProgressRef.current) {
        return;
      }

      const dataCopy = replaceUndefinedWithNull({ ...diff.currentValues });
      const currentDataHash = JSON.stringify(dataCopy);

      // If the data hasn't changed since the last save, skip saving
      if (currentDataHash === lastSavedDataHashRef.current) {
        return;
      }

      // Mark save as in progress
      saveInProgressRef.current = true;

      const res = await dispatch(
        putPreopHistoryDetails({
          case_id: selectedPatient?.case_id,
          payload: dataCopy,
        })
      );

      if (res.payload && selectedPatient?.case_id) {
        const refetchRes = await dispatch(
          fetchPreopDetails({
            case_id: selectedPatient.case_id,
          })
        );

        if (refetchRes.payload) {
          dispatch(setPreopUserDetails(refetchRes.payload));
          lastSavedDataHashRef.current = currentDataHash;
        }
      }

      saveInProgressRef.current = false;
    } catch (err) {
      console.error("Save error:", err);
      saveInProgressRef.current = false;
      throw err;
    }
  };

  const saveStatus = useSelector(
    (state: any) => state.common?.autoSave?.["history"]?.status
  );

  const [localSaveStatus, setLocalSaveStatus] = React.useState<string>("");

  const effectiveSaveStatus = saveStatus || localSaveStatus;

  /**
   * Derive the save message and styling based on the save status
   */
  const { saveMessage, messageStyle, iconName, iconColor } =
    React.useMemo(() => {
      let message = "";
      let style = "text-primaryPurple italic";
      let icon = "alert-circle-outline";
      let color = "#8143d9";

      switch (effectiveSaveStatus) {
        case "changes_detected":
          message = "Changes detected, saving soon...";
          style = "text-primaryBlack italic";
          icon = "alert-circle-outline";
          color = "#f5c542"; // yellow
          break;
        case "saving":
          message = "Saving... Please don't close the app";
          style = "text-primaryPurple font-bold italic";
          icon = "alert-circle-outline";
          color = "#8143d9"; // purple
          break;
        case "saved":
          message = "All changes saved";
          style = "text-green-3 italic";
          icon = "check-circle-outline";
          color = "#28a745"; // green
          break;
        case "validation_failed":
          message = "Validation failed. Please check your inputs.";
          style = "text-red-3 font-bold italic";
          icon = "alert-circle-outline";
          color = "#d93025"; // red
          break;
        case "error":
          message = "Error saving changes. Please try again.";
          style = "text-red-3 font-bold italic";
          icon = "alert-circle-outline";
          color = "#d93025"; // red
          break;
      }

      return {
        saveMessage: message,
        messageStyle: style,
        iconName: icon,
        iconColor: color,
      };
    }, [effectiveSaveStatus]);

  /**
   * Ref to track previous diff values to prevent unnecessary saves
   */
  const prevDiffRef = React.useRef<string>("");

  /**
   * Ref to track if initial data has been loaded
   * This prevents autosave from triggering before data is fetched
   */
  const initialDataLoadedRef = React.useRef<boolean>(false);

  /**
   * Effect to set initialDataLoaded when data is fetched
   */
  React.useEffect(() => {
    // Check if we have valid data
    const hasValidData = userDetails?.case_id !== undefined;

    if (hasValidData && !initialDataLoadedRef.current) {
      initialDataLoadedRef.current = true;
    }
  }, [userDetails]);

  /**
   * Effect to trigger autosave when data changes
   * This uses our new simpleAutoSave service with local state fallback
   */
  React.useEffect(() => {
    // Only proceed if initial data has been loaded and there are actual differences
    if (!initialDataLoadedRef.current) {
      return;
    }

    // Check if case_id exists to prevent API calls with undefined ID
    if (!userDetails?.case_id) {
      return;
    }

    // Only proceed if there are actual differences
    if (diff.hasDiff) {
      // Check if we have any undefined values in critical fields
      const hasUndefinedValues = Object.values(diff.currentValues).every(
        (val) => val === undefined
      );
      if (hasUndefinedValues) {
        return;
      }

      // Convert current values to string for comparison
      const currentDiffString = JSON.stringify(diff.currentValues);

      // Only trigger save if the values have actually changed
      if (currentDiffString !== prevDiffRef.current) {
        // Update the ref with current values
        prevDiffRef.current = currentDiffString;

        // Create a custom wrapper for setSaveStatus that updates both Redux and local state
        const setStatusWithFallback = (payload: {
          screenId: string;
          status: string;
        }) => {
          // Update local state as a fallback
          setLocalSaveStatus(payload.status);
          // Also dispatch to Redux
          return dispatch(setSaveStatus(payload));
        };

        // Trigger the simple autosave with the current values
        simpleAutoSave(
          "history", // Screen ID
          diff.currentValues, // Data to save
          saveDetails, // Save function
          4000, // 4 second delay before saving
          dispatch, // Redux dispatch
          setStatusWithFallback, // Custom action to set save status with fallback
          setLastSavedData // Action to set last saved data
        );
      } else {
        console.log("No actual changes in diff values, skipping autosave");
      }
    }
  }, [
    diff.hasDiff,
    diff.currentValues,
    dispatch,
    setLocalSaveStatus,
    userDetails,
  ]);

  const handleCancel = () => {
    // Reset the fields to the initial data
    dispatch(
      setPreopUserDetails({
        ...userDetails,
        ...preopDetails,
      })
    );
  };
  const handleOpen = (id?: string) => {
    onMedOpen(id);
  };

  const handleAddMedication = () => {
    handleOpen();
  };

  const handleClose = () => {
    if (medicationBottomSheetRef.current) {
      medicationBottomSheetRef.current.stateCleanUp();
    }
    onMedClose();
  };

  React.useEffect(() => {
    if (
      anticoagulation_selected &&
      selected.length === 0 &&
      !medDeleted &&
      !initialUpdate
    ) {
      setSelected(anticoagulation_selected);
      setInitialUpdate(true);
    }
  }, [anticoagulation_selected, medDeleted]);

  const renderMed = ({ item, index }) => {
    return (
      <>
        <View
          className={`${
            index === 0
              ? "rounded-t-md"
              : index === med_data.length - 1
              ? "rounded-b-md"
              : ""
          } flex-row justify-between bg-primaryBg  items-center p-2 }`}
        >
          <View className="flex-row items-center">
            <Text className="text-md font-bold text-primaryPurple">
              {item.med_name}
            </Text>
            <Text className="text-sm text-primaryBlack">
              {" - "}
              {item.med_dose} {item.dosing_frequency}
            </Text>
          </View>

          <View className="flex-row space-x-4 items-center">
            {/* Edit Button */}
            <TouchableOpacity
              onPress={() => {
                handleOpen(item.id);
              }}
              className="px-4 py-2"
            >
              <MaterialCommunityIcons name="pencil" size={20} color="#3b82f6" />
            </TouchableOpacity>

            {/* Delete Button */}
            <TouchableOpacity
              onPress={() => {
                setMedToDelete(item.id);
                setDeleteConfirmationVisible(true);
              }}
              className="px-4 py-2"
            >
              <MaterialCommunityIcons
                name="trash-can"
                size={20}
                color="#ef4444"
              />
            </TouchableOpacity>
          </View>
        </View>

        {/* Add LineSeperator except for the last item */}
        {index < med_data.length - 1 && <LineSeperator color="primaryWhite" />}
      </>
    );
  };

  const renderEmpty = () => {
    return (
      <View className="flex-row justify-center">
        <CustomText value={"None"} />
      </View>
    );
  };

  return (
    <View className="p-3">
      {saveMessage && (
        <View
          style={{
            flexDirection: "row",
            justifyContent: "flex-end",
            alignItems: "center",
            marginVertical: 4,
          }}
        >
          <MaterialCommunityIcons name={iconName} color={iconColor} size={20} />
          <Text className={`text-sm ${messageStyle}`} style={{ marginLeft: 6 }}>
            {saveMessage}
          </Text>
        </View>
      )}

      <View className="gap-y-2">
        <Text className="text-primaryPurple text-lg font-semibold">
          Relavant Medical History
        </Text>
        <View className="flex-row  items-center gap-x-4 pr-4">
          <View className="justify-end w-[50%]">
            <TouchableOpacity
              className={`flex-row shadow-sm ${
                chad_score && chad_score < 3 && chad_score >= 1
                  ? "bg-red-1"
                  : chad_score && chad_score >= 4
                  ? "bg-primaryBg"
                  : "bg-red-1"
              } rounded justify-between p-2`}
              onPress={() => {
                chadBottomSheetRef.current?.open();
              }}
            >
              <Text
                className={`
                  ${
                    chad_score && chad_score < 3 && chad_score >= 1
                      ? "text-red-3"
                      : chad_score && chad_score >= 4
                      ? "text-primaryPurple"
                      : "text-red-3"
                  } 
                  `}
              >
                CHA<Text style={{ fontSize: 9 }}>2</Text>DS
                <Text style={{ fontSize: 9 }}>2</Text>-VASc
              </Text>
              <Text
                className={`
                                   ${
                                     chad_score &&
                                     chad_score < 3 &&
                                     chad_score >= 1
                                       ? "text-red-3"
                                       : chad_score && chad_score >= 4
                                       ? "text-primaryPurple"
                                       : "text-red-3"
                                   } 
                                  `}
              >
                {chad_score}
              </Text>
            </TouchableOpacity>
          </View>
          <TouchableOpacity
            className="w-1/2 flex-row shadow-sm bg-primaryBg rounded-md justify-between p-2"
            onPress={() => {
              hasBledBottomSheetRef.current?.open();
            }}
          >
            <Text className="text-primaryPurple">HAS-BLED</Text>
            <Text className="text-primaryPurple">{hasbled_score}</Text>
          </TouchableOpacity>
        </View>
        <LineSeperator extraStyle="mt-2 " />

        {/* Anticoagulation */}
        <View className="gap-2">
          <View className="flex-row justify-between">
            <Text className="text-primaryPurple text-lg font-semibold">
              Current Medication Regimen
            </Text>
            <MaterialCommunityIcons
              name="plus-circle-outline"
              color="#8143d9"
              size={25}
              onPress={handleAddMedication}
            />
          </View>

          <FlatList
            data={med_data}
            renderItem={renderMed}
            keyExtractor={(item) => item.id}
            // ListEmptyComponent={renderEmpty}
            scrollEnabled={false}
          />
        </View>

        <LineSeperator extraStyle="mt-2 " />

        {/* Reason for LAAC */}

        <Text className="text-primaryBlack font-medium ">
          Primary Rationale
        </Text>
        <View className="flex-1">
          <SearchablePicker
            placeholder="Select"
            items={primary_rationale_options || []} // Assuming you have options for primary rationale
            value={primary_rationale_selected_id || ""}
            onValueChange={(option) => {
              dispatch(
                setPreopUserDetails({
                  patient: {
                    ...userDetails?.patient,
                    rationale: {
                      ...userDetails?.patient?.rationale,
                      primary: {
                        ...userDetails?.patient?.rationale?.primary,
                        selected: {
                          ...userDetails?.patient?.rationale?.primary?.selected,
                          id: option.value,
                          name: option.label,
                        },
                      },
                    },
                  },
                })
              );
            }}
            height={38}
            disableError
          />
        </View>

        {primary_rationale_selected_name?.toLowerCase() === "other" && (
          <View>
            <CustomInput
              placeholder="Other"
              height={38}
              inputValue={primary_rationale_other || ""}
              onInputChange={(val) => {
                dispatch(
                  setPreopUserDetails({
                    patient: {
                      ...userDetails?.patient,
                      rationale: {
                        ...userDetails?.patient?.rationale,
                        primary: {
                          ...userDetails?.patient?.rationale?.primary,
                          selected: {
                            ...userDetails?.patient?.rationale?.primary
                              ?.selected,
                            other: val,
                          },
                        },
                      },
                    },
                  })
                );
              }}
            />
          </View>
        )}

        <LineSeperator extraStyle="mt-2 " />

        <Text className="text-primaryBlack font-medium ">
          Secondary Rationale
        </Text>
        <View className="flex-1">
          <SearchablePicker
            placeholder="Select"
            items={secondary_rationale_options || []} // Assuming you have options for secondary rationale
            value={secondary_rationale_selected_id || ""}
            onValueChange={(option) => {
              dispatch(
                setPreopUserDetails({
                  patient: {
                    ...userDetails?.patient,
                    rationale: {
                      ...userDetails?.patient?.rationale,
                      secondary: {
                        ...userDetails?.patient?.rationale?.secondary,
                        selected: {
                          ...userDetails?.patient?.rationale?.secondary
                            ?.selected,
                          id: option.value,
                          name: option.label,
                        },
                      },
                    },
                  },
                })
              );
            }}
            height={38}
            disableError
          />
        </View>

        {secondary_rationale_selected_name?.toLowerCase() === "other" && (
          <View>
            <CustomInput
              placeholder="Other"
              height={38}
              inputValue={secondary_rationale_other || ""}
              onInputChange={(val) => {
                dispatch(
                  setPreopUserDetails({
                    patient: {
                      ...userDetails?.patient,
                      rationale: {
                        ...userDetails?.patient?.rationale,
                        secondary: {
                          ...userDetails?.patient?.rationale?.secondary,
                          selected: {
                            ...userDetails?.patient?.rationale?.secondary
                              ?.selected,
                            other: val,
                          },
                        },
                      },
                    },
                  })
                );
              }}
            />
          </View>
        )}

        <LineSeperator extraStyle="mt-2 mb-1 " color="primaryWhite" />

        <Text className="text-primaryPurple text-lg font-semibold ">
          Atrial Arrhythmia History
        </Text>

        {/* Afib Classification */}

        <Text className="text-primaryBlack font-medium ">
          Afib Classification Type
        </Text>
        <View className="flex-1">
          <SearchablePicker
            placeholder="Select"
            items={afib_classification_options || []}
            value={afib_classification_selected_id || ""}
            onValueChange={(option) => {
              dispatch(
                setPreopUserDetails({
                  patient: {
                    ...userDetails?.patient,
                    afib_classification: {
                      ...userDetails?.patient?.afib_classification,
                      selected: {
                        id: option.value,
                        name: option.label,
                      },
                    },
                  },
                })
              );
            }}
            height={38}
          />
        </View>

        <LineSeperator extraStyle="mt-2 mb-1 " color="primaryWhite" />

        {/* Prior Ablations */}

        <Text className="text-primaryBlack font-medium ">Prior Ablations</Text>
        <View className="flex-1">
          <SearchablePicker
            placeholder="Select"
            items={prior_ablation_options || []} // Assuming you have options for prior ablations
            value={prior_ablation_selected_id || ""}
            onValueChange={(option) => {
              dispatch(
                setPreopUserDetails({
                  patient: {
                    ...userDetails?.patient,
                    prior_ablation: {
                      ...userDetails?.patient?.prior_ablation,
                      selected: {
                        id: option.value,
                        name: option.label,
                      },
                    },
                  },
                })
              );
            }}
            disableError
            height={38}
          />
        </View>

        {prior_ablation_selected_name?.toLowerCase() === "other" && (
          <View className="">
            <CustomInput
              inputValue={other_selected_value || ""}
              onInputChange={(val) => {
                dispatch(
                  setPreopUserDetails({
                    patient: {
                      ...userDetails?.patient,
                      prior_ablation: {
                        ...userDetails?.patient?.prior_ablation,
                        selected: {
                          ...userDetails?.patient?.prior_ablation?.selected,
                          other: val,
                        },
                      },
                    },
                  })
                );
              }}
              placeholder="Other"
            />
          </View>
        )}

        <LineSeperator extraStyle="mt-2 mb-1 " color="primaryWhite" />

        <Text className="text-primaryPurple text-lg font-semibold">
          Social History
        </Text>

        {/* Home Address */}

        <Text className="text-primaryBlack font-medium ">Home Address</Text>
        <View className="flex-1">
          <CustomInput
            inputValue={home_address || ""}
            // error={!home_address}
            onInputChange={(value) => {
              dispatch(
                setPreopUserDetails({
                  patient: {
                    ...userDetails?.patient,
                    social_history: {
                      ...userDetails?.patient?.social_history,
                      home_address: {
                        ...userDetails?.patient?.social_history?.home_address,
                        address: value,
                      },
                    },
                  },
                })
              );
            }}
            height={38}
          />
        </View>

        <LineSeperator extraStyle="mt-2 mb-1 " color="primaryWhite" />

        {/* Distance from Hospital */}

        <Text className="text-primaryBlack font-medium ">
          Distance from hospital(miles)
        </Text>
        <View className="flex-1">
          <CustomInput
            inputValue={distance?.toString() || ""}
            // error={!distance}
            onInputChange={(value) =>
              parseIntInput(value, (updatedValue) => {
                dispatch(
                  setPreopUserDetails({
                    patient: {
                      ...userDetails?.patient,
                      social_history: {
                        ...userDetails?.patient?.social_history,
                        distance: updatedValue,
                      },
                    },
                  })
                );
              })
            }
            height={38}
            keyboardType="numeric"

            // maxLength={3}
          />
        </View>

        <LineSeperator extraStyle="mt-2 mb-1 " color="primaryWhite" />

        {/* Lives Independently */}
        <View className="flex-row justify-between items-center ">
          <Text className="text-primaryBlack font-medium ">
            Lives independently
          </Text>
          <ToggleButton
            messages={["Yes", "No"]}
            selected={lives_independent ? "Yes" : "No"}
            setSelected={(value) => {
              dispatch(
                setPreopUserDetails({
                  patient: {
                    ...userDetails?.patient,
                    social_history: {
                      ...userDetails?.patient?.social_history,
                      lives_independent: value === "Yes" ? true : false,
                    },
                  },
                })
              );
            }}
            width="w-[55px]"
            invertColor
          />
        </View>

        <LineSeperator extraStyle="mt-2 mb-1 " color="primaryWhite" />

        {/* Has Social Support */}
        <View className="flex-row justify-between items-center ">
          <Text className="text-primaryBlack font-medium">
            Has social support
          </Text>
          <ToggleButton
            messages={["Yes", "No"]}
            selected={has_social_support ? "Yes" : "No"}
            setSelected={(value) => {
              dispatch(
                setPreopUserDetails({
                  patient: {
                    ...userDetails?.patient,
                    social_history: {
                      ...userDetails?.patient?.social_history,
                      has_social_support: value === "Yes" ? true : false,
                    },
                  },
                })
              );
            }}
            width="w-[55px]"
          />
        </View>
        <LineSeperator extraStyle="mt-2 mb-5" color="primaryWhite" />

        {/* Action Buttons */}
        <View className="flex-row justify-center gap-4 ">
          {/* <TouchableOpacity
            onPress={saveDetails}
            className={`border 
                       ${
                         !diff?.hasDiff
                           ? `bg-primaryGray border-primaryGray`
                           : `bg-primaryPurple border-primaryPurple`
                       }
                        px-6 py-3 rounded-full`}
            disabled={!diff?.hasDiff}
          >
            <Text className="text-primaryWhite font-semibold">Save</Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={handleCancel}
            className="border border-primaryPurple px-6 py-3 rounded-full bg-primaryBg"
          >
            <Text className="text-primaryPurple">Cancel</Text>
          </TouchableOpacity> */}
          {/* <SaveActionButton
            disabled={!diff.hasDiff}
            onPress={saveDetails}
            onCancel={handleCancel}
          /> */}
        </View>
      </View>
      <PopupModal
        show={modalVisible}
        msg={popupMsg}
        status="warning"
        onClose={() => setModalVisible(false)}
      />
      <PopupModal
        show={deleteConfirmationVisible}
        msg={["Are you sure you want to delete this medication?"]}
        status="warning"
        onClose={() => setDeleteConfirmationVisible(false)}
      >
        <View className="flex-row justify-end mt-4">
          <TouchableOpacity
            onPress={() => setDeleteConfirmationVisible(false)}
            style={{
              paddingHorizontal: 16,
              paddingVertical: 8,
              backgroundColor: "#D1D5DB",
              borderRadius: 6,
              marginRight: 8,
            }}
          >
            <Text style={{ color: "black" }}>Cancel</Text>
          </TouchableOpacity>

          <TouchableOpacity
            onPress={() => {
              if (medToDelete) {
                dispatch(deleteMedicationById(medToDelete));
                setDeleteConfirmationVisible(false);
                setMedToDelete(null);
                setMedDeleted(true);
              }
            }}
            style={{
              paddingHorizontal: 16,
              paddingVertical: 8,
              backgroundColor: "#EF4444",
              borderRadius: 6,
            }}
          >
            <Text style={{ color: "white" }}>Delete</Text>
          </TouchableOpacity>
        </View>
      </PopupModal>
    </View>
  );
};

export default HistoryCard;
