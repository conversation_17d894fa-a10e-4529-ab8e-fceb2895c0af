interface PreopApiResponse {
  preopDetails: CaseDetails | null;
  userDetails: CaseDetails | null;
  loading: boolean;
  error: string | null;
}

interface CaseDetails {
  case_id: string;
  procedure_date: string;
  procedure_time: string;
  patient: {
    id: string;
    first_name: string;
    last_name: string;
    image_url: string | null;
    age: number;
    sex: string;
    cta: boolean;
    tee: boolean;
    anticoagulation: {
      selected: {
        id: string;
        name: string;
        quantity: number;
        dosing_frequency: {
          id: string;
          name: string;
        };
      }[];
      options: {
        id: string;
        name: string;
        quantity: number[];
      }[];
      frequency_options: {
        id: string;
        name: string;
      }[];
    };

    rationale: {
      primary: string;
      secondary: string | null;
    };
    cha2ds2_vasc: {
      score: number;
      calculation: {
        congestive_heart_failure: number;
        hypertension: number;
        age: number;
        diabetes_mellitus: number;
        stroke_tia_thromboembolic_event: number;
        vascular_disease: number;
        sex: number;
      };
    };
    referring_provider: {
      selected: { id: string | null; name: string | null };
      options: { id: string; name: string }[];
    };
    afib_ablation: boolean;
    dob: string;
    has_bled_score: {
      score: number | null;
      calculation: {
        hyperten_uncontrol: number | null;
        abnorm_liver_func: number | null;
        abnorm_renal_func: number | null;
        stroke: number | null;
        bleeding: number | null;
        labile_inr: number | null;
        alcohol: number | null;
        medication_bleeding: number | null;
        age: number | null;
      };
    };
    pcp: {
      selected: { id: string; name: string };
      options: { id: string; name: string }[];
    };
    prior_ablation: string | null;
    afib_classification: {
      selected: { id: string | null; name: string | null };
      options: { id: string; name: string }[];
    };
    social_history: {
      home_address: {
        address: string | null;
        city: string | null;
        state: string | null;
      };
      distance: number | null;
      lives_independent: boolean;
      has_social_support: boolean;
    };
  };
  implanting_physician: {
    selected: { id: string; name: string };
    options: { id: string; name: string }[];
  };
  site: {
    id: string | null;
    name: string | null;
    image_url: string | null;
    state: string | null;
  };
  consult_visit: {
    visit_date: string | null;
    physician_id: string | null;
    procedure_scheduled_date: string | null;
    pre_op: {
      visit_date: string | null;
      physician_name: string | null;
      visit_type: string | null;
    }[];
    post_op: {
      visit_date: string | null;
      physician_name: string | null;
      visit_type: string | null;
    }[];
    scheduled: {
      visit_date: string | null;
      physician_name: string | null;
      visit_type: string | null;
    }[];
  };
  lab_details: {
    hemoglobin: number | null;
    platelets: number | null;
    creatinine: number | null;
    egfr: number | null;
    rhythm_details: {
      selected: { id: string | null; name: string | null };
      options: { id: string; name: string }[];
    };
    ventricular_rate: number | null;
    updated_at: string | null;
  };
}

interface Medication {
  id: string;
  med_id: string;
  med_name: string;
  med_dose: string | number;
  dosing_frequency: string;
  dosing_frequency_id: string;
}

export type { PreopApiResponse, CaseDetails, Medication };
