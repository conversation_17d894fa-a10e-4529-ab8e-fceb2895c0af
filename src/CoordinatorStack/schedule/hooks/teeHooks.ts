import { useSelector } from "react-redux";
import { RootState } from "../../../store";

const useLoaderAndError = () => {
  const loader = useSelector(
    (state: RootState) => state.coordinator.schedule.tee.loading
  );
  const error = useSelector(
    (state: RootState) => state.coordinator.schedule.tee.error
  );
  return { loader, error };
};

const useteeDetails = () => {
  const teeDetails = useSelector(
    (state: RootState) => state.coordinator.schedule.tee.tee
  );
  return teeDetails;
};

const useTEEUserDetails = () => {
  const userDetails = useSelector(
    (state: RootState) => state.coordinator.schedule.tee.userDetails
  );
  return userDetails;
};

const findTEEDiff = () => {
  const teeDetails = useteeDetails();
  const userDetails = useTEEUserDetails();

  const diff = {
    laao_morphology:
      teeDetails?.morphology?.selected?.id ===
      userDetails?.morphology?.selected?.id
        ? null
        : userDetails?.morphology?.selected.id,
  };

  const nonNullDiff = Object.entries(diff).reduce((acc, [key, value]) => {
    if (value !== null && value !== undefined) {
      acc[key] = value;
    }
    return acc;
  }, {});

  return {
    hasDiff: Object.values(nonNullDiff).some((value) => value !== ""),
    currentValues: nonNullDiff,
  };
};
export { useLoaderAndError, useteeDetails, useTEEUserDetails, findTEEDiff };
