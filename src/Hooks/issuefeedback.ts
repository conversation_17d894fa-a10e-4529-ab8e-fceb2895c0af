import * as React from "react";
import { useSelector } from "react-redux";
import { RootState } from "../store";

const useLoaderAndError = () => {
  const loader = useSelector((state: RootState) => state.jira.loading);
  const error = useSelector((state: RootState) => state.jira.error);
  return { loader, error };
};

const useJiraIssue = () => {
  const issue = useSelector((state: RootState) => state.jira.issue);
  return issue;
};

const useJiraDetails = () => {
  const { issue, error, loading } = useSelector(
    (state: RootState) => state.jira
  );

  return { issue, error, loading };
};

const useIssueTypes = (filterSubtasks = true) => {
  const issueTypes = useSelector((state: RootState) => state.jira.issueTypes);
  const loading = useSelector(
    (state: RootState) => state.jira.loadingIssueTypes
  );
  const error = useSelector((state: RootState) => state.jira.issueTypesError);

  // Filter out subtasks if needed and transform data for UI
  const filteredIssueTypes = React.useMemo(() => {
    // Return empty array if issueTypes is null, undefined, or not an array
    if (!issueTypes || !Array.isArray(issueTypes)) {
      return [];
    }

    try {
      return issueTypes
        .filter((type) => !filterSubtasks || !type.subtask) // Filter out subtasks if filterSubtasks is true
        .map((type) => ({
          id: type.id || "",
          name: type.name || "",
          description: type.description || "", // Provide default empty string if description is missing
          subtask: !!type.subtask, // Convert to boolean in case it's undefined
        }));
    } catch (err) {
      console.error("Error processing issue types:", err);
      return [];
    }
  }, [issueTypes, filterSubtasks]);

  return { issueTypes: filteredIssueTypes, loading, error };
};

export { useLoaderAndError, useJiraIssue, useJiraDetails, useIssueTypes };
