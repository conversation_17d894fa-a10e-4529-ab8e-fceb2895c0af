import * as React from "react";
import { Text, View, TouchableOpacity } from "react-native";
import CustomText from "./CustomText";
import {
  fetchGroinAccess,
  putGroinAccess,
} from "../store/rep/ScheduleStack/patientDetails/thunk";
import { useDispatch } from "react-redux";
import { AppDispatch } from "../store";
import { useFocusEffect } from "@react-navigation/native";
import {
  useGroinAccessDetails,
  useLoaderAndError,
} from "../RepStack/schedule/hooks/patientDetailsHooks";
import DatePicker from "react-native-date-picker";
import moment from "moment";
import Heading from "./Heading";
import Loader from "./Loader";
import Error from "./Error";
import { setGroinDetails } from "../store/rep/ScheduleStack/patientDetails";

interface ICaseInfoCardProps {
  name: string;
  age: number;
  gender: string;
  procedureDate: string;
  procedureTime: string;
  implantingPhysician: string;
  hospital: string;
  case_id: string | number;
}

const CaseInfoCard: React.FunctionComponent<ICaseInfoCardProps> = (props) => {
  const dispatch = useDispatch<AppDispatch>();

  const [startTimeModal, setStartTimeModal] = React.useState(false);
  const [endTimeModal, setEndTimeModal] = React.useState(false);

  const {
    groin_access_end_time,
    case_detail_id,
    groin_access_start_time,
    total_groin_time,
  } = useGroinAccessDetails();

  const fetchDetails = async () => {
    try {
      const res = await dispatch(
        fetchGroinAccess({ case_id: props.case_id.toString() })
      );
    } catch (error) {
      console.error(error);
    }
  };

  const putGroin = async (payload) => {
    const res = await dispatch(
      putGroinAccess({
        case_detail_id: case_detail_id,
        payload: payload,
      })
    );

    const fetch = fetchDetails();
    return res;
  };

  useFocusEffect(
    React.useCallback(() => {
      fetchDetails();
    }, [props.case_id])
  );

  const { loading, error } = useLoaderAndError();

  if (error) {
    return <Error message={error} />;
  }

  return (
    <View className="flex-1 h-[100%] bg-primaryBg rounded-lg m-1">
      <View className="p-4">
        <CustomText
          value={"Case Info"}
          className="font-bold text-primaryPurple text-center text-[18px]"
        />

        <View className="flex-col justify-between gap-3 mt-4 bg-primaryWhite rounded-lg p-4">
          <CustomText
            value={
              <>
                {props.name}
                {" | "}
                {props.age}
                {" Y/O "}
                {props.gender}
              </>
            }
            className="text-primaryPurple font-bold"
          />

          <CustomText
            value={
              <>
                <CustomText value="Procedure Time: " className="font-bold" />
                {moment(props.procedureTime, "hh:mm:ss").format("HH:mm")}
                {", "}
                {moment(props.procedureDate).format("MM/DD/YYYY")}
              </>
            }
          />
          <CustomText
            value={
              <>
                <CustomText
                  value="Implanting Physician: "
                  className="font-bold"
                />
                {props.implantingPhysician}
              </>
            }
          />
          <CustomText
            value={
              <>
                <CustomText value="Hospital: " className="font-bold" />
                {props.hospital}
              </>
            }
          />
        </View>
      </View>

      {loading ? (
        <Loader />
      ) : (
        <View className="p-4">
          <View>
            <Heading
              text="Start Time (groin access)"
              size="label"
              color={"black"}
              extraStyle="pb-3"
              showSeperator={false}
            />

            <TouchableOpacity
              onPress={() => {
                setStartTimeModal(true);
              }}
              className="border border-primaryPurple p-4 rounded-lg bg-primaryWhite"
            >
              <CustomText
                value={
                  groin_access_start_time
                    ? moment(groin_access_start_time, "HH:mm:ss").format(
                        "HH:mm"
                      )
                    : "Select Start Time"
                }
                className="text-primaryBlack text-md"
              />
            </TouchableOpacity>
          </View>

          <View className="mt-3">
            <Heading
              text="End Time (groin access)"
              size="label"
              color={"black"}
              extraStyle="pb-3"
              showSeperator={false}
            />
            <TouchableOpacity
              onPress={() => {
                setEndTimeModal(true);
              }}
              className="border border-primaryPurple p-4 rounded-lg bg-primaryWhite"
              disabled={!groin_access_start_time}
            >
              <CustomText
                value={
                  groin_access_end_time
                    ? moment(groin_access_end_time, "HH:mm:ss").format("HH:mm")
                    : "Select End Time"
                }
                className="text-primaryBlack text-md"
              />
            </TouchableOpacity>
          </View>

          <View className="mt-3">
            <Heading
              text="Total Time (hrs)"
              size="label"
              color={"black"}
              extraStyle="pb-3"
              showSeperator={false}
            />
            <TouchableOpacity
              disabled
              className="border border-primaryGray p-4 rounded-lg bg-primaryWhite"
            >
              <CustomText
                value={total_groin_time?.split(":").slice(0, 2).join(":")}
                className="text-primaryBlack text-md"
              />
            </TouchableOpacity>
          </View>
        </View>
      )}
      {/* START TIME */}
      <DatePicker
        modal
        open={startTimeModal}
        date={
          groin_access_start_time
            ? moment(groin_access_start_time, "HH:mm:ss").toDate()
            : new Date()
        }
        onConfirm={(date) => {
          setStartTimeModal(false);
          dispatch(
            setGroinDetails({
              groin_access_start_time: moment(date).format("HH:mm:ss"),
            })
          );
          putGroin({
            groin_access_start_time: moment(date).format("HH:mm:ss"),
          });
        }}
        onCancel={() => {
          setStartTimeModal(false);
        }}
        mode={"time"}
        maximumDate={
          groin_access_end_time
            ? moment(groin_access_end_time, "HH:mm:ss")
                .subtract(1, "minute")
                .toDate()
            : undefined
        }
      />

      {/* END TIME */}
      <DatePicker
        modal
        open={endTimeModal}
        date={
          groin_access_end_time
            ? moment(groin_access_end_time, "HH:mm:ss").toDate()
            : new Date()
        }
        onConfirm={(date) => {
          setEndTimeModal(false);
          dispatch(
            setGroinDetails({
              groin_access_end_time: moment(date).format("HH:mm:ss"),
            })
          );
          putGroin({
            groin_access_end_time: moment(date).format("HH:mm:ss"),
          });
        }}
        onCancel={() => {
          setEndTimeModal(false);
        }}
        mode={"time"}
        minimumDate={
          groin_access_start_time
            ? moment(groin_access_start_time, "HH:mm:ss")
                .add(1, "minute")
                .toDate()
            : undefined
        }
      />
    </View>
  );
};

export default CaseInfoCard;
