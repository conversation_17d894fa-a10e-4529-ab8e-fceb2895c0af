import * as React from 'react';
import {Text, View, Image, ScrollView} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';

interface IPatientDetailCardProps {
  fullName: string;
  age: string;
  diagonosed: string;
  status: string;
}

const PatientDetailCard: React.FunctionComponent<IPatientDetailCardProps> = ({
  fullName,
  age,
  diagonosed,
  status,
}) => {
  return (
    <ScrollView>
      <LinearGradient
        colors={['#8143D9', '#9933CC']}
        start={{x: 0.5, y: 0}}
        end={{x: 0.5, y: 1}}
        className="h-[200px] w-full px-4 rounded-[20px] flex-custom">
        <View className="h-[175px] w-28">
          <Image
            source={require('../../../../assests/melissa.png')}
            className="rounded-[25px] h-full w-full"
          />
        </View>

        <View className="w-2/3 flex gap-[10px]">
          <View className="border-b-[0.4px] border-white pb-1">
            <Text className="text-lg font-bold text-white">{fullName}</Text>
          </View>
          <View className="border-b-[0.4px] border-white pb-1">
            <Text className="text-white">{age}</Text>
          </View>
          <View className="border-b-[0.4px] border-white  pb-1">
            <Text className="text-white">{diagonosed}</Text>
          </View>
          <View className="border-b-[0.4px] border-white pb-1">
            <Text className="text-white">Procedure: {status}</Text>
          </View>
        </View>
      </LinearGradient>
    </ScrollView>
  );
};

export default PatientDetailCard;
