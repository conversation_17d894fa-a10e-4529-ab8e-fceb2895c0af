import { createAsyncThunk } from "@reduxjs/toolkit";
import api from "../../../../api/api";

const fetchCTADeails = createAsyncThunk(
  "schedule/fetchCTADeails",
  async ({ case_id }: { case_id: string }, thunkAPI) => {
    try {
      const response = await api.private.get(
        api.routes.privateRoutes.rep.preop_cta_get(case_id)
      );

      if (response.data && response.data.result) {
        return response.data.result;
      } else {
        return thunkAPI.rejectWithValue(
          'Invalid response format: Missing "result" field'
        );
      }
    } catch (error) {
      console.error("Error caught during API call:", error);
      return thunkAPI.rejectWithValue("Failed to fetch cta details");
    }
  }
);

const fetchTruplanUrl = createAsyncThunk(
  "schedule/fetchTruplanUrl",
  async ({ case_id }: { case_id: string }, thunkAPI) => {
    try {
      const response = await api.private.get(
        api.routes.privateRoutes.rep.truplan_pdf_get(case_id)
      );

      if (response.data && response.data.result) {
        return response.data.result.blob_url;
      } else {
        return thunkAPI.rejectWithValue(
          'Invalid response format: Missing "result" field'
        );
      }
    } catch (error) {
      console.error("Error caught during API call:", error);
      return thunkAPI.rejectWithValue("Failed to fetch trueplan url");
    }
  }
);

const putCtaScreenDetails = createAsyncThunk(
  "schedule/putCtaScreenDetails",
  async ({ case_id, payload }: { case_id: string; payload: any }, thunkAPI) => {
    try {
      console.info("putCtaScreenDetails", payload);
      const response = await api.private.put(
        api.routes.privateRoutes.rep.preop_cta_put(case_id),
        payload
      );
      if (response.data && response.data.result) {
        return response.data.result;
      } else {
        return thunkAPI.rejectWithValue(
          'Invalid response format: Missing "result" field'
        );
      }
    } catch (error: any) {
      console.error(
        "Error caught during API call:",
        error.response.data || error
      );
      return thunkAPI.rejectWithValue("Failed to fetch Preop-CTA details");
    }
  }
);

export { fetchCTADeails, fetchTruplanUrl, putCtaScreenDetails };
