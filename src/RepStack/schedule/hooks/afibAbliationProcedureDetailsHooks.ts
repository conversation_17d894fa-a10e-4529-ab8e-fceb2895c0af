import { useSelector } from "react-redux";
import { RootState } from "../../../store";
import { IProcedureDetails } from "../../../store/rep/ScheduleStack/afibAblation/procedureDetails/types";

const useLoaderAndError = () => {
  const loader = useSelector(
    (state: RootState) => state.rep.afib.procedureDetails.loading
  );
  const error = useSelector(
    (state: RootState) => state.rep.afib.procedureDetails.error
  );
  return { loader, error };
};

const useAfibProcedureUserDetails = (): IProcedureDetails | null => {
  const { userDetails } = useSelector(
    (state: RootState) => state.rep.afib.procedureDetails
  );
  return userDetails;
};

const useAfibProcedureDetails = () => {
  const { procedureDetails } = useSelector(
    (state: RootState) => state.rep.afib.procedureDetails
  );
  return procedureDetails;
};

const findAfibProcedureDiff = () => {
  const userDetails = useAfibProcedureUserDetails();
  const procedureDetails = useAfibProcedureDetails();

  const diff = JSON.stringify(userDetails) !== JSON.stringify(procedureDetails);

  const currentValues = {
    sheath_utilized_id: userDetails?.sheath_utilized?.selected?.id,
    act_sec: userDetails?.act_sec,
    pvi_lesioin_sets_id: userDetails?.pvi_lesioin_sets?.selected?.id,
    csu_pvi_id: userDetails?.pvi_lesioin_sets?.selected?.catheter_used[0]?.id,
    csu_pvi_other_complications:
      userDetails?.pvi_lesioin_sets?.selected?.catheter_used[0]
        ?.csu_pvi_other_complications,
    pvi_add_lesioin_sets_id: userDetails?.add_lesioin_sets?.selected?.id,
    complication_present: userDetails?.complication?.selected
      ?.complication_present
      ? true
      : false,

    complication_other: userDetails?.complication?.selected?.complication_other,

    complication_id: userDetails?.complication?.selected?.id,
    total_fluoro_time: userDetails?.fluroscopy?.total_fluoro_time,
    // total_fluoro: userDetails?.fluroscopy?.total_fluoro,
    contrast: userDetails?.fluroscopy?.contrast,
  };

  return {
    hasDiff: diff,
    currentValues: currentValues,
  };
};

export {
  useLoaderAndError,
  useAfibProcedureUserDetails,
  useAfibProcedureDetails,
  findAfibProcedureDiff,
};
