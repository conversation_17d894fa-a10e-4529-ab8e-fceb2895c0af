import React, { useEffect, useState } from "react";
import { View, Text, Modal, TouchableOpacity, StyleSheet } from "react-native";
import SearchablePicker from "../../../components/SearchablePicker";
import {
  fetchSiteList,
  fetchPatientsList,
} from "../../../store/rep/ScheduleStack/schedules/thunk";
import { useDispatch } from "react-redux";
import { AppDispatch } from "../../../store";
import {
  useSiteList,
  useSelectedDate,
  usePatientsList,
  useLoadersState,
} from "../hooks/schedulesHooks";
import Loader from "../../../components/Loader";
import { CalendarProvider, ExpandableCalendar } from "react-native-calendars";

interface PopupModalProps {
  modalVisible: boolean;
  setModalVisible: React.Dispatch<React.SetStateAction<boolean>>;
}

const PopupModal: React.FC<PopupModalProps> = ({
  modalVisible,
  setModalVisible,
}) => {
  const dispatch = useDispatch<AppDispatch>();
  const [selectedDate1, setSelectedDate1] = useState(
    new Date().toISOString().split("T")[0]
  );

  // State variables
  const [siteList, setSiteList] = useState<{ label: string; value: number }[]>(
    []
  );
  const [siteSelected, setSiteSelected] = useState<number>(0);
  const [patients, setPatients] = useState<{ label: string; value: number }[]>(
    []
  );
  const [selectedPatient, setSelectedPatient] = useState<number>(0);

  // Hooks
  const siteListData = useSiteList();
  const selectedDate = useSelectedDate();
  const patientsList = usePatientsList();
  const { patientLoader } = useLoadersState();

  // Helper functions
  const unMount = () => {
    setSiteList([]);
    setSiteSelected(0);
    setPatients([]);
    setSelectedPatient(0);
  };

  // Create site picker items from fetched data
  const createSitePickerItems = (labels: any[]) =>
    labels.map((label) => ({ label: label.name, value: label.id }));

  // Create patient picker items from fetched patient data
  const createPatientPickerItems = (patientsData: any[]) =>
    patientsData.map((patient) => ({
      label: patient.patient.name,
      value: patient.patient.id,
    }));

  // Fetch patient list when a site is selected
  const fetchPatientList = async () => {
    try {
      const response = await dispatch(
        fetchPatientsList({
          site_id: siteSelected,
          case_date: selectedDate,
        })
      );

      if (response.payload && Array.isArray(response.payload)) {
        const patientsData = createPatientPickerItems(response.payload);
        setPatients(patientsData);
      }
    } catch (error) {
      console.error("Error fetching patient list:", error);
    }
  };

  // Effects
  useEffect(() => {
    if (modalVisible) {
      dispatch(fetchSiteList());
    }

    return () => {
      unMount();
    };
  }, [dispatch, modalVisible]);

  useEffect(() => {
    if (siteListData && Array.isArray(siteListData)) {
      setSiteList(createSitePickerItems(siteListData));
    }
  }, [siteListData]);

  useEffect(() => {
    if (siteSelected) {
      fetchPatientList();
    }
  }, [siteSelected]);

  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={modalVisible}
      onRequestClose={() => setModalVisible(false)}
    >
      <View style={styles.overlay}>
        <View style={styles.modalContent}>
          {/* Title */}
          <Text style={styles.modalTitle}>Select Site and Patient</Text>

          {/* Site Selection */}
          <SearchablePicker
            placeholder={"Select site"}
            items={siteList}
            value={siteSelected}
            onValueChange={setSiteSelected}
          />

          {/* Patients */}
          {siteSelected > 0 && (
            <View style={{ marginTop: 20, width: "100%" }}>
              {patientLoader ? (
                <Loader />
              ) : (
                <SearchablePicker
                  placeholder={"Select patient"}
                  items={patients}
                  value={selectedPatient}
                  onValueChange={setSelectedPatient}
                />
              )}
            </View>
          )}

          {/* Close Button */}
          <TouchableOpacity
            style={styles.closeButton}
            onPress={() => setModalVisible(false)}
          >
            <Text style={styles.closeButtonText}>Close Modal</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0, 0, 0, 0.5)", // Dark overlay
  },
  modalContent: {
    width: "90%", // Full width
    backgroundColor: "white",
    padding: 20,
    borderRadius: 15,
    alignItems: "center",
    shadowColor: "#000",
    shadowOpacity: 0.3,
    shadowRadius: 10,
    elevation: 10,
  },
  calendar: {
    marginBottom: 20,
    borderRadius: 10,
    backgroundColor: "#f9f9f9", // Subtle background color for the calendar
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 15,
    color: "#333",
    alignSelf: "flex-start",
  },
  closeButton: {
    backgroundColor: "#8143d9",
    padding: 10,
    borderRadius: 10,
    marginTop: 20,
    width: "100%",
  },
  closeButtonText: {
    color: "white",
    fontSize: 16,
    textAlign: "center",
  },
});

export default PopupModal;
