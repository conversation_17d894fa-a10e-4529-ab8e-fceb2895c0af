import React, { useRef, useState } from "react";
import { View, TouchableOpacity, Alert, Text } from "react-native";
import Heading from "../../../components/Heading";
import ScreenWrapper from "../../../components/ScreenWrapper";
import PatientDemographicsCard from "../components/PatientDemographicsCard";
import {
  fetchPreopDetails,
  putPreopDetails,
} from "../../../store/rep/ScheduleStack/patientDetails/thunk";
import { useDispatch } from "react-redux";
import { AppDispatch } from "../../../store";
import { useFocusEffect } from "@react-navigation/native";
import { useSelectedPatient } from "../hooks/PatientsHooks";
import {
  usePreopDetails,
  useLoaderAndError,
  useUserDetails,
  formatPreOpDetails,
} from "../hooks/patientDetailsHooks";
import Loader from "../../../components/Loader";
import globalStyles from "../../../styles/GlobalStyles";
import CustomText from "../../../components/CustomText";
import { pick, types } from "react-native-document-picker";
import { uploadPDF } from "../../../store/rep/ScheduleStack/patientDetails/thunk";
import BottomSheetComponent, {
  BottomSheetRefProps,
} from "../../../components/BottomSheetComponent";
import CHADCard from "../../../components/CHADCard";
import HASBLEDCard from "../../../components/HASBLEDCard";
import {
  putChadScoreDetails,
  putHasbledScoreDetails,
} from "../../../store/rep/ScheduleStack/schedules/thunk";
import MedicationBottomSheetComponent, {
  MedicationBottomSheetHandle,
} from "../../components/MedicationBottomSheetComponent";
import { useLoadersState } from "../hooks/schedulesHooks";
import AddReferringProvider from "../components/AddReferringProvider";
import AddPcpProvider from "../components/AddPcpProvider";
import AddSite from "../components/AddSite";
import AddImplantingPhysician from "../components/AddImplantingPhysician";
import { resetSiteDetails } from "../../../store/rep/ScheduleStack/addSite";
import { resetImplantingPhysicianDetails } from "../../../store/rep/ScheduleStack/addPhysician";
import { fetchReferringProviders } from "../../../store/common/addReferringProvider/thunk";
import { resetProviderDetails as resetReferringProviderDetails } from "../../../store/common/addReferringProvider";
import { fetchPcpProviders } from "../../../store/common/addPcpProvider/thunk";
import { resetProviderDetails as resetPcpProviderDetails } from "../../../store/common/addPcpProvider";
import {
  useReferringProviderDetails,
  usePcpProviderDetails,
  formatProvider,
} from "../hooks/addPatientHooks.ts";
import { setPreopUserDetails } from "../../../store/rep/ScheduleStack/patientDetails";

interface IPreOpScreenProps {}

const PreOpScreen: React.FunctionComponent<IPreOpScreenProps> = (props) => {
  const dispatch = useDispatch<AppDispatch>();

  const bottomRef = useRef<BottomSheetRefProps>(null);
  const chadScoreRef = useRef<BottomSheetRefProps>(null);
  const medicationBottomSheetRef = useRef(null);
  const addReferringProviderRef = useRef<BottomSheetRefProps>(null);
  const addPcpProviderRef = useRef<BottomSheetRefProps>(null);
  const addSiteRef = useRef<BottomSheetRefProps>(null);
  const addImplantingPhysicianRef = useRef<BottomSheetRefProps>(null);
  const [medicationKey, setMedicationKey] = useState<number | null>(null);
  const [medicationSelectedId, setMedicationSelectedId] = useState<
    string | null
  >(null);
  const [addReferringProviderVisible, setAddReferringProviderVisible] =
    useState(false);
  const [addPcpProviderVisible, setAddPcpProviderVisible] = useState(false);
  const [addSiteVisible, setAddSiteVisible] = useState(false);
  const [addImplantingPhysicianVisible, setAddImplantingPhysicianVisible] =
    useState(false);

  // Store previous provider counts to detect new additions
  const [previousRefProviderCount, setPreviousRefProviderCount] = useState(0);
  const [previousPcpProviderCount, setPreviousPcpProviderCount] = useState(0);
  const [lastAddedReferringProvider, setLastAddedReferringProvider] =
    useState<string>();
  const [lastAddedPcpProvider, setLastAddedPcpProvider] = useState<string>();
  const [lastAddedSite, setLastAddedSite] = useState<string>();
  const [lastAddedImplantingPhysician, setLastAddedImplantingPhysician] =
    useState<string>();

  const [chad, setChad] = React.useState();
  const [hasbled, setHasbled] = React.useState();

  const [disableTruPlan, setDisableTruPlan] = React.useState(false);
  const userDetails = useUserDetails();
  const [modalType, setModalType] = React.useState<string>("");
  const { agendaLoader } = useLoadersState();
  const selectedPatient = useSelectedPatient();

  // Get provider data
  const refProviders = useReferringProviderDetails();
  const pcpProviders = usePcpProviderDetails();

  // Format providers for dropdowns
  const { providers: refProviderOptions } = formatProvider(refProviders);
  const { providers: pcpOptions } = formatProvider(pcpProviders);

  useFocusEffect(
    React.useCallback(() => {
      const fetchDetails = async () => {
        try {
          if (selectedPatient?.case_id) {
            await dispatch(
              fetchPreopDetails({ case_id: selectedPatient.case_id })
            );

            // Fetch providers
            await dispatch(fetchReferringProviders());
            await dispatch(fetchPcpProviders());
          } else {
            console.error("No selected patient found");
          }
        } catch (err) {
          console.error("Error in demographics API:", err);
        }
      };

      fetchDetails();
    }, [dispatch, selectedPatient])
  );

  // Get formatted details, ensuring we handle the case when userDetails is undefined
  const { cha2ds2_vasc, has_bled } = formatPreOpDetails(userDetails || {});
  const uploader = async () => {
    const PdfFile = await pick({
      allowMultiSelection: false,
      type: types.pdf,
      mode: "import",
      presentationStyle: "formSheet",
    });

    if (PdfFile) {
      const uploadResponse = await dispatch(
        uploadPDF({
          case_id: selectedPatient?.case_id,
          file: PdfFile,
        })
      );

      const response = await dispatch(
        fetchPreopDetails({ case_id: selectedPatient?.case_id })
      );
      setDisableTruPlan(true);
    } else {
      Alert.alert("Please Select PDF File");
    }
  };

  const { loading, error } = useLoaderAndError();

  const handleBottomSheetClose = (ref) => {
    ref.current?.close();
  };

  const handleClose = () => {
    if (medicationBottomSheetRef.current) {
      medicationBottomSheetRef.current.stateCleanUp();
    }
  };

  const handleMedOpen = (id?: string) => {
    setMedicationKey(Date.now());
    if (id) {
      setMedicationSelectedId(id);
    } else {
      setMedicationSelectedId(null);
    }
    bottomRef.current?.open();
  };

  const handleMedClose = () => {
    bottomRef.current?.close();
  };

  const handleOpenReferringProviderSheet = () => {
    // Reset provider details first to ensure we start with a clean form
    dispatch(resetReferringProviderDetails());
    setAddReferringProviderVisible(true);
    addReferringProviderRef.current?.open();
  };

  const handleCloseReferringProviderSheet = () => {
    // Reset provider details when closing to clean up
    dispatch(resetReferringProviderDetails());
    setAddReferringProviderVisible(false);
    addReferringProviderRef.current?.close();
  };

  const handleAddReferringProviderSuccess = async (newProviderId: any) => {
    if (newProviderId) {
      try {
        // Set the last added referring provider for passing to the child component
        setLastAddedReferringProvider(newProviderId);

        // Close the sheet immediately
        handleCloseReferringProviderSheet();

        // FIRST: Fetch updated preop details to get latest provider options
        await dispatch(
          fetchPreopDetails({
            case_id: selectedPatient?.case_id,
          })
        );

        // THEN: Set the new provider ID in Redux state (this will trigger AutoSave)
        dispatch(
          setPreopUserDetails({
            ...userDetails,
            patient: {
              ...userDetails?.patient,
              referring_provider: {
                ...userDetails?.patient?.referring_provider,
                selected: {
                  ...userDetails?.patient?.referring_provider?.selected,
                  id: newProviderId,
                },
              },
            },
          })
        );

        // Also fetch updated provider list for future use
        dispatch(fetchReferringProviders());
      } catch (error) {
        console.error("Error updating referring provider:", error);
        handleCloseReferringProviderSheet();
      }
    } else {
      handleCloseReferringProviderSheet();
    }
  };

  const handleOpenPcpProviderSheet = () => {
    // Reset provider details first to ensure we start with a clean form
    dispatch(resetPcpProviderDetails());
    setAddPcpProviderVisible(true);
    addPcpProviderRef.current?.open();
  };

  const handleClosePcpProviderSheet = () => {
    // Reset provider details when closing to clean up
    dispatch(resetPcpProviderDetails());
    setAddPcpProviderVisible(false);
    addPcpProviderRef.current?.close();
  };

  const handleAddPcpProviderSuccess = async (newProviderId: any) => {
    if (newProviderId) {
      try {
        // Set the last added PCP provider for passing to the child component
        setLastAddedPcpProvider(newProviderId);

        // Close the sheet immediately
        handleClosePcpProviderSheet();

        // FIRST: Fetch updated preop details to get latest provider options
        await dispatch(
          fetchPreopDetails({
            case_id: selectedPatient?.case_id,
          })
        );

        // THEN: Set the new PCP provider ID in Redux state (this will trigger AutoSave)
        dispatch(
          setPreopUserDetails({
            patient: {
              ...userDetails?.patient,
              pcp: {
                ...userDetails?.patient?.pcp,
                selected: {
                  ...userDetails?.patient?.pcp?.selected,
                  id: newProviderId,
                },
              },
            },
          })
        );

        // Optionally fetch updated PCP list (if such an action exists)
        dispatch(fetchPcpProviders?.());
      } catch (error) {
        console.error("Error updating PCP provider:", error);
        handleClosePcpProviderSheet();
      }
    } else {
      handleClosePcpProviderSheet();
    }
  };

  // Site addition handlers
  const handleAddSiteSuccess = async (siteId: any) => {
    if (siteId) {
      try {
        // Set the last added site for passing to the child component
        setLastAddedSite(siteId);

        // Close the sheet immediately
        handleCloseSiteSheet();
      } catch (error) {
        console.error("Error updating site:", error);
        handleCloseSiteSheet();
      }
    } else {
      handleCloseSiteSheet();
    }
  };

  // Site addition handlers
  const handleOpenSiteSheet = () => {
    // Reset form data before opening
    dispatch(resetSiteDetails());
    setAddSiteVisible(true);
    addSiteRef.current?.open();
  };

  const handleCloseSiteSheet = () => {
    setAddSiteVisible(false);
    addSiteRef.current?.close();
  };

  // Implanting physician addition handlers
  const handleOpenImplantingPhysicianSheet = () => {
    // Reset form data before opening
    dispatch(resetImplantingPhysicianDetails());
    setAddImplantingPhysicianVisible(true);
    addImplantingPhysicianRef.current?.open();
  };

  const handleCloseImplantingPhysicianSheet = () => {
    setAddImplantingPhysicianVisible(false);
    addImplantingPhysicianRef.current?.close();
  };

  const handleAddImplantingPhysicianSuccess = async (physicianId: any) => {
    if (physicianId) {
      try {
        // Set the last added implanting physician for passing to the child component
        setLastAddedImplantingPhysician(physicianId);

        // Close the sheet immediately
        handleCloseImplantingPhysicianSheet();
      } catch (error) {
        console.error("Error updating implanting physician:", error);
        handleCloseImplantingPhysicianSheet();
      }
    } else {
      handleCloseImplantingPhysicianSheet();
    }
  };

  const handleChadPress = (calculation: any) => {
    if (calculation) {
      setChad(calculation);
      setModalType("CHAD");
      chadScoreRef.current?.open();
    } else {
      Alert.alert(
        "Data Not Available",
        "CHA₂DS₂-VASc score data is not available yet. Please try again after the data is loaded."
      );
    }
  };

  const handleHasbledPress = (calculation: any) => {
    // Only proceed if calculation is defined
    if (calculation) {
      setHasbled(calculation);
      setModalType("HASBLED");
      chadScoreRef.current?.open();
    } else {
      // Show an alert if the calculation is undefined
      Alert.alert(
        "Data Not Available",
        "HAS-BLED score data is not available yet. Please try again after the data is loaded."
      );
    }
  };

  const saveChadScoreDetails = async (updatedValues: any) => {
    handleBottomSheetClose(chadScoreRef);
    try {
      const res = await dispatch(
        putChadScoreDetails({
          case_id: selectedPatient?.case_id,
          payload: updatedValues,
        })
      );
      if (res) {
        const res = await dispatch(
          fetchPreopDetails({ case_id: selectedPatient.case_id })
        );
      }
    } catch (error) {
      console.error("Error updating CHAD Score:", error);
    }
  };

  const saveHasbledDetails = async (updatedValues: any) => {
    handleBottomSheetClose(chadScoreRef);
    try {
      const res = await dispatch(
        putHasbledScoreDetails({
          case_id: selectedPatient?.case_id,
          payload: updatedValues,
        })
      );
      if (res) {
        const res = await dispatch(
          fetchPreopDetails({ case_id: selectedPatient.case_id })
        );
      }
    } catch (error) {
      console.error("Error updating CHAD Score:", error);
    }
  };

  // Function to auto-select the last added provider
  const autoSelectNewProvider = (
    providerType: "referring_provider" | "pcp_provider",
    currentProviders: any[]
  ) => {
    if (currentProviders && currentProviders.length > 0) {
      // Get the last provider (newest one)
      const lastProvider = currentProviders[currentProviders.length - 1];

      if (providerType === "referring_provider") {
        dispatch(
          setPreopUserDetails({
            patient: {
              ...userDetails?.patient,
              referring_provider: {
                ...userDetails?.patient?.referring_provider,
                selected: {
                  ...userDetails?.patient?.referring_provider?.selected,
                  id: lastProvider.value,
                },
              },
            },
          })
        );
      } else if (providerType === "pcp_provider") {
        dispatch(
          setPreopUserDetails({
            patient: {
              ...userDetails?.patient,
              pcp: {
                ...userDetails?.patient?.pcp,
                selected: {
                  ...userDetails?.patient?.pcp?.selected,
                  id: lastProvider.value,
                },
              },
            },
          })
        );
      }
    }
  };

  // Effect to detect new referring provider additions and auto-select
  React.useEffect(() => {
    if (refProviderOptions && refProviderOptions.length > 0) {
      // If this is the first load, just set the count
      if (previousRefProviderCount === 0) {
        setPreviousRefProviderCount(refProviderOptions.length);
      }
      // If count increased, a new provider was added
      else if (refProviderOptions.length > previousRefProviderCount) {
        autoSelectNewProvider("referring_provider", refProviderOptions);
        setPreviousRefProviderCount(refProviderOptions.length);
      }
    }
  }, [refProviderOptions, previousRefProviderCount]);

  // Effect to detect new PCP provider additions and auto-select
  React.useEffect(() => {
    if (pcpOptions && pcpOptions.length > 0) {
      // If this is the first load, just set the count
      if (previousPcpProviderCount === 0) {
        setPreviousPcpProviderCount(pcpOptions.length);
      }
      // If count increased, a new provider was added
      else if (pcpOptions.length > previousPcpProviderCount) {
        autoSelectNewProvider("pcp_provider", pcpOptions);
        setPreviousPcpProviderCount(pcpOptions.length);
      }
    }
  }, [pcpOptions, previousPcpProviderCount]);

  const handleRefresh = async () => {
    if (!selectedPatient?.case_id) return;
    await dispatch(fetchPreopDetails({ case_id: selectedPatient.case_id }));
  };

  // Show loader if we're loading data or if userDetails isn't available yet
  if (loading || !userDetails) {
    return <Loader />;
  }

  return (
    <>
      <ScreenWrapper direction="column" onRefresh={handleRefresh}>
        {agendaLoader ? (
          <View className="mt-64 flex-1 justify-center items-center">
            <Loader />
          </View>
        ) : (
          <View>
            <View className="bg-primaryBg gap-y-4">
              <PatientDemographicsCard
                onMedOpen={handleMedOpen}
                onMedClose={handleMedClose}
                onOpenReferringProviderSheet={handleOpenReferringProviderSheet}
                onOpenPcpProviderSheet={handleOpenPcpProviderSheet}
                onOpenSiteSheet={handleOpenSiteSheet}
                onOpenImplantingPhysicianSheet={
                  handleOpenImplantingPhysicianSheet
                }
                truplanButton={{
                  onPress: uploader,
                }}
                chadPress={() => handleChadPress(cha2ds2_vasc)}
                hasBledPress={() => handleHasbledPress(has_bled)}
                onReferringProviderAdded={(provider) => {
                  handleAddReferringProviderSuccess(provider);
                }}
                onPcpProviderAdded={(provider) => {
                  // This will be called when a new PCP provider is added
                  handleAddPcpProviderSuccess(provider);
                }}
                onSiteAdded={(site) => {
                  // This will be called when a new site is added
                  handleAddSiteSuccess(site);
                }}
                onImplantingPhysicianAdded={(physician) => {
                  // This will be called when a new implanting physician is added
                  handleAddImplantingPhysicianSuccess(physician);
                }}
                lastAddedReferringProvider={lastAddedReferringProvider}
                lastAddedPcpProvider={lastAddedPcpProvider}
                lastAddedSite={lastAddedSite}
                lastAddedImplantingPhysician={lastAddedImplantingPhysician}
              />
            </View>
          </View>
        )}
        <View className="mt-9"></View>
      </ScreenWrapper>

      <BottomSheetComponent
        ref={chadScoreRef}
        snapPoints={["50%", "65%", "80%"]}
        backgroundColor="white"
        onClose={handleBottomSheetClose(chadScoreRef)}
      >
        {modalType === "CHAD" ? (
          <CHADCard chad={chad} onSave={saveChadScoreDetails} />
        ) : (
          <HASBLEDCard hasbled={hasbled} onSave={saveHasbledDetails} />
        )}
      </BottomSheetComponent>
      <BottomSheetComponent
        ref={bottomRef}
        snapPoints={["75%", "85%"]}
        backgroundColor="white"
        onClose={() => handleClose()}
      >
        {
          <View className="flex-1">
            <MedicationBottomSheetComponent
              ref={medicationBottomSheetRef}
              medicineId={medicationSelectedId || ""}
              bottomSheetClose={handleMedClose}
              medKey={medicationKey}
              isPreOp={true}
            />
          </View>
        }
      </BottomSheetComponent>

      {/* Add Referring Provider Bottom Sheet */}
      <BottomSheetComponent
        ref={addReferringProviderRef}
        snapPoints={["90%", "75%"]}
        backgroundColor="white"
        onClose={handleCloseReferringProviderSheet}
      >
        {addReferringProviderVisible && (
          <>
            <AddReferringProvider
              onSuccess={handleAddReferringProviderSuccess}
              bottomSheetRef={addReferringProviderRef}
            />
          </>
        )}
      </BottomSheetComponent>

      {/* Add PCP Provider Bottom Sheet */}
      <BottomSheetComponent
        ref={addPcpProviderRef}
        snapPoints={["90%", "75%"]}
        backgroundColor="white"
        onClose={handleClosePcpProviderSheet}
      >
        {addPcpProviderVisible && (
          <>
            <AddPcpProvider
              onSuccess={handleAddPcpProviderSuccess}
              bottomSheetRef={addPcpProviderRef}
            />
          </>
        )}
      </BottomSheetComponent>

      {/* Add Site Bottom Sheet */}
      <BottomSheetComponent
        ref={addSiteRef}
        snapPoints={["90%", "75%"]}
        backgroundColor="white"
        onClose={handleCloseSiteSheet}
      >
        {addSiteVisible && (
          <>
            <AddSite
              onSuccess={handleAddSiteSuccess}
              onCancel={handleCloseSiteSheet}
              bottomSheetRef={addSiteRef}
            />
          </>
        )}
      </BottomSheetComponent>

      {/* Add Implanting Physician Bottom Sheet */}
      <BottomSheetComponent
        ref={addImplantingPhysicianRef}
        snapPoints={["90%", "75%"]}
        backgroundColor="white"
        onClose={handleCloseImplantingPhysicianSheet}
      >
        {addImplantingPhysicianVisible && (
          <>
            <AddImplantingPhysician
              onSuccess={handleAddImplantingPhysicianSuccess}
              onCancel={handleCloseImplantingPhysicianSheet}
              bottomSheetRef={addImplantingPhysicianRef}
              currentSiteId={userDetails?.site?.selected?.id}
            />
          </>
        )}
      </BottomSheetComponent>
    </>
  );
};

export default PreOpScreen;
