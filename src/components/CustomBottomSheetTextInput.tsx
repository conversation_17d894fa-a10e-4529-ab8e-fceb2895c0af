import React, { useState } from "react";
import {
  TouchableOpacity,
  View,
  StyleSheet,
  TextStyle,
  ViewStyle,
} from "react-native";
import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons";
import { BottomSheetTextInput } from "@gorhom/bottom-sheet";

interface CustomBottomSheetTextInputProps {
  inputValue: string;
  onInputChange: (text: string) => void;
  placeholder?: string;
  width?: string | number;
  height?: number;
  keyboardType?: "default" | "email-address" | "numeric" | "phone-pad";
  secureTextEntry?: boolean;
  disable?: boolean;
  containerStyle?: ViewStyle;
  inputStyle?: TextStyle;
}

const CustomBottomSheetTextInput: React.FC<CustomBottomSheetTextInputProps> = ({
  inputValue,
  onInputChange,
  placeholder,
  width = "100%",
  height = 45,
  keyboardType = "default",
  secureTextEntry = false,
  disable = true,
  containerStyle,
  inputStyle,
}) => {
  const [isPasswordVisible, setPasswordVisible] = useState(!secureTextEntry);

  const togglePasswordVisibility = () => {
    setPasswordVisible(!isPasswordVisible);
  };

  return (
    <View style={[styles.container, { width }, containerStyle]}>
      <BottomSheetTextInput
        placeholder={placeholder}
        placeholderTextColor="#999999"
        onChangeText={onInputChange}
        value={inputValue}
        style={[
          styles.input,
          { height },
          secureTextEntry && { paddingRight: 40 },
          inputStyle,
        ]}
        keyboardType={keyboardType}
        secureTextEntry={!isPasswordVisible && secureTextEntry}
        editable={disable}
      />

      {secureTextEntry && (
        <TouchableOpacity
          onPress={togglePasswordVisibility}
          style={styles.iconContainer}
        >
          <MaterialCommunityIcons
            name={isPasswordVisible ? "eye-off-outline" : "eye-outline"}
            size={24}
            color="#8143d9"
          />
        </TouchableOpacity>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: "relative",
    backgroundColor: "white",
  },
  input: {
    padding: 5,
    borderRadius: 4,
    borderWidth: 1,
    borderColor: "#8143d9",
    color: "#000000",
  },
  iconContainer: {
    position: "absolute",
    right: 10,
    top: "50%",
    transform: [{ translateY: -12 }],
  },
});

export default CustomBottomSheetTextInput;
