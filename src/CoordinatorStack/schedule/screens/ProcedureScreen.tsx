import React, { useState, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { View, Text, TouchableOpacity } from "react-native";
import CustomCard from "../../../components/CustomCard";
import SearchablePicker from "../../../components/SearchablePicker";
import DatePicker from "react-native-date-picker";
import moment from "moment";
import CustomInput from "../../../components/CustomTextInput";
import ScreenWrapper from "../../../components/ScreenWrapper";
import SaveActionButton from "../../../components/SaveActionButton";
import LineSeperator from "../../../components/LineSeperator";
import Loader from "../../../components/Loader";
import Error from "../../../components/Error";
import { useFocusEffect } from "@react-navigation/native";
import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons";
import { useNavigation } from "@react-navigation/native";
import { AppDispatch } from "../../../store";
import {
  fetchProcedureDetails,
  putProcedureDetails,
} from "../../../store/coordinator/ScheduleStack/procedure/thunk";
import {
  useLoaderAndError,
  useProcedureDetailsUserDetails,
  formatProcedureDetails,
  useProcedureDetails,
  findProcedureDetailsDiff,
} from "../hooks/procedureDetailsHooks";
import { parseIntInput } from "../../../utils";
import { setProcedureDetailsUserDetails } from "../../../store/coordinator/ScheduleStack/procedure";
import { useCoordinatorSelectedPatient } from "../../schedule/hooks/schedulesHooks";
import CustomText from "../../../components/CustomText";
import Heading from "../../../components/Heading";
import ToggleButton from "../../../components/ToggleButton";
import SaveActionV2 from "../../../components/SaveActionButtonV2";
import {
  formatImplantingPhysician,
  useImplantingPhysicianDetails,
} from "../../../utils.tsx";
import { fetchImplantingPhysicians } from "../../../store/coordinator/ScheduleStack/addpatient/thunk.ts";
import PopupModal from "../../../components/Popup";
import OptionSelector from "../../../components/OptionSelector.tsx";
import { simpleAutoSave } from "../../../services/simpleAutoSave.ts";
import {
  setSaveStatus,
  setLastSavedData,
} from "../../../store/services/index.ts";

interface IProcedureScreenProps {
  heading: string;
}

const ProcedureScreen: React.FunctionComponent<IProcedureScreenProps> = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { loader, error } = useLoaderAndError();
  const complication_other_selected_id = "676916a32e31f611af713ee8";
  const selectedPatient = useCoordinatorSelectedPatient();
  const useImplantingPhysicianDetail = useImplantingPhysicianDetails();
  const { implantingPhysicians } = formatImplantingPhysician(
    useImplantingPhysicianDetail
  );
  const userDetails = useProcedureDetailsUserDetails();
  const procedureDetails = useProcedureDetails();
  // Create a ref to track if a save is in progress
  const saveInProgressRef = React.useRef(false);

  // Create a ref to track the last saved data hash
  const lastSavedDataHashRef = React.useRef("");
  const [modalVisible, setModalVisible] = React.useState(false);
  const navigation = useNavigation();

  const [popupMsg, setPopupMsg] = React.useState<string[]>([]);

  const {
    procedure_date,
    implanting_physician,
    implanting_physician_name,
    selectDeviceValue,
    deviceType,
    selectedDevice,
    anesthesiaType,
    anesthesiaTypeSelected,
    residualLeak,
    residualLeakValue,
    ablationCase,
    complications_toggler,
    complication_selected_id,
    complication_other,
    complications_type,
    complication_selected_name,
  } = formatProcedureDetails(userDetails);

  const diff = findProcedureDetailsDiff();

  const [open, setOpen] = useState(false);

  const filteredOptions =
    deviceType?.find((item: any) => item.value === selectedDevice)?.option ||
    [];

  // var temp = false;

  // if (complications_toggler === "Yes" && !complication_selected_id) {
  //   setPopupMsg((prev) => [...prev, "Please Select Complications"]);
  //   temp = true;
  // }

  // if (
  //   complications_toggler === "Yes" &&
  //   complication_selected_id === complication_other_selected_id &&
  //   !complication_other
  // ) {
  //   setPopupMsg((prev) => [...prev, "Please Enter Other Complications"]);
  //   temp = true;
  // }

  useFocusEffect(
    React.useCallback(() => {
      const fetchDetails = async () => {
        if (selectedPatient?.case_id) {
          await dispatch(fetchImplantingPhysicians());

          const res = await dispatch(
            fetchProcedureDetails({
              case_id: selectedPatient?.case_id,
            })
          );
        }
      };
      fetchDetails();
    }, [dispatch, selectedPatient?.case_id])
  );

  const validator = () => {
    // Clear previous popup messages
    setPopupMsg([]);

    let hasError = false;

    // Check anesthesia type selection
    if (
      anesthesiaTypeSelected === null ||
      anesthesiaTypeSelected === undefined ||
      anesthesiaTypeSelected === ""
    ) {
      setPopupMsg((prev) => [...prev, "Please Select Anesthesia Type"]);
      hasError = true;
    }

    // Check residual leak when applicable
    if (
      residualLeak === "Yes" &&
      (residualLeakValue === null ||
        residualLeakValue === undefined ||
        residualLeakValue?.toString() === "")
    ) {
      setPopupMsg((prev) => [...prev, "Please Enter Residual Leak Value"]);
      hasError = true;
    }

    // Check complications if toggled yes
    if (
      complications_toggler === "Yes" &&
      (complication_selected_id === null ||
        complication_selected_id === undefined ||
        complication_selected_id === "")
    ) {
      setPopupMsg((prev) => [...prev, "Please Enter Complications"]);
      hasError = true;
    }

    // Check device type and size: if a device is selected, its size must be provided.
    // References:
    //   - device type: selectedDevice
    //   - device size: userDetails?.device?.selected?.device_size
    if (selectedDevice && selectedDevice !== "") {
      const deviceSize = userDetails?.device?.selected?.device_size;
      if (
        deviceSize === null ||
        deviceSize === undefined ||
        deviceSize.toString().trim() === ""
      ) {
        setPopupMsg((prev) => [...prev, "Please Select Device Size"]);
        hasError = true;
      }
    }

    // If any error exists, display the modal with messages
    if (hasError) {
      setModalVisible(true);
      return false;
    } else {
      return true;
    }
  };

  const saveDetails = async (): Promise<void> => {
    try {
      const isValid = validator();

      if (!isValid) {
        dispatch(
          setSaveStatus({
            screenId: "procedureDetails", // adjust if needed
            status: "validation_failed",
          })
        );
        setLocalSaveStatus("validation_failed");
        throw new Error("Validation failed");
      }

      if (saveInProgressRef.current) {
        return;
      }

      const dataCopy = { ...diff.currentValues };
      const currentDataHash = JSON.stringify(dataCopy);

      if (currentDataHash === lastSavedDataHashRef.current) {
        return;
      }

      saveInProgressRef.current = true;

      const res = await dispatch(
        putProcedureDetails({
          case_id: selectedPatient?.case_id,
          payload: dataCopy,
        })
      );

      if (res.payload && selectedPatient?.case_id) {
        const refetchRes = await dispatch(
          fetchProcedureDetails({
            case_id: selectedPatient.case_id,
          })
        );

        if (refetchRes.payload) {
          dispatch(setProcedureDetailsUserDetails(refetchRes.payload)); // if applicable
          lastSavedDataHashRef.current = currentDataHash;
        }
      }

      saveInProgressRef.current = false;
    } catch (err) {
      console.error("Save error:", err);
      saveInProgressRef.current = false;
      throw err;
    }
  };

  const saveStatus = useSelector(
    (state: any) => state.common?.autoSave?.["procedureDetails"]?.status
  );

  const [localSaveStatus, setLocalSaveStatus] = React.useState<string>("");

  const effectiveSaveStatus = saveStatus || localSaveStatus;

  // Track if user has made changes to prevent auto-navigation on initial load
  const [hasUserMadeChanges, setHasUserMadeChanges] = React.useState(false);

  // Track when user makes changes to any field
  React.useEffect(() => {
    if (diff.hasDiff && !hasUserMadeChanges) {
      setHasUserMadeChanges(true);
    }
  }, [diff.hasDiff, hasUserMadeChanges]);

  // React.useEffect(() => {
  //   if (effectiveSaveStatus === "saved" && hasUserMadeChanges) {
  //     // Add a small delay to ensure the user sees the "saved" message briefly
  //     const timer = setTimeout(() => {
  //       navigation.navigate("Post Op");
  //     }, 1000);

  //     return () => clearTimeout(timer);
  //   }
  // }, [effectiveSaveStatus, navigation, hasUserMadeChanges]);
  /**
   * Derive the save message and styling based on the save status
   */
  const { saveMessage, messageStyle, iconName, iconColor } =
    React.useMemo(() => {
      let message = "";
      let style = "text-primaryPurple italic";
      let icon = "alert-circle-outline";
      let color = "#8143d9";

      switch (effectiveSaveStatus) {
        case "changes_detected":
          message = "Changes detected, saving soon...";
          style = "text-primaryBlack italic";
          icon = "alert-circle-outline";
          color = "#f5c542"; // yellow
          break;
        case "saving":
          message = "Saving... Please don't close the app";
          style = "text-primaryPurple font-bold italic";
          icon = "alert-circle-outline";
          color = "#8143d9"; // purple
          break;
        case "saved":
          message = "All changes saved";
          style = "text-green-3 italic";
          icon = "check-circle-outline";
          color = "#28a745"; // green
          break;
        case "validation_failed":
          message = "Validation failed. Please check your inputs.";
          style = "text-red-3 font-bold italic";
          icon = "alert-circle-outline";
          color = "#d93025"; // red
          break;
        case "error":
          message = "Error saving changes. Please try again.";
          style = "text-red-3 font-bold italic";
          icon = "alert-circle-outline";
          color = "#d93025"; // red
          break;
      }

      return {
        saveMessage: message,
        messageStyle: style,
        iconName: icon,
        iconColor: color,
      };
    }, [effectiveSaveStatus]);

  /**
   * Ref to track previous diff values to prevent unnecessary saves
   */
  const prevDiffRef = React.useRef<string>("");

  /**
   * Ref to track if initial data has been loaded
   * This prevents autosave from triggering before data is fetched
   */
  const initialDataLoadedRef = React.useRef<boolean>(false);

  /**
   * Effect to set initialDataLoaded when data is fetched
   */
  React.useEffect(() => {
    // Check if we have valid data
    const hasValidData = userDetails?.case_id !== undefined;

    if (hasValidData && !initialDataLoadedRef.current) {
      initialDataLoadedRef.current = true;
    }
  }, [userDetails]);

  /**
   * Effect to trigger autosave when data changes
   * This uses our new simpleAutoSave service with local state fallback
   */
  React.useEffect(() => {
    // Only proceed if initial data has been loaded and there are actual differences
    if (!initialDataLoadedRef.current) {
      return;
    }

    // Check if case_id exists to prevent API calls with undefined ID
    if (!userDetails?.case_id) {
      return;
    }

    // Only proceed if there are actual differences
    if (diff.hasDiff) {
      // Check if we have any undefined values in critical fields
      const hasUndefinedValues = Object.values(diff.currentValues).every(
        (val) => val === undefined
      );
      if (hasUndefinedValues) {
        return;
      }

      // Convert current values to string for comparison
      const currentDiffString = JSON.stringify(diff.currentValues);

      // Only trigger save if the values have actually changed
      if (currentDiffString !== prevDiffRef.current) {
        // Update the ref with current values
        prevDiffRef.current = currentDiffString;

        // Create a custom wrapper for setSaveStatus that updates both Redux and local state
        const setStatusWithFallback = (payload: {
          screenId: string;
          status: string;
        }) => {
          // Update local state as a fallback
          setLocalSaveStatus(payload.status);
          // Also dispatch to Redux
          return dispatch(setSaveStatus(payload));
        };

        // Trigger the simple autosave with the current values
        simpleAutoSave(
          "procedureDetails", // Screen ID
          diff.currentValues, // Data to save
          saveDetails, // Save function
          4000, // 4 second delay before saving
          dispatch, // Redux dispatch
          setStatusWithFallback, // Custom action to set save status with fallback
          setLastSavedData // Action to set last saved data
        );
      } else {
        console.log("No actual changes in diff values, skipping autosave");
      }
    }
  }, [
    diff.hasDiff,
    diff.currentValues,
    dispatch,
    setLocalSaveStatus,
    userDetails,
  ]);

  const handleCancel = async () => {
    dispatch(
      setProcedureDetailsUserDetails({
        ...userDetails,
        ...procedureDetails,
      })
    );
  };

  const handleRefresh = async () => {
    if (!selectedPatient?.case_id) return;
    await dispatch(fetchImplantingPhysicians());
    await dispatch(
      fetchProcedureDetails({
        case_id: selectedPatient?.case_id,
      })
    );
  };

  if (loader) {
    return <Loader />;
  }
  if (error) {
    return <Error />;
  }

  return (
    <ScreenWrapper direction="column" onRefresh={handleRefresh}>
      <CustomCard>
        {saveMessage && (
          <View
            style={{
              flexDirection: "row",
              justifyContent: "flex-end",
              alignItems: "center",
              marginVertical: 4,
            }}
          >
            <MaterialCommunityIcons
              name={iconName}
              color={iconColor}
              size={20}
            />
            <Text
              className={`text-sm ${messageStyle}`}
              style={{ marginLeft: 6 }}
            >
              {saveMessage}
            </Text>
          </View>
        )}
        <View className="bg-primaryBg p-4 rounded-md m-2">
          <View className="flex-row gap-4 mb-2">
            <CustomText value={"Date:"} className="font-semibold " />
            <CustomText
              value={
                procedure_date
                  ? moment(procedure_date).format("MM-DD-YYYY")
                  : "Date Not Selected"
              }
            />
          </View>
          <View className="flex-row gap-4">
            <CustomText value={"Implanting MD:"} className="font-semibold " />
            <CustomText
              value={
                implanting_physician_name
                  ? implanting_physician_name
                  : "Physician Not Selected"
              }
            />
          </View>
        </View>

        <LineSeperator extraStyle="my-4" />

        <View className="rounded-md px-2">
          {/* Date Picker */}

          <View>
            <Heading
              text="Device"
              size="sub-heading"
              showSeperator={false}
              extraStyle="mb-2"
            />

            {/* Device Size */}
            <View className="bg-primaryBg rounded-md p-3  flex-row items-center gap-2">
              <View className=" flex-1">
                <SearchablePicker
                  items={deviceType}
                  placeholder="Select"
                  value={selectedDevice}
                  onValueChange={(option) =>
                    dispatch(
                      setProcedureDetailsUserDetails({
                        device: {
                          ...userDetails.device,
                          selected: {
                            id: option.value,
                            name: option.label,
                          },
                        },
                      })
                    )
                  }
                  floatingLabel
                />
              </View>
              <View className="flex-1">
                {filteredOptions && (
                  <SearchablePicker
                    items={filteredOptions}
                    placeholder="Select"
                    value={userDetails?.device?.selected?.device_size?.toString()}
                    onValueChange={(option) =>
                      dispatch(
                        setProcedureDetailsUserDetails({
                          device: {
                            ...userDetails.device,
                            selected: {
                              ...userDetails.device?.selected,
                              device_size: option.value,
                            },
                          },
                        })
                      )
                    }
                    floatingLabel
                  />
                )}
              </View>
            </View>
          </View>

          <LineSeperator extraStyle="mt-4 mb-2" />

          {/* Type of Anesthesia */}
          <View>
            <Heading
              text="Type of Anesthesia"
              size="sub-heading"
              showSeperator={false}
            />
          </View>
          <View className="p-3 rounded-md bg-primaryBg mt-2.5">
            <OptionSelector
              options={anesthesiaType || []}
              selected={anesthesiaTypeSelected}
              onSelect={(option) =>
                dispatch(
                  setProcedureDetailsUserDetails({
                    anesthesia: {
                      ...userDetails.anesthesia,
                      selected: {
                        id: option.value,
                        name: option.label,
                      },
                    },
                  })
                )
              }
            />
            {/* <SearchablePicker
              items={anesthesiaType || []}
              placeholder="Select Anesthesia"
              value={anesthesiaTypeSelected}
              onValueChange={(option) =>
                dispatch(
                  setProcedureDetailsUserDetails({
                    anesthesia: {
                      ...userDetails.anesthesia,
                      selected: {
                        id: option.value,
                        name: option.label,
                      },
                    },
                  })
                )
              }
            /> */}
          </View>

          <LineSeperator extraStyle="my-4" />

          {/* Concomitant Ablation Case */}
          <View className="flex-row items-center justify-between">
            <Heading
              text="Concomitant Ablation Case"
              size="sub-heading"
              showSeperator={false}
            />
            <ToggleButton
              messages={["Yes", "No"]}
              selected={ablationCase}
              invertColor
              setSelected={(value) => {
                dispatch(
                  setProcedureDetailsUserDetails({
                    afib_ablation: value === "Yes" ? true : false,
                  })
                );
              }}
              width="w-[55px]"
            />
          </View>
          <LineSeperator extraStyle="my-4" />

          {/* Residual Peri-Device Leak */}
          <View className="">
            <View className="flex-row items-center justify-between ">
              <Heading
                text="Residual Peri-Device Leak"
                size="sub-heading"
                showSeperator={false}
              />

              <ToggleButton
                messages={["Yes", "No"]}
                selected={residualLeak}
                invertColor
                setSelected={(value) => {
                  dispatch(
                    setProcedureDetailsUserDetails({
                      leak: value === "Yes" ? true : false,
                      leak_value: value === "No" ? null : residualLeakValue,
                    })
                  );
                }}
                width="w-[55px]"
              />
            </View>
            {residualLeak === "Yes" && (
              <View className="mt-4">
                <CustomInput
                  inputValue={residualLeakValue?.toString()}
                  error={!residualLeakValue}
                  onInputChange={(value) =>
                    parseIntInput(
                      value,
                      (updatedValue) => {
                        dispatch(
                          setProcedureDetailsUserDetails({
                            leak_value: updatedValue,
                          })
                        );
                      },
                      0,
                      10
                    )
                  }
                  placeholder="Residual Peri-Device Leak"
                  keyboardType="numeric"
                />
              </View>
            )}
          </View>

          <LineSeperator extraStyle="mt-4 mb-3" />

          <View className="flex-row justify-between items-center ">
            <Heading
              text="Complications"
              color="black"
              size="sub-heading"
              showSeperator={false}
            />
            <ToggleButton
              messages={["Yes", "No"]}
              invertColor
              selected={complications_toggler}
              setSelected={(value) =>
                dispatch(
                  setProcedureDetailsUserDetails({
                    complication: {
                      ...userDetails.complication,
                      selected: {
                        ...userDetails.complication.selected,
                        complication_present: value === "Yes" ? true : false,
                      },
                    },
                  })
                )
              }
              width="w-[55px]"
            />
          </View>

          {complications_toggler === "Yes" && (
            <>
              <View className="mt-3">
                <SearchablePicker
                  items={complications_type}
                  placeholder="Complications"
                  value={complication_selected_id}
                  onValueChange={(option) =>
                    dispatch(
                      setProcedureDetailsUserDetails({
                        complication: {
                          ...userDetails.complication,
                          selected: {
                            ...userDetails.complication.selected,
                            id: option.value,
                            name: option.label,
                          },
                        },
                      })
                    )
                  }
                />
              </View>

              {complication_selected_name?.toLowerCase() === "other" && (
                <View className="mt-3">
                  <CustomInput
                    inputValue={complication_other}
                    error={!complication_other}
                    onInputChange={(val: string) => {
                      dispatch(
                        setProcedureDetailsUserDetails({
                          complication: {
                            ...userDetails.complication,
                            selected: {
                              ...userDetails.complication.selected,
                              complication_other: val,
                            },
                          },
                        })
                      );
                    }}
                    placeholder="Other"
                  />
                </View>
              )}
            </>
          )}

          <LineSeperator extraStyle="mt-4 mb-3" />

          <View className="flex-row justify-center gap-4">
            {/* <TouchableOpacity
              onPress={saveDetails}
              className={`border 
                        ${
                          !diff?.hasDiff
                            ? `bg-primaryGray border-primaryGray`
                            : `bg-primaryPurple border-primaryPurple`
                        }
                         px-6 py-3 rounded-full`}
              disabled={!diff?.hasDiff}
            >
              <Text className="text-primaryWhite font-semibold">Save</Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={handleCancel}
              className="border border-primaryPurple px-6 py-3 rounded-full bg-primaryBg"
            >
              <Text className="text-primaryPurple">Cancel</Text>
            </TouchableOpacity> */}
            {/* <SaveActionButton
              disabled={!diff.hasDiff}
              onPress={saveDetails}
              onCancel={handleCancel}
            /> */}
          </View>
        </View>
      </CustomCard>
      {/* DatePicker Modal */}
      {/* <DatePicker
        modal
        open={open}
        date={
          procedure_date
            ? moment(procedure_date, "YYYY-MM-DD").toDate()
            : new Date()
        }
        mode={"date"}
        onConfirm={(date) => {
          setOpen(false);
          dispatch(
            setProcedureDetailsUserDetails({
              procedure_date: moment(date).format("YYYY-MM-DD"),
            })
          );
        }}
        onCancel={() => setOpen(false)}
      /> */}
      <View className="mt-9"></View>
      <PopupModal
        show={modalVisible}
        msg={popupMsg}
        status="warning"
        onClose={() => setModalVisible(false)}
      />
    </ScreenWrapper>
  );
};

export default ProcedureScreen;
