// LAAAnatomyScreen.js

import React from "react";
import { View, TouchableOpacity, Image, Text } from "react-native";
import Heading from "../../../components/Heading";
import ScreenWrapper from "../../../components/ScreenWrapper";
import zeroDegImage from "../../../../assests/0deg.png";
import fortyfiveDegImage from "../../../../assests/45deg.png";
import ninetyDegImage from "../../../../assests/90deg.png";
import onethirtyfiveDegImage from "../../../../assests/135deg.png";
import OptionSelector from "../../../components/OptionSelector";
import CustomCard from "../../../components/CustomCard";
import LineSeperator from "../../../components/LineSeperator";
import { useSelectedPatient } from "../../../RepStack/schedule/hooks/PatientsHooks";
import { AppDispatch } from "../../../store";
import { useDispatch } from "react-redux";
import { useFocusEffect } from "@react-navigation/native";
import Loader from "../../../components/Loader";
import {
  useLaaAnatomyDetails,
  useLoaderAndError,
  useLaaAnatomyUserDetails,
  findLaaoAnatomyDiff,
} from "../../../RepStack/schedule/hooks/laaanatomyHooks";
import {
  fetchLaaAnatomyDetails,
  putLaaAnatomyDetails,
} from "../../../store/rep/ScheduleStack/laaoProcedures/laaanatomy/thunk";
import { setLaaAnatomyUserDetails } from "../../../store/rep/ScheduleStack/laaoProcedures/laaanatomy";
import { formatLaaAnatomyOptions } from "../../../utils";
import { useNavigation } from "@react-navigation/native";
import SaveActionButton from "../../../components/SaveActionButton";

const sampleUri2 = {
  img1: zeroDegImage,
  img2: ninetyDegImage,
  // img3: ninetyDegImage,
  img3: fortyfiveDegImage,
  img4: onethirtyfiveDegImage,
};

interface ILAAAnatomyScreenProps {
  route: {
    params: {
      morphology: string[];
      angles: string[];
      diameters: string[];
      depths: string[];
    };
  };
}

const LAAAnatomyScreen: React.FunctionComponent<ILAAAnatomyScreenProps> = ({
  route,
}) => {
  // NEW START
  const dispatch = useDispatch<AppDispatch>();
  const selectedPatient = useSelectedPatient();
  const { loader, error } = useLoaderAndError();
  const navigation = useNavigation();

  const userDetails = useLaaAnatomyUserDetails();

  const { morphology_selected, morphologyOptions, study } =
    formatLaaAnatomyOptions(userDetails);

  const diff = findLaaoAnatomyDiff();

  const saveDetails = async () => {
    try {
      const res = await dispatch(
        putLaaAnatomyDetails({
          case_details_id: userDetails?.case_detail_id,
          payload: diff.currentValues,
        })
      );
      if (res.payload) {
        if (selectedPatient?.case_id) {
          const refetchRes = await dispatch(
            fetchLaaAnatomyDetails({ case_id: selectedPatient.case_id })
          );
          if (refetchRes.payload) {
            dispatch(setLaaAnatomyUserDetails(refetchRes.payload));
          }
        }
      }
    } catch (err) {
      console.error(err);
    }
  };

  useFocusEffect(
    React.useCallback(() => {
      const fetchDetails = async () => {
        if (selectedPatient?.case_id) {
          const res = await dispatch(
            fetchLaaAnatomyDetails({ case_id: selectedPatient.case_id })
          );

          if (res.payload) {
            dispatch(setLaaAnatomyUserDetails(res.payload));
          }
        }
      };
      fetchDetails();
    }, [dispatch, selectedPatient?.case_id])
  );

  // NEW END
  const { angles, diameters, depths, morphology } = route.params;
  const sampleUrisArray = Object.values(sampleUri2);
  const numericDiameters = diameters.map((d) =>
    parseInt(d.replace(" mm", ""), 10)
  );

  // Find the maximum diameter
  const maxDiameter = Math.max(...numericDiameters);
  const morphologyData = ["Windsock", "Chicken Wing", "Cactus", "Cauliflower"];
  const [morphology1, setMorphology1] = React.useState<string>("Windsock");

  const handleRefresh = async () => {
    if (!selectedPatient?.case_id) return;
    const refetchRes = await dispatch(
      fetchLaaAnatomyDetails({ case_id: selectedPatient.case_id })
    );
    if (refetchRes.payload) {
      dispatch(setLaaAnatomyUserDetails(refetchRes.payload));
    }
  };
  if (loader) {
    return <Loader />;
  }

  return (
    <ScreenWrapper direction="column" onRefresh={handleRefresh}>
      <CustomCard>
        <View className="mt-2 gap-4 ">
          <Heading
            text="Morphology"
            size="sub-heading"
            color={"black"}
            showSeperator={false}
          />
          <View className="p-3 rounded-md bg-primaryBg">
            <OptionSelector
              options={morphologyOptions}
              selected={morphology_selected}
              onSelect={(option) =>
                dispatch(
                  setLaaAnatomyUserDetails({
                    morphology: {
                      ...userDetails.morphology,
                      selected: {
                        id: option.value,
                        name: option.label,
                      },
                    },
                  })
                )
              }
            />
          </View>
        </View>

        <LineSeperator extraStyle="my-5" />

        <View>
          <View>
            <Heading
              text="Measurements"
              size="sub-heading"
              color={"black"}
              showSeperator={false}
            />
            {study?.map((item, index) => (
              <TouchableOpacity
                key={index}
                className="px-5 py-3 rounded-md bg-primaryPurple shadow-md mb-2 w-[45%] mt-3"
                onPress={() =>
                  navigation.navigate("WebViewer", { link: item.viewer_link })
                }
              >
                <Text className="text-primaryWhite">{`TEE Study ${
                  index + 1
                }`}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>
        {/* <View className="flex-row items-center justify-center">
          <SaveActionButton disabled={!diff.hasDiff} onPress={saveDetails} />
        </View> */}

        <View className="mb-3"></View>
      </CustomCard>

      <View className="mt-9"></View>
    </ScreenWrapper>
  );
};

export default LAAAnatomyScreen;
