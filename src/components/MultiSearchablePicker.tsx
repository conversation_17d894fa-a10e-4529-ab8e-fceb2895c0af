import React, { useState, useEffect } from "react";
import { StyleSheet } from "react-native";
import { Picker } from "react-native-ui-lib";

interface PickerItem {
  label: string;
  value: string;
}

interface IMultiSearchablePickerProps {
  items: PickerItem[];
  placeholder: string;
  value?: string[]; // Array of selected values (strings)
  onValueChange?: (selectedItems: PickerItem[]) => void; // Callback for multiple selected items
  floatingLabel?: boolean;
  width?: any;
  height?: any;
  disable?: boolean;
}

const MultiSearchablePicker: React.FunctionComponent<
  IMultiSearchablePickerProps
> = ({
  items,
  placeholder,
  value = [], // Default to empty array if no value is provided
  onValueChange,
  floatingLabel = false,
  width,
  height,
  disable = true,
}) => {
  const [selectedValues, setSelectedValues] = useState<string[]>(value); // State for selected values (strings)
  const [searchValue, setSearchValue] = useState<string>("");
  const [filteredOptions, setFilteredOptions] = useState<PickerItem[]>(items);

  useEffect(() => {
    // Filter items whenever search value or items change
    const filtered = items.filter((option) =>
      option.label.toLowerCase().includes(searchValue.toLowerCase())
    );
    setFilteredOptions(filtered);
  }, [searchValue, items]);

  useEffect(() => {
    // Sync selectedValues state with the value prop when it changes
    setSelectedValues(value || []);
  }, [value]);

  const handleSearchChange = (searchText: string) => {
    setSearchValue(searchText);
  };

  const handleValueChange = (selectedValues: string[]) => {
    // Update the selected values
    setSelectedValues(selectedValues);

    // Map selected values to full PickerItem objects and pass to onValueChange
    const selectedItems = items.filter((item) =>
      selectedValues.includes(item.value)
    );
    onValueChange?.(selectedItems); // Send full items array with selected label and value
  };

  return (
    <Picker
      placeholder={placeholder}
      floatingPlaceholder={floatingLabel}
      value={selectedValues} // Use array of selected values (strings)
      enableModalBlur={false}
      onChange={handleValueChange} // Handle selection change for multiple items
      style={[styles.picker, { width: width || "auto", height: height || 45 }]}
      topBarProps={{ title: "Select Options" }}
      showSearch
      searchPlaceholder="Search..."
      searchStyle={styles.searchStyle}
      floatingPlaceholderStyle={styles.placeholderStyle}
      onSearchChange={handleSearchChange}
      items={filteredOptions.map((option) => ({
        label: option.label,
        value: option.value,
      }))}
      editable={disable}
      mode="MULTI"
    />
  );
};

const styles = StyleSheet.create({
  picker: {
    borderColor: "#8143d9",
    backgroundColor: "white",
    borderWidth: 1,
    borderRadius: 4,
    padding: 5,
    height: 45,
    justifyContent: "center",
    alignItems: "center",
    marginTop: 5,
    color: "#000000",
  },
  searchStyle: {
    color: "#0059ff",
    fontSize: 16,
  },
  placeholderStyle: {
    fontSize: 16,
    paddingTop: 5,
    paddingBottom: 5,
    color: "#000000",
    justifyContent: "center",
    alignItems: "center",
    fontWeight: "500",
  },
});

export default MultiSearchablePicker;
