import React from "react";
import { View, Text, StyleSheet } from "react-native";

// Define props type
interface InputValidationMsgProps {
  message?: string;
  visible: boolean;
}

const InputValidationMsg: React.FC<InputValidationMsgProps> = ({
  message,
  visible,
}) => {
  if (!visible || !message) return null;

  return (
    <View style={styles.container}>
      <Text style={styles.message}>{message}</Text>
    </View>
  );
};

// Styles
const styles = StyleSheet.create({
  container: {
    marginTop: 4,
    alignSelf: "flex-start",
  },
  message: {
    fontSize: 12,
    color: "red",
  },
});

export default InputValidationMsg;
