import React, { useEffect, useRef } from "react";
import { View } from "react-native";
import { useDispatch } from "react-redux";
import BottomSheetComponent, {
  BottomSheetRefProps,
} from "../../../components/BottomSheetComponent";

// import AddSite from "../components/AddSite";
// import AddImplantingPhysician from "../components/AddImplantingPhysician";
import AddReferringProvider from "../components/AddReferringProvider";
import AddPcpProvider from "../components/AddPcpProvider";

import TopTabs from "./SettingsTopBarStack";

// import { useAddSiteModalState } from "../hooks/addSiteHooks";
import { useAddProviderModalState } from "../hooks/addReferringProvider";
import { useShowAddPcpProviderModal } from "../hooks/addPcpProviderHooks";
// import { useAddPhysicianModalState } from "../hooks/addImplantingPhysicianHooks";

import { showAddSiteModal } from "../../../store/rep/ScheduleStack/addSite";
import { showAddPhysicianModal } from "../../../store/rep/ScheduleStack/addPhysician";
import { showAddProviderModal } from "../../../store/common/addReferringProvider";
import { showAddPcpProviderModal } from "../../../store/common/addPcpProvider";
import { useAddPcpProviderModalState } from "../../../RepStack/schedule/hooks/addPcpProvider";

const SettingsScreen: React.FC = () => {
  const dispatch = useDispatch();
  //   const showSiteModal = useAddSiteModalState();
  const showReferringProviderModal = useAddProviderModalState();
  const showAddPCPProviderModal = useAddPcpProviderModalState();
  //   const showPhysicianModal = useAddPhysicianModalState();

  const bottomSheetRef = useRef<BottomSheetRefProps>(null);

  const handleCloseBottomSheet = () => {
    dispatch(showAddSiteModal(false));
    dispatch(showAddPhysicianModal(false));
    dispatch(showAddProviderModal(false));
    dispatch(showAddPcpProviderModal(false));
  };

  useEffect(() => {
    if (
      //   showSiteModal ||
      //   showPhysicianModal ||
      showReferringProviderModal ||
      showAddPCPProviderModal
    ) {
      bottomSheetRef.current?.open?.();
    } else {
      bottomSheetRef.current?.close?.();
    }
  }, [
    // showSiteModal,
    // showPhysicianModal,
    showReferringProviderModal,
    showAddPCPProviderModal,
  ]);

  return (
    <>
      <View className="flex-1 bg-white">
        <TopTabs />
      </View>

      <BottomSheetComponent
        ref={bottomSheetRef}
        onClose={handleCloseBottomSheet}
        snapPoints={["100%"]}
      >
        <View className="flex-1 p-4">
          {/* {showSiteModal && <AddSite onSuccess={handleCloseBottomSheet} />}
          {showPhysicianModal && (
            <AddImplantingPhysician onSuccess={handleCloseBottomSheet} />
          )} */}
          {showReferringProviderModal && (
            <AddReferringProvider onSuccess={handleCloseBottomSheet} />
          )}
          {showAddPCPProviderModal && (
            <AddPcpProvider onSuccess={handleCloseBottomSheet} />
          )}
        </View>
      </BottomSheetComponent>
    </>
  );
};

export default SettingsScreen;

//new - all 4 tabs

// import * as React from "react";
// import {
//   View,
//   Text,
//   StyleSheet,
//   RefreshControl,
//   ScrollView,
// } from "react-native";
// import { createMaterialTopTabNavigator } from "@react-navigation/material-top-tabs";
// import { useNavigation } from "@react-navigation/native";
// import { useDispatch } from "react-redux";
// import { AppDispatch } from "../../../store";
// import { fetchSites } from "../../../store/rep/ScheduleStack/addSite/thunk";
// import {
//   resetSiteDetails,
//   setActiveTab,
//   showAddSiteModal,
//   setSelectedSite as siteSetSelectedSite,
// } from "../../../store/rep/ScheduleStack/addSite";
// import {
//   resetImplantingPhysicianDetails,
//   setSelectedPhysician,
//   setSelectedSite as physicianSetSelectedSite,
//   showAddPhysicianModal,
// } from "../../../store/rep/ScheduleStack/addPhysician";
// import { useActiveTab, useAddSiteModalState } from "../hooks/addSiteHooks";
// import { useAddPhysicianModalState } from "../hooks/addImplantingPhysicianHooks.ts";
// import AddSite from "../components/AddSite";
// import AddImplantingPhysician from "../components/AddImplantingPhysician";
// import SiteList from "../components/SiteList";
// import PhysicianList from "../components/PhysicianList";
// import BottomSheetComponent, {
//   BottomSheetRefProps,
// } from "../../../components/BottomSheetComponent";
// import { fetchSiteList } from "../../../store/rep/ScheduleStack/schedules/thunk.ts";

// interface ISettingsScreenProps {}

// const Tab = createMaterialTopTabNavigator();

// // Sites Tab Component
// const SitesTabScreen: React.FunctionComponent = () => {
//   const dispatch = useDispatch<AppDispatch>();
//   const [refreshing, setRefreshing] = React.useState(false);

//   const fetchData = async () => {
//     await dispatch(fetchSites());
//     await dispatch(fetchSiteList());
//   };

//   React.useEffect(() => {
//     fetchData();
//   }, []);

//   const handleRefresh = async () => {
//     setRefreshing(true);
//     try {
//       await fetchData();
//     } catch (error) {
//       console.error("Error refreshing data:", error);
//     } finally {
//       setRefreshing(false);
//     }
//   };

//   const handleAddSite = () => {
//     dispatch(siteSetSelectedSite(""));
//     dispatch(physicianSetSelectedSite(""));
//     dispatch(resetSiteDetails());
//     dispatch(showAddSiteModal(true));
//   };

//   return (
//     <ScrollView
//       className="flex-1"
//       refreshControl={
//         <RefreshControl
//           refreshing={refreshing}
//           onRefresh={handleRefresh}
//           colors={["#8143d9"]}
//           tintColor={"#8143d9"}
//         />
//       }
//     >
//       <SiteList onAddPress={handleAddSite} />
//     </ScrollView>
//   );
// };

// // Physicians Tab Component
// const PhysiciansTabScreen: React.FunctionComponent = () => {
//   const dispatch = useDispatch<AppDispatch>();
//   const [refreshing, setRefreshing] = React.useState(false);

//   const fetchData = async () => {
//     await dispatch(fetchSites());
//     await dispatch(fetchSiteList());
//   };

//   React.useEffect(() => {
//     fetchData();
//   }, []);

//   const handleRefresh = async () => {
//     setRefreshing(true);
//     try {
//       await fetchData();
//     } catch (error) {
//       console.error("Error refreshing data:", error);
//     } finally {
//       setRefreshing(false);
//     }
//   };

//   const handleAddPhysician = () => {
//     dispatch(physicianSetSelectedSite(""));
//     dispatch(setSelectedPhysician(""));
//     dispatch(resetImplantingPhysicianDetails());
//     dispatch(showAddPhysicianModal(true));
//   };

//   return (
//     <ScrollView
//       className="flex-1"
//       refreshControl={
//         <RefreshControl
//           refreshing={refreshing}
//           onRefresh={handleRefresh}
//           colors={["#8143d9"]}
//           tintColor={"#8143d9"}
//         />
//       }
//     >
//       <PhysicianList onAddPress={handleAddPhysician} />
//     </ScrollView>
//   );
// };

// // Referring Provider Tab Component
// const ReferringProviderTabScreen: React.FunctionComponent = () => {
//   const dispatch = useDispatch<AppDispatch>();
//   const [refreshing, setRefreshing] = React.useState(false);

//   const fetchData = async () => {
//     await dispatch(fetchSites());
//     await dispatch(fetchSiteList());
//     // Add referring provider specific data fetching here
//   };

//   React.useEffect(() => {
//     fetchData();
//   }, []);

//   const handleRefresh = async () => {
//     setRefreshing(true);
//     try {
//       await fetchData();
//     } catch (error) {
//       console.error("Error refreshing data:", error);
//     } finally {
//       setRefreshing(false);
//     }
//   };

//   const handleAddReferringProvider = () => {
//     // Add referring provider specific logic here
//     dispatch(physicianSetSelectedSite(""));
//     dispatch(setSelectedPhysician(""));
//     dispatch(resetImplantingPhysicianDetails());
//     // dispatch(showAddReferringProviderModal(true)); // Uncomment when modal is implemented
//   };

//   return (
//     <ScrollView
//       className="flex-1"
//       refreshControl={
//         <RefreshControl
//           refreshing={refreshing}
//           onRefresh={handleRefresh}
//           colors={["#8143d9"]}
//           tintColor={"#8143d9"}
//         />
//       }
//     >
//       <View className="flex-1 p-4">
//         <Text className="text-center text-gray-500">
//           Referring Provider List
//         </Text>
//         {/* Replace with actual ReferringProviderList component when available */}
//       </View>
//     </ScrollView>
//   );
// };

// // PCP Provider Tab Component
// const PCPProviderTabScreen: React.FunctionComponent = () => {
//   const dispatch = useDispatch<AppDispatch>();
//   const [refreshing, setRefreshing] = React.useState(false);

//   const fetchData = async () => {
//     await dispatch(fetchSites());
//     await dispatch(fetchSiteList());
//     // Add PCP provider specific data fetching here
//   };

//   React.useEffect(() => {
//     fetchData();
//   }, []);

//   const handleRefresh = async () => {
//     setRefreshing(true);
//     try {
//       await fetchData();
//     } catch (error) {
//       console.error("Error refreshing data:", error);
//     } finally {
//       setRefreshing(false);
//     }
//   };

//   const handleAddPCPProvider = () => {
//     // Add PCP provider specific logic here
//     dispatch(physicianSetSelectedSite(""));
//     dispatch(setSelectedPhysician(""));
//     dispatch(resetImplantingPhysicianDetails());
//     // dispatch(showAddPCPProviderModal(true)); // Uncomment when modal is implemented
//   };

//   return (
//     <ScrollView
//       className="flex-1"
//       refreshControl={
//         <RefreshControl
//           refreshing={refreshing}
//           onRefresh={handleRefresh}
//           colors={["#8143d9"]}
//           tintColor={"#8143d9"}
//         />
//       }
//     >
//       <View className="flex-1 p-4">
//         <Text className="text-center text-gray-500">PCP Provider List</Text>
//         {/* Replace with actual PCPProviderList component when available */}
//       </View>
//     </ScrollView>
//   );
// };

// const SettingsScreen: React.FunctionComponent<ISettingsScreenProps> = () => {
//   const dispatch = useDispatch<AppDispatch>();
//   const navigation = useNavigation();
//   const activeTab = useActiveTab();

//   const showSiteModal = useAddSiteModalState();
//   const showPhysicianModal = useAddPhysicianModalState();
//   const bottomSheetRef = React.useRef<BottomSheetRefProps>(null);

//   React.useEffect(() => {
//     if (showSiteModal || showPhysicianModal) {
//       bottomSheetRef.current?.open();
//     } else {
//       bottomSheetRef.current?.close();
//     }
//   }, [showSiteModal, showPhysicianModal]);

//   const handleCloseBottomSheet = () => {
//     dispatch(showAddSiteModal(false));
//     dispatch(showAddPhysicianModal(false));
//   };

//   const handleRefresh = async () => {
//     // Refresh will be handled by individual tab components
//   };

//   // Custom tab navigator with listeners to handle tab switching logic
//   const TabNavigatorWithListeners = () => {
//     return (
//       <Tab.Navigator
//         screenOptions={{
//           tabBarScrollEnabled: true,
//           tabBarIndicatorStyle: {
//             backgroundColor: "#8143d9",
//             height: "100%",
//           },
//           tabBarActiveTintColor: "white",
//           tabBarInactiveTintColor: "black",
//           tabBarLabelStyle: {
//             fontSize: 12,
//             fontWeight: "bold",
//           },
//         }}
//         sceneContainerStyle={{ backgroundColor: "white" }}
//         initialRouteName={
//           activeTab === "sites"
//             ? "Sites"
//             : activeTab === "implanting-physicians"
//             ? "Implanting Physicians"
//             : activeTab === "referring-provider"
//             ? "Referring Provider"
//             : activeTab === "pcp-provider"
//             ? "PCP Provider"
//             : "Sites"
//         }
//         screenListeners={{
//           tabPress: (e) => {
//             // Prevent default behavior to avoid blinking
//             // Let the native tab navigator handle the smooth transition
//           },
//           focus: (e) => {
//             // Handle tab switching logic - reset data when switching tabs
//             const routeName = e.target?.split("-")[0]; // Get route name from target

//             if (routeName === "Sites") {
//               // Reset physician data when switching to Sites tab
//               dispatch(physicianSetSelectedSite(""));
//               dispatch(setSelectedPhysician(""));
//               dispatch(resetImplantingPhysicianDetails());
//               dispatch(setActiveTab("sites"));
//             } else if (routeName === "Implanting Physicians") {
//               // Reset site data when switching to Physicians tab
//               dispatch(siteSetSelectedSite(""));
//               dispatch(resetSiteDetails());
//               dispatch(setActiveTab("implanting-physicians"));
//             } else if (routeName === "Referring Provider") {
//               // Reset data when switching to Referring Provider tab
//               dispatch(siteSetSelectedSite(""));
//               dispatch(resetSiteDetails());
//               dispatch(physicianSetSelectedSite(""));
//               dispatch(setSelectedPhysician(""));
//               dispatch(resetImplantingPhysicianDetails());
//               dispatch(setActiveTab("referring-provider"));
//             } else if (routeName === "PCP Provider") {
//               // Reset data when switching to PCP Provider tab
//               dispatch(siteSetSelectedSite(""));
//               dispatch(resetSiteDetails());
//               dispatch(physicianSetSelectedSite(""));
//               dispatch(setSelectedPhysician(""));
//               dispatch(resetImplantingPhysicianDetails());
//               dispatch(setActiveTab("pcp-provider"));
//             }

//             // Close any open modals
//             dispatch(showAddSiteModal(false));
//             dispatch(showAddPhysicianModal(false));
//           },
//         }}
//       >
//         <Tab.Screen name="Sites" component={SitesTabScreen} />
//         <Tab.Screen
//           name="Implanting Physicians"
//           component={PhysiciansTabScreen}
//         />
//         <Tab.Screen
//           name="Referring Provider"
//           component={ReferringProviderTabScreen}
//         />
//         <Tab.Screen name="PCP Provider" component={PCPProviderTabScreen} />
//       </Tab.Navigator>
//     );
//   };

//   return (
//     <>
//       <View className="flex-1 bg-primaryWhite">
//         <TabNavigatorWithListeners />
//       </View>

//       <BottomSheetComponent
//         ref={bottomSheetRef}
//         onClose={handleCloseBottomSheet}
//         snapPoints={["60%", "90%"]}
//       >
//         <View className="flex-1 p-4">
//           {showSiteModal && (
//             <AddSite
//               onSuccess={() => {
//                 handleCloseBottomSheet();
//                 handleRefresh();
//               }}
//             />
//           )}
//           {showPhysicianModal && (
//             <AddImplantingPhysician
//               onSuccess={() => {
//                 handleCloseBottomSheet();
//                 handleRefresh();
//               }}
//             />
//           )}
//         </View>
//       </BottomSheetComponent>
//     </>
//   );
// };

// export default SettingsScreen;

//new - only 2 tabs- sites and implanting physicians

// import * as React from "react";
// import {
//   View,
//   Text,
//   StyleSheet,
//   RefreshControl,
//   ScrollView,
// } from "react-native";
// import { createMaterialTopTabNavigator } from "@react-navigation/material-top-tabs";
// import { useNavigation } from "@react-navigation/native";
// import { useDispatch } from "react-redux";
// import { AppDispatch } from "../../../store";
// import { fetchSites } from "../../../store/rep/ScheduleStack/addSite/thunk";
// import {
//   resetSiteDetails,
//   setActiveTab,
//   showAddSiteModal,
//   setSelectedSite as siteSetSelectedSite,
// } from "../../../store/rep/ScheduleStack/addSite";
// import {
//   resetImplantingPhysicianDetails,
//   setSelectedPhysician,
//   setSelectedSite as physicianSetSelectedSite,
//   showAddPhysicianModal,
// } from "../../../store/rep/ScheduleStack/addPhysician";
// import { useActiveTab, useAddSiteModalState } from "../hooks/addSiteHooks";
// import { useAddPhysicianModalState } from "../hooks/addImplantingPhysicianHooks.ts";
// import AddSite from "../components/AddSite";
// import AddImplantingPhysician from "../components/AddImplantingPhysician";
// import SiteList from "../components/SiteList";
// import PhysicianList from "../components/PhysicianList";
// import BottomSheetComponent, {
//   BottomSheetRefProps,
// } from "../../../components/BottomSheetComponent";
// import { fetchSiteList } from "../../../store/rep/ScheduleStack/schedules/thunk.ts";

// interface ISettingsScreenProps {}

// const Tab = createMaterialTopTabNavigator();

// // Sites Tab Component
// const SitesTabScreen: React.FunctionComponent = () => {
//   const dispatch = useDispatch<AppDispatch>();
//   const [refreshing, setRefreshing] = React.useState(false);

//   const fetchData = async () => {
//     await dispatch(fetchSites());
//     await dispatch(fetchSiteList());
//   };

//   React.useEffect(() => {
//     fetchData();
//   }, []);

//   const handleRefresh = async () => {
//     setRefreshing(true);
//     try {
//       await fetchData();
//     } catch (error) {
//       console.error("Error refreshing data:", error);
//     } finally {
//       setRefreshing(false);
//     }
//   };

//   const handleAddSite = () => {
//     dispatch(siteSetSelectedSite(""));
//     dispatch(physicianSetSelectedSite(""));
//     dispatch(resetSiteDetails());
//     dispatch(showAddSiteModal(true));
//   };

//   return (
//     <ScrollView
//       className="flex-1"
//       refreshControl={
//         <RefreshControl
//           refreshing={refreshing}
//           onRefresh={handleRefresh}
//           colors={["#8143d9"]}
//           tintColor={"#8143d9"}
//         />
//       }
//     >
//       <SiteList onAddPress={handleAddSite} />
//     </ScrollView>
//   );
// };

// // Physicians Tab Component
// const PhysiciansTabScreen: React.FunctionComponent = () => {
//   const dispatch = useDispatch<AppDispatch>();
//   const [refreshing, setRefreshing] = React.useState(false);

//   const fetchData = async () => {
//     await dispatch(fetchSites());
//     await dispatch(fetchSiteList());
//   };

//   React.useEffect(() => {
//     fetchData();
//   }, []);

//   const handleRefresh = async () => {
//     setRefreshing(true);
//     try {
//       await fetchData();
//     } catch (error) {
//       console.error("Error refreshing data:", error);
//     } finally {
//       setRefreshing(false);
//     }
//   };

//   const handleAddPhysician = () => {
//     dispatch(physicianSetSelectedSite(""));
//     dispatch(setSelectedPhysician(""));
//     dispatch(resetImplantingPhysicianDetails());
//     dispatch(showAddPhysicianModal(true));
//   };

//   return (
//     <ScrollView
//       className="flex-1"
//       refreshControl={
//         <RefreshControl
//           refreshing={refreshing}
//           onRefresh={handleRefresh}
//           colors={["#8143d9"]}
//           tintColor={"#8143d9"}
//         />
//       }
//     >
//       <PhysicianList onAddPress={handleAddPhysician} />
//     </ScrollView>
//   );
// };

// const SettingsScreen: React.FunctionComponent<ISettingsScreenProps> = () => {
//   const dispatch = useDispatch<AppDispatch>();
//   const navigation = useNavigation();
//   const activeTab = useActiveTab();

//   const showSiteModal = useAddSiteModalState();
//   const showPhysicianModal = useAddPhysicianModalState();
//   const bottomSheetRef = React.useRef<BottomSheetRefProps>(null);

//   React.useEffect(() => {
//     if (showSiteModal || showPhysicianModal) {
//       bottomSheetRef.current?.open();
//     } else {
//       bottomSheetRef.current?.close();
//     }
//   }, [showSiteModal, showPhysicianModal]);

//   const handleCloseBottomSheet = () => {
//     dispatch(showAddSiteModal(false));
//     dispatch(showAddPhysicianModal(false));
//   };

//   const handleRefresh = async () => {
//     // Refresh will be handled by individual tab components
//   };

//   // Custom tab navigator with listeners to handle tab switching logic
//   const TabNavigatorWithListeners = () => {
//     return (
//       <Tab.Navigator
//         screenOptions={{
//           tabBarScrollEnabled: true,
//           tabBarIndicatorStyle: {
//             backgroundColor: "#8143d9",
//             height: 100,
//           },
//           tabBarActiveTintColor: "white",
//           tabBarInactiveTintColor: "black",
//           tabBarLabelStyle: {
//             fontSize: 12,
//             fontWeight: "bold",
//           },
//         }}
//         sceneContainerStyle={{ backgroundColor: "white" }}
//         screenListeners={{
//           focus: (e) => {
//             // Handle tab switching logic - reset data when switching tabs
//             const routeName = e.target?.split("-")[0]; // Get route name from target

//             if (routeName === "Sites") {
//               // Reset physician data when switching to Sites tab
//               dispatch(physicianSetSelectedSite(""));
//               dispatch(setSelectedPhysician(""));
//               dispatch(resetImplantingPhysicianDetails());
//               dispatch(setActiveTab("sites"));
//             } else if (routeName === "Implanting Physicians") {
//               // Reset site data when switching to Physicians tab
//               dispatch(siteSetSelectedSite(""));
//               dispatch(resetSiteDetails());
//               dispatch(setActiveTab("implanting-physicians"));
//             }

//             // Close any open modals
//             dispatch(showAddSiteModal(false));
//             dispatch(showAddPhysicianModal(false));
//           },
//         }}
//       >
//         <Tab.Screen name="Sites" component={SitesTabScreen} />
//         <Tab.Screen
//           name="Implanting Physicians"
//           component={PhysiciansTabScreen}
//         />
//       </Tab.Navigator>
//     );
//   };

//   return (
//     <>
//       <View className="flex-1 bg-primaryWhite">
//         <TabNavigatorWithListeners />
//       </View>

//       <BottomSheetComponent
//         ref={bottomSheetRef}
//         onClose={handleCloseBottomSheet}
//         snapPoints={["60%", "90%"]}
//       >
//         <View className="flex-1 p-4">
//           {showSiteModal && (
//             <AddSite
//               onSuccess={() => {
//                 handleCloseBottomSheet();
//                 handleRefresh();
//               }}
//             />
//           )}
//           {showPhysicianModal && (
//             <AddImplantingPhysician
//               onSuccess={() => {
//                 handleCloseBottomSheet();
//                 handleRefresh();
//               }}
//             />
//           )}
//         </View>
//       </BottomSheetComponent>
//     </>
//   );
// };

// export default SettingsScreen;

//old code-- below

// import * as React from "react";
// import {
//   View,
//   Text,
//   StyleSheet,
//   RefreshControl,
//   ScrollView,
// } from "react-native";
// import { useNavigation } from "@react-navigation/native";
// import { useDispatch } from "react-redux";
// import { AppDispatch } from "../../../store";
// import CustomTabView from "../../../components/CustomTabView";
// import { fetchSites } from "../../../store/rep/ScheduleStack/addSite/thunk";
// import {
//   resetSiteDetails,
//   setActiveTab,
//   showAddSiteModal,
//   setSelectedSite as siteSetSelectedSite,
// } from "../../../store/rep/ScheduleStack/addSite";
// import {
//   resetImplantingPhysicianDetails,
//   setSelectedPhysician,
//   setSelectedSite as physicianSetSelectedSite,
//   showAddPhysicianModal,
// } from "../../../store/rep/ScheduleStack/addPhysician";
// import { useActiveTab, useAddSiteModalState } from "../hooks/addSiteHooks";
// import { useAddPhysicianModalState } from "../hooks/addImplantingPhysicianHooks.ts";
// import AddSite from "../components/AddSite";
// import AddImplantingPhysician from "../components/AddImplantingPhysician";
// import SiteList from "../components/SiteList";
// import PhysicianList from "../components/PhysicianList";
// import BottomSheetComponent, {
//   BottomSheetRefProps,
// } from "../../../components/BottomSheetComponent";
// import { fetchSiteList } from "../../../store/rep/ScheduleStack/schedules/thunk.ts";

// interface ISettingsScreenProps {}

// const SettingsScreen: React.FunctionComponent<ISettingsScreenProps> = () => {
//   const dispatch = useDispatch<AppDispatch>();
//   const navigation = useNavigation();
//   const [refreshing, setRefreshing] = React.useState(false);
//   const activeTab = useActiveTab();

//   const showSiteModal = useAddSiteModalState();
//   const showPhysicianModal = useAddPhysicianModalState();
//   const bottomSheetRef = React.useRef<BottomSheetRefProps>(null);

//   const fetchData = async () => {
//     await dispatch(fetchSites());
//     await dispatch(fetchSiteList());
//     //  else {
//     //   await dispatch(fetchImplantingPhysicians());
//     // }
//   };

//   React.useEffect(() => {
//     fetchData();
//   }, [activeTab]);

//   React.useEffect(() => {
//     if (showSiteModal || showPhysicianModal) {
//       bottomSheetRef.current?.open();
//     } else {
//       bottomSheetRef.current?.close();
//     }
//   }, [showSiteModal, showPhysicianModal]);

//   const handleRefresh = async () => {
//     setRefreshing(true);
//     try {
//       await fetchData();
//     } catch (error) {
//       console.error("Error refreshing data:", error);
//     } finally {
//       setRefreshing(false);
//     }
//   };

//   const handleTabSelect = (value: string) => {
//     handleCloseBottomSheet();
//     dispatch(siteSetSelectedSite(""));
//     dispatch(resetSiteDetails());
//     dispatch(physicianSetSelectedSite(""));
//     dispatch(setSelectedPhysician(""));
//     dispatch(resetImplantingPhysicianDetails());
//     dispatch(setActiveTab(value as "sites" | "implanting-physicians"));
//   };

//   const handleAddSite = () => {
//     dispatch(siteSetSelectedSite(""));
//     dispatch(physicianSetSelectedSite(""));
//     dispatch(resetSiteDetails());
//     dispatch(showAddSiteModal(true));
//   };

//   const handleAddPhysician = () => {
//     dispatch(physicianSetSelectedSite(""));
//     dispatch(setSelectedPhysician(""));
//     dispatch(resetImplantingPhysicianDetails());
//     dispatch(showAddPhysicianModal(true));
//   };

//   const handleCloseBottomSheet = () => {
//     dispatch(showAddSiteModal(false));
//     dispatch(showAddPhysicianModal(false));
//   };

//   const renderContent = () => {
//     if (activeTab === "sites") {
//       return <SiteList onAddPress={handleAddSite} />;
//     } else {
//       return <PhysicianList onAddPress={handleAddPhysician} />;
//     }
//   };

//   return (
//     <>
//       <View className="flex-1 bg-primaryWhite">
//         <View className="px-3 pt-4">
//           <CustomTabView
//             options={[
//               { label: "Sites", value: "sites" },
//               {
//                 label: "Implanting Physicians",
//                 value: "implanting-physicians",
//               },
//             ]}
//             onSelect={handleTabSelect}
//             defaultIndex={activeTab === "sites" ? 0 : 1}
//           />
//         </View>

//         <ScrollView
//           className="flex-1"
//           refreshControl={
//             <RefreshControl
//               refreshing={refreshing}
//               onRefresh={handleRefresh}
//               colors={["#8143d9"]}
//               tintColor={"#8143d9"}
//             />
//           }
//         >
//           {renderContent()}
//         </ScrollView>
//       </View>

//       <BottomSheetComponent
//         ref={bottomSheetRef}
//         onClose={handleCloseBottomSheet}
//         snapPoints={["60%", "90%"]}
//       >
//         <View className="flex-1 p-4">
//           {showSiteModal && (
//             <AddSite
//               onSuccess={() => {
//                 handleCloseBottomSheet();
//                 handleRefresh();
//               }}
//             />
//           )}
//           {showPhysicianModal && (
//             <AddImplantingPhysician
//               onSuccess={() => {
//                 handleCloseBottomSheet();
//                 handleRefresh();
//               }}
//             />
//           )}
//         </View>
//       </BottomSheetComponent>
//     </>
//   );
// };

// export default SettingsScreen;
