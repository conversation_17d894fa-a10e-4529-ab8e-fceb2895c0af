import React from "react";
import { TouchableOpacity, View, Text, StyleSheet } from "react-native";
import Icon from "react-native-vector-icons/MaterialCommunityIcons";

interface RadioProps {
  onChange: (value: boolean) => void;
  selected: boolean;
  yesColor?: string;
  noColor?: string;
  size?: number;
  yesLabel?: string;
  noLabel?: string;
}

const getColor = (color?: string, defaultColor?: string) =>
  color || defaultColor || "gray";

const CustomRadio: React.FC<RadioProps> = ({
  selected,
  onChange,
  yesColor,
  noColor,
  size = 24,
  yesLabel = "Yes",
  noLabel = "No",
}) => {
  return (
    <View style={styles.radioContainer}>
      {/* Yes (True) Radio Button */}
      <TouchableOpacity
        style={styles.radioButtonContainer}
        onPress={() => onChange(true)}
        activeOpacity={0.7}
      >
        <View
          style={[
            styles.radioButton,
            selected
              ? { backgroundColor: getColor(yesColor, "green") }
              : {
                  borderWidth: 2,
                  borderColor: getColor(yesColor, "green"),
                  backgroundColor: "transparent",
                },
            { width: size, height: size },
          ]}
        >
          {selected && <Icon name="check" size={size * 0.5} color={"white"} />}
        </View>
        <Text style={styles.label}>{yesLabel}</Text>
      </TouchableOpacity>

      {/* No (False) Radio Button */}
      <TouchableOpacity
        style={styles.radioButtonContainer}
        onPress={() => onChange(false)}
        activeOpacity={0.7}
      >
        <View
          style={[
            styles.radioButton,
            !selected
              ? { backgroundColor: getColor(noColor, "red") }
              : {
                  borderWidth: 2,
                  borderColor: getColor(noColor, "red"),
                  backgroundColor: "transparent",
                },
            { width: size, height: size },
          ]}
        >
          {!selected && <Icon name="close" size={size * 0.5} color={"white"} />}
        </View>
        <Text style={styles.label}>{noLabel}</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  radioContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 16,
  },
  radioButtonContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  radioButton: {
    borderRadius: 50,
    alignItems: "center",
    justifyContent: "center",
  },
  label: {
    fontSize: 14,
    color: "black",
  },
});

export default CustomRadio;
