interface IPatientCase {
  case_id: string;
  procedure_date: string;
  procedure_time: string;
  patient: {
    id: number;
    first_name: string;
    last_name: string;
    middle_name: string;
    image_url: string | null;
    age: number;
    sex: string;
    cta: boolean;
    tee: boolean;
    anticoagulation: {
      selected: {
        id: string | null;
        name: string | null;
        quantity: number;
        dosing_frequency: {
          id: string;
          name: string;
        };
      }[];
      options: {
        id: string;
        name: string;
        quantity: number[];
      }[];
      frequency_options: {
        id: string;
        name: string;
      }[];
    };
    rationale: {
      selected: {
        id: string | null;
        name: string | null;
        other: string | null;
      }[];
      options: {
        id: string;
        name: string;
      }[];
    };
    cha2ds2_vasc: {
      score: number;
      calculation: {
        congestive_heart_failure: number;
        hypertension: number;
        age: number;
        diabetes_mellitus: number;
        stroke_tia_thromboembolic_event: number;
        vascular_disease: number;
        sex: number;
      };
    };
    referring_provider: {
      selected: {
        id: string | null;
        name: string | null;
      }[];
      options: {
        id: string;
        name: string;
      }[];
    };
    afib_ablation: boolean;
    dob: string;
    has_bled_score: {
      score: number;
      calculation: {
        hyperten_uncontrol: number;
        abnorm_liver_func: number;
        abnorm_renal_func: number;
        stroke: number;
        bleeding: number;
        labile_inr: number;
        alcohol: number;
        medication_bleeding: number;
        age: number;
      };
    };
    truplan_upload_status: string;
    pcp: {
      selected: {
        id: string | null;
        name: string | null;
      }[];
      options: {
        id: string;
        name: string;
      }[];
    };
    prior_ablation: {
      selected: {
        id: string | null;
        name: string | null;
        other: string | null;
      }[];
      options: {
        id: string;
        name: string;
      }[];
    };
    prior_ablation_other: string;
    rationale_other: string;
    study: any[];
  };
  implanting_physician: {
    selected: {
      id: string | null;
      name: string | null;
    }[];
    options: {
      id: string;
      name: string;
    }[];
  };
  site: {
    selected: {
      id: string | null;
      name: string | null;
    }[];
    options: {
      id: string;
      name: string;
    }[];
  };
}

interface IGroinAccess {
  case_detail_id: string;
  groin_access_start_time: string; // ISO 8601 datetime string
  groin_access_end_time: string; // ISO 8601 datetime string
  total_groin_time: string; // ISO 8601 datetime string or duration
}

interface Medication {
  id: string;
  med_id: string;
  med_name: string;
  med_dose: string | number;
  dosing_frequency: string;
  dosing_frequency_id: string;
}

interface PreopApiResponse {
  preopDetails: IPatientCase | null;
  userDetails: IPatientCase | null;
  groinAccess: IGroinAccess;
  loaders: {
    demographicDetailsLoader: boolean;
    groinAccessLoader: boolean;
    updateDemographicsLoader: boolean;
  };
  errors: {
    demographicDetailsError: string | null;
    groinAccessError: string | null;
    updateDemographicsError: string | null;
  };
}

export type { PreopApiResponse, IPatientCase, IGroinAccess, Medication };
