import React, { useState } from "react";
import CustomCard from "../../../components/CustomCard";
import { View, TouchableOpacity, Text } from "react-native";
import CustomText from "../../../components/CustomText";
import LineSeperator from "../../../components/LineSeperator";
import SearchablePicker from "../../../components/SearchablePicker";

interface ICreateTaskProps { }

const CreateTask: React.FunctionComponent<ICreateTaskProps> = (props) => {
    const [phaseOfCare, setPhaseOfCare] = useState("");
    const [task, setTask] = useState("");
    const [assignee, setAssignee] = useState("");

    const handleSave = () => {
        // Logic to save the patient data
      };
    
      const handleCancel = () => {
        // Logic to reset all fields
        setPhaseOfCare("");
        setTask("");
        // setAssignee("");
      }

    return (
        <CustomCard>
            <View className="rounded-md p-3">
                <View className="flex-row justify-between items-center ">
                    <CustomText
                        value={"Phase of Care"}
                        className="font-bold text-md"
                    />
                    <View className="flex-1">
                        <SearchablePicker
                            placeholder="Select Phase of Care"
                            items={[
                                { label: "Pre-Op", value: "pre-op" },
                                { label: "Procedure", value: "procedure" },
                                { label: "Post-Op", value: "post-op" },
                            ]}
                            value={phaseOfCare}
                            onValueChange={(item) => setPhaseOfCare(item.value)}
                        />
                    </View>
                </View>

                <LineSeperator extraStyle="my-5" />

                <View className="flex-row justify-between items-center ">
                    <CustomText
                        value={"Task"}
                        className="font-bold text-md"
                    />
                    <View className="flex-1">
                        <SearchablePicker
                            placeholder="Select Task"
                            items={[
                                { label: "Scan CT", value: "scan-ct" },
                                { label: "Scan TEE", value: "Procedure" },
                                { label: "Upload CT Images", value: "upload-ct" },
                            ]}
                            value={task}
                            onValueChange={(item) => setTask(item.value)}
                        />
                    </View>
                </View>

                <LineSeperator extraStyle="my-5" />

                {/* <View className="flex-row justify-between items-center ">
                    <CustomText
                        value={"Assignee"}
                        className="font-bold text-md"
                    />
                    <View className="flex-1">
                        <SearchablePicker
                            placeholder="Select Assignee"
                            items={[
                                { label: "Rep1", value: "rep1" },
                                { label: "Rep2", value: "rep2" },
                                { label: "Rep3", value: "Rep3" },
                            ]}
                            value={assignee}
                            onValueChange={(item) => setAssignee(item.value)}
                        />
                    </View>
                </View> */}

                <View className="flex-row justify-center gap-4">
                    <TouchableOpacity
                        onPress={handleSave}
                        className="border border-primaryPurple px-6 py-3 rounded-full bg-primaryPurple"
                    >
                        <CustomText className="text-primaryWhite" value="Save"/>
                    </TouchableOpacity>
                    <TouchableOpacity
                        onPress={handleCancel}
                        className="border border-primaryPurple px-6 py-3 rounded-full bg-primaryBg"
                    >
                        <CustomText className="text-primaryPurple" value="Cancel"/>
                    </TouchableOpacity>
                </View>
            </View>

        </CustomCard>
    )
}

export default CreateTask;