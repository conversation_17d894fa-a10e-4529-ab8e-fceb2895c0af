import React from "react";
import { TouchableOpacity, View, Text } from "react-native";
import { MatIcon } from "../utils";

interface CustomCheckBoxProps {
  label?: string;
  checked: boolean;
  onChange: (value: boolean) => void;
}

const CustomCheckBox: React.FC<CustomCheckBoxProps> = ({
  label,
  checked,
  onChange,
}) => {
  return (
    <TouchableOpacity
      className="flex-row items-center space-x-2"
      onPress={() => onChange(!checked)}
      activeOpacity={0.7}
    >
      <View
        className={`w-7 h-7 rounded-full border-2 flex items-start justify-center ${
          checked ? "bg-primaryPurple" : ""
        }`}
      >
        <Text>{checked && MatIcon("check-bold", "#fff", 18)}</Text>
      </View>
      {label && <Text className="text-black">{label}</Text>}
    </TouchableOpacity>
  );
};

export default CustomCheckBox;
