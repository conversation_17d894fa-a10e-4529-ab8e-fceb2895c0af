import React, { useState } from "react";
import { View, Text, TouchableOpacity } from "react-native";
import CustomCard from "../../../components/CustomCard";
import CustomButton from "../../../components/CustomButton";
import CustomInput from "../../../components/CustomTextInput";
import {
  usePreopTesting,
  useUserDetails,
  usePreopTestingDiff,
  usePreopDetails,
} from "../hooks/preopHooks";
import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons";

import SearchablePicker from "../../../components/SearchablePicker";
import { setPreopUserDetails } from "../../../store/coordinator/ScheduleStack/preop";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch } from "../../../store";
import { parseFloatInput } from "../../../utils";
import { putPreopTesting } from "../../../store/coordinator/ScheduleStack/preop/thunk";
import { useCoordinatorSelectedPatient } from "../hooks/schedulesHooks";
import { replaceUndefinedWithNull } from "../../../utils";
import { fetchPreopDetails } from "../../../store/coordinator/ScheduleStack/preop/thunk";
import { fetchProfile } from "../../../store/coordinator/MyProfile/MyProfileScreen/thunk";
import Heading from "../../../components/Heading";
import { useFocusEffect } from "@react-navigation/native";
import {
  useProfileDetails,
  formatProfileDetails,
} from "../../my-profile/hooks/profileHooks";
import LineSeperator from "../../../components/LineSeperator";
import PopupModal from "../../../components/Popup";
import SaveActionButton from "../../../components/SaveActionButton";
import { simpleAutoSave } from "../../../services/simpleAutoSave";
import { setSaveStatus, setLastSavedData } from "../../../store/services";

interface IPreOpTestingCardProps {}

const PreOpTestingCard: React.FunctionComponent<
  IPreOpTestingCardProps
> = () => {
  const dispatch = useDispatch<AppDispatch>();
  const userDetails = useUserDetails();
  const preopDetails = usePreopDetails();
  const selectedPatient = useCoordinatorSelectedPatient();

  const data = usePreopTesting();

  const diff = usePreopTestingDiff();

  const profileDetails = useProfileDetails();

  const { site_id } = formatProfileDetails(profileDetails);

  const [modalVisible, setModalVisible] = React.useState(false);

  const [popupMsg, setPopupMsg] = React.useState<string[]>([]);
  // Create a ref to track if a save is in progress
  const saveInProgressRef = React.useRef(false);

  // Create a ref to track the last saved data hash
  const lastSavedDataHashRef = React.useRef("");
  const validator = () => {
    setPopupMsg([]);

    var temp = false;
    if (
      data?.hgb === 0 ||
      data?.hgb >= 25 ||
      data?.hgb === null ||
      data?.hgb === undefined
    ) {
      setPopupMsg((prev) => [
        ...prev,
        "Please Enter a Valid Hemoglobin Value (Range: 0.1 - 24.9 mg/dL)",
      ]);
      temp = true;
    }

    if (
      data?.plts === null ||
      data?.plts === undefined ||
      data?.plts === 0 ||
      data?.plts >= 1000
    ) {
      setPopupMsg((prev) => [
        ...prev,
        "Please Enter a Valid Platelets Value (Range: 0.1 - 999.9 uL)",
      ]);
      temp = true;
    }

    if (
      data?.cr === null ||
      data?.cr === undefined ||
      data?.cr === 0 ||
      data?.cr >= 20
    ) {
      setPopupMsg((prev) => [
        ...prev,
        "Please Enter a Valid Creatinine Value (Range: 0.1 - 19.9 mg/dL)",
      ]);
      temp = true;
    }

    if (
      data?.egfr === null ||
      data?.egfr === undefined ||
      data?.egfr === 0 ||
      data?.egfr >= 120
    ) {
      setPopupMsg((prev) => [
        ...prev,
        "Please Enter a Valid eGFR Value (Range: 0.1 - 119.9 mL/min)",
      ]);
      temp = true;
    }

    // if (
    //   data?.rhythm_selected_id === null ||
    //   data?.rhythm_selected_id === undefined ||
    //   data?.rhythm_selected_id === ""
    // ) {
    //   setPopupMsg((prev) => [...prev, "Please enter a valid Rhythm value"]);
    //   temp = true;
    // }

    // if (
    //   data?.ventricular_rate === null ||
    //   data?.ventricular_rate === undefined ||
    //   data?.ventricular_rate === 0
    // ) {
    //   setPopupMsg((prev) => [
    //     ...prev,
    //     "Please enter a valid Ventricular value",
    //   ]);
    //   temp = true;
    // }

    if (temp) {
      setModalVisible(true);
      return false;
    } else {
      return true;
    }
  };

  useFocusEffect(
    React.useCallback(() => {
      const fetchDetails = async () => {
        await dispatch(fetchProfile());
      };
      fetchDetails();
    }, [selectedPatient?.case_id])
  );

  const handleCancel = () => {
    dispatch(
      setPreopUserDetails({
        ...userDetails,
        ...preopDetails,
      })
    );
  };

  const saveDetails = async (): Promise<void> => {
    try {
      // Run validation
      const isValid = validator();

      if (!isValid) {
        dispatch(
          setSaveStatus({
            screenId: "preopTesting", // update if needed
            status: "validation_failed",
          })
        );
        setLocalSaveStatus("validation_failed");
        throw new Error("Validation failed");
      }

      // Avoid saving if already in progress
      if (saveInProgressRef.current) {
        return;
      }

      const dataCopy = replaceUndefinedWithNull({ ...diff.currentValues });
      const currentDataHash = JSON.stringify(dataCopy);

      // Skip saving if data hasn't changed
      if (currentDataHash === lastSavedDataHashRef.current) {
        return;
      }

      // Mark save as in progress
      saveInProgressRef.current = true;

      const res = await dispatch(
        putPreopTesting({
          case_id: selectedPatient?.case_id,
          site_id: site_id,
          payload: dataCopy,
        })
      );

      if (res.payload && selectedPatient?.case_id) {
        const refetchRes = await dispatch(
          fetchPreopDetails({
            case_id: selectedPatient.case_id,
          })
        );

        if (refetchRes.payload) {
          dispatch(setPreopUserDetails(refetchRes.payload));
          lastSavedDataHashRef.current = currentDataHash;
        }
      }

      saveInProgressRef.current = false;
    } catch (err) {
      console.error("Save error:", err);
      saveInProgressRef.current = false;
      throw err;
    }
  };

  const saveStatus = useSelector(
    (state: any) => state.common?.autoSave?.["preopTesting"]?.status
  );

  const [localSaveStatus, setLocalSaveStatus] = React.useState<string>("");

  const effectiveSaveStatus = saveStatus || localSaveStatus;

  /**
   * Derive the save message and styling based on the save status
   */
  const { saveMessage, messageStyle, iconName, iconColor } =
    React.useMemo(() => {
      let message = "";
      let style = "text-primaryPurple italic";
      let icon = "alert-circle-outline";
      let color = "#8143d9";

      switch (effectiveSaveStatus) {
        case "changes_detected":
          message = "Changes detected, saving soon...";
          style = "text-primaryBlack italic";
          icon = "alert-circle-outline";
          color = "#f5c542"; // yellow
          break;
        case "saving":
          message = "Saving... Please don't close the app";
          style = "text-primaryPurple font-bold italic";
          icon = "alert-circle-outline";
          color = "#8143d9"; // purple
          break;
        case "saved":
          message = "All changes saved";
          style = "text-green-3 italic";
          icon = "check-circle-outline";
          color = "#28a745"; // green
          break;
        case "validation_failed":
          message = "Validation failed. Please check your inputs.";
          style = "text-red-3 font-bold italic";
          icon = "alert-circle-outline";
          color = "#d93025"; // red
          break;
        case "error":
          message = "Error saving changes. Please try again.";
          style = "text-red-3 font-bold italic";
          icon = "alert-circle-outline";
          color = "#d93025"; // red
          break;
      }

      return {
        saveMessage: message,
        messageStyle: style,
        iconName: icon,
        iconColor: color,
      };
    }, [effectiveSaveStatus]);
  /**
   * Ref to track previous diff values to prevent unnecessary saves
   */
  const prevDiffRef = React.useRef<string>("");

  /**
   * Ref to track if initial data has been loaded
   * This prevents autosave from triggering before data is fetched
   */
  const initialDataLoadedRef = React.useRef<boolean>(false);

  /**
   * Effect to set initialDataLoaded when data is fetched
   */
  React.useEffect(() => {
    // Check if we have valid data
    const hasValidData = userDetails?.case_id !== undefined;

    if (hasValidData && !initialDataLoadedRef.current) {
      initialDataLoadedRef.current = true;
    }
  }, [userDetails]);

  /**
   * Effect to trigger autosave when data changes
   * This uses our new simpleAutoSave service with local state fallback
   */
  React.useEffect(() => {
    // Only proceed if initial data has been loaded and there are actual differences
    if (!initialDataLoadedRef.current) {
      return;
    }

    // Check if case_id exists to prevent API calls with undefined ID
    if (!userDetails?.case_id) {
      return;
    }

    // Only proceed if there are actual differences
    if (diff.hasDiff) {
      // Check if we have any undefined values in critical fields
      const hasUndefinedValues = Object.values(diff.currentValues).every(
        (val) => val === undefined
      );
      if (hasUndefinedValues) {
        return;
      }

      // Convert current values to string for comparison
      const currentDiffString = JSON.stringify(diff.currentValues);

      // Only trigger save if the values have actually changed
      if (currentDiffString !== prevDiffRef.current) {
        // Update the ref with current values
        prevDiffRef.current = currentDiffString;

        // Create a custom wrapper for setSaveStatus that updates both Redux and local state
        const setStatusWithFallback = (payload: {
          screenId: string;
          status: string;
        }) => {
          // Update local state as a fallback
          setLocalSaveStatus(payload.status);
          // Also dispatch to Redux
          return dispatch(setSaveStatus(payload));
        };

        // Trigger the simple autosave with the current values
        simpleAutoSave(
          "preopTesting", // Screen ID
          diff.currentValues, // Data to save
          saveDetails, // Save function
          4000, // 4 second delay before saving
          dispatch, // Redux dispatch
          setStatusWithFallback, // Custom action to set save status with fallback
          setLastSavedData // Action to set last saved data
        );
      } else {
        console.log("No actual changes in diff values, skipping autosave");
      }
    }
  }, [
    diff.hasDiff,
    diff.currentValues,
    dispatch,
    setLocalSaveStatus,
    userDetails,
  ]);

  return (
    <View className="p-3">
      {saveMessage && (
        <View
          style={{
            flexDirection: "row",
            justifyContent: "flex-end",
            alignItems: "center",
            marginVertical: 4,
          }}
        >
          <MaterialCommunityIcons name={iconName} color={iconColor} size={20} />
          <Text className={`text-sm ${messageStyle}`} style={{ marginLeft: 6 }}>
            {saveMessage}
          </Text>
        </View>
      )}
      <View className="gap-y-4">
        {/* Labs */}
        <Text className="text-primaryPurple font-semibold text-lg mb-3">
          Labs
        </Text>

        <View className="flex-column gap-2">
          <Text className="text-primaryBlack font-medium">Hemoglobin</Text>
          <View className="flex-1">
            <CustomInput
              inputValue={data?.hgb?.toString() || ""}
              // onInputChange={setHgb}
              onInputChange={(value) =>
                parseFloatInput(value, (updatedValue) => {
                  dispatch(
                    setPreopUserDetails({
                      lab_details: {
                        ...userDetails?.lab_details,
                        hemoglobin: updatedValue,
                      },
                    })
                  );
                })
              }
              placeholder="mg/dL"
              height={38}
              // maxLength={4}
              keyboardType="numeric"
            />
            <Text className="text-primaryPurple text-xs italic text-right mt-1">
              Last Updated :{" "}
              {userDetails?.lab_details?.hemoglobin_updated_at
                ? userDetails?.lab_details?.hemoglobin_updated_at
                : "N/A"}
            </Text>
          </View>
        </View>
        <LineSeperator />

        <View className="flex-column gap-2">
          <Text className="text-primaryBlack font-medium">Platelets</Text>

          <View className="flex-1">
            <CustomInput
              inputValue={data?.plts?.toString() || ""}
              onInputChange={(value) =>
                parseFloatInput(value, (updatedValue) => {
                  dispatch(
                    setPreopUserDetails({
                      lab_details: {
                        ...userDetails?.lab_details,
                        platelets: updatedValue,
                      },
                    })
                  );
                })
              }
              placeholder="uL"
              height={38}
              keyboardType="numeric"
            />
            <Text className="text-primaryPurple text-xs italic text-right mt-1">
              Last Updated :{" "}
              {userDetails?.lab_details?.platelets_updated_at
                ? userDetails?.lab_details?.platelets_updated_at
                : "N/A"}
            </Text>
          </View>
        </View>
        <LineSeperator />

        <View className="flex-column gap-2">
          <Text className="text-primaryBlack font-medium">Creatinine</Text>
          <View className="flex-1">
            <CustomInput
              inputValue={data?.cr?.toString() || ""}
              onInputChange={(value) =>
                parseFloatInput(value, (updatedValue) => {
                  dispatch(
                    setPreopUserDetails({
                      lab_details: {
                        ...userDetails?.lab_details,
                        creatinine: updatedValue,
                      },
                    })
                  );
                })
              }
              placeholder="mg/dL"
              height={38}
              keyboardType="numeric"
            />
            <Text className="text-primaryPurple text-xs italic text-right mt-1">
              Last Updated :{" "}
              {userDetails?.lab_details?.creatinine_updated_at
                ? userDetails?.lab_details?.creatinine_updated_at
                : "N/A"}
            </Text>
          </View>
        </View>
        <LineSeperator />

        <View className="flex-column gap-2">
          <Text className="text-primaryBlack font-medium">eGFR</Text>
          <View className="flex-1">
            <CustomInput
              inputValue={data?.egfr?.toString() || ""}
              onInputChange={(value) =>
                parseFloatInput(value, (updatedValue) => {
                  dispatch(
                    setPreopUserDetails({
                      lab_details: {
                        ...userDetails?.lab_details,
                        egfr: updatedValue,
                      },
                    })
                  );
                })
              }
              placeholder="mL/min"
              height={38}
              keyboardType="numeric"
            />
            <Text className="text-primaryPurple text-xs italic text-right mt-1">
              Last Updated :{" "}
              {userDetails?.lab_details?.egfr_updated_at
                ? userDetails?.lab_details?.egfr_updated_at
                : "N/A"}
            </Text>
          </View>
        </View>

        {/* EKG */}

        {/* <Text className="text-primaryPurple font-semibold text-lg mb-3">
          EKG
        </Text>
        <View className="flex-row items-center justify-between">
          <Text className="text-primaryBlack font-medium w-[40%]">Rhythm</Text>
          <View className="flex-1">
            <SearchablePicker
              items={data?.rhythm__options}
              value={data?.rhythm_selected_id || ""}
              placeholder="Regular/Irregular"
              onValueChange={(option) => {
                dispatch(
                  setPreopUserDetails({
                    lab_details: {
                      ...userDetails?.lab_details,
                      rhythm_details: {
                        ...userDetails?.lab_details?.rhythm_details,
                        selected: {
                          id: option.value,
                          name: option.value,
                        },
                      },
                    },
                  })
                );
              }}
              height={38}
            />
          </View>
        </View>

        <View className="flex-row items-center justify-between">
          <Text className="text-primaryBlack font-medium w-[40%]">
            Ventricular Rate
          </Text>
          <View className="flex-1">
            <CustomInput
              inputValue={data?.ventricular_rate?.toString() || ""}
              onInputChange={(value) =>
                parseFloatInput(value, (updatedValue) => {
                  dispatch(
                    setPreopUserDetails({
                      lab_details: {
                        ...userDetails?.lab_details,
                        ventricular_rate: updatedValue,
                      },
                    })
                  );
                })
              }
              placeholder="bpm"
              height={38}
            />
          </View>
        </View> */}
        <LineSeperator />

        <View className="flex-row justify-center gap-4 ">
          {/* <TouchableOpacity
            onPress={saveDetails}
            className={`border 
                               ${
                                 !diff?.hasDiff
                                   ? `bg-primaryGray border-primaryGray`
                                   : `bg-primaryPurple border-primaryPurple`
                               }
                                px-6 py-3 rounded-full`}
            disabled={!diff?.hasDiff}
          >
            <Text className="text-primaryWhite font-semibold">Save</Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => handleCancel()}
            className="border border-primaryPurple px-6 py-3 rounded-full bg-primaryBg"
          >
            <Text className="text-primaryPurple">Cancel</Text>
          </TouchableOpacity> */}
          {/* <SaveActionButton
            disabled={!diff.hasDiff}
            onPress={saveDetails}
            onCancel={handleCancel}
          /> */}
        </View>
      </View>

      <PopupModal
        show={modalVisible}
        msg={popupMsg}
        status="warning"
        onClose={() => setModalVisible(false)}
      />
    </View>
  );
};

export default PreOpTestingCard;
