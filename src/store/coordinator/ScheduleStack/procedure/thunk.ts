import { createAsyncThunk } from "@reduxjs/toolkit";
import api from "../../../../api/api";
import { IProcedureDetails } from "./types";

// Fetch procedure details
const fetchProcedureDetails = createAsyncThunk(
  "tasks/fetchProcedureDetails",
  async ({ case_id }: { case_id: string }, thunkAPI) => {
    try {
      const response = await api.private.get(
        api.routes.privateRoutes.coordinator.procedure_get(case_id)
      );

      if (response.data && response.data.result) {
        return response.data.result as IProcedureDetails;
      } else {
        return thunkAPI.rejectWithValue(
          'Invalid response format: Missing "result" field'
        );
      }
    } catch (error: any) {
      console.error("Error fetching procedure details:", error.response.data);

      if (error.response) {
        if (error.response.status === 404) {
        }
        return thunkAPI.rejectWithValue("Failed to fetch procedure details");
      } else if (error.request) {
        console.error("No response received:", error.request);
        return thunkAPI.rejectWithValue("No response received from the server");
      } else {
        console.error("Error occurred:", error.message);
        return thunkAPI.rejectWithValue(
          error.message || "An unknown error occurred"
        );
      }
    }
  }
);

// Update procedure details
const putProcedureDetails = createAsyncThunk(
  "tasks/putProcedureDetails",
  async (
    { case_id, payload }: { case_id: string; payload: IProcedureDetails },
    thunkAPI
  ) => {
    try {
      const response = await api.private.put(
        api.routes.privateRoutes.coordinator.procedure_put(case_id),
        payload
      );

      if (response.data && response.data.result) {
        return response.data.result as IProcedureDetails;
      } else {
        return thunkAPI.rejectWithValue(
          'Invalid response format: Missing "result" field'
        );
      }
    } catch (error: any) {
      if (error.response) {
        console.error("Error updating procedure details:", error.response.data);
        if (error.response.status === 404) {
        }
        return thunkAPI.rejectWithValue(
          error.response.data.error_description ||
            "Failed to update procedure details"
        );
      } else if (error.request) {
        console.error("No response received:", error.request);
        return thunkAPI.rejectWithValue("No response received from the server");
      } else {
        console.error("Error occurred:", error.message);
        return thunkAPI.rejectWithValue(
          error.message || "An unknown error occurred"
        );
      }
    }
  }
);

export { fetchProcedureDetails, putProcedureDetails };
