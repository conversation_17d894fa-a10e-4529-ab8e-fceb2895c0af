import { RootState } from "../store";
import { useSelector } from "react-redux";

const getDashboardId = () => {
  const dashboardId = useSelector(
    (state: RootState) =>
      state.common.dashboard.dashboardDetails?.dashboards[0]?.id
  );
  return dashboardId;
};

const getToken = () => {
  const token = useSelector(
    (state: RootState) => state.common.dashboard?.token
  );
  return token;
};

export { getDashboardId, getToken };
