import {
  View,
  Text,
  TouchableOpacity,
  TextInput,
  SafeAreaView,
  FlatList,
  Modal,
} from "react-native";
import React, { useCallback, useEffect, useState } from "react";
import CustomText from "./CustomText";
import { MatIcon } from "../utils";

type valueType = { label: string; Id: string | number; value: string };

type CustomPickerProps = {
  value: string | undefined;
  setValue: React.Dispatch<React.SetStateAction<string | undefined>> | any;
  ModalTitle?: string | undefined;
  placeHolder: string;
  data: valueType[];
  fullScreen: boolean;
  searchPlaceholder?: string | undefined;
};

const CustomPicker: React.FC<CustomPickerProps> = ({
  value,
  setValue,
  ModalTitle,
  placeHolder,
  data,
  fullScreen,
  searchPlaceholder,
}) => {
  const [modal, setModal] = useState<boolean>(false);
  const [borderColor, setBorderColor] = useState<string>("");
  const [search, setSearch] = useState<string>("");
  const [renderData, setRenderData] = useState<any>(data || []);

  useEffect(() => {
    setRenderData(data || []);
  }, [data]);

  const handleSelectValue = useCallback(
    (item: valueType) => {
      setValue(item);
      setModal(false);
      setBorderColor("border-primaryGray");
    },
    [setValue]
  );

  const handleSearch = (value: string) => {
    setSearch(value);
    if (!value) {
      setRenderData(data || []);
    } else {
      const lowercasedValue = value.toLowerCase();
      setRenderData(
        (data || []).filter((item: { label: string }) =>
          item.label.toLowerCase().includes(lowercasedValue)
        )
      );
    }
  };

  const selectedLabel = value?.label || placeHolder;

  if (fullScreen) {
    return (
      <SafeAreaView>
        <TouchableOpacity
          className="mt-4 border-primaryPurple border-[1px] p-4 rounded-lg"
          onPress={() => setModal(true)}
        >
          <CustomText value={selectedLabel} />
        </TouchableOpacity>
        <Modal
          visible={modal}
          onRequestClose={() => setModal(false)}
          transparent={true}
          animationType="slide"
        >
          <SafeAreaView
            className="flex-1 items-center justify-end"
            style={{ backgroundColor: "rgba(0, 0, 0, 0.3)" }}
          >
            <View className="h-full py-2 mt-3 bg-primaryWhite w-full rounded-t-3xl justify-start">
              <View className="mb-8 mt-3 mx-2">
                <View className="flex pr-3 flex-row items-center justify-between">
                  {ModalTitle ? (
                    <CustomText
                      className="text-primaryBlack text-3xl font-bold"
                      value={ModalTitle}
                    />
                  ) : (
                    <View />
                  )}
                  <TouchableOpacity onPress={() => setModal(false)}>
                    {MatIcon("close", "#8143d9", 25)}
                  </TouchableOpacity>
                </View>
                <View
                  className={`flex flex-row px-3 items-center border-b-2 gap-x-2 ${borderColor}`}
                >
                  {MatIcon("magnify", "#8143d9", 25)}
                  <TextInput
                    placeholder={searchPlaceholder || "Search"}
                    onFocus={() => setBorderColor("border-primaryPurple")}
                    onBlur={() => setBorderColor("border-primaryGray")}
                    className="w-full text-lg"
                    value={search}
                    onChangeText={handleSearch}
                  />
                </View>
                <FlatList
                  data={renderData}
                  showsVerticalScrollIndicator={false}
                  keyExtractor={(item) => item.value.toString()}
                  renderItem={({ item }) => (
                    <TouchableOpacity
                      className="py-5 border-primaryGray border-b-[1px] flex flex-row items-center justify-between"
                      onPress={() => handleSelectValue(item)}
                    >
                      <CustomText className="text-lg ml-2" value={item.label} />
                      {item.value === value?.value &&
                        MatIcon("check", "#8143d9", 25)}
                    </TouchableOpacity>
                  )}
                />
              </View>
            </View>
          </SafeAreaView>
        </Modal>
      </SafeAreaView>
    );
  }

  return null;
};

export default CustomPicker;
