// addReferringProviderHooks.tsx

import { useSelector } from "react-redux";
import { RootState } from "../../../store";
import {
  ReferringProvider,
  CredentialOption,
} from "../../../store/common/addReferringProvider/types";

const useLoaderAndError = () => {
  const loader = useSelector(
    (state: RootState) => state.common.referringProviders.loading
  );

  const error = useSelector(
    (state: RootState) => state.common.referringProviders.error
  );
  return { loader, error };
};

const useReferringProviderDetails = () => {
  const { providerDetails } = useSelector(
    (state: RootState) => state.common.referringProviders
  );
  return providerDetails;
};

// referring provider list
const useReferringProvidersListData = () => {
  return useSelector(
    (state: RootState) => state.common.referringProviders.providerListDetails
  );
};
const useUserReferringProviderDetails = () => {
  const { userDetails } = useSelector(
    (state: RootState) => state.common.referringProviders
  );
  return userDetails;
};

const useActiveTab = (): "referring-providers" | "pcp-providers" => {
  return useSelector(
    (state: RootState) => state.common.referringProviders.activeTab
  );
};

const useAddReferringProviderModalState = (): boolean => {
  return useSelector(
    (state: RootState) => state.common.referringProviders.showAddProviderModal
  );
};

const useSelectedReferringProvider = (): ReferringProvider => {
  return useSelector(
    (state: RootState) => state.common.referringProviders.selectedProvider
  );
};

const formatPostReferringProvider = (data: ReferringProvider | null) => {
  const first_name = data?.first_name || "";
  const last_name = data?.last_name || "";
  const middle_name = data?.middle_name || "";
  const credential = data?.credential || "";
  const npi_number = data?.npi_number || "";
  const email_id = data?.email_id || "";
  const phone_number = data?.phone_number || "";
  const fax_number = data?.fax_number || "";
  const address = data?.address || "";
  const city = data?.city || "";
  const state = data?.state || "";
  const zip_code = data?.zip_code || "";
  const hospital_system = data?.hospital_system || "";

  return {
    first_name,
    last_name,
    middle_name,
    credential,
    npi_number,
    email_id,
    phone_number,
    fax_number,
    address,
    city,
    state,
    zip_code,
    hospital_system,
  };
};

const findPostReferringProviderDiff = () => {
  const userDetails = useUserReferringProviderDetails();
  const providerDetails = useReferringProviderDetails();

  const diff = JSON.stringify(userDetails) !== JSON.stringify(providerDetails);

  const currentValues = {
    first_name: userDetails?.first_name || "",
    last_name: userDetails?.last_name || "",
    middle_name: userDetails?.middle_name || "",
    credential: userDetails?.credential || "",
    npi_number: userDetails?.npi_number || "",
    email_id: userDetails?.email_id || "",
    phone_number: userDetails?.phone_number || "",
    fax_number: userDetails?.fax_number || "",
    address: userDetails?.address || "",
    city: userDetails?.city || "",
    state: userDetails?.state || "",
    zip_code: userDetails?.zip_code || "",
    hospital_system: userDetails?.hospital_system || "",
  };

  return {
    hasDiff: diff,
    currentValues,
  };
};

const useReferringProviderCredentialOptions = (): CredentialOption[] => {
  return useSelector(
    (state: RootState) => state.common.referringProviders.credentialOptions
  );
};
export {
  useActiveTab,
  useLoaderAndError,
  useAddReferringProviderModalState,
  useReferringProviderDetails,
  useUserReferringProviderDetails,
  useSelectedReferringProvider,
  formatPostReferringProvider,
  findPostReferringProviderDiff,
  useReferringProviderCredentialOptions,
};
