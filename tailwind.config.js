/** @type {import('tailwindcss').Config} */

module.exports = {
  content: ["./App.{js,jsx,ts,tsx}", "./src/**/*.{js,jsx,ts,tsx}"],
  presets: [require("nativewind/preset")],
  theme: {
    colors: {
      primaryPurple: "#8143d9",
      primaryBlack: "#000000",
      primaryWhite: "#FFFFFF",
      primaryBg: "#f8f6ff",
      primaryGray: "#888888",
      secondaryGray: "#d1d5db",
      lightGray: "#e5e7eb",
      greyText: "#888888",
      "red-1": "#fef2f2",
      "green-1": "#f0fdf4",
      "yellow-1": "#fefce8",
      "red-2": "#fee2e2",
      "yellow-2": "#fef9c3",
      "green-2": "#dcfce7",
      "red-3": "#dc2626",
      "yellow-3": "#fde047",
      "green-3": "#16a34a",
      "yellow-4": "#ca8a04",
      "orange-3": "#FFAC1C",
      "blue-3": "#3b82f6",
    },
    extend: {
      fontFamily: {
        verdana: ["verdana"], // Define your custom font
        "verdana-bold": ["verdana-bold"],
      },
      // fontSize: {
      //   heading: ['24px', {lineHeight: '30px'}],
      //   subheading: ['16px', {lineHeight: '24px'}],
      //   paragraph: ['14px', {lineHeight: '20px'}],
      //   label: ['24px', {lineHeight: '20px'}],
      // },
      // fontWeight: {
      //   normal: '400',
      //   medium: '500',
      //   bold: '700',
      //   extraBold: '800',
      // },
      padding: {
        container: "16px",
      },
    },
  },
  plugins: [],
};
