import React from "react";
import { createStackNavigator } from "@react-navigation/stack";
import ScheduleScreen from "./screens/ScheduleScreen";
import PatientDetailsScreen from "./screens/PatientDetailsScreen";
import CTAScreen from "./screens/CTAScreen";
import PreOpTEEScreen from "./screens/PreOpTEEScreen";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import PDFViewer from "./screens/PdfViewer";
import WebViewer from "./screens/WebViewScreen";

const ScheduleStack = createStackNavigator();

export default function ScheduleScreenStack(): React.JSX.Element {
  const insets = useSafeAreaInsets();

  return (
    <ScheduleStack.Navigator>
      <ScheduleStack.Screen
        name="Schedule"
        component={ScheduleScreen}
        options={{
          headerShown: true,
        }}
      />

      <ScheduleStack.Screen
        name="Details"
        component={WebViewer}
        options={{ title: "" }}
      />
    </ScheduleStack.Navigator>
  );
}
