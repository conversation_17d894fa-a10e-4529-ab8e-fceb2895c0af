import React, { useRef } from "react";
import { createStackNavigator } from "@react-navigation/stack";
import Home from "./screens/HomeScreen";
import BottomSheetComponent, {
  BottomSheetRefProps,
} from "../../components/BottomSheetComponent";
import { StyleSheet } from "react-native";
import { useChatBotState } from "../components/hooks";
import { useDispatch } from "react-redux";
import { AppDispatch } from "../../store";
import { setChatBotState } from "../../store/chatbot";
import WebView from "react-native-webview";
import { Enviromnent, SchedulerUrl } from "../../api/config";
import WebViewer from "./screens/WebViewScreen";

interface IRepHomeStackProps {}

const HomeScreenStack: React.FunctionComponent<IRepHomeStackProps> = (
  props
) => {
  const HomeStack = createStackNavigator();

  const bottomRef = useRef<BottomSheetRefProps>(null);
  const webviewRef = useRef<WebView>();

  const dispatch = useDispatch<AppDispatch>();

  const [snapIndex, setSnapIndex] = React.useState(0);

  return (
    <>
      <HomeStack.Navigator>
        <HomeStack.Screen name="Dashboard" component={Home} />
        <HomeStack.Screen
          name="WebViewer"
          component={WebViewer}
          options={{ title: "" }}
        />
      </HomeStack.Navigator>

      {/* <BottomSheetComponent
        ref={bottomRef}
        snapPoints={["50%", "65%", "80%"]}
        backgroundColor="white"
        onClose={() => {
          dispatch(
            setChatBotState({
              currentScreen: "HomeTab",
              isOpened: false,
            })
          );
        }}
        onSnapTo={(number) => {
          setSnapIndex(number);
        }}
      >
        <WebView
          ref={webviewRef}
          source={{
            uri: `${SchedulerUrl[Enviromnent]}/chat_bot.html`,
          }}
          style={{ flex: 1 }}
          javaScriptEnabled={true}
          useWebKit={true}
          // injectedJavaScript={`
          //     document.querySelector('input').addEventListener('focus', () => {
          //        document.querySelector('input').style-marginBottom = 50;
          //     });
          //   `}
          scrollEnabled={true}
          onMessage={(event) => {
            const { data } = event.nativeEvent;
            if (data === "close") {
              bottomRef.current?.close();
            }
          }}
          incognito={true}
          cacheEnabled={false}
          pullToRefreshEnabled={true}
          onError={(err) => console.error("WebView Error:", err)}
        />
      </BottomSheetComponent> */}
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 24,
    backgroundColor: "grey",
  },
  contentContainer: {
    flex: 1,
    alignItems: "center",
  },
  input: {
    marginTop: 8,
    marginBottom: 10,
    borderRadius: 10,
    fontSize: 16,
    lineHeight: 20,
    padding: 8,
    backgroundColor: "rgba(151, 151, 151, 0.25)",
  },
});
export default HomeScreenStack;
