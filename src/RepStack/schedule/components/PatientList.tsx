// PatientList.tsx
import React from "react";
import { View, ScrollView } from "react-native";
import PatientCard from "./PatientCard";
import { genUUID } from "../../../utils";
import { usePatientCases } from "../hooks/PatientsHooks";
import { useSelector } from "react-redux";
import { RootState } from "../../../store";
import Loader from "../../../components/Loader";

interface PatientListProps {
  onChadPress: (patient: any) => void;
}

const PatientList: React.FunctionComponent<PatientListProps> = ({
  onChadPress,
}) => {
  const data = usePatientCases();
  const loader = useSelector((state: RootState) => state.patients.loading);

  if (loader) {
    return (
      <View className="mt-36 flex justify-center items-center w-full h-full">
        <Loader />
      </View>
    );
  }

  return (
    <View className="flex-column h-[100%]">
      <ScrollView className="">
        <View className="flex-column items-center mb-16">
          {data.map((patient, index) => (
            <View className="pt-4" key={index}>
              <PatientCard
                key={genUUID()}
                patientCase={patient}
                chadPress={() => onChadPress(patient)}
              />
            </View>
          ))}
        </View>
      </ScrollView>
    </View>
  );
};

export default PatientList;
