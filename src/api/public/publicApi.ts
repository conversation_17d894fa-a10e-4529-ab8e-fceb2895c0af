// public.ts
import axios, {AxiosInstance, AxiosResponse} from 'axios';
import apiUrl from '../config';
import {Enviromnent} from '../config';

class PublicApiService {
  private api: AxiosInstance;

  constructor() {
    this.api = axios.create({
      baseURL: apiUrl[Enviromnent],
    });
  }

  // Method to make GET requests
  public async get<T = any>(url: string): Promise<AxiosResponse<T>> {
    return this.api.get<T>(url);
  }

  // Method to make POST requests
  public async post<T = any>(
    url: string,
    data?: any,
  ): Promise<AxiosResponse<T>> {
    return this.api.post<T>(url, data);
  }

  // Method to make PUT requests
  public async put<T = any>(
    url: string,
    data?: any,
  ): Promise<AxiosResponse<T>> {
    return this.api.put<T>(url, data);
  }

  // Method to make DELETE requests
  public async delete<T = any>(url: string): Promise<AxiosResponse<T>> {
    return this.api.delete<T>(url);
  }
}

export default new PublicApiService();
