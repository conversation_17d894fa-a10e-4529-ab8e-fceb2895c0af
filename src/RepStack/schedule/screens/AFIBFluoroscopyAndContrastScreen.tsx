import * as React from "react";
import ScreenWrapper from "../../../components/ScreenWrapper";
import { View } from "react-native";
import CustomInput from "../../../components/CustomTextInput";
import CustomCard from "../../../components/CustomCard";
import Heading from "../../../components/Heading";
import { parseFloatInput } from "../../../utils";
import LineSeperator from "../../../components/LineSeperator";
import {
  fetchFluoroscopyDetails,
  putFluoroscopyDetails,
} from "../../../store/rep/ScheduleStack/laaoProcedures/fluoroscopy/thunk";
import { useSelectedPatient } from "../hooks/PatientsHooks";
import { AppDispatch } from "../../../store";
import { useDispatch } from "react-redux";
import { formatFluoroscopyOptions } from "../../../utils";
import {
  useFluoroscopyUserDetails,
  useLoaderAndError,
  findFluoroscopyDiff,
} from "../hooks/fluoroscopyHooks";
import { setFluoroscopyUserDetails } from "../../../store/rep/ScheduleStack/laaoProcedures/fluoroscopy";
import { useFocusEffect } from "@react-navigation/native";
import Loader from "../../../components/Loader";
import SaveActionButton from "../../../components/SaveActionButton";

interface IAFIBFluoroscopyAndContrastScreenProps {}

const AFIBFluoroscopyAndContrastScreen: React.FunctionComponent<
  IAFIBFluoroscopyAndContrastScreenProps
> = (props) => {
  // NEW START

  const dispatch = useDispatch<AppDispatch>();
  const selectedPatient = useSelectedPatient();
  const userDetails = useFluoroscopyUserDetails();
  const { loader, error } = useLoaderAndError();
  const diff = findFluoroscopyDiff();

  const { creatinine_value, fluoro_time, fluoro_total, total_contrast } =
    formatFluoroscopyOptions(userDetails);

  const saveDetails = async () => {
    try {
      const res = await dispatch(
        putFluoroscopyDetails({
          case_details_id: userDetails?.case_detail_id,
          payload: diff.currentValues,
        })
      );
      if (res.payload) {
        if (selectedPatient?.case_id) {
          const refetchRes = await dispatch(
            fetchFluoroscopyDetails({ case_id: selectedPatient.case_id })
          );
          if (refetchRes.payload) {
            dispatch(setFluoroscopyUserDetails(refetchRes.payload));
          }
        }
      }
    } catch (err) {
      console.error(err);
    }
  };

  useFocusEffect(
    React.useCallback(() => {
      const fetchDetails = async () => {
        if (selectedPatient?.case_id) {
          const res = await dispatch(
            fetchFluoroscopyDetails({ case_id: selectedPatient.case_id })
          );

          if (res.payload) {
            dispatch(setFluoroscopyUserDetails(res.payload));
          }
        }
      };
      fetchDetails();
    }, [dispatch, selectedPatient?.case_id])
  );

  const handleRefresh = async () => {
    if (!selectedPatient?.case_id) return;
    const refetchRes = await dispatch(
      fetchFluoroscopyDetails({ case_id: selectedPatient.case_id })
    );
    if (refetchRes.payload) {
      dispatch(setFluoroscopyUserDetails(refetchRes.payload));
    }
  };

  // NEW END

  if (loader) {
    return <Loader />;
  }

  return (
    <ScreenWrapper direction="column" onRefresh={handleRefresh}>
      <CustomCard extraStyle="">
        <SaveActionButton disabled={!diff.hasDiff} onPress={saveDetails} />

        <View>
          <Heading
            text="Total Fluoro Time (min)"
            size="sub-heading"
            extraStyle="pb-3"
            showSeperator={false}
          />
          <CustomInput
            inputValue={creatinine_value?.toString()}
            onInputChange={(value) =>
              parseFloatInput(value, (updatedValue) => {
                dispatch(
                  setFluoroscopyUserDetails({
                    creatinine_value: updatedValue,
                  })
                );
              })
            }
            placeholder="mg/dL"
            keyboardType="numeric"
          />
        </View>

        <LineSeperator extraStyle="my-5" />

        <View>
          <Heading
            text="Total Fluoro (mGy)"
            size="sub-heading"
            extraStyle="pb-3"
            showSeperator={false}
          />
          <CustomInput
            inputValue={fluoro_time?.toString()}
            onInputChange={(value) =>
              parseFloatInput(value, (updatedValue) => {
                dispatch(
                  setFluoroscopyUserDetails({
                    fluoro_time: updatedValue,
                  })
                );
              })
            }
            placeholder="min"
            keyboardType="numeric"
          />
        </View>

        <LineSeperator extraStyle="my-5" />

        <View>
          <Heading
            text="Contrast (mL)"
            size="sub-heading"
            extraStyle="pb-3"
            showSeperator={false}
          />
          <CustomInput
            inputValue={fluoro_total?.toString()}
            onInputChange={(value) =>
              parseFloatInput(value, (updatedValue) => {
                dispatch(
                  setFluoroscopyUserDetails({
                    fluoro_total: updatedValue,
                  })
                );
              })
            }
            placeholder="mGy"
            keyboardType="numeric"
          />
        </View>

        <LineSeperator extraStyle="my-5" />

        <View>
          <Heading
            text="Complications"
            size="sub-heading"
            extraStyle="pb-3"
            showSeperator={false}
          />
          <CustomInput
            inputValue={total_contrast?.toString()}
            onInputChange={(value) =>
              parseFloatInput(value, (updatedValue) => {
                dispatch(
                  setFluoroscopyUserDetails({
                    total_contrast: updatedValue,
                  })
                );
              })
            }
            placeholder="mL"
            keyboardType="numeric"
          />
        </View>
        <View className="mb-3 "></View>
      </CustomCard>
    </ScreenWrapper>
  );
};

export default AFIBFluoroscopyAndContrastScreen;
