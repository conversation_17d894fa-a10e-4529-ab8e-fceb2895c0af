// import React from "react";
// import { View, Text, TouchableOpacity } from "react-native";
// import { MatIcon } from "../utils";

// type ToggleButtonProps = {
//   messages: [string, string];
//   selected: string; // This prop will hold the currently selected value
//   setSelected: React.Dispatch<React.SetStateAction<string>>; // Function to update the selected value
//   disabled?: boolean;
//   width?: string | number;
//   invertColor?: boolean;
//   customToggler?: boolean;
//   customColors?: [string, string];
//   yesIconName?: string; // Icon for the "yes" state
//   noIconName?: string; // Icon for the "no" state
// };

// const ToggleButton: React.FC<ToggleButtonProps> = ({
//   messages,
//   selected,
//   setSelected,
//   disabled = false,
//   width,
//   invertColor = false,
//   customToggler,
//   customColors,
//   yesIconName = "check-circle", // Default icon for "yes"
//   noIconName = "close-circle", // Default icon for "no"
// }) => {
//   const handlePress = () => {
//     // Toggle between the two messages
//     setSelected(selected === messages[0] ? messages[1] : messages[0]);
//   };

//   return (
//     <View>
//       {customToggler ? (
//         <TouchableOpacity
//           disabled={disabled}
//           className={`
//             ${width}
//             `}
//           style={[
//             { opacity: disabled ? 0.5 : 1 },
//             { borderRadius: 18 },
//             selected === messages[0]
//               ? { backgroundColor: customColors?.[0] || "#16a34a" }
//               : { backgroundColor: customColors?.[1] || "#dc2626" },
//           ]}
//           onPress={handlePress}
//         >
//           {selected === messages[0] ? (
//             <View className="flex-row justify-between items-center">
//               {MatIcon(yesIconName, "#ffffff", 22)}
//               <Text className="text-primaryWhite font-bold text-sm mr-2">
//                 {messages[0]}
//               </Text>
//             </View>
//           ) : (
//             <View className="flex-row justify-between items-center">
//               <Text className="text-primaryWhite font-bold text-sm ml-2">
//                 {messages[1]}
//               </Text>
//               {MatIcon(noIconName, "#ffffff", 22)}
//             </View>
//           )}
//         </TouchableOpacity>
//       ) : (
//         <TouchableOpacity
//           disabled={disabled}
//           className={`${width}`}
//           style={[
//             invertColor
//               ? selected === messages[0]
//                 ? { backgroundColor: "#dc2626" }
//                 : { backgroundColor: "#16a34a" }
//               : selected === messages[0]
//               ? { backgroundColor: "#16a34a" }
//               : { backgroundColor: "#dc2626" },
//             { opacity: disabled ? 0.5 : 1 },
//             { borderRadius: 18 },
//           ]}
//           onPress={handlePress}
//         >
//           {selected === messages[0] ? (
//             <View className="flex-row justify-between items-center">
//               {MatIcon(yesIconName, "#ffffff", 22)}
//               <Text className="text-primaryWhite font-bold text-sm mr-2">
//                 {messages[0]}
//               </Text>
//             </View>
//           ) : (
//             <View className="flex-row justify-between items-center">
//               <Text className="text-primaryWhite font-bold text-sm ml-2">
//                 {messages[1]}
//               </Text>
//               {MatIcon(noIconName, "#ffffff", 22)}
//             </View>
//           )}
//         </TouchableOpacity>
//       )}
//     </View>
//   );
// };

// export default ToggleButton;

import React from "react";
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  GestureResponderEvent,
} from "react-native";
import { MatIcon } from "../utils";

type ToggleButtonProps = {
  messages: [string, string];
  selected: string;
  setSelected: React.Dispatch<React.SetStateAction<string>>;
  disabled?: boolean;
  width?: string | number;
  invertColor?: boolean;
  customToggler?: boolean;
  customColors?: [string, string];
  yesIconName?: string;
  noIconName?: string;
  isBox?: boolean;
  // New prop: if true then the color logic is flipped:
  // Yes becomes a filled solid green and no is outlined.
  isPositive?: boolean;
};

const ToggleButton: React.FC<ToggleButtonProps> = ({
  messages,
  selected,
  setSelected,
  disabled = false,
  width = "120px",
  invertColor = false,
  customToggler,
  customColors,
  yesIconName = "check-circle",
  noIconName = "close-circle",
  isBox = true,
  isPositive = false,
}) => {
  const defaultYesColor = "#16a34a";
  const defaultNoColor = "#dc2626";

  const yesColor = customColors
    ? invertColor
      ? customColors[1]
      : customColors[0]
    : invertColor
    ? defaultNoColor
    : defaultYesColor;

  const noColor = customColors
    ? invertColor
      ? customColors[0]
      : customColors[1]
    : invertColor
    ? defaultYesColor
    : defaultNoColor;

  const isYesSelected = selected === messages[0];

  const handlePress = (event: GestureResponderEvent) => {
    if (disabled) return;
    setSelected(isYesSelected ? messages[1] : messages[0]);
  };

  // Determine styles based on isPositive flag and isBox flag.
  if (!isBox) {
    // Custom toggler (non-box) variant
    // For non-positive, use original logic (filled background based on selection)
    // For positive:
    //  - If "yes" is selected, fill with yesColor.
    //  - If "no" is selected, use transparent background with outlined style.
    const dynamicStyle = isPositive
      ? isYesSelected
        ? { backgroundColor: yesColor, borderWidth: 0 }
        : {
            backgroundColor: noColor,
            borderWidth: 0,
          }
      : { backgroundColor: isYesSelected ? yesColor : noColor };

    return (
      <TouchableOpacity
        disabled={disabled}
        className={typeof width === "string" ? width : undefined}
        style={[
          styles.iconContainer,
          dynamicStyle,
          { opacity: disabled ? 0.5 : 1 },
        ]}
        onPress={handlePress}
      >
        <View style={styles.iconContent}>
          {isYesSelected ? (
            <>
              {MatIcon(yesIconName, "#ffffff", 22)}
              <Text style={styles.iconText}>{messages[0]}</Text>
            </>
          ) : (
            <>
              <Text style={styles.iconText}>{messages[1]}</Text>
              {MatIcon(noIconName, "#ffffff", 22)}
            </>
          )}
        </View>
      </TouchableOpacity>
    );
  }

  // isBox variant
  // For non-positive:
  //  - When yes is selected, show an outlined button (transparent background with yesColor border).
  //  - When no is selected, show a filled button with noColor.
  // For positive:
  //  - When yes is selected, show a filled button with yesColor.
  //  - When no is selected, show an outlined button with noColor (transparent background).
  const dynamicBoxStyle = isPositive
    ? isYesSelected
      ? { backgroundColor: yesColor, borderWidth: 0 }
      : { backgroundColor: noColor, borderWidth: 0 }
    : isYesSelected
    ? { backgroundColor: yesColor, borderWidth: 0 }
    : { backgroundColor: noColor, borderWidth: 0 };

  return (
    <TouchableOpacity
      disabled={disabled}
      className={typeof width === "string" ? width : undefined}
      style={[
        styles.container,
        dynamicBoxStyle,
        { opacity: disabled ? 0.5 : 1 },
      ]}
      onPress={handlePress}
    >
      <View style={styles.content}>
        <Text
          style={[
            styles.label,
            // For positive variant, adjust text color based on background fill choices.
            // isPositive
            //   ? isYesSelected
            //     ? { color: "#ffffff" }
            //     : { color: noColor }
            //   : isYesSelected
            //   ? { color: yesColor }
            //   : { color: "#ffffff" },
          ]}
        >
          {isYesSelected ? messages[0] : messages[1]}
        </Text>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    borderRadius: 6,
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: 6,
    paddingHorizontal: 8,
  },
  content: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
  },
  label: {
    fontSize: 14,
    fontWeight: "bold",
    color: "#ffffff",
  },
  iconContainer: {
    borderRadius: 18,
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: 2,
    paddingHorizontal: 2,
  },
  iconContent: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  iconText: {
    color: "#ffffff",
    fontSize: 14,
    fontWeight: "bold",
    marginHorizontal: 8,
  },
});

export default ToggleButton;
