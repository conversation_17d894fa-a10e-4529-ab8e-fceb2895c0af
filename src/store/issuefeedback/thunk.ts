import { createAsyncThunk } from "@reduxjs/toolkit";
import api from "../../api/api";

const getIssueTypes = createAsyncThunk(
  "getjira/getIssueTypes",
  async (_, thunkAPI) => {
    try {
      const response = await api.private.get(
        api.routes.privateRoutes.jira.get_issue_types
      );

      if (response.data) {
        return response.data.result.issue_types;
      } else {
        return thunkAPI.rejectWithValue("Invalid response format");
      }
    } catch (error: any) {
      if (error.response) {
        return thunkAPI.rejectWithValue(
          error.response?.data?.message || "Failed to fetch issue types"
        );
      } else if (error.request) {
        console.error("No response received:", error.request);
        return thunkAPI.rejectWithValue("No response received from the server");
      } else {
        console.error("Error occurred:", error.message);
        return thunkAPI.rejectWithValue(
          error.message || "An unknown error occurred"
        );
      }
    }
  }
);

const createJiraIssue = createAsyncThunk(
  "putjira/createJiraIssue",
  async (
    {
      summary,
      description,
      files,
      issue_type,
    }: {
      summary: string;
      description: string;
      files: File[];
      issue_type: string;
    },
    thunkAPI
  ) => {
    try {
      const formData = new FormData();

      // Append required fields
      formData.append("summary", summary);
      formData.append("description", description);
      formData.append("issue_type", issue_type);

      // Append files if provided
      if (files && files.length > 0) {
        files.forEach((file) => {
          formData.append("files", file);
        });
      }
      const response = await api.private.post(
        api.routes.privateRoutes.jira.create_issue,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
          params: {}, // fulfills the required CustomAxiosRequestConfig.params field
        }
      );

      if (response.data) {
        return response.data.result;
      } else {
        return thunkAPI.rejectWithValue("Invalid response format");
      }
    } catch (error: any) {
      if (error.response) {
        return thunkAPI.rejectWithValue(
          error.response?.data?.message || "Failed to create Jira issue"
        );
      } else if (error.request) {
        console.error("No response received:", error.request);
        return thunkAPI.rejectWithValue("No response received from the server");
      } else {
        console.error("Error occurred:", error.message);
        return thunkAPI.rejectWithValue(
          error.message || "An unknown error occurred"
        );
      }
    }
  }
);

export { createJiraIssue, getIssueTypes };
