const buttonBase = "px-4 py-2";

const globalStyles = {
  text: {
    text_black_md: `text-primaryBlack text-md`,
    text_black_lg: `text-primaryBlack text-lg`,
    text_White_md: `text-primaryWhite text-md`,
    text_White_lg: `text-primaryWhite text-lg`,
  },

  containers: {
    flex_end: "flex justify-end items-end",
    flex_start: "flex justify-start items-start",
    flex_center: "flex justify-center items-center",
    flex_between: "flex justify-between items-center",
    flex_end_row: "flex-row justify-end items-end",
    flex_start_row: "flex-row justify-start items-start",
    flex_center_row: "flex-row justify-center items-center",
    flex_between_row: "flex-row justify-between items-center",
  },

  buttons: {
    base: buttonBase,
    rounded: {
      md: `${buttonBase} rounded-md`,
      lg: `${buttonBase} rounded-lg`,
      full: `${buttonBase} rounded-full`,
    },
  },

  lineseperator: `my-5`,
};

export default globalStyles;
