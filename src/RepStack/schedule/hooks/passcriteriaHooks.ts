import { useSelector } from "react-redux";
import { RootState } from "../../../store";

const useLoaderAndError = () => {
  const loader = useSelector(
    (state: RootState) => state.rep.passcriteria.loading
  );
  const error = useSelector((state: RootState) => state.rep.passcriteria.error);
  return { loader, error };
};

const usePassCriteriaDetails = () => {
  const passCriteriaDetails = useSelector(
    (state: RootState) => state.rep.passcriteria.passCriteriaDetails
  );
  return passCriteriaDetails;
};

const usePassCriteriaUserDetails = () => {
  const userDetails = useSelector(
    (state: RootState) => state.rep.passcriteria.userDetails
  );
  return userDetails;
};

const optimalSizesArray = () => {
  const optimalSizes = useSelector(
    (state: RootState) => state.rep.passcriteria.optimalSizes
  );
  return optimalSizes;
};

const findPassCriteriaDiff = () => {
  const passCriteriaDetails = usePassCriteriaDetails();

  const userDetails = usePassCriteriaUserDetails();

  const diff = {
    position:
      passCriteriaDetails?.position === userDetails?.position
        ? null
        : userDetails?.position,

    anchor:
      passCriteriaDetails?.anchor === userDetails?.anchor
        ? null
        : userDetails?.anchor,

    leak:
      passCriteriaDetails?.leak === userDetails?.leak
        ? null
        : userDetails?.leak,

    leak_value:
      passCriteriaDetails?.leak_value === userDetails?.leak_value
        ? null
        : userDetails?.leak_value,

    compression_ratio:
      JSON.stringify(passCriteriaDetails?.compression_ratio) ===
      JSON.stringify(userDetails?.compression_ratio)
        ? null
        : userDetails?.compression_ratio,

    device_size:
      passCriteriaDetails?.device?.selected === userDetails?.device?.selected
        ? null
        : userDetails?.device?.selected,
  };

  const nonNullDiff = Object.entries(diff).reduce((acc, [key, value]) => {
    if (value !== null && value !== undefined) {
      acc[key] = value;
    }
    return acc;
  }, {});

  nonNullDiff.compression_status = useSelector(
    (state: RootState) => state.rep.passcriteria.passed
  );

  const hasDiff = Object.entries(nonNullDiff).some(
    ([key, value]) => key !== "compression_status" && value !== ""
  );

  return {
    hasDiff,
    currentValues: nonNullDiff,
  };
};

export {
  useLoaderAndError,
  usePassCriteriaDetails,
  usePassCriteriaUserDetails,
  optimalSizesArray,
  findPassCriteriaDiff,
};
