import * as React from "react";
import { View, TouchableOpacity, Text } from "react-native";
import DocumentPicker from "react-native-document-picker";
import CustomCard from "./CustomCard";
import Heading from "./Heading";
import CustomInput from "./CustomTextInput";
import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons";
import { useLoaderAndError, useIssueTypes } from "../Hooks/issuefeedback";
import Loader from "./Loader";
import PopupModal from "./Popup";
import SearchablePicker from "./SearchablePicker";
import { useDispatch } from "react-redux";
import { getIssueTypes } from "../store/issuefeedback/thunk";
import { useFocusEffect } from "@react-navigation/native";

const IssueFeedback = ({ onSubmit, setShowIssueFeedback }) => {
  const dispatch = useDispatch();
  const [issue, setIssue] = React.useState({
    summary: "",
    description: "",
    issue_type: "",
  });
  const [files, setFiles] = React.useState([]);
  const { loader } = useLoaderAndError();
  const [saved, setSaved] = React.useState(false);
  const [popupMsg, setPopupMsg] = React.useState([]);
  const [modalVisible, setModalVisible] = React.useState(false);
  const [thankYouVisible, setThankYouVisible] = React.useState(false);

  // Get issue types
  const { issueTypes, loading: loadingIssueTypes } = useIssueTypes();

  // Fetch issue types when component comes into focus
  useFocusEffect(
    React.useCallback(() => {
      const fetchData = async () => {
        try {
          await dispatch(getIssueTypes());
        } catch (error) {
          console.error("Error fetching data:", error);
        }
      };

      fetchData();

      // Return cleanup function
      return () => {
        // Any cleanup if needed
      };
    }, [dispatch])
  );

  const prevLoader = React.useRef(loader);
  React.useEffect(() => {
    if (prevLoader.current && !loader) {
      setIssue({
        summary: "",
        description: "",
        issue_type: "",
      });
      setFiles([]);
      setSaved(true);
      // Show thank you message with a slight delay to ensure state updates properly
      setTimeout(() => {
        setThankYouVisible(true);
      }, 100);

      const timer = setTimeout(() => {
        setSaved(false);
        setThankYouVisible(false);
        // Close the feedback form after showing thank you message
        setShowIssueFeedback(false);
      }, 4000);

      return () => clearTimeout(timer);
    }
    prevLoader.current = loader;
  }, [loader, setShowIssueFeedback]);

  const handleChange = (field, value) => {
    setIssue((prevState) => ({ ...prevState, [field]: value }));
    if (saved) setSaved(false);
    if (thankYouVisible) setThankYouVisible(false);
  };

  const handleFileSelect = async () => {
    try {
      const result = await DocumentPicker.pick({
        type: [DocumentPicker.types.allFiles],
      });
      setFiles(result);
      if (saved) setSaved(false);
      if (thankYouVisible) setThankYouVisible(false);
    } catch (error) {
      if (!DocumentPicker.isCancel(error)) {
        console.error("Error picking document: ", error);
      }
    }
  };

  const validateForm = () => {
    setPopupMsg([]);
    let temp = false;

    if (!issue.summary.trim()) {
      setPopupMsg((prev) => [...prev, "Please enter a valid issue title"]);
      temp = true;
    }
    if (!issue.description.trim()) {
      setPopupMsg((prev) => [
        ...prev,
        "Please enter a valid issue description",
      ]);
      temp = true;
    }
    if (!issue.issue_type) {
      setPopupMsg((prev) => [...prev, "Please select an issue type"]);
      temp = true;
    }
    if (temp) {
      setModalVisible(true);
      return false;
    }
    return true;
  };

  const handleSubmit = () => {
    if (validateForm()) {
      onSubmit(issue, files);
    }
  };

  const handleClose = () => setShowIssueFeedback(false);

  // Convert issue types to format required by SearchablePicker
  const issueTypeItems = React.useMemo(() => {
    if (!issueTypes || !Array.isArray(issueTypes) || issueTypes.length === 0) {
      return [];
    }

    try {
      const mappedItems = issueTypes.map((type) => ({
        label: type.name || "Unknown Type",
        value: type.name || "", // Use the name as the value to send in the payload
        id: type.id || "", // Keep the ID for reference if needed
        description: type.description || "",
      }));
      return mappedItems;
    } catch (err) {
      console.error("Error converting issue types for picker:", err);
      return [];
    }
  }, [issueTypes]);

  // If thank you message is visible, show only that
  if (thankYouVisible) {
    return (
      <View style={{ flex: 1, padding: 16, zIndex: 100 }}>
        <CustomCard paddingTop extraStyle="mb-4 p-6">
          <TouchableOpacity
            onPress={handleClose}
            style={{ position: "absolute", top: 10, right: 10 }}
          >
            <MaterialCommunityIcons name="close" color="#000" size={24} />
          </TouchableOpacity>

          <View
            style={{
              height: 200,
              justifyContent: "center",
              alignItems: "center",
              width: "100%",
            }}
          >
            <MaterialCommunityIcons
              name="check-circle"
              color="#16a34a"
              size={60}
            />
            <Text
              style={{
                fontSize: 20,
                color: "#16a34a",
                fontWeight: "600",
                marginTop: 20,
                textAlign: "center",
              }}
            >
              Thank you for your feedback!
            </Text>
            <Text
              style={{
                fontSize: 14,
                color: "#666",
                marginTop: 10,
                textAlign: "center",
              }}
            >
              We appreciate your input and will review it shortly.
            </Text>
          </View>
        </CustomCard>
      </View>
    );
  }

  // Otherwise show the form
  return (
    <View style={{ flex: 1, padding: 16, zIndex: 100 }}>
      <CustomCard paddingTop extraStyle="mb-4 p-6 gap-y-4">
        <TouchableOpacity
          onPress={handleClose}
          style={{ position: "absolute", top: 10, right: 10 }}
        >
          <MaterialCommunityIcons name="close" color="#000" size={24} />
        </TouchableOpacity>

        <Heading
          text="Issue Title"
          size="sub-heading"
          showSeperator={false}
          extraStyle="-mb-2"
        />
        <CustomInput
          inputValue={issue.summary}
          placeholder="Issue title"
          error={!issue.summary.trim()}
          onInputChange={(value) => handleChange("summary", value)}
          editable={!loader}
        />

        <Heading
          text="Description"
          size="sub-heading"
          showSeperator={false}
          extraStyle="-mb-2"
        />
        <CustomInput
          inputValue={issue.description}
          placeholder="Issue description"
          error={!issue.description.trim()}
          onInputChange={(value) => handleChange("description", value)}
          multiline
          editable={!loader}
        />

        <Heading
          text="Issue Type"
          size="sub-heading"
          showSeperator={false}
          extraStyle="-mb-2"
        />

        <SearchablePicker
          placeholder="Select issue type"
          items={issueTypeItems}
          value={issue.issue_type}
          onValueChange={(item) => {
            handleChange("issue_type", item.value);
          }}
          error={!issue.issue_type}
          disable={true} // In this component, true means editable
        />
        {/* Show description of selected issue type if available */}
        {issue.issue_type && issueTypeItems.length > 0 && (
          <Text
            style={{
              fontSize: 12,
              color: "#666",
              marginTop: 4,
              fontStyle: "italic",
            }}
          >
            {issueTypeItems.find((item) => item.value === issue.issue_type)
              ?.description || `Selected: ${issue.issue_type}`}
          </Text>
        )}

        <View className="flex-row items-center space-x-2">
          <TouchableOpacity
            className="bg-primaryPurple px-4 py-2 rounded-md w-40"
            onPress={handleFileSelect}
            disabled={loader}
          >
            <Text className="text-primaryWhite text-center">
              Add Attachment
            </Text>
          </TouchableOpacity>
          {files.length > 0 && (
            <View className="flex-1 p-2 bg-gray-100 rounded-md">
              {files.map((file, index) => (
                <Text
                  key={index}
                  className="text-primaryPurple text-sm"
                  numberOfLines={2}
                  ellipsizeMode="tail"
                >
                  {file.name}
                </Text>
              ))}
            </View>
          )}
        </View>

        {loader ? (
          <Loader />
        ) : (
          <View className="flex items-center gap-y-2">
            <TouchableOpacity
              className="bg-primaryPurple px-4 py-2 rounded-md w-28"
              onPress={handleSubmit}
            >
              <Text className="text-primaryWhite text-center">Save</Text>
            </TouchableOpacity>
          </View>
        )}
      </CustomCard>

      <PopupModal
        show={modalVisible}
        msg={popupMsg}
        status="warning"
        onClose={() => setModalVisible(false)}
      />
    </View>
  );
};

export default IssueFeedback;
