import { useSelector } from "react-redux";
import { RootState } from "../../../store";
import { State } from "react-native-gesture-handler";
import { CaseDetails } from "../../../store/clinician/ScheduleStack/preop/types";
import { current } from "@reduxjs/toolkit";
import api from "../../../api/api";

const useLoaderAndError = () => {
  const { error, loading } = useSelector(
    (state: RootState) => state?.clinician?.preop
  );

  return {
    error: error,
    loader: loading,
  };
};

const useUserDetails = (): CaseDetails => {
  const { userDetails } = useSelector(
    (state: RootState) => state?.clinician?.preop
  );

  return userDetails;
};

const usePreopDetails = (): CaseDetails => {
  const { preopDetails } = useSelector(
    (state: RootState) => state?.clinician?.preop
  );

  return preopDetails;
};

interface IPatientDemographics {
  first_name: string | null;
  middle_name: string | null;
  last_name: string | null;
  dob: string | null;
  sex: string | null;
  pcp_selected_id: string | null;
  pcp_options: { label: string; value: string }[] | null;
  referring_provider_id: string | null;
  referring_provider_options: { label: string; value: string }[] | null;
  bmi: number | null;
}

const usePatientDemographics = (): IPatientDemographics => {
  const userDetails = useUserDetails();

  const patientDemographics = {
    first_name: userDetails?.patient?.first_name || null,
    middle_name: userDetails?.patient?.middle_name || null,
    last_name: userDetails?.patient?.last_name || null,
    dob: userDetails?.patient?.dob || null,
    sex: userDetails?.patient?.sex || null,
    pcp_selected_id: userDetails?.patient?.pcp?.selected?.id || null,
    pcp_options: userDetails?.patient?.pcp?.options
      ? userDetails.patient.pcp.options.map((option) => ({
          label: option.name,
          value: option.id,
        }))
      : null,
    referring_provider_id:
      userDetails?.patient?.referring_provider?.selected?.id || null,
    referring_provider_options: userDetails?.patient?.referring_provider
      ?.options
      ? userDetails.patient.referring_provider.options.map((option) => ({
          label: option.name,
          value: option.id,
        }))
      : null,
    bmi: userDetails?.patient?.bmi,
  };

  return patientDemographics;
};

const usePatientDemographicsDiff = () => {
  const userDetails = useUserDetails();
  const preopDetails = usePreopDetails();

  const diff = {
    first_name:
      preopDetails?.patient?.first_name === userDetails?.patient?.first_name,
    middle_name:
      preopDetails?.patient?.middle_name === userDetails?.patient?.middle_name,
    last_name:
      preopDetails?.patient?.last_name === userDetails?.patient?.last_name,
    dob: preopDetails?.patient?.dob === userDetails?.patient?.dob,
    gender: preopDetails?.patient?.sex === userDetails?.patient?.sex,
    pcp:
      preopDetails?.patient?.pcp?.selected?.id ===
      userDetails?.patient?.pcp?.selected?.id,
    referring:
      preopDetails?.patient?.referring_provider?.selected?.id ===
      userDetails?.patient?.referring_provider?.selected?.id,
    bmi: preopDetails?.patient?.bmi === userDetails?.patient?.bmi,
  };

  const currentValues = {
    first_name: userDetails?.patient?.first_name || "",
    middle_name: userDetails?.patient?.middle_name || "",
    last_name: userDetails?.patient?.last_name || "",
    dob: userDetails?.patient?.dob || "",
    sex: userDetails?.patient?.sex || "",
    pcp: userDetails?.patient?.pcp?.selected?.id
      ? [userDetails?.patient?.pcp?.selected?.id]
      : [],
    referring_providers: userDetails?.patient?.referring_provider?.selected?.id
      ? [userDetails?.patient?.referring_provider?.selected?.id]
      : [],
  };

  return {
    hasDiff: Object.values(diff).includes(false),
    currentValues: currentValues,
  };
};

interface ISiteDetails {
  hospital: string | null;
  state: string | null;
  implanting_physician_selected_id: string | null;
  implanting_physician_options:
    | {
        label: string;
        value: string;
      }[]
    | null;
}

const useSiteDetails = (): ISiteDetails => {
  const userDetails = useUserDetails();

  const hospital = userDetails?.site?.name;

  const state = userDetails?.site?.state;

  const implanting_physician_selected_id =
    userDetails?.implanting_physician?.selected?.id;

  const implanting_physician_options =
    userDetails?.implanting_physician?.options?.map((option) => ({
      label: option.name,
      value: option.id,
    }));

  return {
    hospital,
    state,
    implanting_physician_selected_id,
    implanting_physician_options,
  };
};

interface IHistory {
  chad_score: number | null;
  hasbled_score: number | null;
  hasbled: {
    score: number | null;
    abnorm_liver_func: { isChecked: boolean; value: number };
    abnorm_renal_func: { isChecked: boolean; value: number };
    age: number;
    alcohol: { isChecked: boolean; value: number };
    bleeding: { isChecked: boolean; value: number };
    hyperten_uncontrol: { isChecked: boolean; value: number };
    labile_inr: { isChecked: boolean; value: number };
    medication_bleeding: { isChecked: boolean; value: number };
    stroke: { isChecked: boolean; value: number };
  };
  anticoagulation_selected_array: string[];
  anticoagulation_options:
    | {
        label: string;
        value: string;
      }[]
    | null;

  primary_rationale_options:
    | {
        label: string;
        value: string;
      }[]
    | null;
  primary_rationale_selected_id: string | null;
  primary_rationale_selected_name: string | null;
  primary_rationale_other: string | null;
  secondary_rationale_options:
    | {
        label: string;
        value: string;
      }[]
    | null;
  secondary_rationale_selected_id: string | null;
  secondary_rationale_selected_name: string | null;
  secondary_rationale_other: string | null;
  afib_classification_selected_id: string | null;
  afib_classification_options:
    | {
        label: string;
        value: string;
      }[]
    | null;
  prior_ablation_options:
    | {
        label: string;
        value: string;
      }[]
    | null;
  prior_ablation_selected_id: string | null;
  prior_ablation_selected_name: string | null;
  other_selected_value: string | null;
  home_address: string | null;
  distance: number | null;
  lives_independent: boolean;
  has_social_support: boolean;
}

const useHistory = (): IHistory => {
  const userDetails = useUserDetails();

  const chad_score = userDetails?.patient?.cha2ds2_vasc?.score;

  const hasbled = userDetails?.patient?.has_bled_score?.calculation;

  const hasbled_score = userDetails?.patient?.has_bled_score?.score;

  const anticoagulation_selected_array =
    userDetails?.patient?.anticoagulation?.selected.map((item) => item.id);

  const anticoagulation_options =
    userDetails?.patient?.anticoagulation?.options?.map((item) => ({
      label: item.name,
      value: item.id,
    }));

  const primary_rationale_options =
    userDetails?.patient?.rationale?.primary?.options?.map((option) => ({
      label: option.name,
      value: option.id,
    }));
  const primary_rationale_selected_id =
    userDetails?.patient?.rationale?.primary?.selected?.id;

  const primary_rationale_selected_name =
    userDetails?.patient?.rationale?.primary?.selected?.name;

  const primary_rationale_other =
    userDetails?.patient?.rationale?.primary?.selected?.other;

  const prior_ablation_selected_name =
    userDetails?.patient?.prior_ablation?.selected?.name;
  const other_selected_value =
    userDetails?.patient?.prior_ablation?.selected?.other;
  const secondary_rationale_options =
    userDetails?.patient?.rationale?.secondary?.options?.map((option) => ({
      label: option.name,
      value: option.id,
    }));
  const secondary_rationale_selected_id =
    userDetails?.patient?.rationale?.secondary?.selected?.id;

  const secondary_rationale_selected_name =
    userDetails?.patient?.rationale?.secondary?.selected?.name;

  const secondary_rationale_other =
    userDetails?.patient?.rationale?.secondary?.selected?.other;

  const secondary = userDetails?.patient?.rationale?.secondary;

  const afib_classification_selected_id =
    userDetails?.patient?.afib_classification?.selected?.id;

  const afib_classification_options =
    userDetails?.patient?.afib_classification?.options?.map((option) => ({
      label: option.name,
      value: option.id,
    }));

  const prior_ablation_options =
    userDetails?.patient?.prior_ablation?.options?.map((option) => ({
      label: option.name,
      value: option.id,
    }));

  const prior_ablation_selected_id =
    userDetails?.patient?.prior_ablation?.selected?.id;
  const home_address =
    userDetails?.patient?.social_history?.home_address?.address;

  const distance = userDetails?.patient?.social_history?.distance;

  const lives_independent =
    userDetails?.patient?.social_history?.lives_independent;

  const has_social_support =
    userDetails?.patient?.social_history?.has_social_support;

  return {
    chad_score,
    hasbled_score,
    hasbled,
    afib_classification_options,
    afib_classification_selected_id,
    anticoagulation_options,
    anticoagulation_selected_array,
    distance,
    has_social_support,
    home_address,
    lives_independent,
    primary_rationale_options,
    primary_rationale_selected_id,
    secondary_rationale_options,
    secondary_rationale_selected_id,
    prior_ablation_options,
    prior_ablation_selected_id,
    prior_ablation_selected_name,
    other_selected_value,
    secondary_rationale_selected_name,
    primary_rationale_selected_name,
    primary_rationale_other,
    secondary_rationale_other,
  };
};

const useMedData = () => {
  const med = useSelector(
    (state: RootState) => state.clinician.preop.medication
  );

  return med;
};

const useApiMedData = () => {
  const data = useSelector(
    (state: RootState) => state.clinician.preop.apiMedication
  );
  return data;
};

const useHistoryDiff = () => {
  const userDetails = useUserDetails();
  const preopDetails = usePreopDetails();
  const med = useMedData();
  const apiMed = useApiMedData();

  const diff = {
    anticoagulant: JSON.stringify(med) === JSON.stringify(apiMed),

    rationale_primary:
      preopDetails?.patient?.rationale?.primary ===
      userDetails?.patient?.rationale?.primary,
    rationale_secondary:
      preopDetails?.patient?.rationale?.secondary ===
      userDetails?.patient?.rationale?.secondary,
    afib_classification:
      preopDetails?.patient?.afib_classification?.selected?.id ===
      userDetails?.patient?.afib_classification?.selected?.id,
    prior_ablations:
      preopDetails?.patient?.prior_ablation ===
      userDetails?.patient?.prior_ablation,
    distance:
      preopDetails?.patient?.social_history?.distance ===
      userDetails?.patient?.social_history?.distance,
    home_address:
      preopDetails?.patient?.social_history?.home_address?.address ===
      userDetails?.patient?.social_history?.home_address?.address,
    lives_independently:
      preopDetails?.patient?.social_history?.lives_independent ===
      userDetails?.patient?.social_history?.lives_independent,
    has_social_support:
      preopDetails?.patient?.social_history?.has_social_support ===
      userDetails?.patient?.social_history?.has_social_support,
  };

  const currentValues = {
    anticoagulant: med
      ? med?.map((item) => ({
          medicine: item.med_id,
          quantity: item.med_dose,
          dosing_frequency: item.dosing_frequency_id,
        }))
      : [],
    rationale: userDetails?.patient?.rationale?.primary.selected.id,
    rationale_other: userDetails?.patient?.rationale?.primary?.selected?.other,
    secondary_rationale: userDetails?.patient?.rationale?.secondary.selected.id,
    secondary_rationale_other:
      userDetails?.patient?.rationale?.secondary?.selected?.other,
    prior_ablation: userDetails?.patient?.prior_ablation?.selected?.id,
    prior_ablation_other: userDetails?.patient?.prior_ablation?.selected?.other,
    afib_classification_type_id:
      userDetails?.patient?.afib_classification?.selected?.id,
    social_history: {
      home_address: {
        address: userDetails?.patient?.social_history?.home_address?.address,
        city: userDetails?.patient?.social_history?.home_address?.city || "",
        state: userDetails?.patient?.social_history?.home_address?.state || "",
      },
      distance: userDetails?.patient?.social_history?.distance,
      lives_independent:
        userDetails?.patient?.social_history?.lives_independent,
      has_social_support:
        userDetails?.patient?.social_history?.has_social_support,
    },
  };

  return {
    hasDiff: Object.values(diff).includes(false),
    currentValues: currentValues,
  };
};

const useMedicationData = (medicineId?: string) => {
  const userDetails = useUserDetails();

  const anticoagulationOptions =
    userDetails?.patient?.anticoagulation?.options?.map((item) => ({
      label: item.name,
      value: item.id,
    }));

  const med_data = useMedData();

  const med_selected_to_edit = med_data?.find((med) => med.id === medicineId);

  const dosageOptions = userDetails?.patient?.anticoagulation?.options
    ?.find((item) => item.id === med_selected_to_edit?.med_id)
    ?.quantity?.map((quantity) => ({
      label: quantity?.toString(),
      value: quantity?.toString(),
    }));

  const findDosageOptions = (medicationId: string) => {
    return userDetails?.patient?.anticoagulation?.options
      ?.find((item) => item.id === medicationId)
      ?.quantity?.map((quantity) => ({
        label: quantity?.toString(),
        value: quantity?.toString(),
      }));
  };

  const frequency_options =
    userDetails?.patient?.anticoagulation?.frequency_options?.map((item) => ({
      label: item.name,
      value: item.id,
    }));

  return {
    anticoagulationOptions,
    med_data,
    med_selected_to_edit,
    dosageOptions,
    frequency_options,
    findDosageOptions,
  };
};

interface IConsultVisit {
  implantingPhysician: string | null;
  pre_op: {
    visit_date: string | null;
    physician_name: string | null;
  }[];
  post_op: {
    visit_date: string | null;
    physician_name: string | null;
  }[];
  schdeuled: {
    visit_date: string | null;
    physician_name: string | null;
  }[];
}

const useConsultVisit = (data: IConsultVisit) => {
  const userDetails = useUserDetails();
  const visit_date = userDetails?.consult_visit?.visit_date;
  const implantingPhysician = userDetails?.consult_visit?.physician_id;
  const pre_op = userDetails?.consult_visit?.pre_op?.map((item) => ({
    visit_date: item.visit_date,
    physician_name: item.physician_name,
  }));
  const post_op = userDetails?.consult_visit?.post_op?.map((item) => ({
    visit_date: item.visit_date,
    physician_name: item.physician_name,
  }));
  const schdeuled = userDetails?.consult_visit?.scheduled?.map((item) => ({
    visit_date: item.visit_date,
    physician_name: item.physician_name,
  }));

  return {
    pre_op,
    post_op,
    schdeuled,
    implantingPhysician,
    visit_date,
  };
};

const useConsultVisitDiff = () => {
  const userDetails = useUserDetails();
  const preopDetails = usePreopDetails();

  const diff = {
    visit_date:
      preopDetails?.consult_visit?.visit_date ===
      userDetails?.consult_visit?.visit_date,
    physician_id:
      preopDetails?.consult_visit?.physician_id ===
      userDetails?.consult_visit?.physician_id,
    procedure_scheduled_date:
      preopDetails?.consult_visit?.procedure_scheduled_date ===
      userDetails?.consult_visit?.procedure_scheduled_date,
  };

  const currentValues = {
    visit_date: userDetails?.consult_visit?.visit_date,
    physician_id: userDetails?.consult_visit?.physician_id,
  };

  return {
    hasDiff: Object.values(diff).includes(false),
    currentValues: currentValues,
  };
};

interface IpreopTesting {
  hgb: number | null;
  hemoglobin_updated_at: string | null;
  plts: number | null;
  plts_updated_at: string | null;
  cr: number | null;
  cr_updated_at: string | null;
  egfr: number | null;
  egfr_updated_at: string | null;
  rhythm_selected_id: string | null;
  rhythm__options: {
    label: string;
    value: string;
  }[];
  ventricular_rate: number | null;
}

const usePreopTesting = (): IpreopTesting => {
  const userDetails = useUserDetails();

  const hgb = userDetails?.lab_details?.hemoglobin;
  const hemoglobin_updated_at = userDetails?.lab_details?.hemoglobin_updated_at;
  const plts = userDetails?.lab_details?.platelets;
  const plts_updated_at = userDetails?.lab_details?.platelets_updated_at;
  const cr = userDetails?.lab_details?.creatinine;
  const cr_updated_at = userDetails?.lab_details?.creatinine_updated_at;
  const egfr = userDetails?.lab_details?.egfr;
  const egfr_updated_at = userDetails?.lab_details?.egfr_updated_at;
  const rhythm_selected_id =
    userDetails?.lab_details?.rhythm_details?.selected?.id;
  const rhythm__options =
    userDetails?.lab_details?.rhythm_details?.options?.map((option) => ({
      label: option.name,
      value: option.id,
    }));

  const ventricular_rate = userDetails?.lab_details?.ventricular_rate;

  return {
    hgb,
    plts,
    cr,
    egfr,
    rhythm_selected_id,
    rhythm__options,
    ventricular_rate,
    hemoglobin_updated_at,
    plts_updated_at,
    cr_updated_at,
    egfr_updated_at,
  };
};

const usePreopTestingDiff = () => {
  const userDetails = useUserDetails();
  const preopDetails = usePreopDetails();

  const diff = {
    hgb:
      preopDetails?.lab_details?.hemoglobin ===
      userDetails?.lab_details?.hemoglobin,
    plts:
      preopDetails?.lab_details?.platelets ===
      userDetails?.lab_details?.platelets,
    cr:
      preopDetails?.lab_details?.creatinine ===
      userDetails?.lab_details?.creatinine,
    egfr: preopDetails?.lab_details?.egfr === userDetails?.lab_details?.egfr,
    rhythm_details:
      preopDetails?.lab_details?.rhythm_details?.selected?.id ===
      userDetails?.lab_details?.rhythm_details?.selected?.id,
    ventricular_rate:
      preopDetails?.lab_details?.ventricular_rate ===
      userDetails?.lab_details?.ventricular_rate,
  };

  const currentValues = {
    hemoglobin: userDetails?.lab_details?.hemoglobin,
    platelets: userDetails?.lab_details?.platelets,
    creatinine: userDetails?.lab_details?.creatinine,
    egfr: userDetails?.lab_details?.egfr,
    rhythm_id: userDetails?.lab_details?.rhythm_details?.selected?.id,
    ventricular_rate: userDetails?.lab_details?.ventricular_rate,
  };

  return {
    hasDiff: Object.values(diff).includes(false),
    currentValues: currentValues,
  };
};

export {
  useLoaderAndError,
  useUserDetails,
  usePatientDemographics,
  useSiteDetails,
  useHistory,
  useConsultVisit,
  usePreopTesting,
  usePreopDetails,
  usePatientDemographicsDiff,
  useHistoryDiff,
  usePreopTestingDiff,
  useConsultVisitDiff,
  useMedicationData,
};
