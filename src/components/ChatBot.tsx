import React, { useState, useEffect, useCallback } from "react";
import { SchedulerUrl, Enviromnent } from "../api/config";
import { useNavigation } from "@react-navigation/native";
import AsyncStorage from "@react-native-async-storage/async-storage";
import Loader from "../components/Loader";
import { useFocusEffect } from "@react-navigation/native";
import {
  View,
  Modal,
  KeyboardAvoidingView,
  Platform,
  TouchableOpacity,
  Text,
} from "react-native";
import { AppDispatch } from "../store";
import { setChatBotState, setPatientDetails } from "../store/chatbot";
import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons";
import WebView from "react-native-webview";
import { useDispatch } from "react-redux";
import { useChatBotState } from "../RepStack/components/hooks";
import { fetchProfile } from "../store/coordinator/MyProfile/MyProfileScreen/thunk";
import { createJiraIssue } from "../store/issuefeedback/thunk";
import IssueFeedback from "./IssueFeedback";
import { IJiraIssue } from "../store/issuefeedback/types";
import { jwtDecode } from "jwt-decode";
import { setSelectedPatient as repSetSelectedPatient } from "../store/rep/ScheduleStack/patients";
import { setSelectedPatient as coordinatorSetSelectedPatient } from "../store/coordinator/ScheduleStack/schedule";
import { setSelectedPatient as clinicianSetSelectedPatient } from "../store/clinician/ScheduleStack/schedule";

interface CustomJwtPayload {
  realm_access?: {
    roles: string[];
  };
}
const Chatbot: React.FC = () => {
  // const { top: safeAreaTop } = useSafeAreaInsets();

  const [isChatbotVisible, setIsChatbotVisible] = useState(true);
  const excludedScreens = [
    "Home",
    "Tasks",
    "Schedule",
    "Patients",
    "My Profile",
    "Scheduler",
  ];
  const dispatch = useDispatch<AppDispatch>();
  const navigation = useNavigation();
  const {
    chatBotIsOpened,
    chatBotScreenFocused,
    caseId,
    patientName,
    screenPath,
  } = useChatBotState();
  const [accessToken, setAccessToken] = useState("");
  const [userId, setUserId] = useState("");
  const [showIssueFeedback, setShowIssueFeedback] = useState(false);
  const [showLoader, setShowLoader] = useState(true);
  const [loaded, setLoaded] = useState(false);

  useEffect(() => {
    const fetchAccessToken = async () => {
      try {
        const accessToken = await AsyncStorage.getItem("accessToken");
        if (accessToken) {
          setAccessToken(accessToken);
        } else {
          console.error("Access token not found");
        }
      } catch (error) {
        console.error("Error retrieving access token:", error);
      }
    };
    const fetchDetails = async () => {
      const res = await dispatch(fetchProfile());
      setUserId(res?.payload?.user_id);
    };

    fetchAccessToken();
    fetchDetails();
  }, []);

  useFocusEffect(
    useCallback(() => {
      if (!loaded) {
        setShowLoader(true);
      }
    }, [loaded])
  );

  const handleLoadEnd = () => {
    setTimeout(() => {
      setShowLoader(false);
      setLoaded(true);
    }, 3500);
  };

  const handlePostIssue = () => {
    setShowIssueFeedback(true);
  };

  const toggleChatbot = () => {
    setIsChatbotVisible((prevState) => !prevState);
    dispatch(
      setChatBotState({
        caseId: caseId,
        patientName: patientName,
        currentScreen: chatBotScreenFocused,
        screenPath: screenPath,
        isOpened: !chatBotIsOpened,
      })
    );
  };
  const displayPatientName = excludedScreens.includes(chatBotScreenFocused)
    ? null
    : patientName;

  // Determine if caseId should be sent to WebView
  const webViewCaseId = excludedScreens.includes(chatBotScreenFocused)
    ? ""
    : caseId;

  const handleWebViewMessage = (event: any) => {
    console.log("WebView event received:", event);
    try {
      console.log("Raw event data:", event.nativeEvent.data);
      const navigationData = JSON.parse(event.nativeEvent.data);
      console.log("Parsed navigation data:", navigationData);

      const { screen, params, case_id, patient_name } = navigationData;

      const roles =
        jwtDecode<CustomJwtPayload>(accessToken)?.realm_access?.roles;
      if (case_id) {
        dispatch(
          setPatientDetails({
            caseId: case_id,
            patientName: patient_name,
          })
        );
        if (roles?.includes("REP") || roles?.includes("REP_ADVISOR")) {
          dispatch(
            repSetSelectedPatient({
              case_id: case_id,
              resetOthers: true,
            })
          );
        } else if (roles?.includes("CLINICAL_COORDINATOR")) {
          dispatch(
            coordinatorSetSelectedPatient({
              case_id: case_id,
              resetOthers: true,
            })
          );
        } else if (roles?.includes("CLINICIAN")) {
          console;
          dispatch(
            clinicianSetSelectedPatient({
              case_id: case_id,
              resetOthers: true,
            })
          );
        } else {
          console.warn("Role does not match any expected categories:", roles);
        }
      } else {
        console.warn("Selected patient not found for case_id:", case_id);
      }

      if (screen) {
        navigation.navigate(screen, params);
      } else {
        console.error(
          "Screen property missing in navigation data:",
          navigationData
        );
      }
    } catch (error: any) {
      console.error("Error parsing navigation message:", error.message);
    }
  };

  const handleSubmit = async (issueData: IJiraIssue, files: File[]) => {
    const { summary, description, issue_type } = issueData;

    try {
      const res = await dispatch(
        createJiraIssue({ summary, description, issue_type, files })
      );
      if (res) {
        setTimeout(() => {
          setShowIssueFeedback(false);
        }, 2500);
      }
    } catch (error) {
      console.error("Error in handleSubmit:", error);
    }
  };
  // console.log(accessToken, "access token");
  // console.log(userId, "userID");
  // console.log(caseId, "caseid");
  // console.log(patientName, "patient name");
  // console.log(screenPath, "screen path");

  return (
    <>
      <View className="z-10">
        <Modal
          visible={isChatbotVisible}
          transparent={true}
          animationType="slide"
          onRequestClose={toggleChatbot}
        >
          <KeyboardAvoidingView
            style={{
              flex: 1,
              marginTop: Platform.OS === "ios" ? 60 : 0,
              justifyContent: "center",
              alignItems: "center",
              backgroundColor: "rgba(0, 0, 0, 0.5)",
            }}
            behavior={Platform.OS === "ios" ? "padding" : "height"}
            keyboardVerticalOffset={Platform.OS === "ios" ? 12 : 0}
          >
            <View className="w-full h-full bg-[#F7F7F7] shadow-[0_2px_4px_#000] pb-2">
              <View className="w-full  bg-[#8143d9] p-3 flex flex-row justify-between items-center">
                <View>
                  <Text className="text-lg font-bold text-primaryWhite">
                    {chatBotScreenFocused}
                  </Text>
                  {displayPatientName && (
                    <Text className="text-sm font-semibold text-primaryWhite">
                      {displayPatientName}
                    </Text>
                  )}
                </View>
                <View className="flex flex-row gap-x-2 items-center">
                  <TouchableOpacity
                    className="rounded-md px-2 py-[2px] mt-1 bg-primaryBg"
                    onPress={handlePostIssue}
                  >
                    <Text className="text-sm text-primaryPurple font-semibold">
                      Need Help ?
                    </Text>
                  </TouchableOpacity>

                  <TouchableOpacity onPress={toggleChatbot}>
                    <MaterialCommunityIcons
                      className="mt-1"
                      name="close"
                      color="#FFFFFF"
                      size={24}
                    />
                  </TouchableOpacity>
                </View>
              </View>
              <WebView
                source={{
                  uri: `${SchedulerUrl[Enviromnent]}/chatbot/index.html?access_token=${accessToken}&user_id=${userId}&case_id=${webViewCaseId}&screen=${screenPath}&platform=mobile`,
                  // uri: `http://********:5500/chatbot/index.html?access_token=${accessToken}&user_id=${userId}&case_id=${webViewCaseId}&screen=${screenPath}&platform=mobile`,
                }}
                onLoadEnd={handleLoadEnd}
                style={{ flex: 1 }}
                cacheEnabled={false}
                cacheMode={"LOAD_NO_CACHE"}
                javaScriptEnabled={true}
                domStorageEnabled={true}
                onMessage={handleWebViewMessage}
              />
              {showIssueFeedback && (
                <View
                  style={{
                    position: "absolute",
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    backgroundColor: "rgba(0, 0, 0, 0.9)",
                    justifyContent: "center",
                    alignItems: "center",
                  }}
                >
                  <View
                    style={{
                      width: "100%",
                      position: "absolute",
                      top: "20%",
                    }}
                  >
                    <IssueFeedback
                      onSubmit={handleSubmit}
                      setShowIssueFeedback={setShowIssueFeedback}
                    />
                  </View>
                </View>
              )}

              {showLoader && <Loader />}
            </View>
          </KeyboardAvoidingView>
        </Modal>
      </View>
    </>
  );
};

export default Chatbot;
