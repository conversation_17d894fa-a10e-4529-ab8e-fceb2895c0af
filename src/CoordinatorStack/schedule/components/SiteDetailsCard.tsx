import React, { useState } from "react";
import { View, Text, TouchableOpacity } from "react-native";
import CustomCard from "../../../components/CustomCard";
import SearchablePicker from "../../../components/SearchablePicker";
import { useSiteDetails } from "../hooks/preopHooks";
import CustomInput from "../../../components/CustomTextInput";
import CustomText from "../../../components/CustomText";
import { setPreopUserDetails } from "../../../store/coordinator/ScheduleStack/preop";
import { useDispatch } from "react-redux";
import { AppDispatch } from "../../../store";
import { useUserDetails } from "../hooks/preopHooks";

interface ISiteDetailsCardProps {}

const SiteDetailsCard: React.FunctionComponent<ISiteDetailsCardProps> = () => {
  const initialData = {
    hospital: "OHSU",
    state: "Portland, OR",
    physician: "<PERSON>, <PERSON>",
  };

  const dispatch = useDispatch<AppDispatch>();

  const userDetails = useUserDetails();

  const data = useSiteDetails();

  const [selectedHospital, setSelectedHospital] = useState<string | null>(
    initialData.hospital
  );
  const [selectedState, setSelectedState] = useState<string | null>(
    initialData.state
  );
  const [selectedPhysician, setSelectedPhysician] = useState<string | null>(
    initialData.physician
  );

  const handleSave = () => {
    // Logic for saving data
  };

  // const [test, setTest] = React.useState(["Legacy Emanuel"]);

  const handleCancel = () => {
    // Reset the fields to the initial data
    setSelectedHospital(initialData.hospital);
    setSelectedState(initialData.state);
    setSelectedPhysician(initialData.physician);
  };

  return (
    <CustomCard>
      <View className="bg-primaryBg rounded-md p-3">
        <View>
          <View className="flex-row justify-between items-center mb-3">
            <Text className="text-primaryBlack font-medium w-[40%]">
              Hospital
            </Text>
            <View className="flex-1">
              <CustomText value={data?.hospital} />
            </View>
          </View>

          {/* State Picker */}
          <View className="flex-row justify-between items-center mb-3">
            <Text className="text-primaryBlack font-medium w-[40%]">State</Text>
            <View className="flex-1">
              <CustomText value={data?.state} />
            </View>
          </View>

          {/* Implanting Physician Picker */}
          <View className="flex-row justify-between items-center mb-3">
            <Text className="text-primaryBlack font-medium w-[40%]">
              Implanting Physician
            </Text>
            <View className="flex-1">
              <SearchablePicker
                placeholder="Select Physician"
                items={data?.implanting_physician_options || []}
                value={data?.implanting_physician_selected_id || ""}
                onValueChange={(option) => {
                  dispatch(
                    setPreopUserDetails({
                      implanting_physician: {
                        ...userDetails?.implanting_physician,
                        selected: {
                          ...userDetails?.implanting_physician?.selected,
                          id: option.value,
                        },
                      },
                    })
                  );
                }}
              />
            </View>
          </View>
        </View>

        {/* Action Buttons */}
        <View className="flex-row justify-center gap-4 mt-6">
          <TouchableOpacity
            onPress={handleSave}
            className="border border-primaryPurple px-6 py-3 rounded-full bg-primaryPurple"
          >
            <Text className="text-primaryWhite font-semibold">Save</Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={handleCancel}
            className="border border-primaryPurple px-6 py-3 rounded-full bg-primaryBg"
          >
            <Text className="text-primaryPurple">Cancel</Text>
          </TouchableOpacity>
        </View>
      </View>
    </CustomCard>
  );
};

export default SiteDetailsCard;
