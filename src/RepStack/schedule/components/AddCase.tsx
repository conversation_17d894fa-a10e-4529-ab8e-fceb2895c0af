import React, { useEffect, useState } from "react";
import {
  View,
  Modal,
  TouchableOpacity,
  Text,
  Button,
  StyleSheet,
} from "react-native";
import DatePicker from "react-native-date-picker";
import SearchablePicker from "../../../components/SearchablePicker";
import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons";
import SaveActionButton from "../../../components/SaveActionButton";

import { useDispatch } from "react-redux";
import { AppDispatch } from "../../../store";
import Loader from "../../../components/Loader";
import {
  useSiteList,
  usePatientsList,
  useLoadersState,
} from "../hooks/schedulesHooks";
import moment from "moment";
import {
  fetchSiteList,
  fetchPatientsList,
  putRepCase,
} from "../../../store/rep/ScheduleStack/schedules/thunk";
import CustomText from "../../../components/CustomText";
import LineSeperator from "../../../components/LineSeperator";
import CustomCard from "../../../components/CustomCard";

interface AddCaseProps {
  onSuccess?: () => void;
}

const AddCase: React.FC<AddCaseProps> = ({ onSuccess }) => {
  const dispatch = useDispatch<AppDispatch>();

  // State variables
  const [date, setDate] = useState(new Date());
  const [open, setOpen] = useState(false);
  const [siteList, setSiteList] = useState<{ label: string; value: number }[]>(
    []
  );
  const [siteSelected, setSiteSelected] = useState<number>(0);
  const [patients, setPatients] = useState<{ label: string; value: number }[]>(
    []
  );
  const [selectedPatient, setSelectedPatient] = useState<number>(0);

  // Hooks for fetched data and loader
  const siteListData = useSiteList();
  const patientsList = usePatientsList();
  const { patientLoader } = useLoadersState();

  // Clean up states on close or unmount
  const unMount = () => {
    setSiteList([]);
    setSiteSelected(0);
    setPatients([]);
    setSelectedPatient(0);
  };

  const cleanState = () => {
    setSiteSelected(0);
    setPatients([]);
    setSelectedPatient(0);
  };

  // Fetch patient list based on selected site and date
  const fetchPatientList = async () => {
    try {
      const response = await dispatch(
        fetchPatientsList({
          site_id: siteSelected,
          case_date: date.toISOString().split("T")[0],
        })
      );
      if (response.payload && Array.isArray(response.payload)) {
        setPatients(
          response.payload.map((patient: any) => ({
            label: patient.patient.name,
            value: patient.case_id,
          }))
        );
      }
    } catch (error) {
      console.error("Error fetching patient list:", error);
    }
  };

  // Handle adding a case
  const handleAddCase = async () => {
    try {
      await dispatch(putRepCase({ case_id: selectedPatient }));
      cleanState();
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error("Error adding case:", error);
    }
  };

  // Effects to fetch site list when modal opens and cleanup on close
  useEffect(() => {
    dispatch(fetchSiteList());
  }, [dispatch]);

  // Update siteList when siteListData changes
  useEffect(() => {
    if (siteListData && Array.isArray(siteListData)) {
      setSiteList(
        siteListData.map((site: any) => ({
          label: site.name,
          value: site.id,
        }))
      );
    }
  }, [siteListData]);

  // Fetch patients when site selection changes or date changes
  useEffect(() => {
    if (siteSelected) {
      fetchPatientList();
    }
  }, [siteSelected, date]);

  const handleCancel = () => {
    cleanState();
  };

  return (
    <>
      {patientLoader ? (
        <View className="mt-64">
          <Loader />
        </View>
      ) : (
        <View className="p-3 bg-primaryWhite">
          <View className="rounded-md p-3">
            <Text className="text-primaryPurple text-center font-semibold text-lg mb-5">
              Assign Case
            </Text>
            <View className="flex-row justify-between items-center">
              <Text className="text-primaryBlack font-medium w-[40%]">
                Date
              </Text>
              <View className="flex-1">
                <TouchableOpacity
                  onPress={() => setOpen(true)}
                  className={`bg-primaryWhite border h-[48px] rounded-md justify-center px-3 ${
                    date ? "border-primaryPurple" : "border-red-3"
                  }`}
                >
                  <Text className="text-primaryBlack">
                    {date ? moment(date).format("MM-DD-YYYY") : "Select Date"}
                  </Text>
                </TouchableOpacity>
              </View>
            </View>
            <LineSeperator extraStyle="my-5" />

            <View className="flex-row justify-between items-center">
              <Text className="text-primaryBlack font-medium w-[40%]">
                Site
              </Text>
              <View className="flex-1">
                <SearchablePicker
                  placeholder={"Select site"}
                  items={siteList}
                  value={siteSelected}
                  onValueChange={(value) => setSiteSelected(value.value)}
                />
              </View>
            </View>
            <LineSeperator extraStyle="my-5" />
            {siteSelected ? (
              patients.length > 0 ? (
                <View className="flex-row justify-between items-center">
                  <Text className="text-primaryBlack font-medium w-[40%]">
                    Patient
                  </Text>
                  <View className="flex-1">
                    <SearchablePicker
                      placeholder={"Select patient"}
                      items={patients}
                      value={selectedPatient}
                      height={40}
                      onValueChange={(value) => setSelectedPatient(value.value)}
                    />
                  </View>
                </View>
              ) : (
                <View className="mt-2 mb-2 rounded-md p-2 w-full items-center">
                  <CustomText
                    className="text-primaryPurple"
                    value={"No Patients found for selected site and date"}
                  />
                </View>
              )
            ) : null}
            {selectedPatient ? (
              <>
                <LineSeperator extraStyle="my-5" />
                <View className="w-full items-center mt-2">
                  <SaveActionButton
                    disabled={false}
                    onPress={handleAddCase}
                    onCancel={handleCancel}
                  />
                </View>
              </>
            ) : null}
          </View>
        </View>
      )}
      <DatePicker
        modal
        open={open}
        date={date ? moment(date, "YYYY-MM-DD").toDate() : new Date()}
        mode="date"
        onConfirm={(date) => {
          setDate(date);
          setOpen(false);
        }}
        onCancel={() => setOpen(false)}
      />
    </>
  );
};

export default AddCase;
