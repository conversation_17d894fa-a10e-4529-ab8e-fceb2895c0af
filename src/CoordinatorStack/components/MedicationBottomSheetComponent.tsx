import React, { forwardRef, useImperativeHandle } from "react";
import { TouchableOpacity, View } from "react-native";
import SearchablePicker from "../../components/SearchablePicker";
import Heading from "../../components/Heading";
import CustomText from "../../components/CustomText";
import { useMedicationData as usePostOpMedicationData } from "../schedule/hooks/postopHooks";
import { useMedicationData as usePreOpMedicationData } from "../schedule/hooks/preopHooks";
import {
  updateMedicationById as updatePostOpMedicationById,
  addMedicationById as addPostOpMedicationById,
} from "../../store/coordinator/ScheduleStack/postop";
import {
  updateMedicationById as updatePreOpMedicationById,
  addMedicationById as addPreOpMedicationById,
} from "../../store/coordinator/ScheduleStack/preop";
import { useDispatch } from "react-redux";
import { AppDispatch } from "../../store";
import CustomInput from "../../components/CustomTextInput";
import { genUUID, parseIntInput } from "../../utils";
import { BottomSheetTextInput } from "@gorhom/bottom-sheet";
import PopupModal from "../../components/Popup";
import SaveActionButton from "../../components/SaveActionButton";

interface IMedicationBottomSheetComponentProps {
  medicineId?: string;
  bottomSheetClose?: () => void;
  medKey: string;
  isPreOp?: boolean;
}

export interface MedicationBottomSheetHandle {
  stateCleanUp: () => void;
}

const MedicationBottomSheetComponent = forwardRef<
  MedicationBottomSheetHandle,
  IMedicationBottomSheetComponentProps
>(({ medicineId, bottomSheetClose, medKey, isPreOp }, ref) => {
  let anticoagulationOptions,
    med_selected_to_edit,
    duration_options,
    frequency_options,
    findDosageOptions;

  if (isPreOp) {
    ({
      anticoagulationOptions,
      med_selected_to_edit,
      frequency_options,
      findDosageOptions,
    } = usePreOpMedicationData(medicineId));
  } else {
    ({
      anticoagulationOptions,
      med_selected_to_edit,
      duration_options,
      frequency_options,
      findDosageOptions,
    } = usePostOpMedicationData(medicineId));
  }

  const dispatch = useDispatch<AppDispatch>();

  const [modalVisible, setModalVisible] = React.useState(false);
  const [popupMsg, setPopupMsg] = React.useState<string[]>([]);

  // Local state for inputs
  const [MedicationName, setMedicationName] = React.useState<string>("");
  const [selectMedication, setSelectMedication] = React.useState<string>("");
  const [selectDosage, setSelectDosage] = React.useState<string>("");
  const [selectDosageOptions, setSelectDosageOptions] =
    React.useState<any>(null);
  const [selectFrequencyName, setSelectFrequencyName] =
    React.useState<string>("");
  const [selectFrequency, setSelectFrequency] = React.useState<string>("");
  const [selectDuration, setSelectDuration] = React.useState<string>("");
  const [selectPeriodName, setSelectPeriodName] = React.useState<string>("");
  const [selectPeriod, setSelectPeriod] = React.useState<string>("");

  const [originalMed, setOriginalMed] = React.useState({
    MedicationName: "",
    selectMedication: "",
    selectDosage: "",
    selectFrequency: "",
    selectFrequencyName: "",
    selectDuration: "",
    selectPeriod: "",
    selectPeriodName: "",
  });

  const stateCleanUp = () => {
    setMedicationName("");
    setSelectMedication("");
    setSelectDosage("");
    setSelectDosageOptions(null);
    setSelectFrequency("");
    setSelectFrequencyName("");
    setSelectDuration("");
    setSelectPeriod("");
    setSelectPeriodName("");
  };

  const loadStates = () => {
    const medData = {
      MedicationName: med_selected_to_edit?.med_name || "",
      selectMedication: med_selected_to_edit?.med_id || "",
      selectDosage: med_selected_to_edit?.med_dose?.toString() || "",
      selectFrequency:
        med_selected_to_edit?.dosing_frequency_id?.toString() || "",
      selectFrequencyName: med_selected_to_edit?.dosing_frequency || "",
      selectDuration: med_selected_to_edit?.period_id?.toString() || "",
      selectPeriod: med_selected_to_edit?.period_count?.toString() || "",
      selectPeriodName: med_selected_to_edit?.period || "",
    };

    setMedicationName(medData.MedicationName);
    setSelectMedication(medData.selectMedication);
    const doseOptions = findDosageOptions(medData.selectMedication);
    setSelectDosageOptions(doseOptions);
    setSelectDosage(medData.selectDosage);
    setSelectFrequency(medData.selectFrequency);
    setSelectFrequencyName(medData.selectFrequencyName);
    setSelectDuration(medData.selectDuration);
    setSelectPeriod(medData.selectPeriod);
    setSelectPeriodName(medData.selectPeriodName);

    // Store the original data for later diff checks
    setOriginalMed(medData);
  };

  const isFormChanged = () => {
    return (
      MedicationName !== originalMed.MedicationName ||
      selectMedication !== originalMed.selectMedication ||
      selectDosage !== originalMed.selectDosage ||
      selectFrequency !== originalMed.selectFrequency ||
      selectFrequencyName !== originalMed.selectFrequencyName ||
      selectDuration !== originalMed.selectDuration ||
      selectPeriod !== originalMed.selectPeriod ||
      selectPeriodName !== originalMed.selectPeriodName
    );
  };

  const disabled = !isFormChanged();

  React.useEffect(() => {
    if (med_selected_to_edit) {
      loadStates();
    }

    return () => {
      stateCleanUp();
    };
  }, [med_selected_to_edit, medicineId, medKey]);

  React.useEffect(() => {
    const doseOptions = findDosageOptions(selectMedication);
    setSelectDosageOptions(doseOptions);
  }, [selectMedication]);

  useImperativeHandle(ref, () => ({
    stateCleanUp,
  }));

  const validator = () => {
    setPopupMsg([]);
    if (!MedicationName) {
      setPopupMsg((prev) => [...prev, "Please Enter a Medication Name"]);
      return false;
    }
    if (!selectMedication) {
      setPopupMsg((prev) => [...prev, "Please Select a Medication"]);
      return false;
    }
    if (!selectDosage) {
      setPopupMsg((prev) => [...prev, "Please Select a Dosage"]);
      return false;
    }
    if (MedicationName?.toLowerCase() != "warfarin" && !selectFrequency) {
      setPopupMsg((prev) => [...prev, "Please Select a Frequency"]);
      return false;
    }
    if (!isPreOp) {
      if (!selectDuration) {
        setPopupMsg((prev) => [...prev, "Please Select a Duration"]);
        return false;
      }
      if (selectPeriodName?.toLowerCase() !== "indefinitely" && !selectPeriod) {
        setPopupMsg((prev) => [...prev, "Please Select a Period"]);
        return false;
      }
    }

    return true;
  };

  const handleSave = () => {
    if (medicineId) {
      if (validator()) {
        const updatedMed = {
          med_name: MedicationName,
          med_id: selectMedication,
          med_dose: selectDosage,
          dosing_frequency: selectFrequencyName,
          dosing_frequency_id: selectFrequency,
          period: selectPeriodName,
          period_id: selectDuration,
          period_count:
            selectPeriodName?.toLowerCase() === "indefinitely"
              ? ""
              : selectPeriod,
        };
        if (isPreOp) {
          dispatch(updatePreOpMedicationById({ id: medicineId, updatedMed }));
        } else {
          dispatch(updatePostOpMedicationById({ id: medicineId, updatedMed }));
        }
        bottomSheetClose && bottomSheetClose();
      } else {
        setModalVisible(true);
      }
    } else {
      if (validator()) {
        const addMed = {
          id: genUUID(),
          med_name: MedicationName,
          med_id: selectMedication,
          med_dose: selectDosage,
          dosing_frequency: selectFrequencyName,
          dosing_frequency_id: selectFrequency,
          period: selectPeriodName,
          period_id: selectDuration,
          period_count:
            selectPeriodName?.toLowerCase() === "indefinitely"
              ? ""
              : selectPeriod,
        };
        if (isPreOp) {
          dispatch(addPreOpMedicationById(addMed));
        } else {
          dispatch(addPostOpMedicationById(addMed));
        }
        bottomSheetClose && bottomSheetClose();
      } else {
        setModalVisible(true);
      }
    }
  };

  return (
    <View className="flex-1 p-3">
      {/* Select Medication */}
      <View className="mb-6">
        <Heading text="Medication" size="sub-heading" showSeperator={false} />
        <SearchablePicker
          items={anticoagulationOptions || []}
          value={selectMedication}
          placeholder="Select"
          onValueChange={(value) => {
            setMedicationName(value.label);
            setSelectMedication(value.value);
            setSelectDosage("");
          }}
        />
      </View>

      {/* Select Dosage */}
      <View className="mb-6">
        {MedicationName?.toLowerCase() === "warfarin" ? (
          <Heading text="Range" size="sub-heading" showSeperator={false} />
        ) : (
          <Heading text="Dosage" size="sub-heading" showSeperator={false} />
        )}

        <SearchablePicker
          items={
            selectDosageOptions?.sort(
              (a: { label: number }, b: { label: number }) => a.label - b.label
            ) || []
          }
          value={selectDosage}
          placeholder="Select"
          onValueChange={(value) => setSelectDosage(value?.value)}
          disable={selectMedication ? true : false}
        />
      </View>

      {/* Select Frequency */}

      {MedicationName?.toLowerCase() != "warfarin" && (
        <View className="mb-6">
          <Heading text="Frequency" size="sub-heading" showSeperator={false} />
          <SearchablePicker
            items={frequency_options || []}
            value={selectFrequency}
            placeholder="Select"
            onValueChange={(value) => {
              setSelectFrequency(value.value);
              setSelectFrequencyName(value.label);
            }}
            disable={selectDosage ? true : false}
          />
        </View>
      )}

      {/* Select Duration */}
      {!isPreOp && (
        <View className="flex-row justify-between items-center gap-2 mb-6">
          <View className="flex-1">
            <Heading text="Duration" size="sub-heading" showSeperator={false} />
            <SearchablePicker
              items={duration_options || []}
              value={selectDuration}
              placeholder="Select"
              onValueChange={(value) => {
                setSelectDuration(value.value);
                setSelectPeriodName(value.label);
              }}
              disable={
                MedicationName?.toLowerCase() != "warfarin"
                  ? selectFrequency
                    ? true
                    : false
                  : true
              }
            />
          </View>

          {!isPreOp && selectPeriodName?.toLowerCase() !== "indefinitely" && (
            <View className="flex-1">
              <Heading
                text="Period"
                size="sub-heading"
                showSeperator={false}
                extraStyle="mb-1"
              />

              <BottomSheetTextInput
                className={`bg-primaryWhite py-3.5 px-2 rounded-md border ${
                  selectDuration
                    ? selectPeriod
                      ? "border-primaryPurple"
                      : "border-red-3"
                    : "border-primaryGray"
                }  text-primaryBlack`}
                value={selectPeriod}
                onChangeText={(value) => {
                  if (Number(value) === 0) {
                    setSelectPeriod("");
                    return;
                  }

                  parseIntInput(value, (parsedValue) => {
                    if (parsedValue > 0) {
                      setSelectPeriod(parsedValue.toString());
                    } else {
                      setSelectPeriod("");
                    }
                  });
                }}
                keyboardType="numeric"
                editable={selectDuration ? true : false}
              />
            </View>
          )}
        </View>
      )}

      {/* Select Period if applicable */}

      {/* Action Buttons */}
      <View className="flex items-center">
        {/* <TouchableOpacity
          className={`border 
                    ${
                      disabled
                        ? `bg-primaryGray border-primaryGray`
                        : `bg-primaryPurple border-primaryPurple`
                    }
                     px-6 py-3 rounded-full`}
          disabled={disabled}
          onPress={handleSave}
        >
          <CustomText
            value={medicineId ? "Update" : "Add"}
            className="text-primaryWhite text-center"
          />
        </TouchableOpacity>
        <TouchableOpacity
          className="bg-primaryWhite p-3 rounded-full border-primaryPurple border w-[20%]"
          onPress={() => {
            stateCleanUp();
            bottomSheetClose && bottomSheetClose();
          }}
        >
          <CustomText
            value="Cancel"
            className="text-primaryPurple text-center"
          />
        </TouchableOpacity> */}
        <SaveActionButton
          disabled={disabled}
          onPress={handleSave}
          onCancel={() => {
            stateCleanUp();
            bottomSheetClose && bottomSheetClose();
          }}
        />
      </View>
      <PopupModal
        show={modalVisible}
        msg={popupMsg}
        status="warning"
        onClose={() => setModalVisible(false)}
      />
    </View>
  );
});

export default MedicationBottomSheetComponent;
