import React from "react";
import { createStackNavigator } from "@react-navigation/stack";
import ScheduleScreen from "./screens/ScheduleScreen";
import PatientDetailsScreen from "./screens/PatientDetailsScreen";
import CTAScreen from "./screens/CTAScreen";
import PreOpTEEScreen from "./screens/PreOpTEEScreen";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import PDFViewer from "./screens/PdfViewer";
import WebViewer from "./screens/WebViewScreen";
import SettingScreen from "./screens/SettingsScreen";

const ScheduleStack = createStackNavigator();

export default function ScheduleScreenStack(): React.JSX.Element {
  const insets = useSafeAreaInsets();

  return (
    <ScheduleStack.Navigator>
      <ScheduleStack.Screen
        name="Schedule"
        component={ScheduleScreen}
        options={{
          headerShown: true,

          headerStyle: {
            height: insets.top,
          },
        }}
      />
      <ScheduleStack.Screen
        name="Patient Details"
        component={PatientDetailsScreen}
        // options={{headerBackTitle: 'Homepage'}}
      />
      <ScheduleStack.Screen
        name="CTA"
        component={CTAScreen}
        options={{ title: "  PRE-OP CTA" }}
      />
      <ScheduleStack.Screen
        name="TEE"
        component={PreOpTEEScreen}
        options={{ title: "PRE-OP TEE" }}
      />
      <ScheduleStack.Screen
        name="PdfViewer"
        component={PDFViewer}
        options={{ title: "" }}
      />
      <ScheduleStack.Screen
        name="WebViewer"
        component={WebViewer}
        options={{ title: "" }}
      />
      <ScheduleStack.Screen
        name="SettingsScreen"
        component={SettingScreen}
        options={{ title: "Settings" }}
      />
    </ScheduleStack.Navigator>
  );
}
