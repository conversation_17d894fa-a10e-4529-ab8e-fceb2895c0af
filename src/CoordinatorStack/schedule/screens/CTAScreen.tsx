// CTAScreen.js

import React from "react";
import { View, Image, Text, TextInput, TouchableOpacity } from "react-native";
import Heading from "../../../components/Heading";
import ScreenWrapper from "../../../components/ScreenWrapper";
import CustomCard from "../../../components/CustomCard";
import LineSeperator from "../../../components/LineSeperator";
import {
  fetchCTADeails,
  fetchTruplanUrl,
  putCtaScreenDetails,
} from "../../../store/coordinator/ScheduleStack/preop-cta/thunk";
import { useCoordinatorSelectedPatient } from "../hooks/schedulesHooks";
import { useDispatch } from "react-redux";
import { AppDispatch } from "../../../store";
import Loader from "../../../components/Loader";
import {
  useCTADetails,
  useLoaderAndError,
  useTruplanLink,
} from "../hooks/ctaHooks";
import CustomText from "../../../components/CustomText";
import globalStyles from "../../../styles/GlobalStyles";
import { useNavigation } from "@react-navigation/native";
import CustomInput from "../../../components/CustomTextInput";
import SaveActionButton from "../../../components/SaveActionButton";
import { MatIcon } from "../../../utils";
import {
  setCtaNotes,
  resetCtaNotes,
} from "../../../store/coordinator/ScheduleStack/preop-cta";

interface ICTAScreenProps {
  route: {
    params: {
      morphology: string;
      ostium: string;
      depth: string;
      clotData: string[];
    };
  };
}

const CTAScreen: React.FunctionComponent<ICTAScreenProps> = ({ route }) => {
  const dispatch = useDispatch<AppDispatch>();
  const selectedPatient = useCoordinatorSelectedPatient();
  const navigation = useNavigation();
  const truplanLink = useTruplanLink();

  const {
    max_diameter,
    area,
    avg_diameter,
    comments,
    depth,
    laa_cross_section_url,
    laa_long_axis_url,
    min_diameter,
    perimeter,
    study,
    notes,
  } = useCTADetails();

  const laainfo = [
    {
      label: "Max Diameter",
      value: `${max_diameter} mm`,
    },
    {
      label: "Min Diameter",
      value: `${min_diameter} mm`,
    },
    {
      label: "Avg Diameter",
      value: `${avg_diameter} mm`,
    },
    {
      label: "Area",
      value: `${area} mm²`,
    },
    {
      label: "Perimeter",
      value: `${perimeter} mm`,
    },
    {
      label: "Depth",
      value: `${depth} mm`,
    },
  ];
  const { loader, error } = useLoaderAndError();

  const fetchDetails = async () => {
    if (selectedPatient?.case_id) {
      const res = await dispatch(
        fetchCTADeails({ case_id: selectedPatient.case_id })
      );

      const truplanLink = await dispatch(
        fetchTruplanUrl({ case_id: selectedPatient.case_id })
      );
    }
  };

  const saveDetails = async () => {
    try {
      const res = await dispatch(
        putCtaScreenDetails({
          case_id: selectedPatient?.case_id,
          payload: {
            notes,
          },
        })
      );
      await fetchDetails();
    } catch (err) {
      console.error(err);
    }
  };

  React.useEffect(() => {
    fetchDetails();
  }, []);

  const handleRefresh = async () => {
    await fetchDetails();
  };

  if (loader) {
    return <Loader />;
  }

  return (
    <ScreenWrapper onRefresh={handleRefresh}>
      <CustomCard>
        <View className={`${globalStyles.containers.flex_end}`}>
          <View className={`${globalStyles.containers.flex_between_row} gap-2`}>
            <TouchableOpacity
              disabled={truplanLink ? false : true}
              className={`${globalStyles.buttons.rounded.md} 
              ${truplanLink ? "bg-primaryPurple" : "bg-secondaryGray"}
              my-2`}
              onPress={() => {
                navigation.navigate("PdfViewer", {
                  url: truplanLink,
                });
              }}
            >
              <CustomText
                value={"View pdf"}
                className="font-semibold text-md"
                color="text-primaryWhite"
              />
            </TouchableOpacity>

            <TouchableOpacity
              className={`${globalStyles.buttons.rounded.md}  ${
                study[0]?.viewer_link ? "bg-primaryPurple" : "bg-secondaryGray"
              }`}
              disabled={study[0]?.viewer_link ? false : true}
              onPress={() => {
                navigation.navigate("WebViewer", {
                  link: study[0]?.viewer_link,
                });
              }}
            >
              <CustomText
                value={"View Images"}
                className="font-semibold text-md"
                color="text-primaryWhite"
              />
            </TouchableOpacity>
          </View>
        </View>
        <View className={globalStyles.containers.flex_between_row}>
          <Heading
            text="LAA Information"
            size="sub-heading"
            showSeperator={false}
          />
        </View>
        <View className="flex-column justify-center pt-2">
          {laainfo?.map((info, index) => (
            <View className="flex-row justify-between items-center px border-b border-primaryGray py-2">
              <Text className="text-primaryBlack text-xs m-2">
                {info.label}
              </Text>
              <Text className="text-primaryBlack text-xs m-2">
                {info.value}
              </Text>
            </View>
          ))}
        </View>

        <LineSeperator extraStyle="my-5" />

        <View>
          <Heading
            text="LAA Cross-Section"
            size="sub-heading"
            extraStyle="mb-3"
            showSeperator={false}
          />
          <View className="bg-primaryBlack">
            <Image
              source={{ uri: laa_cross_section_url }}
              className="w-[100%] h-[250px]"
              resizeMode="contain"
            />
          </View>
        </View>

        <LineSeperator extraStyle="my-5" />

        <View>
          <Heading
            text="LAA Long Axis"
            size="sub-heading"
            extraStyle="mb-3"
            showSeperator={false}
          />
          <View className="bg-primaryBlack">
            <Image
              source={{ uri: laa_long_axis_url }}
              className="w-[100%] h-[250px]"
              resizeMode="contain"
            />
          </View>
        </View>
        <LineSeperator extraStyle="my-5" />

        <View>
          <Heading
            text="Comments"
            size="sub-heading"
            extraStyle="mb-3"
            showSeperator={false}
          />

          <CustomText
            value={comments}
            className="border border-secondaryGray rounded p-4"
          />
        </View>

        <LineSeperator extraStyle="my-5" />

        <View>
          <View className={`${globalStyles.containers.flex_between_row}`}>
            <Heading
              text="Notes"
              size="sub-heading"
              extraStyle="mb-3"
              showSeperator={false}
            />
          </View>

          <CustomText
            value={notes}
            className="border border-secondaryGray rounded p-4"
          />
        </View>

        <View className="mt-3"></View>
      </CustomCard>

      <View className="mt-9"></View>
    </ScreenWrapper>
  );
};

export default CTAScreen;
