import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { Site } from "./types";
import { fetchSites, fetchSpecificSite, postSite, putSite } from "./thunk";
import initialState from "./initialstate";

const sitesSlice = createSlice({
  name: "sites",
  initialState,
  reducers: {
    setSiteDetails: (state, action) => {
      state.userDetails = {
        ...state.userDetails,
        ...action.payload,
      };
    },

    resetSiteDetails: (state) => {
      state.userDetails = null;
    },
    setActiveTab: (
      state,
      action: PayloadAction<"sites" | "implanting-physicians">
    ) => {
      state.activeTab = action.payload;
    },
    showAddSiteModal: (state, action: PayloadAction<boolean>) => {
      state.showAddSiteModal = action.payload;
    },
    setSelectedSite: (state, action: PayloadAction<string>) => {
      state.selectedSite = action.payload;
    },
  },
  extraReducers: (builder) => {
    // Fetch Sites
    builder.addCase(fetchSites.pending, (state) => {
      state.loading = true;
      state.error = null;
      ``;
    });
    builder.addCase(
      fetchSites.fulfilled,
      (state, action: PayloadAction<Site>) => {
        state.loading = false;
      }
    );
    builder.addCase(fetchSites.rejected, (state, action) => {
      state.loading = false;
      state.error = action.error.message || "Failed to fetch sites";
    });

    // Fectch Specific Site
    builder.addCase(fetchSpecificSite.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(
      fetchSpecificSite.fulfilled,
      (state, action: PayloadAction<Site>) => {
        state.siteDetails = action.payload;
        state.userDetails = action.payload;
        state.loading = false;
      }
    );
    builder.addCase(fetchSpecificSite.rejected, (state, action) => {
      state.loading = false;
      state.error = action.error.message || "Failed to fetch site";
    });

    //Put Site
    builder.addCase(putSite.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(putSite.fulfilled, (state, action: PayloadAction<Site>) => {
      state.siteDetails = action.payload;
      state.userDetails = action.payload;
      state.loading = false;
    });
    builder.addCase(putSite.rejected, (state, action) => {
      state.loading = false;
      state.error = action.error.message || "Failed to update site";
    });

    // Add Site
    builder.addCase(postSite.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(
      postSite.fulfilled,
      (state, action: PayloadAction<Site>) => {
        state.siteDetails = action.payload;
        state.userDetails = action.payload;
        state.loading = false;
      }
    );
    builder.addCase(postSite.rejected, (state, action) => {
      state.loading = false;
      state.error = action.error.message || "Failed to add site";
    });
  },
});

export const {
  setActiveTab,
  showAddSiteModal,
  setSelectedSite,
  setSiteDetails,
  resetSiteDetails,
} = sitesSlice.actions;
export default sitesSlice.reducer;
