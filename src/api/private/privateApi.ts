// private.ts
import axios, { AxiosInstance, AxiosResponse } from "axios";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { CustomAxiosRequestConfig } from "./types";
import apiUrl from "../config";
import { Enviromnent } from "../config";
import routes from "../public/routes";
import { logout } from "../../store/auth";
import store from "../../store";

class PrivateApiService {
  private api: AxiosInstance;

  constructor() {
    this.api = axios.create({
      baseURL: apiUrl[Enviromnent],
    });

    this.initializeRequestInterceptor();
    this.initializeResponseInterceptor();
  }

  private async getAccessToken(): Promise<string | null> {
    return await AsyncStorage.getItem("accessToken");
  }

  private async getRefreshToken(): Promise<string | null> {
    return await AsyncStorage.getItem("refreshToken");
  }

  private async setTokens(
    accessToken: string,
    refreshToken: string
  ): Promise<void> {
    await AsyncStorage.setItem("accessToken", accessToken);
    await AsyncStorage.setItem("refreshToken", refreshToken);
  }

  private initializeRequestInterceptor() {
    const attachToken = async (
      config: CustomAxiosRequestConfig
    ): Promise<CustomAxiosRequestConfig> => {
      const token = await this.getAccessToken();
      if (token) {
        config.headers = config.headers || {};
        config.headers.Authorization = `Bearer ${token}`;
      }
      return config;
    };

    this.api.interceptors.request.use(
      (config) => attachToken(config as CustomAxiosRequestConfig),
      (error) => Promise.reject(error)
    );
  }

  private initializeResponseInterceptor() {
    this.api.interceptors.response.use(
      (response) => response,
      async (error) => {
        const originalRequest = error.config as CustomAxiosRequestConfig;

        if (error.response?.status === 401 && !originalRequest._retry) {
          console.warn("Access Token Failed");
          originalRequest._retry = true;

          try {
            const refreshToken = await this.getRefreshToken();

            if (!refreshToken) throw new Error("No refresh token available");

            const { status, data } = await this.api.post(routes.refresh, {
              refresh_token: refreshToken,
            });

            const { access_token, refresh_token } = data.result;

            if (access_token) {
              await this.setTokens(access_token, refresh_token);

              originalRequest.headers = originalRequest.headers || {};
              originalRequest.headers.Authorization = `Bearer ${access_token}`;

              return this.api(originalRequest);
            } else {
              console.warn("logout triggered from axios");
              store.dispatch(logout());
              throw new Error("Failed to refresh token");
            }
          } catch (refreshError) {
            if (refreshError.response.status === 400) {
              console.error("logout triggered from axios status 400");
              store.dispatch(logout());
            }
            return Promise.reject(refreshError);
          }
        }

        return Promise.reject(error);
      }
    );
  }

  // Method to make GET requests
  public async get<T = any>(
    url: string,
    config?: CustomAxiosRequestConfig
  ): Promise<AxiosResponse<T>> {
    return this.api.get<T>(url, config);
  }

  // Method to make POST requests
  public async post<T = any>(
    url: string,
    data?: any,
    config?: CustomAxiosRequestConfig
  ): Promise<AxiosResponse<T>> {
    return this.api.post<T>(url, data, config);
  }

  // Method to make PUT requests
  public async put<T = any>(
    url: string,
    data?: any,
    config?: CustomAxiosRequestConfig
  ): Promise<AxiosResponse<T>> {
    return this.api.put<T>(url, data, config);
  }

  // Method to make DELETE requests
  public async delete<T = any>(
    url: string,
    config?: CustomAxiosRequestConfig
  ): Promise<AxiosResponse<T>> {
    return this.api.delete<T>(url, config);
  }
}

export default new PrivateApiService();
