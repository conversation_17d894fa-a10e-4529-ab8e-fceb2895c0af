/**
 * Sample React Native App
 * https://github.com/facebook/react-native
 *
 * @format
 */

// import React, { useEffect, useState } from "react";
// import "./global.css";
// import type { PropsWithChildren } from "react";
// import {
//   SafeAreaView,
//   StatusBar,
//   useColorScheme,
//   Text,
//   View,
//   StyleSheet,
//   Platform,
//   Modal,
//   TouchableOpacity,
//   Linking,
//   ActivityIndicator,
//   Alert,
//   AppState,
//   Image,
// } from "react-native";
// import {
//   NavigationContainer,
//   useNavigationContainerRef,
// } from "@react-navigation/native";
// import { SafeAreaProvider } from "react-native-safe-area-context";
// import { Provider } from "react-redux";
// import store from "./src/store";
// import RBAC from "./src/RBAC";
// import { sentry } from "./package.json";
// import * as Sentry from "@sentry/react-native";
// import DeviceInfo from "react-native-device-info";
// import CormetrixLogo from "./assests/logo.png";
// import AsyncStorage from "@react-native-async-storage/async-storage";
// import {
//   configureReanimatedLogger,
//   ReanimatedLogLevel,
// } from "react-native-reanimated";
// import { Enviromnent } from "./src/api/config";
// import api from "./src/api/api";

// // This is the default configuration
// configureReanimatedLogger({
//   level: ReanimatedLogLevel.warn,
//   strict: false, // Reanimated runs in strict mode by default
// });

// Sentry.init({
//   dsn: "https://<EMAIL>/4508793589661696",
// });

// // Force Update Component
// interface ForceUpdateModalProps {
//   visible: boolean;
//   newVersion: string | null;
//   onUpdate: () => void;
// }

// const ForceUpdateModal = ({
//   visible,
//   newVersion,
//   onUpdate,
// }: ForceUpdateModalProps) => {
//   return (
//     <Modal visible={visible} transparent={true} animationType="fade">
//       <View style={forceUpdateStyles.overlay}>
//         <View style={forceUpdateStyles.modal}>
//           <View className="flex w-1/2 h-16 mx-auto justify-center items-center">
//             <Image source={CormetrixLogo} className="w-full h-full" />
//           </View>

//           <Text style={forceUpdateStyles.title}>Update Available</Text>

//           <Text style={forceUpdateStyles.message}>
//             A new version {newVersion} is available. Please update to continue
//             using the app.
//           </Text>

//           <TouchableOpacity
//             style={forceUpdateStyles.updateButton}
//             onPress={onUpdate}
//             activeOpacity={0.8}
//           >
//             <Text style={forceUpdateStyles.updateButtonText}>Update Now</Text>
//           </TouchableOpacity>
//         </View>
//       </View>
//     </Modal>
//   );
// };

// // Constants for AsyncStorage keys
// const UPDATE_REQUIRED_KEY = "update_required";
// const NEW_VERSION_KEY = "new_version";

// function App(): React.JSX.Element {
//   const [loading, setLoading] = useState(true);
//   const [forceUpdateRequired, setForceUpdateRequired] = useState(false);
//   const [newVersion, setNewVersion] = useState<string | null>(null);
//   const appState = React.useRef(AppState.currentState);

//   // Load update requirement state from AsyncStorage on startup
//   const loadUpdateState = async () => {
//     try {
//       // Only check for updates in PROD environment
//       if (Enviromnent !== "PROD") {
//         return false;
//       }

//       const [savedUpdateRequired, savedNewVersion] = await Promise.all([
//         AsyncStorage.getItem(UPDATE_REQUIRED_KEY),
//         AsyncStorage.getItem(NEW_VERSION_KEY),
//       ]);

//       if (savedUpdateRequired === "true" && savedNewVersion) {
//         setForceUpdateRequired(true);
//         setNewVersion(savedNewVersion);
//         // We still want to check for updates, but we'll show the modal immediately
//         checkForUpdate();
//         return true;
//       }
//       return false;
//     } catch (error) {
//       console.error("Failed to load update state:", error);
//       return false;
//     }
//   };

//   // Save update requirement state to AsyncStorage
//   const saveUpdateState = async (
//     isRequired: boolean,
//     version: string | null
//   ) => {
//     try {
//       await Promise.all([
//         AsyncStorage.setItem(UPDATE_REQUIRED_KEY, String(isRequired)),
//         version
//           ? AsyncStorage.setItem(NEW_VERSION_KEY, version)
//           : AsyncStorage.removeItem(NEW_VERSION_KEY),
//       ]);
//     } catch (error) {
//       console.error("Failed to save update state:", error);
//     }
//   };

//   // Handle app state changes (foreground/background)
//   React.useEffect(() => {
//     const subscription = AppState.addEventListener("change", (nextAppState) => {
//       // App came to foreground
//       if (
//         appState.current.match(/inactive|background/) &&
//         nextAppState === "active"
//       ) {
//         // Check again when app comes to foreground
//         loadUpdateState().then((hasStoredUpdate) => {
//           if (!hasStoredUpdate) {
//             checkForUpdate();
//           }
//         });
//       }

//       appState.current = nextAppState;
//     });

//     return () => {
//       subscription.remove();
//     };
//   }, []);

//   // Initial app load
//   React.useEffect(() => {
//     const initApp = async () => {
//       const hasStoredUpdate = await loadUpdateState();
//       if (!hasStoredUpdate) {
//         await checkForUpdate();
//       } else {
//         setLoading(false);
//       }
//     };

//     initApp();
//   }, []);

//   const isDarkMode = useColorScheme() === "dark";
//   const navigationRef = useNavigationContainerRef();

//   useEffect(() => {
//     const listener = navigationRef.addListener("state", () => {
//       const routes = navigationRef.getCurrentRoute();
//     });

//     return () => {
//       navigationRef.removeListener("state", listener);
//     };
//   }, [navigationRef]);

//   // Version check function
//   const checkForUpdate = async () => {
//     try {
//       // Only check for updates in PROD environment
//       if (Enviromnent !== "PROD") {
//         setLoading(false);
//         return;
//       }

//       setLoading(true);

//       // Get current app version from device
//       const currentVersion = DeviceInfo.getVersion();
//       const platform = Platform.OS === "ios" ? "IOS" : "ANDROID";

//       // Fetch version info using the API service with proper environment handling
//       const response = await api.public.get(`/version?platform=${platform}`);

//       if (
//         response.data &&
//         response.data.status === "success" &&
//         response.data.result
//       ) {
//         const { published_version, app_version } = response.data.result;

//         // Compare current device version with published store version
//         const needsUpdate =
//           compareVersions(currentVersion, published_version) < 0;

//         if (needsUpdate) {
//           // Set state and persist to storage
//           setNewVersion(app_version); // Show your custom app version
//           setForceUpdateRequired(true);
//           // Save update requirement to persistent storage
//           await saveUpdateState(true, app_version);
//         } else {
//           // If no update needed, clear any previously stored update requirement
//           await saveUpdateState(false, null);
//         }
//       } else {
//         console.warn("Invalid API response:", response.data);
//       }
//     } catch (error) {
//       console.error("Version check failed:", error);
//     } finally {
//       setLoading(false);
//     }
//   };

//   // Version comparison function
//   const compareVersions = (
//     currentVersion: string,
//     publishedVersion: string
//   ) => {
//     try {
//       const parseVersion = (version: string) => {
//         return version.split(".").map((num) => parseInt(num, 10));
//       };

//       const current = parseVersion(currentVersion);
//       const published = parseVersion(publishedVersion);

//       for (let i = 0; i < Math.max(current.length, published.length); i++) {
//         const currentPart = current[i] || 0;
//         const publishedPart = published[i] || 0;

//         if (currentPart < publishedPart) return -1;
//         if (currentPart > publishedPart) return 1;
//       }

//       return 0;
//     } catch (error) {
//       console.error("Error comparing versions:", error);
//       // Fallback to simple numeric comparison
//       const current = parseFloat(currentVersion);
//       const published = parseFloat(publishedVersion);

//       if (current < published) return -1;
//       if (current > published) return 1;
//       return 0;
//     }
//   };

//   // Handle update button press
//   const handleUpdatePress = () => {
//     // We should maintain the update required state even if the user returns without updating
//     // When the app comes back to foreground, it will check again

//     const storeUrl =
//       Platform.OS === "ios"
//         ? "https://apps.apple.com/us/app/atriai/id6740474499"
//         : "https://play.google.com/store/apps/details?id=com.atriai";

//     // Log when user is redirected to store
//     Linking.openURL(storeUrl)
//       .then(() => {
//         console.log("User redirected to store, update state persists");
//       })
//       .catch((err) => {
//         console.error("Failed to open store URL:", err);
//         Alert.alert(
//           "Error",
//           "Unable to open store. Please update the app manually from the App Store/Play Store."
//         );
//       });

//     // On app resume, the AppState listener will check if the app was actually updated
//   };

//   const CustomStatusBar = ({ backgroundColor, ...props }) => (
//     <View style={[styles.statusBar, { backgroundColor }]}>
//       <SafeAreaView>
//         <StatusBar
//           translucent
//           backgroundColor={backgroundColor}
//           {...props}
//           barStyle={"dark-content"}
//         />
//       </SafeAreaView>
//     </View>
//   );

//   Text.defaultProps = Text.defaultProps || {};
//   Text.defaultProps.allowFontScaling = false;

//   // Show loading screen while checking for updates
//   if (loading) {
//     return (
//       <View style={forceUpdateStyles.loadingContainer}>
//         <ActivityIndicator size="large" color="#8143d9" />
//       </View>
//     );
//   }

//   // Show force update modal if update is required
//   if (forceUpdateRequired) {
//     return (
//       <View style={forceUpdateStyles.loadingContainer}>
//         <ForceUpdateModal
//           visible={true}
//           newVersion={newVersion}
//           onUpdate={handleUpdatePress}
//         />
//       </View>
//     );
//   }

//   return (
//     <Provider store={store}>
//       <CustomStatusBar backgroundColor={"#ffffff"} />
//       <SafeAreaView style={container.AndroidSafeArea}>
//         <RBAC />
//       </SafeAreaView>
//     </Provider>
//   );
// }

// const container = StyleSheet.create({
//   AndroidSafeArea: {
//     flex: 1,
//     backgroundColor: "#8143d9",
//     paddingTop: Platform.OS === "android" ? StatusBar.currentHeight - 3 : 0,
//   },
// });

// const styles = StyleSheet.create({
//   statusBar: {
//     // height: STATUSBAR_HEIGHT,
//   },
//   content: {
//     flex: 1,
//     backgroundColor: "white",
//   },
// });

// const forceUpdateStyles = StyleSheet.create({
//   loadingContainer: {
//     flex: 1,
//     justifyContent: "center",
//     alignItems: "center",
//     backgroundColor: "#ffffff",
//   },
//   loadingText: {
//     marginTop: 16,
//     fontSize: 16,
//     color: "#666",
//   },
//   overlay: {
//     flex: 1,
//     backgroundColor: "rgba(0, 0, 0, 0.8)",
//     justifyContent: "center",
//     alignItems: "center",
//     padding: 20,
//   },
//   modal: {
//     backgroundColor: "white",
//     borderRadius: 20,
//     padding: 30,
//     width: "90%",
//     maxWidth: 400,
//     alignItems: "center",
//     shadowColor: "#000",
//     shadowOffset: {
//       width: 0,
//       height: 10,
//     },
//     shadowOpacity: 0.25,
//     shadowRadius: 20,
//     elevation: 20,
//   },
//   iconContainer: {
//     marginBottom: 20,
//   },
//   icon: {
//     fontSize: 60,
//   },
//   title: {
//     fontSize: 24,
//     fontWeight: "bold",
//     color: "#333",
//     marginBottom: 15,
//     textAlign: "center",
//     marginTop: 20,
//   },
//   message: {
//     fontSize: 16,
//     color: "#666",
//     textAlign: "center",
//     marginBottom: 10,
//     lineHeight: 22,
//   },
//   subMessage: {
//     fontSize: 14,
//     color: "#999",
//     textAlign: "center",
//     marginBottom: 30,
//     lineHeight: 20,
//   },
//   updateButton: {
//     backgroundColor: "#8143d9",
//     paddingHorizontal: 40,
//     paddingVertical: 15,
//     borderRadius: 30,
//     minWidth: 200,
//     shadowColor: "#8143d9",
//     shadowOffset: {
//       width: 0,
//       height: 4,
//     },
//     shadowOpacity: 0.3,
//     shadowRadius: 10,
//     elevation: 8,
//   },
//   updateButtonText: {
//     color: "white",
//     fontSize: 18,
//     fontWeight: "bold",
//     textAlign: "center",
//   },
// });

// export default App;

import React, { useEffect } from "react";
import "./global.css";
import type { PropsWithChildren } from "react";
import {
  SafeAreaView,
  StatusBar,
  useColorScheme,
  Text,
  View,
  StyleSheet,
  Platform,
} from "react-native";
import {
  NavigationContainer,
  useNavigationContainerRef,
} from "@react-navigation/native";
import { SafeAreaProvider } from "react-native-safe-area-context";
import { Provider } from "react-redux";
import store from "./src/store";
import RBAC from "./src/RBAC";
import { sentry } from "./package.json";
import * as Sentry from "@sentry/react-native";
import {
  configureReanimatedLogger,
  ReanimatedLogLevel,
} from "react-native-reanimated";

// This is the default configuration
configureReanimatedLogger({
  level: ReanimatedLogLevel.warn,
  strict: false, // Reanimated runs in strict mode by default
});

Sentry.init({
  dsn: "https://<EMAIL>/4508793589661696",
});

function App(): React.JSX.Element {
  React.useEffect(() => {
    // authCheck();
  }, []);
  const isDarkMode = useColorScheme() === "dark";
  const navigationRef = useNavigationContainerRef();

  useEffect(() => {
    const listener = navigationRef.addListener("state", () => {
      const routes = navigationRef.getCurrentRoute();
    });

    return () => {
      navigationRef.removeListener("state", listener);
    };
  }, [navigationRef]);

  const CustomStatusBar = ({ backgroundColor, ...props }) => (
    <View style={[styles.statusBar, { backgroundColor }]}>
      <SafeAreaView>
        <StatusBar
          translucent
          backgroundColor={backgroundColor}
          {...props}
          barStyle={"dark-content"}
        />
      </SafeAreaView>
    </View>
  );

  Text.defaultProps = Text.defaultProps || {};
  Text.defaultProps.allowFontScaling = false;

  return (
    <Provider store={store}>
      {/* <StatusBar barStyle={"dark-content"} /> */}
      <CustomStatusBar backgroundColor={"#ffffff"} />
      <SafeAreaView style={container.AndroidSafeArea}>
        <RBAC />
      </SafeAreaView>
    </Provider>
  );
}

const container = StyleSheet.create({
  AndroidSafeArea: {
    flex: 1,
    backgroundColor: "#8143d9",
    paddingTop: Platform.OS === "android" ? StatusBar.currentHeight - 3 : 0,
  },
});

const styles = StyleSheet.create({
  statusBar: {
    // height: STATUSBAR_HEIGHT,
  },
  content: {
    flex: 1,
    backgroundColor: "white",
  },
});

// export default Sentry.wrap(App);
// if (sentry) {
//   export default Sentry.wrap(App);
// } else {

// }

export default App;

// export default Sentry.wrap(App);
