import React, { useCallback, useEffect, useState } from "react";
import { ScrollView, RefreshControl } from "react-native";
import { useDispatch } from "react-redux";
import { useFocusEffect } from "@react-navigation/native";
import { AppDispatch } from "../../../store";

import { fetchSiteList } from "../../../store/rep/ScheduleStack/schedules/thunk";
import { fetchSites } from "../../../store/rep/ScheduleStack/addSite/thunk";

import {
  resetSiteDetails,
  showAddSiteModal,
} from "../../../store/rep/ScheduleStack/addSite";
import { setSelectedSite as siteSetSelectedSite } from "../../../store/rep/ScheduleStack/addSite";
import { setSelectedSite as physicianSetSelectedSite } from "../../../store/rep/ScheduleStack/addPhysician";

import SiteList from "../components/SiteList";

const SitesScreen: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const [refreshing, setRefreshing] = useState(false);

  const fetchData = async () => {
    await dispatch(fetchSites());
    await dispatch(fetchSiteList());
  };

  // 🔁 Fetch on first load
  useEffect(() => {
    fetchData();
  }, []);

  // 🔁 Auto reset and refetch on tab focus
  useFocusEffect(
    useCallback(() => {
      dispatch(resetSiteDetails());
      dispatch(siteSetSelectedSite(""));
      dispatch(physicianSetSelectedSite(""));
      dispatch(showAddSiteModal(false));
      fetchData();

      return () => {
        // Optional cleanup
      };
    }, [dispatch])
  );

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchData();
    setRefreshing(false);
  };

  const handleAddSite = () => {
    dispatch(siteSetSelectedSite(""));
    dispatch(physicianSetSelectedSite(""));
    dispatch(resetSiteDetails());
    dispatch(showAddSiteModal(true));
  };

  return (
    <ScrollView
      className="flex-1"
      refreshControl={
        <RefreshControl
          refreshing={refreshing}
          onRefresh={handleRefresh}
          colors={["#8143d9"]}
          tintColor={"#8143d9"}
        />
      }
    >
      <SiteList onAddPress={handleAddSite} />
    </ScrollView>
  );
};

export default SitesScreen;
