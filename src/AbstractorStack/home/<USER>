import * as React from "react";
import { createStackNavigator } from "@react-navigation/stack";
import HomeScreen from "./screens/HomeScreen";
import WebViewer from "../schedule/screens/WebViewScreen";

interface IHomeScreenStackProps {}

const HomeScreenStack: React.FunctionComponent<IHomeScreenStackProps> = (
  props
) => {
  const HomeStack = createStackNavigator();

  return (
    <HomeStack.Navigator>
      <HomeStack.Screen name="Home" component={HomeScreen} />
      <HomeStack.Screen
        name="WebViewer"
        component={WebViewer}
        options={{ title: "" }}
      />
    </HomeStack.Navigator>
  );
};

export default HomeScreenStack;
