import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import AsyncStorage from "@react-native-async-storage/async-storage";
import api from "../../api/api";
import { act } from "react";

interface User {
  [key: string]: any;
}

interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  loading: boolean;
  error: string | null;
  errorStatus: boolean;
}

const initialState: AuthState = {
  user: null,
  isAuthenticated: false,
  loading: false,
  error: null,
  errorStatus: false,
};

const authSlice = createSlice({
  name: "auth",
  initialState,
  reducers: {
    loginStart: (state) => {
      state.loading = true;
      state.error = null;
      state.errorStatus = false;
    },
    loginSuccess: (state, action: PayloadAction) => {
      state.isAuthenticated = true;
      state.loading = false;
    },
    loginFailure: (state, action: PayloadAction) => {
      state.loading = false;
      state.error = "Login Failed";
      state.errorStatus = true;
    },
    logoutSuccess: (state) => {
      state.user = null;
      state.isAuthenticated = false;
    },
    setAuthLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setAuthError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
  },
});

export const {
  loginStart,
  loginSuccess,
  loginFailure,
  logoutSuccess,
  setAuthLoading,
  setAuthError,
} = authSlice.actions;

export default authSlice.reducer;

// Thunk actions
export const login =
  (email: string, password: string) => async (dispatch: any) => {
    try {
      dispatch(loginStart());

      let response = await api.public.post(api.routes.publicRoutes.login, {
        username: email,
        password: password,
      });

      if (response.status === 200) {
        await AsyncStorage.setItem(
          "accessToken",
          response.data.result.access_token
        );
        await AsyncStorage.setItem(
          "refreshToken",
          response.data.result.refresh_token
        );

        dispatch(loginSuccess(response.data));
        return "login success";
      }
    } catch (error) {
      dispatch(loginFailure());
      console.error("error during login", error);
      return Promise.reject(new Error("Login Failed"));
    }
  };

const logoutApiCall = async () => {
  console.info("logout api call triggered");
  const payload = { refresh_token: await AsyncStorage.getItem("refreshToken") };
  return await api.private.post(api.routes.publicRoutes.logout, payload);
};

const removeTokens = async () => {
  await AsyncStorage.removeItem("accessToken");
  await AsyncStorage.removeItem("refreshToken");
};

export const logout = () => async (dispatch: any) => {
  try {
    const res = await logoutApiCall();
    console.info("logout api response", res.data);
    await removeTokens();
    dispatch(logoutSuccess());
  } catch (error) {
    dispatch(setAuthError("Failed to log out."));
    await removeTokens();
    dispatch(logoutSuccess());
  }
};
