interface Patient {
  case_id: string;
  patient: {
    afib_ablation: boolean;
    age: number;
    anticoagulation: string | null;
    cha2ds2_vasc: {
      score: number;
      calculation: {
        congestive_heart_failure: number;
        hypertension: number;
        age: number;
        diabetes_mellitus: number;
        stroke_tia_thromboembolic_event: number;
        vascular_disease: number;
        sex: number;
      };
    };
    cta: boolean;
    tee: boolean;
    id: string;
    name: string;
    rationale: string;
    referring_provider: any;
    sex: string;
  };
  procedure_date: string;
  procedure_time: string;
  // chadPress: void;
}

interface PatientState {
  patients: Patient[];
  loading: boolean;
  error: string | null;
  selectedDate: string | null;
  selectedHospitalName: string | null;
  selectedHospitalId: number | null;
  implantingPhysicianName: string | null;
  implantingPhysicianImage: string | null;
  hospitalImage: string | null;
  selectedPatient: Patient | null;
}

interface siteDetails {
  selectedDate: string;
  selectedHospitalName: string;
  selectedHospitalId: number | null;
  implantingPhysicianName: string | null;
  implantingPhysicianImage: string;
  hospitalImage: string | null;
}

export type { PatientState, siteDetails };
