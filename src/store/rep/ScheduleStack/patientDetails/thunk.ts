import { createAsyncThunk } from "@reduxjs/toolkit";
import api from "../../../../api/api";
import ReactNativeBlobUtil from "react-native-blob-util";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { Alert } from "react-native";

const fetchPreopDetails = createAsyncThunk(
  "schedule/fetchPreopDetails",
  async ({ case_id }: { case_id: string }, thunkAPI) => {
    try {
      const response = await api.private.get(
        api.routes.privateRoutes.rep.pre_op(case_id)
      );

      if (response.data && response.data.result) {
        return response.data.result;
      } else {
        return thunkAPI.rejectWithValue(
          'Invalid response format: Missing "result" field'
        );
      }
    } catch (error: any) {
      console.error("Error caught during API call:", error.message || error);
      return thunkAPI.rejectWithValue(
        error.response?.data?.message || "Failed to fetch demographic details"
      );
    }
  }
);

const puttruplanStatus = createAsyncThunk(
  "schedule/puttruplanStatus",
  async ({ case_id, payload }: { case_id: string; payload: any }, thunkAPI) => {
    try {
      console.info(
        "puttruplanStatus:",
        "case_id:",
        case_id,
        "payload:",
        payload
      );
      const response = await api.private.put(
        api.routes.privateRoutes.rep.preop_cta_put(case_id),
        payload
      );
      if (response.data && response.data.result) {
        return response.data.result;
      } else {
        return thunkAPI.rejectWithValue(
          'Invalid response format: Missing "result" field'
        );
      }
    } catch (error: any) {
      console.error(
        "Error caught during API call:",
        error.response.data || error
      );
      return thunkAPI.rejectWithValue("Failed to PUT truplanStatus");
    }
  }
);

const uploadPDF = createAsyncThunk(
  "schedule/uploadPDF",
  async ({ case_id, file }: { case_id: string; file: any }, thunkAPI) => {
    try {
      const dispatch = thunkAPI.dispatch;
      const url = api.private.post(
        api.routes.privateRoutes.coordinator.truplan_pdf_post(case_id)
      );

      const uploadResponse = await ReactNativeBlobUtil.fetch(
        "PUT",
        (
          await url
        ).data.result.blob_url,
        {
          "x-ms-blob-type": "BlockBlob",
          "Content-Type": "application/pdf",
        },
        ReactNativeBlobUtil.wrap(file[0].uri)
      );

      if (
        uploadResponse.respInfo.status >= 200 &&
        uploadResponse.respInfo.status < 300
      ) {
        // ✅ Ensure we **await** the `dispatch` call
        await dispatch(
          puttruplanStatus({
            case_id: case_id,
            payload: { truplan_upload_status: "In Progress" },
          })
        ).unwrap(); // Unwraps to throw if rejected

        Alert.alert("File Uploaded Successfully");
      } else {
        return thunkAPI.rejectWithValue(
          "File upload failed with status: " + uploadResponse.respInfo.status
        );
      }
    } catch (error: any) {
      console.error(error);
      console.error("Error caught during API call:", error.response?.data);
      return thunkAPI.rejectWithValue(
        error.response?.data?.message || "Failed to upload PDF"
      );
    }
  }
);

const fetchGroinAccess = createAsyncThunk(
  "schedule/fetchGroinAccess",
  async ({ case_id }: { case_id: string }, thunkAPI) => {
    try {
      const response = await api.private.get(
        api.routes.privateRoutes.rep.groin_access_get(case_id)
      );

      if (response.data && response.data.result) {
        return response.data.result;
      } else {
        return thunkAPI.rejectWithValue(
          'Invalid response format: Missing "result" field'
        );
      }
    } catch (error: any) {
      console.error("Error caught during API call:", error.message || error);
      return thunkAPI.rejectWithValue("Failed to fetch groin access details");
    }
  }
);

const putGroinAccess = createAsyncThunk(
  "schedule/putGroinAccess",
  async (
    { case_detail_id, payload }: { case_detail_id: string; payload: any },
    thunkAPI
  ) => {
    try {
      const response = await api.private.put(
        api.routes.privateRoutes.rep.groin_access_put(case_detail_id),
        payload
      );

      if (response.data && response.data.result) {
        return response.data.result;
      } else {
        return thunkAPI.rejectWithValue(
          'Invalid response format: Missing "result" field'
        );
      }
    } catch (error: any) {
      console.error("Error caught during API call:", error.message || error);
      return thunkAPI.rejectWithValue("Failed to SAVE groin access details");
    }
  }
);

const putPreopDetails = createAsyncThunk(
  "schedule/putPreopDetails",
  async ({ case_id, payload }: { case_id: string; payload: any }, thunkAPI) => {
    try {
      const response = await api.private.put(
        api.routes.privateRoutes.rep.pre_op(case_id),
        payload
      );

      if (response.data && response.data.result) {
        return response.data.result;
      } else {
        return thunkAPI.rejectWithValue(
          'Invalid response format: Missing "result" field'
        );
      }
    } catch (error: any) {
      console.error("Error caught during API call:", error.message || error);
      return thunkAPI.rejectWithValue(
        error.response?.data?.message || "Failed to update demographic details"
      );
    }
  }
);

export {
  fetchPreopDetails,
  uploadPDF,
  fetchGroinAccess,
  putGroinAccess,
  puttruplanStatus,
  putPreopDetails,
};
