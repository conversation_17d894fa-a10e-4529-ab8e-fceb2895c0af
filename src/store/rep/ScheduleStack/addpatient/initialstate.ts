import {
  IPostPatient,
  IReferringProvider,
  IImplantingPhysician,
} from "./types";

const initialState = {
  postPatient: {
    patientDetails: null as IPostPatient | null,
    userDetails: null as IPostPatient | null,
    loading: false,
    error: null as string | null,
  },

  referringProviders: {
    referringProviders: null as IRef<PERSON>ringProvider[] | null,
    loading: false,
    error: null as string | null,
  },

  implantingPhysicians: {
    implantingPhysicians: null as IImplantingPhysician[] | null,
    loading: false,
    error: null as string | null,
  },
};

export default initialState;
