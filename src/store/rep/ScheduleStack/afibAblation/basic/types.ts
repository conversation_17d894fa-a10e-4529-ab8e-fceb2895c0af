interface IAfibBasic {
  procedure_date: string | null;
  physician_details: {
    selected: {
      id: string | null;
      name: string | null;
      npi_number: string | null;
    };
    options: {
      id: string;
      name: string;
      npi_number: string;
    }[];
  };
  site_details: {
    selected: {
      id: string | null;
      name: string | null;
    };
    options: {
      id: string;
      name: string;
    }[];
  };
  groin_access_start_time: string | null;
  sheath_removal_end_time: string | null;
  study: {
    viewer_link: string;
    uuid: string;
    modality: string;
  }[];
}

export type { IAfibBasic };
