import { useSelector } from "react-redux";
import { RootState } from "../../../store";

const useCoordinatorTasks = () => {
  const { tasks } = useSelector((state: RootState) => state.coordinator.tasks);
  return tasks;
};

const useLoaderAndError = () => {
  const loader = useSelector(
    (state: RootState) => state.coordinator.tasks.loading
  );
  const error = useSelector(
    (state: RootState) => state.coordinator.tasks.error
  );
  return { loader, error };
};

export { useCoordinatorTasks, useLoaderAndError };
