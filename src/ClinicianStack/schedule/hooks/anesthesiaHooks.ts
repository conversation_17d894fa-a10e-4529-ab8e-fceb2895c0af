import { useSelector } from "react-redux";
import { RootState } from "../../../store";

const useAnesthesiaDetails = () => {
  const { anesthesiaDetails } = useSelector(
    (state: RootState) => state.rep.anesthesia
  );
  return anesthesiaDetails;
};

const useAnesthesiaUserDetails = () => {
  const { userDetails } = useSelector(
    (state: RootState) => state.rep.anesthesia
  );
  return userDetails;
};

const useLoaderAndError = () => {
  const loader = useSelector(
    (state: RootState) => state.rep.anesthesia.loading
  );
  const error = useSelector((state: RootState) => state.rep.anesthesia.error);
  return { loader, error };
};

const findAnesthesiaDiff = () => {
  const anesthesiaDetails = useAnesthesiaDetails();
  const userDetails = useAnesthesiaUserDetails();

  // If either anesthesiaDetails or userDetails is null/undefined, no diff
  if (!anesthesiaDetails || !userDetails) {
    return {
      hasDiff: false,
      currentValues: {},
    };
  }

  // Helper function to safely compare values, treating undefined/null as equal
  const safeCompare = (val1: any, val2: any) => {
    // If both are null/undefined, they're equal
    if (val1 == null && val2 == null) return true;
    // If one is null/undefined and the other isn't, they're different
    if ((val1 == null) !== (val2 == null)) return false;
    // Otherwise, compare normally
    return val1 === val2;
  };

  const diff = {
    anesthesia_type: safeCompare(
      anesthesiaDetails?.anesthesia?.selected.id,
      userDetails?.anesthesia?.selected.id
    ),

    imaging_type: safeCompare(
      anesthesiaDetails?.imaging?.selected.id,
      userDetails?.imaging?.selected.id
    ),

    catheter_type: safeCompare(
      anesthesiaDetails?.catheter?.selected.id,
      userDetails?.catheter?.selected.id
    ),

    fluid_bolus: safeCompare(
      anesthesiaDetails?.fluid_bolus?.selected.id,
      userDetails?.fluid_bolus?.selected.id
    ),

    la_pressure: safeCompare(
      anesthesiaDetails?.la_pressure,
      userDetails?.la_pressure
    ),
  };

  const currentValues = {
    anesthesia_type: userDetails?.anesthesia?.selected.id,
    procedure_imaging_type: userDetails?.imaging?.selected.id,
    catheter_type: userDetails?.catheter?.selected.id
      ? userDetails?.catheter?.selected.id
      : null,
    fluid_bolus: userDetails?.fluid_bolus?.selected.id,
    la_pressure: userDetails?.la_pressure,
  };

  return {
    hasDiff: Object.values(diff).includes(false),
    currentValues: currentValues,
  };
};

export {
  useAnesthesiaDetails,
  useAnesthesiaUserDetails,
  useLoaderAndError,
  findAnesthesiaDiff,
};
