import * as React from "react";
import { useState, useEffect, useRef } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  FlatList,
  Modal,
  ScrollView,
} from "react-native";

import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons";
import { useNavigation } from "@react-navigation/native";
import { useDispatch, useSelector } from "react-redux";
import LineSeperator from "../../../components/LineSeperator";
import moment from "moment";
import globalStyles from "../../../styles/GlobalStyles";
import { AppDispatch } from "../../../store";
import CustomText from "../../../components/CustomText";
import { MatIcon } from "../../../utils";
import Heading from "../../../components/Heading";
import CustomInput from "../../../components/CustomTextInput";
import SearchablePicker from "../../../components/SearchablePicker";
import DatePicker from "react-native-date-picker";
import SaveActionButton from "../../../components/SaveActionButton";
import { useFocusEffect } from "@react-navigation/native";
import { useSelectedPatient } from "../hooks/PatientsHooks";
import { fetchPreopDetails } from "../../../store/rep/ScheduleStack/patientDetails/thunk";
import { fetchSchedules } from "../../../store/rep/ScheduleStack/schedules/thunk";
import { fetchCases } from "../../../store/rep/ScheduleStack/patients/thunk";
import { useSiteDetails } from "../hooks/PatientsHooks";
import { deleteMedicationById } from "../../../store/rep/ScheduleStack/patientDetails";
import PopupModal from "../../../components/Popup";
import { parseCharInput } from "../../../utils";
import ToggleButton from "../../../components/ToggleButton";
import { setSiteDetails } from "../../../store/rep/ScheduleStack/patients";
import AddReferringProvider from "../components/AddReferringProvider";
// Import directly from @gorhom/bottom-sheet
import BottomSheetComponent, {
  BottomSheetRefProps,
} from "../../../components/BottomSheetComponent";

// Delete confirmation popup component
const DeleteConfirmationPopup = ({
  visible,
  onCancel,
  onConfirm,
  title,
  message,
}) => {
  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={visible}
      onRequestClose={onCancel}
    >
      <View
        style={{
          flex: 1,
          justifyContent: "center",
          alignItems: "center",
          backgroundColor: "rgba(0,0,0,0.5)",
        }}
      >
        <View
          style={{
            backgroundColor: "white",
            borderRadius: 10,
            padding: 20,
            width: "80%",
            alignItems: "center",
          }}
        >
          <Text style={{ fontSize: 18, fontWeight: "bold", marginBottom: 10 }}>
            {title}
          </Text>
          <Text style={{ textAlign: "center", marginBottom: 20 }}>
            {message}
          </Text>
          <View
            style={{
              flexDirection: "row",
              justifyContent: "space-between",
              width: "100%",
            }}
          >
            <TouchableOpacity
              onPress={onCancel}
              style={{
                padding: 10,
                backgroundColor: "#f0f0f0",
                borderRadius: 5,
                flex: 1,
                marginRight: 10,
                alignItems: "center",
              }}
            >
              <Text>Cancel</Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={onConfirm}
              style={{
                padding: 10,
                backgroundColor: "#ef4444",
                borderRadius: 5,
                flex: 1,
                alignItems: "center",
              }}
            >
              <Text style={{ color: "white" }}>Delete</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

import {
  usePreopDetails,
  useUserDetails,
  findPatientDemographicsDiff,
  formatPreOpDetails,
  useMedicationData,
  useLoaderAndError,
} from "../hooks/patientDetailsHooks";
import {
  useReferringProviderDetails,
  useImplantingPhysicianDetails,
  useLoaderAndErrorReferringProvider,
  useLoaderAndErrorImplantingPhysician,
} from "../hooks/addPatientHooks";
import { setPreopUserDetails } from "../../../store/rep/ScheduleStack/patientDetails";

import { fetchReferringProviders } from "../../../store/common/addReferringProvider/thunk";
import { fetchPcpProviders } from "../../../store/common/addPcpProvider/thunk";

import {
  // fetchReferringProviders,
  fetchImplantingPhysicians,
  fetchRationale,
} from "../../../store/rep/ScheduleStack/addpatient/thunk";
import { putPreopDetails } from "../../../store/rep/ScheduleStack/patientDetails/thunk";
import CustomCard from "../../../components/CustomCard";
import { useSchedules } from "../hooks/schedulesHooks";
import { useAutosave } from "../../../utils/AutoSave";
import Loader from "../../../components/Loader";
import { simpleAutoSave } from "../../../services/simpleAutoSave";
import { setSaveStatus, setLastSavedData } from "../../../store/services";

interface PreOpImaging {
  cta: boolean;
  tee: boolean;
}

interface IPatientDemographicsCardProps {
  onMedOpen: (id?: string) => void;
  onMedClose: () => void;
  onOpenReferringProviderSheet: () => void;
  onOpenPcpProviderSheet?: () => void;
  onOpenSiteSheet?: () => void;
  onOpenImplantingPhysicianSheet?: () => void;
  truplanButton: {
    onPress: () => void;
  };
  chadPress: (calculation: any) => void;
  hasBledPress: (calculation: any) => void;
  onReferringProviderAdded?: (provider: any) => void;
  onPcpProviderAdded?: (provider: any) => void;
  onSiteAdded?: (site: any) => void;
  onImplantingPhysicianAdded?: (physician: any) => void;
  lastAddedReferringProvider?: any;
  lastAddedPcpProvider?: any;
  lastAddedSite?: any;
  lastAddedImplantingPhysician?: any;
}

const PatientDemographicsCard: React.FunctionComponent<
  IPatientDemographicsCardProps
> = ({
  onMedOpen,
  onMedClose,
  onOpenReferringProviderSheet,
  onOpenPcpProviderSheet,
  onOpenSiteSheet,
  onOpenImplantingPhysicianSheet,
  truplanButton,
  chadPress,
  hasBledPress,
  onReferringProviderAdded,
  onPcpProviderAdded,
  onSiteAdded,
  onImplantingPhysicianAdded,
  lastAddedReferringProvider,
  lastAddedPcpProvider,
  lastAddedSite,
  lastAddedImplantingPhysician,
}) => {
  const navigation = useNavigation();
  const dispatch = useDispatch<AppDispatch>();

  const selectedPatient = useSelectedPatient();
  const userDetails = useUserDetails();
  const preopDetails = usePreopDetails();
  const schedules = useSchedules();
  const { loader, error } = useLoaderAndError();
  const morphData = [];
  const [open, setOpen] = useState(false);
  const [medDeleted, setMedDeleted] = useState(false);
  const [medToDelete, setMedToDelete] = useState<string | null>(null);
  const { med_data } = useMedicationData();
  const [selected, setSelected] = useState([]);
  const [initialUpdate, setInitialUpdate] = useState(false);
  const [modalVisible, setModalVisible] = React.useState(false);
  const [popupMsg, setPopupMsg] = React.useState<string[]>([]);
  const [deleteConfirmationVisible, setDeleteConfirmationVisible] =
    useState(false);
  // These variables are now received as props
  // Removed local state variables to avoid name conflicts with props
  // Store previous provider counts to detect new additions
  const [previousRefProviderCount, setPreviousRefProviderCount] = useState(0);
  const [previousPcpProviderCount, setPreviousPcpProviderCount] = useState(0);
  // Create a ref to track if a save is in progress
  const saveInProgressRef = React.useRef(false);

  // Create a ref to track the last saved data hash
  const lastSavedDataHashRef = React.useRef("");

  // Add a ref to track site transition state to temporarily skip implanting physician validation
  const siteTransitionRef = React.useRef(false);
  const {
    case_id,
    first_name,
    last_name,
    middle_name,
    patient_age,
    patient_sex,
    patient_dob,
    prior_ablation_options,
    prior_ablation_selected_id,
    prior_ablation_selected_name,
    other_selected_value,
    anticoagulation_selected,
    cha2ds2_vasc_score,
    has_bled_score,
    rationale_options,
    rationale_selected_id,
    rationale_selected_name,
    rationale_other,
    referring_provider_options,
    referring_provider_selected_id,
    pcp_options,
    pcp_selected_id,
    procedure_date,
    procedure_time,
    implanting_physician_selected_id,
    implanting_physician_options,
    site_options,
    site_selected_id,
    tee_status,
    truplan_upload_status,
    cta_status,
    study,
    pre_op_imaging,
  } = formatPreOpDetails(userDetails);

  const implantingPhysicians = useImplantingPhysicianDetails();
  const [openProcedureDatePicker, setOpenProcedureDatePicker] = useState(false);
  const [openProcedureTimePicker, setOpenProcedureTimePicker] = useState(false);

  const diff = findPatientDemographicsDiff();

  const handleOpen = (id?: string) => {
    onMedOpen(id);
  };

  const handleAddMedication = () => {
    handleOpen();
  };

  // Handle context-based add operation
  const handleContextBasedAdd = (context: string) => {
    // Store current counts before opening sheet
    if (context === "referring_provider") {
      // Save current count to detect new additions
      setPreviousRefProviderCount(referring_provider_options?.length || 0);
      // Use the parent component's function to open the bottom sheet
      onOpenReferringProviderSheet();
    } else if (context === "pcp_provider" && onOpenPcpProviderSheet) {
      // Save current count to detect new additions
      setPreviousPcpProviderCount(pcp_options?.length || 0);
      // Use the parent component's function to open the PCP provider bottom sheet
      onOpenPcpProviderSheet();
    } else if (context === "site" && onOpenSiteSheet) {
      // Open the site addition bottom sheet
      onOpenSiteSheet();
    } else if (
      context === "implanting_physician" &&
      onOpenImplantingPhysicianSheet
    ) {
      // Open the implanting physician addition bottom sheet
      onOpenImplantingPhysicianSheet();
    }
  };

  // BottomSheetComponent handles the backdrop internally, so we don't need a separate renderBackdrop function

  const fetchData = async () => {
    const startWeek = moment(procedure_date)
      .startOf("isoWeek")
      .format("YYYY-MM-DD");
    const endWeek = moment(procedure_date)
      .endOf("isoWeek")
      .format("YYYY-MM-DD");

    try {
      // Fetch schedules for the week
      const schedulesResult = await dispatch(
        fetchSchedules({
          from_date: startWeek,
          to_date: endWeek,
        })
      );

      // Only proceed if we successfully fetched schedules
      if (schedulesResult.meta.requestStatus === "fulfilled") {
        // Use the fresh data from the API response instead of the stale store data
        const freshSchedules = schedulesResult.payload || [];

        const startOfWeek = moment(procedure_date).startOf("isoWeek");
        const endOfWeek = moment(procedure_date).endOf("isoWeek");
        const allDates = [];
        const day = startOfWeek.clone();

        while (day.isSameOrBefore(endOfWeek, "day")) {
          allDates.push(day.format("YYYY-MM-DD"));
          day.add(1, "day");
        }

        const groupedSchedules = allDates.map((date) => ({
          title: date,
          data: freshSchedules.filter(
            (item: any) => item.procedure_date === date
          ),
        }));

        // Only update site details if we have valid data and matching site ID
        groupedSchedules.forEach((group) => {
          if (group.data && group.data.length > 0) {
            group.data.forEach((details: any) => {
              if (details.site.id == site_selected_id) {
                dispatch(
                  setSiteDetails({
                    selectedDate: details.procedure_date,
                    selectedHospitalName: details.site.name,
                    selectedHospitalId: details.site.id,
                    implantingPhysicianName: details.implanting_physician.name,
                    implantingPhysicianImage:
                      details.implanting_physician.image_url,
                    hospitalImage: details.site.image_url,
                  })
                );
              }
            });
          }
        });
      }
    } catch (error) {
      console.error("Error fetching schedules:", error);
    }
  };

  React.useEffect(() => {
    if (
      anticoagulation_selected &&
      selected.length === 0 &&
      !medDeleted &&
      !initialUpdate
    ) {
      setSelected(anticoagulation_selected);
      setInitialUpdate(true);
    }
  }, [anticoagulation_selected, medDeleted]);

  React.useEffect(() => {
    if (site_selected_id) {
      dispatch(fetchImplantingPhysicians(site_selected_id));
    }
  }, [dispatch, site_selected_id]);

  const renderMed = ({ item, index }) => {
    return (
      <>
        <View
          className={`${
            index === 0
              ? "rounded-t-md"
              : index === med_data.length - 1
              ? "rounded-b-md"
              : ""
          } flex-row justify-between bg-primaryBg items-center p-2`}
        >
          <View className="flex-row items-center">
            <Text className="text-md font-bold text-primaryPurple">
              {item.med_name}
            </Text>
            <Text className="text-sm text-primaryBlack">
              {" - "}
              {item.med_dose} {item.dosing_frequency}
            </Text>
          </View>

          <View className="flex-row space-x-4 items-center">
            {/* Edit Button */}
            <TouchableOpacity
              onPress={() => {
                handleOpen(item.id);
              }}
              className="px-4 py-2"
            >
              <MaterialCommunityIcons name="pencil" size={20} color="#3b82f6" />
            </TouchableOpacity>

            <TouchableOpacity
              onPress={() => {
                setMedToDelete(item.id);
                setDeleteConfirmationVisible(true);
              }}
              className="px-4 py-2"
            >
              <MaterialCommunityIcons
                name="trash-can"
                size={20}
                color="#ef4444"
              />
            </TouchableOpacity>
          </View>
        </View>

        {/* Add LineSeperator except for the last item */}
        {index < med_data.length - 1 && <LineSeperator color="primaryWhite" />}
      </>
    );
  };

  const renderEmpty = () => {
    return (
      <View className="flex-row justify-center">
        <CustomText value={"None"} />
      </View>
    );
  };

  const validator = () => {
    setPopupMsg([]);

    var temp = false;
    if (first_name === "" || first_name === null || first_name === undefined) {
      setPopupMsg((prev) => [...prev, "Please Enter First Name"]);
      temp = true;
    }

    if (last_name === "" || last_name === null || last_name === undefined) {
      setPopupMsg((prev) => [...prev, "Please Enter Last Name"]);
      temp = true;
    }

    if (
      patient_dob === null ||
      patient_dob === undefined ||
      patient_dob === ""
    ) {
      setPopupMsg((prev) => [...prev, "Please Enter Date of Birth"]);
      temp = true;
    }

    if (
      patient_sex === null ||
      patient_sex === undefined ||
      patient_sex === ""
    ) {
      setPopupMsg((prev) => [...prev, "Please Select Gender"]);
      temp = true;
    }

    if (
      procedure_date === null ||
      procedure_date === undefined ||
      procedure_date === ""
    ) {
      setPopupMsg((prev) => [...prev, "Please Enter Procedure Date"]);
      temp = true;
    }

    if (
      procedure_time === null ||
      procedure_time === undefined ||
      procedure_time === ""
    ) {
      setPopupMsg((prev) => [...prev, "Please Enter Procedure Time"]);
      temp = true;
    }

    if (
      site_selected_id === null ||
      site_selected_id === undefined ||
      site_selected_id === ""
    ) {
      setPopupMsg((prev) => [...prev, "Please Select Site"]);
      temp = true;
    }

    // Always validate implanting physician - show popup even during site transitions
    // This helps user understand they need to select/add a physician for the new site
    if (
      implanting_physician_selected_id === null ||
      implanting_physician_selected_id === undefined ||
      implanting_physician_selected_id === ""
    ) {
      setPopupMsg((prev) => [...prev, "Please Select Implanting Physician"]);
      temp = true;
    }

    // if (
    //   referring_provider_selected_id === null ||
    //   referring_provider_selected_id === undefined ||
    //   referring_provider_selected_id === ""
    // ) {
    //   setPopupMsg((prev) => [...prev, "Please Select Referring Provider"]);
    //   temp = true;
    // }

    // if (
    //   pcp_selected_id === null ||
    //   pcp_selected_id === undefined ||
    //   pcp_selected_id === ""
    // ) {
    //   setPopupMsg((prev) => [...prev, "Please Select PCP"]);
    //   temp = true;
    // }

    // if (
    //   rationale_selected_id === null ||
    //   rationale_selected_id === undefined ||
    //   rationale_selected_id === ""
    // ) {
    //   setPopupMsg((prev) => [...prev, "Please Select Rationale"]);
    //   temp = true;
    // }

    // if (
    //   rationale_selected_name?.toLowerCase() === "other" &&
    //   !rationale_other
    // ) {
    //   setPopupMsg((prev) => [...prev, "Please Enter Other Rationale"]);
    //   temp = true;
    // }
    // if (
    //   prior_ablation_selected_id === null ||
    //   prior_ablation_selected_id === undefined ||
    //   prior_ablation_selected_id === ""
    // ) {
    //   setPopupMsg((prev) => [...prev, "Please Select Prior Ablation"]);
    //   temp = true;
    // }
    // if (
    //   prior_ablation_selected_name?.toLowerCase() === "other" &&
    //   !other_selected_value
    // ) {
    //   setPopupMsg((prev) => [...prev, "Please Enter Prior Ablation Other"]);
    //   temp = true;
    // }

    if (temp) {
      setModalVisible(true);
      return false;
    } else {
      return true;
    }
  };

  const handleRefresh = async () => {
    try {
      if (selectedPatient?.case_id) {
        await dispatch(
          fetchPreopDetails({
            case_id: selectedPatient?.case_id,
          })
        );
      }

      if (site_selected_id && procedure_date) {
        await dispatch(
          fetchCases({
            siteId: site_selected_id,
            date: procedure_date,
          })
        );
      }
    } catch (error) {
      console.error("Error in handleRefresh:", error);
    }
  };

  const saveDetails = async (): Promise<void> => {
    try {
      // Run validation
      const isValid = validator();

      if (!isValid) {
        dispatch(
          setSaveStatus({
            screenId: "preop",
            status: "validation_failed",
          })
        );
        setLocalSaveStatus("validation_failed");
        throw new Error("Validation failed");
      }

      // Avoid saving if already in progress
      if (saveInProgressRef.current) {
        return;
      }

      const dataCopy = { ...diff.currentValues };
      const currentDataHash = JSON.stringify(dataCopy);

      // If the data hasn't changed since the last save, skip saving
      if (currentDataHash === lastSavedDataHashRef.current) {
        return;
      }

      // Mark that a save is in progress
      saveInProgressRef.current = true;

      let result;

      // Store the current values before making any API calls
      const currentSiteId = site_selected_id;
      const currentProcedureDate = procedure_date;

      if (diff.hasDiff) {
        result = await dispatch(
          putPreopDetails({
            case_id: selectedPatient?.case_id,
            payload: dataCopy,
          })
        );

        // Update the last saved data hash
        lastSavedDataHashRef.current = currentDataHash;

        // Refetch preop details
        if (selectedPatient?.case_id) {
          const refetchRes = await dispatch(
            fetchPreopDetails({ case_id: selectedPatient.case_id })
          );

          // Then refresh schedule and case data
          await fetchData();

          if (currentSiteId && currentProcedureDate) {
            await dispatch(
              fetchCases({
                siteId: currentSiteId,
                date: currentProcedureDate,
              })
            );
          }
        }
      }

      saveInProgressRef.current = false;
    } catch (err) {
      saveInProgressRef.current = false;
      console.error("Save error:", err);
      throw err;
    }
  };

  const saveStatus = useSelector(
    (state: any) => state.common?.autoSave?.["preop"]?.status
  );

  const [localSaveStatus, setLocalSaveStatus] = React.useState<string>("");

  const effectiveSaveStatus = saveStatus || localSaveStatus;

  // Function to check if all required fields are filled (excluding referring provider and PCP provider)
  const areRequiredFieldsFilled = () => {
    return (
      first_name &&
      first_name !== "" &&
      last_name &&
      last_name !== "" &&
      patient_dob &&
      patient_dob !== "" &&
      patient_sex &&
      patient_sex !== "" &&
      procedure_date &&
      procedure_date !== "" &&
      procedure_time &&
      procedure_time !== "" &&
      site_selected_id &&
      site_selected_id !== "" &&
      implanting_physician_selected_id &&
      implanting_physician_selected_id !== ""
    );
  };

  // Track if user has made changes to prevent auto-navigation on initial load
  const [hasUserMadeChanges, setHasUserMadeChanges] = React.useState(false);

  // Track when user makes changes to any field
  React.useEffect(() => {
    if (diff.hasDiff && !hasUserMadeChanges) {
      setHasUserMadeChanges(true);
    }
  }, [diff.hasDiff, hasUserMadeChanges]);

  // Add effect to navigate to LAAO Procedure screen when all required fields are filled and saved
  React.useEffect(() => {
    if (
      effectiveSaveStatus === "saved" &&
      areRequiredFieldsFilled() &&
      hasUserMadeChanges
    ) {
      // Add a small delay to ensure the user sees the "saved" message briefly
      const timer = setTimeout(() => {
        (navigation as any).navigate("LAAO Procedure");
      }, 1500);

      return () => clearTimeout(timer);
    }
  }, [
    effectiveSaveStatus,
    navigation,
    first_name,
    last_name,
    patient_dob,
    patient_sex,
    procedure_date,
    procedure_time,
    site_selected_id,
    implanting_physician_selected_id,
    hasUserMadeChanges,
  ]);

  /**
   * Derive the save message and styling based on the save status
   */
  const { saveMessage, messageStyle, iconName, iconColor } =
    React.useMemo(() => {
      let message = "";
      let style = "text-primaryPurple italic";
      let icon = "alert-circle-outline";
      let color = "#8143d9";

      switch (effectiveSaveStatus) {
        case "changes_detected":
          message = "Changes detected, saving soon...";
          style = "text-primaryBlack italic";
          icon = "alert-circle-outline";
          color = "#f5c542"; // yellow
          break;
        case "saving":
          message = "Saving... Please don't close the app";
          style = "text-primaryPurple font-bold italic";
          icon = "alert-circle-outline";
          color = "#8143d9"; // purple
          break;
        case "saved":
          message = "All changes saved";
          style = "text-green-3 italic";
          icon = "check-circle-outline";
          color = "#28a745"; // green
          break;
        case "validation_failed":
          message = "Validation failed. Please check your inputs.";
          style = "text-red-3 font-bold italic";
          icon = "alert-circle-outline";
          color = "#d93025"; // red
          break;
        case "error":
          message = "Error saving changes. Please try again.";
          style = "text-red-3 font-bold italic";
          icon = "alert-circle-outline";
          color = "#d93025"; // red
          break;
      }

      return {
        saveMessage: message,
        messageStyle: style,
        iconName: icon,
        iconColor: color,
      };
    }, [effectiveSaveStatus]);

  /**
   * Ref to track previous diff values to prevent unnecessary saves
   */
  const prevDiffRef = React.useRef<string>("");

  /**
   * Ref to track if initial data has been loaded
   * This prevents autosave from triggering before data is fetched
   */
  const initialDataLoadedRef = React.useRef<boolean>(false);

  /**
   * Effect to set initialDataLoaded when data is fetched
   */
  React.useEffect(() => {
    // Check if we have valid data
    const hasValidData = userDetails?.case_id !== undefined;

    if (hasValidData && !initialDataLoadedRef.current) {
      initialDataLoadedRef.current = true;
    }
  }, [userDetails]);

  // Remove console logs for production

  /**
   * Effect to trigger autosave when data changes
   * This uses our new simpleAutoSave service with local state fallback
   */
  React.useEffect(() => {
    // Only proceed if initial data has been loaded and there are actual differences
    if (!initialDataLoadedRef.current) {
      return;
    }

    // Check if case_id exists to prevent API calls with undefined ID
    if (!userDetails?.case_id) {
      return;
    }

    // Only proceed if there are actual differences
    if (diff.hasDiff) {
      // Check if we have any undefined values in critical fields
      const hasUndefinedValues = Object.values(diff.currentValues).every(
        (val) => val === undefined
      );
      if (hasUndefinedValues) {
        return;
      }

      // Convert current values to string for comparison
      const currentDiffString = JSON.stringify(diff.currentValues);

      // Only trigger save if the values have actually changed
      if (currentDiffString !== prevDiffRef.current) {
        // Update the ref with current values
        prevDiffRef.current = currentDiffString;

        // Create a custom wrapper for setSaveStatus that updates both Redux and local state
        const setStatusWithFallback = (payload: {
          screenId: string;
          status: string;
        }) => {
          // Update local state as a fallback
          setLocalSaveStatus(payload.status);
          // Also dispatch to Redux
          return dispatch(setSaveStatus(payload));
        };

        // Trigger the simple autosave with the current values
        simpleAutoSave(
          "preop", // Screen ID
          diff.currentValues, // Data to save
          saveDetails, // Save function
          300,
          dispatch, // Redux dispatch
          setStatusWithFallback, // Custom action to set save status with fallback
          setLastSavedData // Action to set last saved data
        );
      } else {
      }
    }
  }, [
    diff.hasDiff,
    diff.currentValues,
    dispatch,
    setLocalSaveStatus,
    userDetails,
  ]);

  const handleCancel = () => {
    dispatch(
      setPreopUserDetails({
        ...userDetails,
        ...preopDetails,
      })
    );
  };
  // Function to update referring provider after adding a new one
  const updateReferringProvider = async (provider: any) => {
    if (provider && provider.id) {
      try {
        // FIRST: Set the new provider ID in Redux state immediately for UI update
        dispatch(
          setPreopUserDetails({
            patient: {
              ...userDetails?.patient,
              referring_provider: {
                ...userDetails?.patient?.referring_provider,
                selected: {
                  ...userDetails?.patient?.referring_provider?.selected,
                  id: provider.id,
                },
              },
            },
          })
        );

        // THEN: Fetch updated preop details to get the latest provider options
        if (selectedPatient?.case_id) {
          await dispatch(
            fetchPreopDetails({
              case_id: selectedPatient.case_id,
            })
          );
        }

        // Also fetch updated provider list for future use
        dispatch(fetchReferringProviders());
      } catch (error) {
        console.error("Error updating referring provider:", error);
      }
    }
  };

  // Function to update PCP provider after adding a new one
  const updatePcpProvider = async (provider: any) => {
    if (provider && provider.id) {
      try {
        // FIRST: Set the new provider ID in Redux state immediately for UI update
        dispatch(
          setPreopUserDetails({
            patient: {
              ...userDetails?.patient,
              pcp: {
                ...userDetails?.patient?.pcp,
                selected: {
                  ...userDetails?.patient?.pcp?.selected,
                  id: provider.id,
                },
              },
            },
          })
        );

        // THEN: Fetch updated preop details to get the latest provider options
        if (selectedPatient?.case_id) {
          await dispatch(
            fetchPreopDetails({
              case_id: selectedPatient.case_id,
            })
          );
        }

        // Also fetch updated provider list for future use
        dispatch(fetchPcpProviders());
      } catch (error) {
        console.error("Error updating PCP provider:", error);
      }
    }
  };

  // Function to update site after adding a new one
  const updateSite = async (site: any) => {
    if (site && site.id) {
      try {
        // Site addition in progress - validation will run normally

        try {
          const physicianResult = await dispatch(
            fetchImplantingPhysicians(site.id)
          );

          if (physicianResult.payload?.length === 0) {
            console.log("ℹ️ User will need to add physicians to this site");
          }
        } catch (error) {
          console.error("❌ Error fetching implanting physicians:", error);
        }

        // THEN: Set the new site ID in Redux state immediately for UI update
        // AND reset implanting physician since they are site-specific
        dispatch(
          setPreopUserDetails({
            ...userDetails,
            site: {
              ...userDetails?.site,
              selected: {
                ...userDetails?.site?.selected,
                id: site.id,
              },
            },
            // Reset implanting physician when site changes (site-specific requirement)
            implanting_physician: {
              ...userDetails?.implanting_physician,
              selected: {
                ...userDetails?.implanting_physician?.selected,
                id: null,
              },
            },
          })
        );

        // FINALLY: Fetch updated preop details to get the latest site options
        if (selectedPatient?.case_id) {
          await dispatch(
            fetchPreopDetails({
              case_id: selectedPatient.case_id,
            })
          );
        }
      } catch (error) {
        console.error("❌ Error during site addition:", error);
      }
    }
  };

  // Function to update implanting physician after adding a new one
  const updateImplantingPhysician = async (physician: any) => {
    if (physician && physician.id) {
      try {
        // FIRST: Set the new physician ID in Redux state immediately for UI update
        dispatch(
          setPreopUserDetails({
            ...userDetails,
            implanting_physician: {
              ...userDetails?.implanting_physician,
              selected: {
                ...userDetails?.implanting_physician?.selected,
                id: physician.id,
              },
            },
          })
        );

        // THEN: Fetch updated preop details to get the latest physician options
        if (selectedPatient?.case_id) {
          await dispatch(
            fetchPreopDetails({
              case_id: selectedPatient.case_id,
            })
          );
        }
      } catch (error) {
        console.error("Error updating implanting physician:", error);
      }
    }
  };

  // Effect to call updateReferringProvider when lastAddedReferringProvider changes
  React.useEffect(() => {
    if (lastAddedReferringProvider) {
      const providerObject = {
        id: lastAddedReferringProvider,
        name: `Provider ${lastAddedReferringProvider}`,
      };
      updateReferringProvider(providerObject);
    }
  }, [lastAddedReferringProvider]);

  // Effect to call updatePcpProvider when lastAddedPcpProvider changes
  React.useEffect(() => {
    if (lastAddedPcpProvider) {
      const providerObject = {
        id: lastAddedPcpProvider,
        name: `Provider ${lastAddedPcpProvider}`,
      };
      updatePcpProvider(providerObject);
    }
  }, [lastAddedPcpProvider]);

  // Effect to call updateSite when lastAddedSite changes
  React.useEffect(() => {
    if (lastAddedSite) {
      const siteObject = {
        id: lastAddedSite,
        name: `Site ${lastAddedSite}`,
      };
      updateSite(siteObject);
    }
  }, [lastAddedSite]);

  // Effect to call updateImplantingPhysician when lastAddedImplantingPhysician changes
  React.useEffect(() => {
    if (lastAddedImplantingPhysician) {
      const physicianObject = {
        id: lastAddedImplantingPhysician,
        name: `Physician ${lastAddedImplantingPhysician}`,
      };
      updateImplantingPhysician(physicianObject);
    }
  }, [lastAddedImplantingPhysician]);

  // Log removed for performance

  if (loader) {
    return (
      <View className="mt-64">
        <Loader />
      </View>
    );
  }

  // Remove hardcoded value and console log that may affect performance
  return (
    <>
      <CustomCard>
        {saveMessage && (
          <View
            style={{
              flexDirection: "row",
              justifyContent: "flex-end",
              alignItems: "center",
              marginVertical: 4,
            }}
          >
            <MaterialCommunityIcons
              name={iconName}
              color={iconColor}
              size={20}
            />
            <Text
              className={`text-sm ${messageStyle}`}
              style={{ marginLeft: 6 }}
            >
              {saveMessage}
            </Text>
          </View>
        )}
        <View className="flex-row items-center justify-between w-full mb-2">
          <View className="flex-column flex-1 p-1">
            {/* <View className="flex-row justify-between items-center mb-2  ">
              <View className="flex-row">
                <Text className="font-semibold text-lg text-primaryPurple">
                  {first_name} {last_name}
                </Text>
                <View className="h-6 border-l-[2px] rounded-full border-primaryPurple ml-2" />
                <View className="flex-row items-center ml-2">
                  <Text className="font-semibold text-lg text-primaryPurple mr-1">
                    {patient_age}
                  </Text>
                  <Text className="font-semibold text-lg text-primaryPurple mr-1">
                    Y/O
                  </Text>
                  <Text className="font-semibold text-lg text-primaryPurple">
                    {patient_sex === "Male" ? "M" : "F"}
                  </Text>
                </View>
              </View>
            </View> */}

            <View className="mt-3 rounded-lg ">
              <View className="mb-3">
                <Heading
                  text="First Name"
                  size="sub-heading"
                  showSeperator={false}
                />
                <View className="mt-2">
                  <CustomInput
                    inputValue={first_name}
                    error={first_name === "" || first_name === undefined}
                    onInputChange={(value) => {
                      parseCharInput(value, (parsedValue) => {
                        dispatch(
                          setPreopUserDetails({
                            ...userDetails,
                            patient: {
                              ...userDetails?.patient,
                              first_name: parsedValue,
                            },
                          })
                        );
                      });
                    }}
                    placeholder="First Name"
                  />
                </View>
              </View>

              <LineSeperator extraStyle="mt-2 mb-3" />

              <View className="mb-3">
                <Heading
                  text="Middle Name"
                  size="sub-heading"
                  showSeperator={false}
                />
                <View className="mt-2">
                  <CustomInput
                    inputValue={middle_name}
                    onInputChange={(value) => {
                      parseCharInput(value, (parsedValue) => {
                        dispatch(
                          setPreopUserDetails({
                            ...userDetails,
                            patient: {
                              ...userDetails?.patient,
                              middle_name: parsedValue,
                            },
                          })
                        );
                      });
                    }}
                    placeholder="Middle Name"
                  />
                </View>
              </View>

              <LineSeperator extraStyle="mt-2 mb-3" />

              <View className="mb-3">
                <Heading
                  text="Last Name"
                  size="sub-heading"
                  showSeperator={false}
                />
                <View className="mt-2">
                  <CustomInput
                    inputValue={last_name}
                    error={last_name === "" || last_name === undefined}
                    onInputChange={(value) => {
                      parseCharInput(value, (parsedValue) => {
                        dispatch(
                          setPreopUserDetails({
                            ...userDetails,
                            patient: {
                              ...userDetails?.patient,
                              last_name: parsedValue,
                            },
                          })
                        );
                      });
                    }}
                    placeholder="Last Name"
                  />
                </View>
              </View>

              <LineSeperator extraStyle="mt-2 mb-3" />

              <View className="mb-3">
                <View className="flex-row items-center">
                  <Heading
                    text="Date of Birth"
                    size="sub-heading"
                    showSeperator={false}
                  />
                  <Text className="ml-2 font-semibold text-lg text-primaryPurple mr-1">
                    {patient_age}
                  </Text>
                  <Text className="font-semibold text-lg text-primaryPurple mr-1">
                    Y/O
                  </Text>
                </View>
                <View className="mt-2">
                  <TouchableOpacity
                    onPress={() => setOpen(true)}
                    className={`bg-primaryWhite border h-[45px] rounded-md justify-center px-3 ${
                      patient_dob ? "border-primaryPurple" : "border-red-3"
                    }`}
                  >
                    <Text className="text-primaryBlack">
                      {patient_dob
                        ? moment(patient_dob).format("MM-DD-YYYY")
                        : "Select Date"}
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
              <LineSeperator extraStyle="mt-2 mb-3" />

              {/* <View className="mb-3 flex-row justify-between items-center">
                <Heading text="Sex" size="sub-heading" showSeperator={false} />
                <View className="mt-2">
                  <ToggleButton
                    messages={["Female", "Male"]}
                    selected={patient_sex === "M" ? "Male" : "Female"}
                    setSelected={(value) => {
                      dispatch(
                        setPreopUserDetails({
                          patient: {
                            ...userDetails?.patient,
                            sex: value === "Male" ? "M" : "F",
                          },
                        })
                      );
                    }}
                    yesIconName="human-female"
                    noIconName="human-male"
                    disabled={false}
                    width={"w-[77px]"}
                    invertColor
                    customColors={["#8143d9", "#8143d9"]}
                    customToggler
                    isBox={false}
                  />
                </View>
               
              </View> */}
              <View className="mb-3">
                <Heading
                  text="Gender"
                  size="sub-heading"
                  showSeperator={false}
                />
                <View className="mt-2 flex-1">
                  <SearchablePicker
                    placeholder="Select Gender"
                    items={[
                      { label: "Male", value: "M" },
                      { label: "Female", value: "F" },
                    ]}
                    value={patient_sex || ""}
                    onValueChange={(option) => {
                      dispatch(
                        setPreopUserDetails({
                          patient: {
                            ...userDetails?.patient,
                            sex: option.value,
                          },
                        })
                      );
                    }}
                  />
                </View>
              </View>

              <LineSeperator extraStyle="mt-2 mb-3" />

              <View className="mb-3">
                <Heading
                  text="Procedure Date"
                  size="sub-heading"
                  showSeperator={false}
                />
                <View className="mt-2">
                  <TouchableOpacity
                    onPress={() => setOpenProcedureDatePicker(true)}
                    className={`bg-primaryWhite border ${
                      procedure_date ? "border-primaryPurple" : "border-red-3"
                    } h-[45px] rounded justify-center px-3 flex-1`}
                  >
                    <CustomText
                      value={
                        procedure_date
                          ? moment(procedure_date).format("MM/DD/YYYY")
                          : "Select Date"
                      }
                      className={`
                  ${procedure_date ? "" : "text-primaryGray"}
                  text-md
                  `}
                    />
                  </TouchableOpacity>
                </View>
              </View>

              <LineSeperator extraStyle="mt-2 mb-3" />

              <View className="mb-3">
                <Heading
                  text="Procedure Time"
                  size="sub-heading"
                  showSeperator={false}
                />
                <View className="mt-2">
                  <TouchableOpacity
                    onPress={() => setOpenProcedureTimePicker(true)}
                    className={`bg-primaryWhite border ${
                      procedure_time ? "border-primaryPurple" : "border-red-3"
                    } h-[45px] rounded justify-center px-3 flex-1`}
                  >
                    <CustomText
                      value={
                        procedure_time
                          ? moment(procedure_time, "HH:mm:ss").format("HH:mm")
                          : "Select Time"
                      }
                      className={`
                  ${procedure_time ? "" : "text-primaryGray"}
                  text-md
                  `}
                    />
                  </TouchableOpacity>
                </View>
              </View>

              <LineSeperator extraStyle="mt-2 mb-3" />

              <View className="mb-3">
                <Heading
                  text="Hospital"
                  size="sub-heading"
                  showSeperator={false}
                />
                <View className="mt-1">
                  <SearchablePicker
                    items={site_options}
                    value={site_selected_id}
                    placeholder="Select"
                    onValueChange={(option) => {
                      // Combine both updates in a single dispatch
                      dispatch(
                        setPreopUserDetails({
                          ...userDetails,
                          site: {
                            ...userDetails?.site,
                            selected: {
                              ...userDetails?.site?.selected,
                              id: option.value,
                            },
                          },
                          implanting_physician: {
                            ...userDetails?.implanting_physician,
                            selected: {
                              ...userDetails?.implanting_physician?.selected,
                              id: null,
                            },
                          },
                        })
                      );

                      // Fetch implanting physicians after updating the state
                      // if (option.value) {
                      //   console.log(
                      //     "🔄 Manual site selection - fetching physicians for site:",
                      //     option.value
                      //   );
                      //   console.log(
                      //     "🔄 Manual site ID type:",
                      //     typeof option.value
                      //   );
                      //   console.log(
                      //     "🔄 Manual site ID length:",
                      //     option.value.length
                      //   );
                      //   dispatch(fetchImplantingPhysicians(option.value));
                      // }
                    }}
                    // screenContext="site"
                    // onAddPress={handleContextBasedAdd}
                    // showAddInSearch={true}
                    disableError
                  />
                </View>
              </View>
              <LineSeperator extraStyle="mt-2 mb-3" />

              <View className="mb-3">
                <Heading
                  text="Implanting Physician"
                  size="sub-heading"
                  showSeperator={false}
                />
                <View className="mt-1">
                  <SearchablePicker
                    items={
                      implantingPhysicians?.length > 0 && implantingPhysicians
                    }
                    value={implanting_physician_selected_id}
                    placeholder="Select"
                    error={
                      implanting_physician_selected_id === null ||
                      implanting_physician_selected_id === ""
                    }
                    onValueChange={(option) => {
                      dispatch(
                        setPreopUserDetails({
                          ...userDetails,
                          implanting_physician: {
                            ...userDetails?.implanting_physician,
                            selected: {
                              ...userDetails?.implanting_physician?.selected,
                              id: option.value,
                            },
                          },
                        })
                      );
                    }}
                    // screenContext="implanting_physician"
                    // onAddPress={handleContextBasedAdd}
                    // showAddInSearch={true}
                    disableError
                  />
                </View>
              </View>
              <LineSeperator extraStyle="mt-2 mb-3" />

              <View className="mb-3">
                <Heading
                  text="Referring Provider"
                  size="sub-heading"
                  showSeperator={false}
                />
                <View className="mt-1">
                  <SearchablePicker
                    items={referring_provider_options}
                    value={referring_provider_selected_id}
                    placeholder="Select"
                    key={`referring-picker-${
                      referring_provider_options
                        ? referring_provider_options.length
                        : 0
                    }-${referring_provider_selected_id || "empty"}`}
                    onValueChange={(option) => {
                      dispatch(
                        setPreopUserDetails({
                          ...userDetails,
                          patient: {
                            ...userDetails?.patient,
                            referring_provider: {
                              ...userDetails?.patient?.referring_provider,
                              selected: {
                                ...userDetails?.patient?.referring_provider
                                  ?.selected,
                                id: option.value,
                                name: option.label,
                              },
                            },
                          },
                        })
                      );
                    }}
                    screenContext="referring_provider"
                    onAddPress={handleContextBasedAdd}
                    disableError
                    showAddInSearch={true}
                  />
                </View>
              </View>

              <LineSeperator extraStyle="mt-2 mb-3" />

              <View className="mb-3">
                <Heading text="PCP" size="sub-heading" showSeperator={false} />
                <View className="mt-1">
                  <SearchablePicker
                    items={pcp_options}
                    value={pcp_selected_id}
                    placeholder="Select"
                    key={`pcp-picker-${pcp_options ? pcp_options.length : 0}-${
                      pcp_selected_id || "empty"
                    }`}
                    onValueChange={(option) => {
                      dispatch(
                        setPreopUserDetails({
                          patient: {
                            ...userDetails?.patient,
                            pcp: {
                              ...userDetails?.patient?.pcp,
                              selected: {
                                ...userDetails?.patient?.pcp?.selected,
                                id: option.value,
                                name: option.label,
                              },
                            },
                          },
                        })
                      );
                    }}
                    screenContext="pcp_provider"
                    onAddPress={handleContextBasedAdd}
                    disableError
                    showAddInSearch={true}
                  />
                </View>
              </View>

              <LineSeperator extraStyle="mt-2 mb-3" />

              <View className="mb-3">
                <Heading
                  text="Patient Rationale"
                  size="sub-heading"
                  showSeperator={false}
                />
                <View className="mt-1">
                  <SearchablePicker
                    placeholder="Select"
                    items={rationale_options}
                    value={rationale_selected_id}
                    onValueChange={(option) => {
                      dispatch(
                        setPreopUserDetails({
                          patient: {
                            ...userDetails?.patient,
                            rationale: {
                              ...userDetails?.patient?.rationale,
                              selected: {
                                id: option.value,
                                name: option.label,
                              },
                            },
                          },
                        })
                      );
                    }}
                    disableError
                  />
                </View>
              </View>

              {rationale_selected_name?.toLowerCase() === "other" && (
                <View className="">
                  <CustomInput
                    inputValue={rationale_other}
                    error={
                      rationale_other === "" || rationale_other === undefined
                    }
                    onInputChange={(val) => {
                      dispatch(
                        setPreopUserDetails({
                          ...userDetails,
                          patient: {
                            ...userDetails?.patient,
                            rationale: {
                              ...userDetails?.patient?.rationale,
                              selected: {
                                ...userDetails?.patient?.rationale?.selected,
                                other: val,
                              },
                            },
                          },
                        })
                      );
                    }}
                    placeholder="Other"
                  />
                </View>
              )}

              <LineSeperator extraStyle="mt-4 mb-3" />

              <View className="gap-2">
                <View className="flex-row justify-between">
                  <Text className="text-primaryBlack text-lg font-semibold">
                    Current Medication Regimen
                  </Text>
                  <MaterialCommunityIcons
                    name="plus-circle-outline"
                    color="#8143d9"
                    size={25}
                    onPress={handleAddMedication}
                  />
                </View>

                {med_data && med_data.length > 0 ? (
                  <FlatList
                    data={med_data}
                    renderItem={renderMed}
                    keyExtractor={(item) => item.id}
                    scrollEnabled={false}
                  />
                ) : (
                  <View className="py-2">
                    {/* <Text className="text-gray-500 italic">
                      No data available
                    </Text> */}
                  </View>
                )}
              </View>

              <LineSeperator extraStyle="mt-4 mb-3" />

              <View className="mb-3">
                <Heading
                  text="Prior Ablations"
                  size="sub-heading"
                  showSeperator={false}
                />
                <View className="mt-1">
                  <SearchablePicker
                    placeholder="Select"
                    items={prior_ablation_options}
                    value={prior_ablation_selected_id}
                    onValueChange={(option) => {
                      dispatch(
                        setPreopUserDetails({
                          patient: {
                            ...userDetails?.patient,
                            prior_ablation: {
                              ...userDetails?.patient?.prior_ablation,
                              selected: {
                                id: option.value,
                                name: option.label,
                              },
                            },
                          },
                        })
                      );
                    }}
                    disableError
                  />
                </View>
              </View>
              <LineSeperator extraStyle="mt-4 mb-3" />

              {prior_ablation_selected_name?.toLowerCase() === "other" && (
                <View className="">
                  <CustomInput
                    inputValue={other_selected_value}
                    error={
                      other_selected_value === "" ||
                      other_selected_value === undefined
                    }
                    onInputChange={(val) => {
                      dispatch(
                        setPreopUserDetails({
                          ...userDetails,
                          patient: {
                            ...userDetails?.patient,
                            prior_ablation: {
                              ...userDetails?.patient?.prior_ablation,
                              selected: {
                                ...userDetails?.patient?.prior_ablation
                                  ?.selected,
                                other: val,
                              },
                            },
                          },
                        })
                      );
                    }}
                    placeholder="Other"
                  />
                </View>
              )}

              {/* <View className="mt-4 flex-row justify-center gap-4">
                <SaveActionButton
                  disabled={!diff.hasDiff}
                  onPress={saveDetails}
                  onCancel={handleCancel}
                />
              </View> */}
            </View>
          </View>
        </View>
      </CustomCard>
      <CustomCard>
        <View className="flex-row justify-between gap-2 px-2 py-2">
          <View className="gap-2 w-[50%]">
            <TouchableOpacity
              className={`flex-row shadow-sm ${
                cha2ds2_vasc_score < 3
                  ? "bg-red-1"
                  : cha2ds2_vasc_score >= 3
                  ? "bg-primaryBg"
                  : "bg-primaryBg"
              } rounded justify-between p-2 mb-1`}
              onPress={() => chadPress(cha2ds2_vasc_score)}
            >
              <Text
                className={`${
                  cha2ds2_vasc_score < 3
                    ? "text-red-3"
                    : cha2ds2_vasc_score >= 3
                    ? "text-primaryPurple"
                    : "text-primaryPurple"
                }`}
              >
                CHA<Text style={{ fontSize: 9 }}>2</Text>DS
                <Text style={{ fontSize: 9 }}>2</Text>-VASc
              </Text>
              <Text
                className={`
                          ${
                            cha2ds2_vasc_score < 3
                              ? "text-red-3"
                              : cha2ds2_vasc_score >= 3
                              ? "text-primaryPurple"
                              : "text-primaryPurple"
                          }
                          `}
              >
                {cha2ds2_vasc_score}
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              className="flex-row shadow-sm bg-primaryBg rounded justify-between p-2"
              onPress={() => hasBledPress(has_bled_score)}
            >
              <Text className=" text-primaryPurple">HAS-BLED</Text>
              <Text className=" text-primaryPurple">{has_bled_score}</Text>
            </TouchableOpacity>
          </View>
          {pre_op_imaging && (
            <View className="items-end gap-2 w-[50%] px-2">
              <TouchableOpacity
                className={`${
                  cta_status || study?.length > 0
                    ? `bg-green-2`
                    : `bg-secondaryGray`
                } rounded items-center w-[70%] p-2 shadow-sm`}
                onPress={() => {
                  if (cta_status) {
                    navigation.navigate("CTA", {
                      ostium: "23 mm x 29 mm",
                      morphology: "Windsock",
                      depth: "35 mm",
                      clotData: morphData,
                    });
                  } else if (study?.length > 0 && !cta_status) {
                    navigation.navigate("WebViewer", {
                      link: study[0]?.viewer_link,
                    });
                  }
                }}
                disabled={cta_status || study?.length > 0 ? false : true}
              >
                <Text
                  className={`
                    ${
                      cta_status || study?.length > 0
                        ? `text-green-3`
                        : `text-primaryWhite`
                    }
                    `}
                >
                  {"Pre-Op CTA"}
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                className={`${
                  pre_op_imaging?.tee ? `bg-green-2` : `bg-secondaryGray`
                } rounded items-center w-[70%] p-2 bg-green-2 shadow-sm`}
                onPress={() => {
                  navigation.navigate("TEE", {
                    morphology: morphData,
                    clotData: morphData,
                    angles: ["0", "90", "45", "135"],
                    // diameters: ['19 mm', '14 mm', '15 mm', '18 mm',],
                    // depths: ['21 mm', '21 mm', '21 mm', '19 mm'],
                    diameters: ["26 mm", "19 mm", "19 mm", "18 mm"],
                    depths: ["37 mm", "30 mm", "35 mm", "26 mm"],
                  });
                }}
                disabled={pre_op_imaging?.tee ? false : true}
              >
                <Text
                  className={`
                    ${
                      pre_op_imaging?.tee ? `text-green-3` : `text-primaryWhite`
                    }
                    `}
                >
                  {"Pre-Op TEE"}
                </Text>
              </TouchableOpacity>
            </View>
          )}
        </View>
        <View className={`${globalStyles.containers.flex_center} my-3`}>
          <TouchableOpacity
            className={`
            p-3 rounded-md
             ${
               (truplan_upload_status?.toLowerCase() === "pending" ||
                 truplan_upload_status?.toLowerCase() === "completed" ||
                 truplan_upload_status?.toLowerCase() === "failed") &&
               (cta_status || study?.length > 0)
                 ? `bg-primaryPurple`
                 : `bg-secondaryGray`
             }
           `}
            disabled={
              (truplan_upload_status?.toLowerCase() === "pending" ||
                truplan_upload_status?.toLowerCase() === "completed" ||
                truplan_upload_status?.toLowerCase() === "failed") &&
              (cta_status || study?.length > 0)
                ? false
                : true
            }
            onPress={truplanButton.onPress}
          >
            <View className="flex-row justify-center items-center">
              <CustomText
                value={
                  truplan_upload_status?.toLowerCase() === "pending" ||
                  truplan_upload_status?.toLowerCase() === "completed" ||
                  truplan_upload_status?.toLowerCase() === "failed"
                    ? "Upload TruPlan"
                    : "Processing TruPlan..."
                }
                className={`text-primaryWhite font-semibold`}
              />
            </View>
          </TouchableOpacity>
        </View>
        {truplan_upload_status?.toLowerCase() === "completed" && (
          <CustomText
            value={
              <Text className="text-green-3">TruPlan is already uploaded</Text>
            }
            className={`text-center`}
          />
        )}
      </CustomCard>

      <PopupModal
        show={modalVisible}
        msg={popupMsg}
        status="warning"
        onClose={() => setModalVisible(false)}
      />

      <PopupModal
        show={deleteConfirmationVisible}
        msg={["Are you sure you want to delete this medication?"]}
        status="warning"
        onClose={() => setDeleteConfirmationVisible(false)}
      >
        <View className="flex-row justify-end mt-4">
          <TouchableOpacity
            onPress={() => setDeleteConfirmationVisible(false)}
            style={{
              paddingHorizontal: 16,
              paddingVertical: 8,
              backgroundColor: "#D1D5DB",
              borderRadius: 6,
              marginRight: 8,
            }}
          >
            <Text style={{ color: "black" }}>Cancel</Text>
          </TouchableOpacity>

          <TouchableOpacity
            onPress={() => {
              if (medToDelete) {
                dispatch(deleteMedicationById(medToDelete));
                setDeleteConfirmationVisible(false);
                setMedToDelete(null);
                setMedDeleted(true);
              }
            }}
            style={{
              paddingHorizontal: 16,
              paddingVertical: 8,
              backgroundColor: "#EF4444",
              borderRadius: 6,
            }}
          >
            <Text style={{ color: "white" }}>Delete</Text>
          </TouchableOpacity>
        </View>
      </PopupModal>
      <DatePicker
        modal
        open={open}
        date={
          patient_dob ? moment(patient_dob, "YYYY-MM-DD").toDate() : new Date()
        }
        mode="date"
        maximumDate={new Date()}
        onConfirm={(date) => {
          setOpen(false);
          dispatch(
            setPreopUserDetails({
              patient: {
                ...userDetails?.patient,
                dob: moment(date).format("YYYY-MM-DD"),
              },
            })
          );
        }}
        onCancel={() => setOpen(false)}
      />
      <DatePicker
        modal
        open={openProcedureDatePicker}
        date={
          procedure_date
            ? moment(procedure_date, "YYYY-MM-DD").toDate()
            : new Date()
        }
        onConfirm={(date) => {
          setOpenProcedureDatePicker(false);
          dispatch(
            setPreopUserDetails({
              ...userDetails,
              procedure_date: moment(date).format("YYYY-MM-DD"),
            })
          );
        }}
        onCancel={() => setOpenProcedureDatePicker(false)}
        mode="date"
        minimumDate={new Date()}
      />

      <DatePicker
        modal
        open={openProcedureTimePicker}
        date={
          procedure_time
            ? moment(procedure_time, "HH:mm:ss").toDate()
            : new Date()
        }
        onConfirm={(date) => {
          setOpenProcedureTimePicker(false);
          dispatch(
            setPreopUserDetails({
              ...userDetails,
              procedure_time: moment(date).format("HH:mm:ss"),
            })
          );
        }}
        onCancel={() => {
          setOpenProcedureTimePicker(false);
        }}
        mode={"time"}
      />
    </>
  );
};

export default PatientDemographicsCard;
