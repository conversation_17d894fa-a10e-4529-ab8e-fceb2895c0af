import { createSlice } from "@reduxjs/toolkit";
import initialState from "./initialstate";
import { createJiraIssue, getIssueTypes } from "./thunk";

const jiraSlice = createSlice({
  name: "jira",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      // createJiraIssue cases
      .addCase(createJiraIssue.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createJiraIssue.fulfilled, (state, action) => {
        state.loading = false;
        state.issue = action.payload;
      })
      .addCase(createJiraIssue.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      // getIssueTypes cases
      .addCase(getIssueTypes.pending, (state) => {
        state.loadingIssueTypes = true;
        state.issueTypesError = null;
      })
      .addCase(getIssueTypes.fulfilled, (state, action) => {
        state.loadingIssueTypes = false;
        state.issueTypes = action.payload;
      })
      .addCase(getIssueTypes.rejected, (state, action) => {
        state.loadingIssueTypes = false;
        state.issueTypesError = action.payload as string;
      });
  },
});

export default jiraSlice.reducer;
