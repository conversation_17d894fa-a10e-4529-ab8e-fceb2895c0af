/**
 * Simple AutoSave Service
 *
 * This is a simplified version of the autosave service that should work more reliably.
 * It provides debouncing, queuing, and status tracking for autosave operations.
 */

// Track debounce timers for each screen
const debounceTimers: Record<string, NodeJS.Timeout | null> = {};

// Track save operations in progress for each screen
const saveInProgress: Record<string, boolean> = {};

// Queue for pending save operations for each screen
interface QueueItem {
  data: any;
  timestamp: number;
}

const saveQueue: Record<string, QueueItem[]> = {};

// Cache of last saved data for each screen
const lastSavedDataCache: Record<string, any> = {};

/**
 * Process the save queue for a specific screen
 */
const processSaveQueue = async (
  screenId: string,
  saveFunction: (data: any) => Promise<void>,
  dispatch: any,
  setSaveStatus: (payload: { screenId: string; status: string }) => any,
  setLastSavedData: (payload: { screenId: string; data: any }) => any
) => {
  // If no queue exists or queue is empty, exit
  if (!saveQueue[screenId] || saveQueue[screenId].length === 0) {
    saveInProgress[screenId] = false;
    return;
  }

  // Mark as in progress
  saveInProgress[screenId] = true;

  const queueItem = saveQueue[screenId].pop();

  if (!queueItem) {
    saveInProgress[screenId] = false;
    return;
  }

  try {
    // Set status to saving
    dispatch(setSaveStatus({ screenId, status: "saving" }));

    await saveFunction(queueItem.data);

    // Update the lastSavedDataCache with the successfully saved data
    lastSavedDataCache[screenId] = JSON.parse(JSON.stringify(queueItem.data));

    // Set status to saved
    dispatch(setSaveStatus({ screenId, status: "saved" }));

    // Update Redux with last saved data
    dispatch(setLastSavedData({ screenId, data: queueItem.data }));
  } catch (error) {
    console.error(`Error saving data for ${screenId}:`, error);
    dispatch(setSaveStatus({ screenId, status: "error" }));
  } finally {
    // Process the next item in the queue if any
    if (saveQueue[screenId] && saveQueue[screenId].length > 0) {
      processSaveQueue(
        screenId,
        saveFunction,
        dispatch,
        setSaveStatus,
        setLastSavedData
      );
    } else {
      // No more items in the queue
      console.log(
        `No more items in queue for ${screenId}, marking as not in progress`
      );
      saveInProgress[screenId] = false;
    }
  }
};

/**
 * Simple autosave function with debouncing and queuing
 */
export const simpleAutoSave = (
  screenId: string,
  data: any,
  saveFunction: (data: any) => Promise<void>,
  delay: number,
  dispatch: any,
  setSaveStatus: (payload: { screenId: string; status: string }) => any,
  setLastSavedData: (payload: { screenId: string; data: any }) => any
) => {
  // Check if there are actual changes by comparing with last saved data
  const dataString = JSON.stringify(data);
  const lastSavedDataString = JSON.stringify(lastSavedDataCache[screenId]);

  // Only proceed if there are actual changes
  if (dataString === lastSavedDataString) {
    return; // No changes detected, exit early
  }

  // Reset saveInProgress if it's been stuck
  if (saveInProgress[screenId]) {
    saveInProgress[screenId] = false;
  }

  // Initialize queue for this screen if it doesn't exist
  if (!saveQueue[screenId]) {
    saveQueue[screenId] = [];
  }

  // Clear existing timer if any
  if (debounceTimers[screenId]) {
    clearTimeout(debounceTimers[screenId]);
  }

  dispatch(setSaveStatus({ screenId, status: "changes_detected" }));

  debounceTimers[screenId] = setTimeout(() => {
    // Add the data to the queue with a timestamp
    saveQueue[screenId] = [{ data, timestamp: Date.now() }];

    // If no save is in progress, start processing the queue
    if (!saveInProgress[screenId]) {
      processSaveQueue(
        screenId,
        saveFunction,
        dispatch,
        setSaveStatus,
        setLastSavedData
      );
    } else {
      console.log(
        `Save already in progress for ${screenId}, not starting queue processing`
      );
    }

    // Clean up timer
    debounceTimers[screenId] = null;
  }, delay);
};
