import React, { useState, useEffect } from "react";
import { Text, TouchableOpacity, View } from "react-native";
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";
import { BottomTabNavigationOptions } from "@react-navigation/bottom-tabs";
import { MatIcon } from "../../utils";
import Chatbot from "../../components/ChatBot";
// Screen Stack
import RepHomeStack from "../home";
import ScheduleScreenStack from "../schedule";
import MyProfileScreenStack from "../my-profile";
import TasksScreenStack from "../tasks";
import SchedulerStack from "../scheduler";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import { AppDispatch } from "../../store";
import { useDispatch } from "react-redux";
import { setChatBotState } from "../../store/chatbot";
import { useChatBotState } from "./hooks";
import ChatBot from "../../components/ChatBot";

const Tab = createBottomTabNavigator();

type BottomBarProps = {
  initialRoute?: string;
  adviser?: boolean;
};

const BottomBar: React.FC<BottomBarProps> = ({
  initialRoute = "ScheduleTab",
  adviser = false,
}) => {
  const { chatBotIsOpened, chatBotScreenFocused, screenPath } =
    useChatBotState();

  const dispatch = useDispatch<AppDispatch>();

  const handleChatBotToggle = () => {
    // Toggle the chatbot state immediately
    dispatch(
      setChatBotState({
        screenPath: screenPath,
        currentScreen: chatBotScreenFocused,
        isOpened: !chatBotIsOpened,
      })
    );
  };
  return (
    <GestureHandlerRootView>
      <Tab.Navigator
        initialRouteName={initialRoute}
        screenOptions={{
          animation: "shift",
          tabBarStyle: {
            backgroundColor: "#8143D9",
          },
          tabBarActiveTintColor: "#FFFFFF",
          tabBarInactiveTintColor: "#D2BEFE",
          headerShown: false,
        }}
      >
        <Tab.Screen
          name="DashboardTab"
          component={RepHomeStack}
          options={
            {
              tabBarLabel: "Dashboard",
              tabBarIcon: ({ color, size }) =>
                MatIcon("view-dashboard", color, size),
            } as BottomTabNavigationOptions
          }
        />
        {adviser && (
          <Tab.Screen
            name="SchedulerTab"
            component={SchedulerStack}
            options={
              {
                tabBarLabel: "Scheduler",
                tabBarIcon: ({ color, size }) =>
                  MatIcon("account-clock", color, size),
              } as BottomTabNavigationOptions
            }
          />
        )}

        <Tab.Screen
          name="TasksTab"
          component={TasksScreenStack}
          options={{
            tabBarLabel: "Tasks",
            tabBarIcon: ({ color, size }) =>
              MatIcon("clipboard-check-outline", color, size),
          }}
        />
        <Tab.Screen
          name="ScheduleTab"
          component={ScheduleScreenStack}
          options={
            {
              tabBarLabel: "Schedule",
              tabBarIcon: ({ color, size }) =>
                MatIcon("account-group", color, size),
            } as BottomTabNavigationOptions
          }
        />
        <Tab.Screen
          name="MyProfileTab"
          component={MyProfileScreenStack}
          options={
            {
              tabBarLabel: "My Profile",
              tabBarIcon: ({ color, size }) => MatIcon("account", color, size),
            } as BottomTabNavigationOptions
          }
        />
        <Tab.Screen
          name="Ask CM"
          options={{
            tabBarIcon: ({ color, size }) => MatIcon("robot", color, size),
            tabBarButton: (props) => (
              <TouchableOpacity
                {...props}
                onPress={handleChatBotToggle} // Use the new function here
              ></TouchableOpacity>
            ),
          }}
        >
          {() => null}
        </Tab.Screen>
      </Tab.Navigator>
      {chatBotIsOpened ? <ChatBot /> : null}
    </GestureHandlerRootView>
  );
};

export default BottomBar;
