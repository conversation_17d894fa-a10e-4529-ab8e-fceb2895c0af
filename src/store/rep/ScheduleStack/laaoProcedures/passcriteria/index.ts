import { createSlice } from "@reduxjs/toolkit";
import initialState from "./initialstate";
import { fetchPassCriteriaDetails, putPassCriteriaDetails } from "./thunk";

const passcriteriaSlice = createSlice({
  name: "laaoimplant",
  initialState,
  reducers: {
    setPassCriteriaUserDetails: (state, action) => {
      state.userDetails = {
        ...state.userDetails,
        ...action.payload,
      };
    },

    setPassedFailed: (state, action) => {
      state.passed = action.payload;
    },

    setSelectedDeviceSize: (state, action) => {
      state.userDetails.device.selected = action.payload;
    },

    setOptimalSizesArray: (state, action) => {
      state.optimalSizes = action.payload;
    },

    resetPassCriteriaState: (state) => {
      state.passCriteriaDetails = null;
      state.userDetails = null;
    },

    // Reducer for updating value for angle 0
    setCompressionValueForAngle0: (state, action) => {
      state.userDetails.compression_ratio =
        state.userDetails.compression_ratio.map((item) =>
          item.angle === 0
            ? {
                ...item,
                value: action.payload.updatedValue,
                compression: action.payload.compression,
              }
            : item
        );
    },

    // Reducer for updating value for angle 45
    setCompressionValueForAngle45: (state, action) => {
      state.userDetails.compression_ratio =
        state.userDetails.compression_ratio.map((item) =>
          item.angle === 45
            ? {
                ...item,
                value: action.payload.updatedValue,
                compression: action.payload.compression,
              }
            : item
        );
    },

    // Reducer for updating value for angle 90
    setCompressionValueForAngle90: (state, action) => {
      state.userDetails.compression_ratio =
        state.userDetails.compression_ratio.map((item) =>
          item.angle === 90
            ? {
                ...item,
                value: action.payload.updatedValue,
                compression: action.payload.compression,
              }
            : item
        );
    },

    // Reducer for updating value for angle 135
    setCompressionValueForAngle135: (state, action) => {
      state.userDetails.compression_ratio =
        state.userDetails.compression_ratio.map((item) =>
          item.angle === 135
            ? {
                ...item,
                value: action.payload.updatedValue,
                compression: action.payload.compression,
              }
            : item
        );
    },

    setCompressionForAngle0: (state, action) => {
      if (state.userDetails && state.userDetails.compression_ratio) {
        state.userDetails.compression_ratio =
          state.userDetails.compression_ratio.map((item) =>
            item.angle === 0
              ? {
                  ...item,
                  compression: action.payload,
                }
              : item
          );
      }
    },
    setCompressionForAngle45: (state, action) => {
      if (state.userDetails && state.userDetails.compression_ratio) {
        state.userDetails.compression_ratio =
          state.userDetails.compression_ratio.map((item) =>
            item.angle === 45
              ? {
                  ...item,
                  compression: action.payload,
                }
              : item
          );
      }
    },
    setCompressionForAngle90: (state, action) => {
      if (state.userDetails && state.userDetails.compression_ratio) {
        state.userDetails.compression_ratio =
          state.userDetails.compression_ratio.map((item) =>
            item.angle === 90
              ? {
                  ...item,
                  compression: action.payload,
                }
              : item
          );
      }
    },
    setCompressionForAngle135: (state, action) => {
      if (state.userDetails && state.userDetails.compression_ratio) {
        state.userDetails.compression_ratio =
          state.userDetails.compression_ratio.map((item) =>
            item.angle === 135
              ? {
                  ...item,
                  compression: action.payload,
                }
              : item
          );
      }
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchPassCriteriaDetails.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchPassCriteriaDetails.fulfilled, (state, action) => {
        state.loading = false;
        state.passCriteriaDetails = action.payload;
      })
      .addCase(fetchPassCriteriaDetails.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });

    // builder
    //   .addCase(putPassCriteriaDetails.pending, (state) => {
    //     state.loading = true;
    //   })
    //   .addCase(putPassCriteriaDetails.fulfilled, (state, action) => {
    //     state.loading = false;
    //     state.error = null;
    //   })
    //   .addCase(putPassCriteriaDetails.rejected, (state, action) => {
    //     state.loading = false;
    //     state.error = action.payload as string;
    //   });
  },
});

export const {
  setPassCriteriaUserDetails,
  resetPassCriteriaState,
  setOptimalSizesArray,
  setSelectedDeviceSize,
  setCompressionValueForAngle0,
  setCompressionValueForAngle45,
  setCompressionValueForAngle90,
  setCompressionValueForAngle135,
  setPassedFailed,
  setCompressionForAngle0,
  setCompressionForAngle45,
  setCompressionForAngle90,
  setCompressionForAngle135,
} = passcriteriaSlice.actions;

export default passcriteriaSlice.reducer;
