import * as React from "react";
import ScreenWrapper from "../../../components/ScreenWrapper";
import { Image, Text, View } from "react-native";
import <PERSON><PERSON>ogo from "../../../../assests/bs-logo.png";
import {
  usecaseSynopsisDetails,
  useLoaderAndError,
} from "../hooks/reportHooks";
import { fetchcaseSynopsis } from "../../../store/clinician/ScheduleStack/report/thunk";
import { useDispatch } from "react-redux";
import { AppDispatch } from "../../../store";
import { useClinicianSelectedPatient } from "../hooks/schedulesHooks";
import { useFocusEffect } from "@react-navigation/native";
import { capitalizeName, formatReportData } from "../../../utils";
import Loader from "../../../components/Loader";
import moment from "moment";

interface IReportScreenProps {}

const ReportScreen: React.FunctionComponent<IReportScreenProps> = (props) => {
  // NEW START

  const dispatch = useDispatch<AppDispatch>();
  const selectedPatient = useClinicianSelectedPatient();
  const details = usecaseSynopsisDetails();
  const reportDetails = formatReportData(details.caseSynopsis);

  const { loader, error } = useLoaderAndError();
  useFocusEffect(
    React.useCallback(() => {
      const fetchDetails = async () => {
        if (selectedPatient?.case_id) {
          const response = await dispatch(
            fetchcaseSynopsis({
              case_id: selectedPatient.case_id,
            })
          );
        }
      };

      fetchDetails();
    }, [selectedPatient?.case_id, dispatch])
  );

  // NEW END

  const uiSection1 = {
    e1: {
      title: "Pt Name",
      value: reportDetails.ptName,
    },
    e2: {
      title: "DOB",
      value: reportDetails?.dob,
    },
  };

  const uiSection2 = {
    e1: {
      title: "Procedure Date",
      value: reportDetails?.date,
    },
  };

  const uiSection3 = {
    e0: {
      title: "Hospital",
      value: reportDetails?.hospital,
    },
    e1: {
      title: "Implanting MD",
      value: reportDetails?.implantingMD,
    },
    e2: {
      title: "Watchman case specialist",
      value: reportDetails?.caseSpecialist,
    },
  };

  const PatientDetails = () => {
    const entries = Object.values(uiSection3);

    return (
      <View className="rounded-lg mt-2">
        <View className="gap-4 py-2">
          <View className="flex-1 flex-row justify-start">
            <View>
              <Text className="font-semibold text-primaryBlack">
                {uiSection1.e1.title?.toUpperCase()}:
              </Text>
            </View>
            <View className="ml-2">
              <Text className="text-primaryBlack text-left">
                {uiSection1.e1.value
                  ? capitalizeName(uiSection1.e1.value)
                  : "N/A"}
              </Text>
            </View>
          </View>
          <View className="flex-1 flex-row justify-start">
            <View className="">
              <Text className="font-semibold text-primaryBlack">
                {uiSection1.e2.title?.toUpperCase()}:
              </Text>
            </View>
            <View className="ml-2">
              <Text className="text-primaryBlack text-center">
                {uiSection1.e2.value
                  ? moment(uiSection1.e2.value).format("MM/DD/YYYY")
                  : "N/A"}
              </Text>
            </View>
          </View>
        </View>

        <View className="flex-row justify-between py-2">
          <View className="flex-1 flex-row justify-start">
            <View>
              <Text className="font-semibold text-primaryBlack">
                {uiSection2.e1.title?.toUpperCase()}:
              </Text>
            </View>
            <View className="ml-2">
              <Text className="text-primaryBlack text-left">
                {uiSection2.e1.value
                  ? moment(uiSection2.e1.value).format("MM/DD/YYYY")
                  : "N/A"}
              </Text>
            </View>
          </View>
        </View>

        <View>
          {entries.map((data, index) => (
            <View className="flex-row py-2" key={index}>
              <View>
                <Text className="text-primaryBlack font-semibold text-md">
                  {data.title?.toUpperCase()}:
                </Text>
              </View>
              <View className="ml-2">
                <Text className="text-primaryBlack text-md text-center">
                  {data.value ? data.value : "N/A"}
                </Text>
              </View>
            </View>
          ))}
        </View>
      </View>
    );
  };

  const elementSection2 = reportDetails?.baselineTable;

  const elementSection3 = {
    e1: {
      title: "Device",
      value: reportDetails?.device + " " + reportDetails?.deviceName,
    },
  };

  const elementSection4 = {
    e1: {
      title: "Leak",
      value: `${reportDetails?.leak} mm`,
    },
    // e2: {
    //   title: "complications",
    //   value: reportDetails?.complications,
    // },
    // e3: {
    //   title: "Post drug Rx",
    //   value: reportDetails?.postDrugRx?.join(", "),
    // },
    e4: {
      title: "LAP",
      value: `${reportDetails?.lap} mmHg`,
    },
    e5: {
      title: "ACT",
      value: `${reportDetails?.act} sec`,
    },
    e6: {
      title: "Creatinine",
      value: `${reportDetails?.creatinine} mg/dL`,
    },
    e7: {
      title: "Fluoro time",
      value: `${reportDetails?.fluoroTime} min`,
    },
    // e8: {
    //   title: "Fluoro",
    //   value: `${reportDetails?.fluoro} gy/cm2`,
    // },
  };

  const table2 = reportDetails?.finalMeasurements;

  const elementSection6 = {
    e1: {
      title: "PT Rationale",
      value: reportDetails?.ptRationale,
    },
    e2: {
      title: "CHA2DS2-Vasc",
      value: reportDetails?.CHAD,
    },
    e3: {
      title: "Referring Provider",
      value: reportDetails?.referringProvider,
    },
    e4: {
      title: "PCP",
      value: reportDetails?.pcp,
    },
  };

  const elementSection7 = {
    heading: "Final Measurements",
    0: {
      w: 21,
      per: 22,
    },
    45: {
      w: 21,
      per: 22,
    },
    90: {
      w: 21,
      per: 22,
    },
    135: {
      w: 20,
      per: 26,
    },
  };

  const elementSection8 = {
    e1: {
      title: "LAA Type",
      value: reportDetails?.laaType,
    },
    e3: {
      title: "Final TSP Location",
      value: reportDetails?.finalTspLocation,
    },
  };

  const handleRefresh = async () => {
    if (!selectedPatient?.case_id) return;
    await dispatch(
      fetchcaseSynopsis({
        case_id: selectedPatient.case_id,
      })
    );
  };

  if (loader) {
    return <Loader />;
  }

  return (
    <ScreenWrapper direction="column" onRefresh={handleRefresh}>
      <View className="flex-row justify-between items-center  border-primaryGray pb-3">
        <Text className="text-primaryPurple text-xl font-bold ">
          {reportDetails?.deviceName}
        </Text>
        <Image source={BostonLogo} className="mr-5" />
      </View>

      <PatientDetails />

      {elementSection2 && (
        <View className="flex-row justify-between items-start mt-2 py-2">
          <View className="bg-primaryWhite flex-1">
            <View className="border border-primaryBlack rounded-lg p-4 bg-white">
              <Text className="font-semibold text-center text-primaryBlack ">
                {elementSection2?.tableHeading?.toUpperCase()}
              </Text>
              <View className="flex-row border-b border-primaryBlack py-2">
                <Text className="flex-1 font-semibold text-center text-primaryPurple mt-3">
                  Angle
                </Text>
                <Text className="flex-1 font-semibold text-center text-primaryPurple">
                  W {"       "}(mm)
                </Text>
                <Text className="flex-1 font-semibold text-center text-primaryPurple">
                  L {"    "} (mm)
                </Text>
              </View>

              {Object.entries(elementSection2).map(([key, values], index) => {
                if (isNaN(key)) return null;

                return (
                  <View
                    key={index}
                    className={`flex-row py-2 ${
                      index === Object.entries(elementSection2).length - 1
                        ? "border-None"
                        : "border-b border-primaryBlack"
                    }`}
                  >
                    <Text className="flex-1 text-center text-primaryBlack">
                      {key}°
                    </Text>
                    <Text className="flex-1 text-center text-primaryBlack">
                      {values.value1 ? values.value1 : "N/A"}
                    </Text>
                    <Text className="flex-1 text-center text-primaryBlack">
                      {values.value2 ? values.value2 : "N/A"}
                    </Text>
                  </View>
                );
              })}
            </View>
          </View>

          <View className="flex-1 pl-4 ">
            {Object.entries(elementSection8).map(([key, value], index) => (
              <View key={index} className="flex-row mb-3">
                <View className="flex-1">
                  <Text
                    className="text-md font-semibold text-primaryBlack "
                    numberOfLines={2}
                  >
                    {value.title?.toUpperCase()}:
                  </Text>
                </View>
                <View className="flex-1 justify-end">
                  <Text
                    className="text-md text-primaryBlack  text-end"
                    // numberOfLines={}
                  >
                    {value.value ? value.value : "N/A"}
                  </Text>
                </View>
              </View>
            ))}
          </View>
        </View>
      )}

      <View className="flex-row mt-1 py-2">
        <View className="">
          <Text className="text-primaryBlack font-semibold text-md">
            {elementSection3.e1.title?.toUpperCase()}:
          </Text>
        </View>
        <View className="ml-2">
          <Text className="text-primaryBlack text-md text-center">
            {elementSection3.e1.value ? elementSection3.e1.value : "N/A"}
          </Text>
        </View>
      </View>

      {table2 && (
        <View className="flex-row justify-between items-start mt-2">
          <View className="mt-1 bg-primaryWhite flex-1">
            <View className="border border-primaryBlack rounded-lg p-4 bg-white">
              <Text className="font-semibold text-center text-primaryBlack ">
                {table2?.heading?.toUpperCase()}
              </Text>
              <View className="flex-row border-b border-primaryBlack py-2">
                <Text className="flex-1 mt-3 font-semibold text-center text-primaryPurple">
                  Angle
                </Text>
                <Text className="flex-1 font-semibold text-center text-primaryPurple">
                  W {"      "}(mm)
                </Text>
                <Text className="flex-1 font-semibold text-center text-primaryPurple">
                  C {"      "} (%)
                </Text>
              </View>

              {Object.entries(table2)?.map(([key, values], index) => {
                if (isNaN(key)) return null;
                return (
                  <View
                    key={index}
                    className={`flex-row py-2 ${
                      index === Object.entries(table2).length
                        ? "border-None"
                        : "border-b border-primaryBlack"
                    }`}
                  >
                    <Text className="flex-1 text-center text-primaryBlack">
                      {key}°
                    </Text>
                    <Text className="flex-1 text-center text-primaryBlack">
                      {values.w ? values.w : "N/A"}
                    </Text>
                    <Text className="flex-1 text-center text-primaryBlack">
                      {values.per ? values.per : "N/A"}
                    </Text>
                  </View>
                );
              })}
            </View>
          </View>

          <View className="flex-1 pl-4 ">
            <View className="flex-row flex-wrap mb-2">
              <Text className="text-md font-semibold text-primaryBlack uppercase">
                COMPLICATIONS:{" "}
              </Text>
              <Text
                className="text-md text-primaryBlack text-left"
                numberOfLines={3}
                ellipsizeMode="head"
              >
                {reportDetails?.complications
                  ? reportDetails?.complications
                  : "N/A"}
              </Text>
            </View>
            {Object.entries(elementSection4).map(([key, value], index) => (
              <View key={index} className="flex-row mb-3 justify-between ">
                <View
                  className="flex-2 justify-start items-start
                "
                >
                  <Text className="text-md font-semibold text-primaryBlack ">
                    {value.title?.toUpperCase()}:
                  </Text>
                </View>
                <View className="ml-2 flex-1 justify-start items-start">
                  <Text
                    className="text-md text-primaryBlack text-left"
                    numberOfLines={3}
                    style={{ flexWrap: "wrap" }}
                    ellipsizeMode="tail"
                  >
                    {String(value.value).includes("0")
                      ? value.title === "Leak"
                        ? "No leak"
                        : "N/A"
                      : value.value}
                  </Text>
                </View>
              </View>
            ))}
            <View className="flex-row flex-wrap">
              <Text className="text-md font-semibold text-primaryBlack uppercase">
                POST DRUG RX:{" "}
              </Text>
              <Text
                className="text-md text-primaryBlack text-left"
                numberOfLines={3}
                ellipsizeMode="head"
              >
                {reportDetails?.postDrugRx
                  ? reportDetails?.postDrugRx
                      .map((item) =>
                        item.includes("Indefinitely")
                          ? item.replace("x 0", "-").trim()
                          : item
                      )
                      .join(", ")
                  : "N/A"}
              </Text>
            </View>
          </View>
        </View>
      )}

      {elementSection6 && (
        <View className="py-2 mt-2">
          {Object.entries(elementSection6).map(([key, value], index) => (
            <View key={index} className="flex-row mb-3">
              <View className="">
                <Text className="text-md font-semibold text-primaryBlack ">
                  {value.title?.toUpperCase()}:
                </Text>
              </View>
              <View className="ml-2">
                <Text className="text-md text-primaryBlack text-center">
                  {value.value ? value.value : "N/A"}
                </Text>
              </View>
            </View>
          ))}
        </View>
      )}

      <View className="mt-9"></View>
    </ScreenWrapper>
  );
};

export default ReportScreen;
