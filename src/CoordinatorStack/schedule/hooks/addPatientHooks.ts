import { useSelector } from "react-redux";
import { RootState } from "../../../store";
import {
  IPostPatient,
  IReferringProvider,
  IImplantingPhysician,
} from "../../../store/coordinator/ScheduleStack/addpatient/types";

const useLoaderAndErrorPostPatient = () => {
  const loader = useSelector(
    (state: RootState) => state.coordinator.schedule.postPatient.loading
  );
  const error = useSelector(
    (state: RootState) => state.coordinator.schedule.postPatient.error
  );
  return { loader, error };
};

const useLoaderAndErrorReferringProvider = () => {
  const loader = useSelector(
    (state: RootState) => state.common.referringProviders.loading
  );
  const error = useSelector(
    (state: RootState) => state.common.referringProviders.error
  );
  return { loader, error };
};

const useLoaderAndErrorImplantingPhysician = () => {
  const loader = useSelector(
    (state: RootState) =>
      state.coordinator.schedule.implantingPhysicians.loading
  );
  const error = useSelector(
    (state: RootState) => state.coordinator.schedule.implantingPhysicians.error
  );
  return { loader, error };
};

const useReferringProviderDetails = () => {
  // Get the provider LIST details from the Redux state
  const referringProviderListDetails = useSelector(
    (state: RootState) => state.common.referringProviders.providerListDetails
  );
  // Transform the provider list for dropdown
  if (
    Array.isArray(referringProviderListDetails) &&
    referringProviderListDetails.length > 0
  ) {
    return referringProviderListDetails.map((provider: any) => {
      // Construct the label to always show first name and last name, even if no credentials
      let label = "";

      // Always include first name and last name
      if (provider.first_name || provider.last_name) {
        const firstName = provider.first_name || "";
        const lastName = provider.last_name || "";
        label = `${firstName} ${lastName}`.trim();

        // Add credentials if available
        if (provider.credential) {
          label += `, ${provider.credential}`;
        }
      } else if (provider.name) {
        // Fallback to provider.name if first_name/last_name are not available
        label = provider.name;
      } else {
        // Last resort fallback
        label = "Unknown Provider";
      }

      return {
        value: provider.id,
        label: label,
      };
    });
  } else {
    console.log("No referring providers found in store");
    return [];
  }
};

const usePostPatientUserDetails = () => {
  const { userDetails } = useSelector(
    (state: RootState) => state.coordinator.schedule.postPatient
  );
  return userDetails;
};

const usePostPatientDetails = () => {
  const { patientDetails } = useSelector(
    (state: RootState) => state.coordinator.schedule.postPatient
  );
  return patientDetails;
};

const findPostPatientDiff = () => {
  const userDetails = usePostPatientUserDetails();
  const procedureDetails = usePostPatientDetails();

  const diff = JSON.stringify(userDetails) !== JSON.stringify(procedureDetails);

  const currentValues = {
    first_name: userDetails?.first_name || null,
    last_name: userDetails?.last_name || null,
    middle_name: userDetails?.middle_name || null,
    dob: userDetails?.dob || null,
    sex: userDetails?.sex || null,
    mrn: userDetails?.mrn || null,
    ssn: userDetails?.ssn || null,
    referring_providers: Array.isArray(userDetails?.referring_providers)
      ? userDetails.referring_providers
      : userDetails?.referring_providers
      ? [userDetails.referring_providers]
      : [],
    rationale: userDetails?.rationale_selected_id || null,
    rationale_other: userDetails?.rationale_other || null,
    procedure_date: userDetails?.procedure_date || null,
    procedure_time: userDetails?.procedure_time || null,
    implanting_physician_id: userDetails?.implanting_physician_id || null,
  };

  return {
    hasDiff: diff,
    currentValues: currentValues,
  };
};

const formatReferringProvider = (data: IReferringProvider[] | null) => {
  return {
    referringProviders: data || [],
  };
};

const formatPcpProvider = (data: any[] | null) => {
  // Transform PcpProvider[] to SearchablePicker format
  if (Array.isArray(data) && data.length > 0) {
    const formattedProviders = data.map((provider) => ({
      label:
        provider.name ||
        `${provider.first_name || ""} ${provider.last_name || ""}`.trim(),
      value: provider.id,
    }));
    return {
      pcpProviders: formattedProviders,
    };
  }

  return {
    pcpProviders: [],
  };
};

const formatTimeToHHMMSS = (timestamp: string): string => {
  if (!timestamp) {
    throw new Error("Invalid timestamp provided.");
  }

  const timePart = timestamp.split("T")[1]?.split(".")[0];
  if (!timePart) {
    throw new Error("Invalid timestamp format.");
  }

  return timePart;
};

const formatPostPatient = (data: IPostPatient) => {
  const first_name = data?.first_name || "";
  const last_name = data?.last_name || "";
  const middle_name = data?.middle_name || "";
  const dob = data?.dob || null;
  const sex = data?.sex || "";
  const mrn = data?.mrn || "";
  const ssn = data?.ssn || "";
  const referring_provider = data?.referring_providers || "";
  const pcp_provider = data?.pcp_providers || "";
  const rationale = data?.rationale_selected_id || "";
  const rationale_other = data?.rationale_other || "";
  const procedure_date = data?.procedure_date || null;
  const procedure_time = data?.procedure_time || null;
  const implanting_physician = data?.implanting_physician_id || "";

  return {
    first_name,
    last_name,
    middle_name,
    dob,
    sex,
    mrn,
    ssn,
    referring_provider,
    pcp_provider,
    rationale,
    rationale_other,
    procedure_date,
    procedure_time,
    implanting_physician,
  };
};

export {
  useLoaderAndErrorPostPatient,
  useLoaderAndErrorReferringProvider,
  useLoaderAndErrorImplantingPhysician,
  useReferringProviderDetails,
  usePostPatientUserDetails,
  usePostPatientDetails,
  findPostPatientDiff,
  formatPostPatient,
  formatReferringProvider,
  formatPcpProvider,
};
