import * as React from 'react';
import {Text, View} from 'react-native';

interface ISiteDetailsProps {}

const SiteDetails: React.FunctionComponent<ISiteDetailsProps> = props => {
  const uiElements = {
    e1: {
      title: 'Hospital',
      value: 'Mayo Clinic',
    },
    e2: {
      title: 'State',
      value: 'NY',
    },
    e4: {
      title: 'PCP',
      value: '<PERSON>, D<PERSON>',
    },
    e5: {
      title: 'Implanting Physician',
      value: '<PERSON>, <PERSON>',
    },
  };

  return (
    // <View className="p-4 py-6 bg-primaryWhite rounded-lg ">
    //   <View className="flex-row justify-between items-center mb-4">
    //     <Text className="text-gray">Hospital</Text>
    //     <View className="flex-1 mx-2 py-1 bg-primaryBg rounded-lg">
    //       <Text className="text-center font-semibold">Mayo Clinic</Text>
    //     </View>
    //     <Text className="text-gray">State</Text>
    //     <View className="flex-1 mx-2 py-1 bg-primaryBg rounded-lg">
    //       <Text className="text-center font-semibold">New York</Text>
    //     </View>
    //   </View>

    //   <View className="flex-row justify-between mb-4">
    //     <Text className="text-gray">Account</Text>
    //     <View className="flex-1 mx-2 py-1 bg-primaryBg rounded-lg">
    //       <Text className="text-center font-semibold">**********</Text>
    //     </View>
    //     <Text className="text-gray">PCP</Text>
    //     <View className="flex-1 mx-2 py-1 bg-primaryBg rounded-lg">
    //       <Text className="text-center font-semibold">PCP</Text>
    //     </View>
    //   </View>
    //   <Text className="text-gray">Implanting Physician</Text>
    //   <View className="flex-1 mb-4 mx-2 ml-36 border-b border-gray" />
    //   <Text className="text-gray">Referring Provider</Text>
    //   <View className="flex-1 mx-2 ml-32 border-b border-gray" />
    // </View>

    <View className="bg-primaryWhite p-4 rounded-lg">
      {Object.entries(uiElements).map(([key, value], index) => {
        return (
          <View
            key={index}
            className="flex-row justify-between items-center mb-4 px-4">
            <Text className="text-primatyBlack flex-1">{value.title}</Text>
            <View className="flex-1 mx-2 py-1 border-b border-primaryGray">
              <Text className="text-primatyBlack text-center">
                {value.value}
              </Text>
            </View>
          </View>
        );
      })}
    </View>
  );
};

export default SiteDetails;
