import * as React from "react";
import { View, Text, TouchableOpacity, StyleSheet } from "react-native";
import { useNavigation } from "@react-navigation/native";
import moment from "moment";
import LineSeperator from "../../../components/LineSeperator";
import { usePatientCases } from "../hooks/PatientsHooks";
import { useDispatch } from "react-redux";
import { AppDispatch } from "../../../store";
import { setSelectedPatient } from "../../../store/rep/ScheduleStack/patients";
import { MatIcon } from "../../../utils";
import { setPatientDetails } from "../../../store/chatbot";
import { capitalizeName } from "../../../utils";

interface PatientCase {
  case_id: string;
  patient: {
    afib_ablation: boolean;
    age: number;
    anticoagulation: string[] | null;
    cha2ds2_vasc: {
      score: number;
      calculation: {
        congestive_heart_failure: number;
        hypertension: number;
        age: number;
        diabetes_mellitus: number;
        stroke_tia_thromboembolic_event: number;
        vascular_disease: number;
        sex: number;
      };
    };
    cta: boolean;
    tee: boolean;
    id: string;
    name: string;
    rationale: string;
    referring_provider: any;
    sex: string;
    study: any[];
  };
  procedure_date: string;
  procedure_time: string;
}

interface PatientCardProps {
  patientCase: PatientCase;
  chadPress: (calculation: any) => void;
}

const PatientCard: React.FunctionComponent<PatientCardProps> = ({
  patientCase,
  chadPress,
}) => {
  const dispatch = useDispatch<AppDispatch>();
  const navigation = useNavigation();
  const patientCases = usePatientCases();

  // Function to handle patient selection
  const selectPatient = (caseId: string) => {
    const selectedPatient = patientCases.find(
      (patientCase) => patientCase.case_id === caseId
    );

    if (selectedPatient) {
      dispatch(setSelectedPatient(selectedPatient));
      dispatch(
        setPatientDetails({
          caseId: selectedPatient?.case_id,
          patientName: selectedPatient?.patient?.name,
        })
      );
    } else {
      console.error("Patient case not found");
    }
  };

  const { patient, procedure_date, procedure_time } = patientCase;
  const morphData = []; // This can be replaced with actual data if available
  return (
    // <View style={styles.cardContainer}>
    <View className="flex-column w-[100%] bg-primaryWhite rounded-lg p-3 shadow-sm">
      {patient.afib_ablation && (
        <View style={styles.ribbonContainer}>
          <Text style={styles.ribbonText}>+ Afib Ablation</Text>
        </View>
      )}
      <TouchableOpacity
        onPress={() => {
          selectPatient(patientCase.case_id); // Pass case_id instead of the full patientCase object
          navigation.navigate("Patient Details", {
            name: patient.name,
          });
        }}
        className="flex-row items-center justify-between w-full"
      >
        <View className="flex-column flex-1 p-1">
          <View className="flex-row justify-between items-start p-1">
            <View className="flex-row items-center justify-between">
              <Text className="font-semibold text-center text-lg text-primaryPurple mr-2">
                {/* {patient.name} */}
                {capitalizeName(patient.name)}
              </Text>
              <View className="h-6 border-l-[2px] rounded-full border-primaryPurple" />
              <View className="flex-row items-center ml-2">
                <Text className="font-semibold text-lg text-primaryPurple mr-1">
                  {patient.age}
                </Text>
                <Text className="font-semibold text-lg text-primaryPurple mr-1">
                  Y/O
                </Text>
                <Text className="font-semibold text-lg text-primaryPurple">
                  {patient.sex}
                </Text>
              </View>
            </View>
            <View className="flex-row justify-between items-center">
              <View className="mr-1">
                {MatIcon("clock-time-four-outline", "#8143d9", 20)}
              </View>
              <Text className="text-center text-primaryPurple text-lg font-semibold">
                {procedure_time
                  ? moment(procedure_time, "HH:mm:ss").format("hh:mm A")
                  : "N/A"}
              </Text>
            </View>
          </View>

          <View className="flex-row gap-2 p-1">
            <Text className="font-md text-primaryBlack font-semibold ">
              Patient Rationale:
            </Text>
            <Text className="text-primaryBlack">
              {patient.rationale ? patient.rationale : "N/A"}
            </Text>
          </View>

          <View className="flex-row gap-2 p-1">
            <Text className="font-md text-primaryBlack font-semibold ">
              Referring Provider:
            </Text>
            <Text className="text-primaryBlack">
              {patient.referring_provider.name
                ? patient.referring_provider.name
                : "N/A"}
            </Text>
          </View>

          <View className="flex-row gap-2 p-1">
            <Text>
              <Text className="font-md text-primaryBlack font-semibold">
                Current Medication Regimen:{" "}
              </Text>

              <Text className="text-primaryBlack " numberOfLines={3}>
                {patient?.anticoagulation?.length
                  ? (() => {
                      const meds = patient.anticoagulation.map(
                        (med: any) => med
                      );
                      const hasAspirin = meds.includes("Aspirin");
                      const hasClopidogrel = meds.includes("Clopidogrel");

                      // If both Aspirin and Clopidogrel are present, replace them with "DAPT"
                      if (hasAspirin && hasClopidogrel) {
                        return [
                          ...meds.filter(
                            (med) => med !== "Aspirin" && med !== "Clopidogrel"
                          ),
                          "DAPT",
                        ].join(", ");
                      }

                      return meds.join(", ");
                    })()
                  : "None"}
              </Text>
            </Text>
          </View>
        </View>
      </TouchableOpacity>

      <LineSeperator extraStyle="my-2" />

      <View className="flex-row justify-between px-2 py-2">
        <View className="justify-end w-[50%]">
          <TouchableOpacity
            className={`flex-row shadow-sm ${
              patient?.cha2ds2_vasc?.score < 3
                ? "bg-red-1"
                : patient?.cha2ds2_vasc?.score >= 3
                ? "bg-primaryBg"
                : "bg-primaryBg"
            } rounded justify-between p-2 mb-1`}
            onPress={() => chadPress(patient.cha2ds2_vasc.calculation)}
          >
            <Text
              className={`${
                patient?.cha2ds2_vasc?.score < 3
                  ? "text-red-3"
                  : patient?.cha2ds2_vasc?.score >= 3
                  ? "text-primaryPurple"
                  : "text-primaryPurple"
              }`}
            >
              CHA<Text style={{ fontSize: 9 }}>2</Text>DS
              <Text style={{ fontSize: 9 }}>2</Text>-VASc
            </Text>
            <Text
              className={`
                                       ${
                                         patient?.cha2ds2_vasc?.score < 3
                                           ? "text-red-3"
                                           : patient?.cha2ds2_vasc?.score >= 3
                                           ? "text-primaryPurple"
                                           : "text-primaryPurple"
                                       }
                                       `}
            >
              {patient?.cha2ds2_vasc?.score}
            </Text>
          </TouchableOpacity>
        </View>

        <View className="items-end gap-2 w-[50%]">
          <TouchableOpacity
            className={`${
              patient?.cta || patient?.study?.length > 0
                ? `bg-green-2`
                : `bg-secondaryGray`
            } rounded items-center w-[60%] p-2 shadow-sm`}
            onPress={() => {
              if (!patient?.cta && patient?.study?.length > 0) {
                navigation.navigate("WebViewer", {
                  link: patient?.study[0]?.viewer_link,
                });
              } else if (patient?.cta) {
                selectPatient(patientCase.case_id);
                navigation.navigate("CTA");
              }
            }}
            disabled={patient?.cta || patient?.study?.length > 0 ? false : true}
          >
            <Text
              className={`
                    ${
                      patient?.cta || patient?.study?.length > 0
                        ? `text-green-3`
                        : `text-primaryWhite`
                    }
                    `}
            >
              {"Pre-Op CTA"}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            className={`${
              patient?.tee ? `bg-green-2` : `bg-secondaryGray`
            } rounded items-center w-[60%] p-2 shadow-sm`}
            disabled={patient?.tee ? false : true}
            onPress={() => {
              selectPatient(patientCase.case_id);
              navigation.navigate("TEE", {
                morphology: morphData,
                clotData: morphData,
                angles: ["0", "90", "45", "135"],
                diameters: ["26 mm", "19 mm", "19 mm", "18 mm"],
                depths: ["37 mm", "30 mm", "35 mm", "26 mm"],
              });
            }}
          >
            <Text
              className={`
                    ${patient?.tee ? `text-green-3` : `text-primaryWhite`}
                    `}
            >
              {"Pre-Op TEE"}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  ribbonContainer: {
    position: "absolute",
    top: 0,
    right: 12,

    backgroundColor: "#FF4D4F", // Red color for the ribbon
    paddingVertical: 2,
    marginRight: "5%",
    width: 100,
    alignItems: "center",
    // justifyContent: "center",
    // zIndex: 10,
    // borderTopRightRadius: 8, // top-right corner radius
    // borderBottomRightRadius: 5,
    // borderTopLeftRadius: 5,
    // borderBottomLeftRadius: 5,
  },
  ribbonText: {
    color: "#FFFFFF",
    fontWeight: "bold",
    fontSize: 10,
  },
});

export default PatientCard;
