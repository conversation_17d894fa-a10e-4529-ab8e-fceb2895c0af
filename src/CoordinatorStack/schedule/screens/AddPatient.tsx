import React, { useState } from "react";
import { View, Text, TouchableOpacity } from "react-native";
import CustomCard from "../../../components/CustomCard";
import ScreenWrapper from "../../../components/ScreenWrapper";
import SaveActionButton from "../../../components/SaveActionButton";
import LineSeperator from "../../../components/LineSeperator";
import SearchablePicker from "../../../components/SearchablePicker";
import DatePicker from "react-native-date-picker";
import moment from "moment";
import CustomInput from "../../../components/CustomTextInput";
import CustomBottomSheetTextInput from "../../../components/CustomBottomSheetTextInput";
import CustomText from "../../../components/CustomText";
import { setAfibBAsicUserDetails } from "../../../store/rep/ScheduleStack/afibAblation/basic";
import { Line } from "react-native-svg";
import { useDispatch } from "react-redux";
import { AppDispatch } from "../../../store";
import AddReferringProvider from "../components/AddReferringProvider";
import AddPcpProvider from "../components/AddPcpProvider";
import CustomBottomModal, {
  CustomBottomModalRefProps,
} from "../../../components/CustomBottomModal";
import { useCoordinatorSelectedPatient } from "../../schedule/hooks/schedulesHooks";
import {
  useReferringProviderDetails,
  findPostPatientDiff,
  usePostPatientUserDetails,
  formatPostPatient,
  useLoaderAndErrorPostPatient,
  formatPcpProvider,
} from "../hooks/addPatientHooks.ts";
import { useProvidersListData } from "../hooks/addPcpProviderHooks.ts";
import {
  useImplantingPhysicianDetails,
  formatImplantingPhysician,
  parseCharInput,
  parseAlphaNumericInput,
  formatRationale,
  useRationaleDetails,
  parseSSNInput,
  parseMRNInput,
} from "../../../utils.tsx";
import { useFocusEffect } from "@react-navigation/native";
import { fetchReferringProviders } from "../../../store/common/addReferringProvider/thunk.ts";
import { fetchPcpProviders } from "../../../store/common/addPcpProvider/thunk.ts";
import {
  // fetchReferringProviders,
  fetchImplantingPhysicians,
  postPatientDetails,
  fetchRationale,
} from "../../../store/coordinator/ScheduleStack/addpatient/thunk";
import {
  setPatientDetails,
  resetPatientDetails,
} from "../../../store/coordinator/ScheduleStack/addpatient";
import { IPostPatient } from "../../../store/coordinator/ScheduleStack/addpatient/types";
import PopupModal from "../../../components/Popup";
import Loader from "../../../components/Loader.tsx";

interface IAddPatientProps {
  onClose: () => void;
  bottomSheetRef: React.RefObject<any>;
  onSave: () => void;
}

const AddPatient: React.FunctionComponent<IAddPatientProps> = ({
  onClose,
  bottomSheetRef,
  onSave,
}) => {
  const dispatch = useDispatch<AppDispatch>();
  const { loader, error } = useLoaderAndErrorPostPatient();
  const useReferringProviderDetail = useReferringProviderDetails();
  const usePcpProviderDetail = useProvidersListData();
  const useImplantingPhysicianDetail = useImplantingPhysicianDetails();
  const useRationaleDetail = useRationaleDetails();
  const userDetails = usePostPatientUserDetails();
  const [popupMsg, setPopupMsg] = React.useState<string[]>([]);
  const [modalVisible, setModalVisible] = React.useState(false);
  const [rationaleSelectedName, setRationaleSelectedName] = React.useState("");

  // Bottom sheet refs and state for provider addition
  const addReferringProviderBottomSheetRef =
    React.useRef<CustomBottomModalRefProps>(null);
  const addPcpProviderBottomSheetRef =
    React.useRef<CustomBottomModalRefProps>(null);
  const [isAddingReferringProvider, setIsAddingReferringProvider] =
    React.useState(false);
  const [isAddingPcpProvider, setIsAddingPcpProvider] = React.useState(false);

  // Track provider counts to detect new additions
  const [previousRefProviderCount, setPreviousRefProviderCount] =
    React.useState(0);
  const [previousPcpProviderCount, setPreviousPcpProviderCount] =
    React.useState(0);

  const validator = () => {
    setPopupMsg([]);

    var temp = false;
    if (first_name === "" || first_name === null || first_name === undefined) {
      setPopupMsg((prev) => [...prev, "Please Enter First Name"]);
      temp = true;
    }

    if (last_name === "" || last_name === null || last_name === undefined) {
      setPopupMsg((prev) => [...prev, "Please Enter Last Name"]);
      temp = true;
    }

    if (sex === "" || sex === null || sex === undefined) {
      setPopupMsg((prev) => [...prev, "Please Enter Gender"]);
      temp = true;
    }

    if (dob === "" || dob === null || dob === undefined) {
      setPopupMsg((prev) => [...prev, "Please Enter DOB"]);
      temp = true;
    }

    if (mrn === "" || mrn === null || mrn === undefined) {
      setPopupMsg((prev) => [...prev, "Please Enter MRN Value"]);
      temp = true;
    }

    if (
      procedure_date === "" ||
      procedure_date === null ||
      procedure_date === undefined
    ) {
      setPopupMsg((prev) => [...prev, "Please Enter Procedure Date"]);
      temp = true;
    }

    if (
      procedure_time === "" ||
      procedure_time === null ||
      procedure_time === undefined
    ) {
      setPopupMsg((prev) => [...prev, "Please Enter Procedure Time"]);
      temp = true;
    }

    if (
      implanting_physician === "" ||
      implanting_physician === null ||
      implanting_physician === undefined
    ) {
      setPopupMsg((prev) => [...prev, "Please Select Implanting Physician"]);
    }

    if (temp) {
      setModalVisible(true);
      return false;
    } else {
      return true;
    }
  };

  const {
    first_name,
    last_name,
    middle_name,
    dob,
    sex,
    mrn,
    ssn,
    referring_provider,
    pcp_provider,
    rationale,
    rationale_other,
    procedure_date,
    procedure_time,
    implanting_physician,
  } = formatPostPatient(userDetails || ({} as IPostPatient));
  const referringProviders = useReferringProviderDetail;
  const { pcpProviders } = formatPcpProvider(usePcpProviderDetail);

  const { implantingPhysicians } = formatImplantingPhysician(
    useImplantingPhysicianDetail
  );

  const { rationaleOptions } = formatRationale(useRationaleDetail);

  const fetchDetails = async () => {
    try {
      const refProviderAction = await dispatch(fetchReferringProviders());
      if (fetchReferringProviders.fulfilled.match(refProviderAction)) {
        console.log(
          "Successfully fetched referring providers:",
          refProviderAction.payload?.length || 0
        );
      } else {
        console.error(
          "Failed to fetch referring providers:",
          refProviderAction.error
        );
      }

      const pcpProviderAction = await dispatch(fetchPcpProviders());
      if (fetchPcpProviders.fulfilled.match(pcpProviderAction)) {
        console.log(
          "Successfully fetched PCP providers:",
          pcpProviderAction.payload?.length || 0
        );
      } else {
        console.error(
          "Failed to fetch PCP providers:",
          pcpProviderAction.error
        );
      }

      await dispatch(fetchImplantingPhysicians());
      await dispatch(fetchRationale());
    } catch (error) {
      console.error("Error in fetchDetails:", error);
    }
  };

  // Fetch initial data when screen is focused
  useFocusEffect(
    React.useCallback(() => {
      fetchDetails();
    }, [])
  );

  React.useEffect(() => {
    fetchDetails();
  }, [dispatch]);

  // Track referring provider count changes to detect new additions
  React.useEffect(() => {
    if (referringProviders && referringProviders.length > 0) {
      if (
        referringProviders.length > previousRefProviderCount &&
        previousRefProviderCount > 0
      ) {
        // A new provider was added, find and select it
        const newProvider = referringProviders[referringProviders.length - 1];
        if (newProvider) {
          dispatch(
            setPatientDetails({
              referring_providers: newProvider.value,
            })
          );
        }
        setIsAddingReferringProvider(false);
      }
      // Update the count for next comparison
      setPreviousRefProviderCount(referringProviders.length);
    }
  }, [referringProviders]);

  // Track PCP provider count changes to detect new additions
  React.useEffect(() => {
    if (pcpProviders && pcpProviders.length > 0) {
      if (
        pcpProviders.length > previousPcpProviderCount &&
        previousPcpProviderCount > 0
      ) {
        // A new provider was added, find and select it
        const newProvider = pcpProviders[pcpProviders.length - 1];
        if (newProvider) {
          dispatch(
            setPatientDetails({
              pcp_providers: newProvider.value,
            })
          );
        }
        setIsAddingPcpProvider(false);
      }
      // Update the count for next comparison
      setPreviousPcpProviderCount(pcpProviders.length);
    }
  }, [pcpProviders]);

  const [dobDateModal, setDobDateModal] = React.useState(false);
  const [procedureDateModal, setProcedureDateModal] = React.useState(false);
  const [procedureTimeModal, setProcedureTimeModal] = React.useState(false);
  const diff = findPostPatientDiff();
  const saveDetails = async () => {
    try {
      if (validator()) {
        const res = await dispatch(
          postPatientDetails({
            payload: diff.currentValues,
          })
        );
        if (res) {
          bottomSheetRef.current?.close();
          onSave();
        }
      }
    } catch (err) {
      console.error(err);
    }
  };

  const handleCancel = () => {
    dispatch(resetPatientDetails());
    bottomSheetRef.current?.close();
  };

  // Handle opening add provider modal
  const handleAddProvider = (
    context: "referring_provider" | "pcp_provider"
  ) => {
    // Store current counts before opening modal
    if (context === "referring_provider") {
      setPreviousRefProviderCount(referringProviders?.length || 0);
    } else if (context === "pcp_provider") {
      setPreviousPcpProviderCount(pcpProviders?.length || 0);
    }

    // If already open, close it first to reset states
    if (context === "referring_provider") {
      addReferringProviderBottomSheetRef.current?.close();
      setTimeout(() => {
        addReferringProviderBottomSheetRef.current?.open();
      }, 100);
    } else if (context === "pcp_provider") {
      addPcpProviderBottomSheetRef.current?.close();
      setTimeout(() => {
        addPcpProviderBottomSheetRef.current?.open();
      }, 100);
    }
  };

  // Handler for adding new referring provider
  const handleAddReferringProvider = (newProviderId: string) => {
    setIsAddingReferringProvider(true);

    // Fetch updated provider list in background
    dispatch(fetchReferringProviders()).then(() => {
      // Set the new provider ID in the patient details
      dispatch(
        setPatientDetails({
          referring_providers: newProviderId,
        })
      );
      setIsAddingReferringProvider(false);
    });
  };

  // Handler for adding new PCP provider
  const handleAddPcpProvider = (newProviderId: string) => {
    setIsAddingPcpProvider(true);

    // Fetch updated provider list in background
    dispatch(fetchPcpProviders()).then(() => {
      // Set the new provider ID in the patient details
      dispatch(
        setPatientDetails({
          pcp_providers: newProviderId,
        })
      );
      setIsAddingPcpProvider(false);
    });
  };

  return (
    <>
      {loader ? (
        <View className="mt-64">
          <Loader />
        </View>
      ) : (
        <View className="p-3 bg-primaryWhite">
          <View className="rounded-md p-3">
            <Text className="text-primaryPurple text-center font-semibold text-lg mb-6">
              Add Patient
            </Text>
            {/* Patient Details */}
            <View className="flex-row justify-between items-center ">
              <Text className="text-primaryBlack font-medium w-[40%]">
                First Name <Text className="text-red-3 text-lg">*</Text>
              </Text>
              <View className="flex-1">
                <CustomBottomSheetTextInput
                  inputValue={first_name}
                  placeholder="First Name"
                  onInputChange={(value) => {
                    parseCharInput(value, (parsedValue) => {
                      dispatch(
                        setPatientDetails({
                          first_name: parsedValue,
                        })
                      );
                    });
                  }}
                />
              </View>
            </View>

            <LineSeperator extraStyle="my-5" />
            <View className="flex-row justify-between items-center ">
              <Text className="text-primaryBlack font-medium w-[40%]">
                Middle Name
              </Text>
              <View className="flex-1">
                <CustomInput
                  inputValue={middle_name}
                  placeholder="Middle Name"
                  onInputChange={(value) =>
                    parseCharInput(value, (parsedValue) => {
                      dispatch(
                        setPatientDetails({
                          middle_name: parsedValue,
                        })
                      );
                    })
                  }
                />
              </View>
            </View>
            <LineSeperator extraStyle="my-5" />

            <View className="flex-row justify-between items-center ">
              <Text className="text-primaryBlack font-medium w-[40%]">
                Last Name <Text className="text-red-3 text-lg">*</Text>
              </Text>
              <View className="flex-1">
                <CustomInput
                  inputValue={last_name}
                  onInputChange={(value) =>
                    parseCharInput(value, (parsedValue) => {
                      dispatch(
                        setPatientDetails({
                          last_name: parsedValue,
                        })
                      );
                    })
                  }
                  placeholder="Last Name"
                />
              </View>
            </View>
            <LineSeperator extraStyle="my-5" />

            <View className="flex-row justify-between items-center ">
              <Text className="text-primaryBlack font-medium w-[40%]">
                Gender <Text className="text-red-3 text-lg">*</Text>
              </Text>
              <View className="flex-1">
                <SearchablePicker
                  placeholder="Select"
                  items={[
                    { label: "Male", value: "M" },
                    { label: "Female", value: "F" },
                  ]}
                  value={sex}
                  onValueChange={(value) =>
                    dispatch(setPatientDetails({ sex: value.value }))
                  }
                  disableError
                />
              </View>
            </View>
            <LineSeperator extraStyle="my-5" />

            <View className="flex-row justify-between items-center ">
              <Text className="text-primaryBlack font-medium w-[40%]">
                Date of Birth <Text className="text-red-3 text-lg">*</Text>
              </Text>
              <TouchableOpacity
                onPress={() => {
                  setDobDateModal(true);
                }}
                className="flex-1 border border-primaryPurple pl-2 pr-4 py-4 rounded-lg"
              >
                <CustomText
                  value={dob ? moment(dob).format("MM/DD/YYYY") : "Select"}
                  className="text-primaryGray text-md"
                />
              </TouchableOpacity>
            </View>
            <LineSeperator extraStyle="my-5" />

            <View className="flex-row justify-between items-center">
              <Text className="text-primaryBlack font-medium w-[40%]">SSN</Text>
              <View className="flex-1">
                <CustomInput
                  inputValue={
                    ssn && ssn.length === 9
                      ? ssn.replace(/^(\d{3})(\d{2})(\d{4})$/, "$1-$2-$3")
                      : ssn
                  }
                  placeholder="SSN"
                  onInputChange={(value) =>
                    parseSSNInput(value, () => {
                      dispatch(
                        setPatientDetails({
                          ssn: value.replace(/[^0-9]/g, ""),
                        })
                      );
                    })
                  }
                  keyboardType="numeric"
                />
              </View>
            </View>
            <LineSeperator extraStyle="my-5" />

            <View className="flex-row justify-between items-center">
              <Text className="text-primaryBlack font-medium w-[40%]">
                MRN <Text className="text-red-3 text-lg">*</Text>
              </Text>
              <View className="flex-1">
                <CustomInput
                  inputValue={mrn}
                  placeholder="MRN"
                  onInputChange={(value) =>
                    parseMRNInput(value, (cleanedValue) =>
                      dispatch(
                        setPatientDetails({
                          mrn: cleanedValue,
                        })
                      )
                    )
                  }
                />
              </View>
            </View>

            <LineSeperator extraStyle="my-5" />

            <View className="flex-row justify-between items-center">
              <Text className="text-primaryBlack font-medium w-[40%]">
                Referring Provider
              </Text>
              <View className="flex-1">
                <SearchablePicker
                  placeholder="Select"
                  items={referringProviders}
                  value={referring_provider}
                  onValueChange={(value) =>
                    dispatch(
                      setPatientDetails({
                        referring_providers: value.value,
                      })
                    )
                  }
                  disableError
                  screenContext="referring_provider"
                  onAddPress={() => handleAddProvider("referring_provider")}
                  showAddInSearch={true}
                />
              </View>
            </View>
            <LineSeperator extraStyle="my-5" />

            <View className="flex-row justify-between items-center">
              <Text className="text-primaryBlack font-medium w-[40%]">
                PCP Provider
              </Text>
              <View className="flex-1">
                <SearchablePicker
                  placeholder="Select"
                  items={pcpProviders?.map((provider) => ({
                    label: provider?.label,
                    value: provider?.value,
                  }))}
                  value={pcp_provider}
                  onValueChange={(value) =>
                    dispatch(
                      setPatientDetails({
                        pcp_providers: value.value,
                      })
                    )
                  }
                  disableError
                  screenContext="pcp_provider"
                  onAddPress={() => handleAddProvider("pcp_provider")}
                  showAddInSearch={true}
                />
              </View>
            </View>
            <LineSeperator extraStyle="my-5" />

            <View className="flex-row justify-between items-center ">
              <Text className="text-primaryBlack font-medium w-[40%]">
                Rationale
              </Text>
              <View className="flex-1">
                <View className="flex-1">
                  <SearchablePicker
                    placeholder="Select"
                    items={rationaleOptions.map((item) => ({
                      label: item.name,
                      value: item.id,
                    }))}
                    value={rationale}
                    onValueChange={(value) => {
                      setRationaleSelectedName(value.label);
                      dispatch(
                        setPatientDetails({
                          rationale_selected_id: value.value,
                        })
                      );
                    }}
                    disableError
                  />
                </View>

                {rationaleSelectedName?.toLowerCase() === "other" && (
                  <View className="mt-4 flex-1">
                    <CustomInput
                      placeholder="Other"
                      height={38}
                      inputValue={rationale_other || ""}
                      onInputChange={(val) => {
                        dispatch(
                          setPatientDetails({
                            rationale_other: val,
                          })
                        );
                      }}
                    />
                  </View>
                )}
              </View>
            </View>

            <LineSeperator extraStyle="my-5" />

            <View className="flex-row justify-between items-center ">
              <Text className="text-primaryBlack font-medium w-[40%]">
                Procedure Date <Text className="text-red-3 text-lg">*</Text>
              </Text>
              <TouchableOpacity
                onPress={() => {
                  setProcedureDateModal(true);
                }}
                className="flex-1 border border-primaryPurple pl-2 pr-4 py-4 rounded-lg"
              >
                <CustomText
                  value={
                    procedure_date
                      ? moment(procedure_date).format("MM/DD/YYYY")
                      : "Select"
                  }
                  className="text-primaryGray text-md"
                />
              </TouchableOpacity>
            </View>

            <LineSeperator extraStyle="my-5" />

            <View className="flex-row justify-between items-center ">
              <Text className="text-primaryBlack font-medium w-[40%]">
                Procedure Time <Text className="text-red-3 text-lg">*</Text>
              </Text>
              <TouchableOpacity
                onPress={() => {
                  setProcedureTimeModal(true);
                }}
                className="flex-1 border border-primaryPurple pl-2 pr-4 py-4 rounded-lg"
              >
                <CustomText
                  value={
                    procedure_time
                      ? moment(procedure_time, "HH:mm:ss").format("HH:mm")
                      : "Select"
                  }
                  className="text-primaryGray text-md"
                />
              </TouchableOpacity>
            </View>

            <LineSeperator extraStyle="my-5" />

            <View className="flex-row justify-between items-center">
              <View className="w-[40%]">
                <Text className="text-primaryBlack font-medium ">
                  Implanting
                  <Text className="ml-2 text-red-3 text-lg">*</Text>
                </Text>
                <Text className="text-primaryBlack font-medium ">
                  Physician
                </Text>
              </View>
              <View className="flex-1">
                <SearchablePicker
                  placeholder="Select"
                  items={implantingPhysicians.map((physician) => ({
                    label: physician.name,
                    value: physician.id,
                  }))}
                  value={implanting_physician}
                  onValueChange={(value) =>
                    dispatch(
                      setPatientDetails({
                        implanting_physician_id: value.value,
                      })
                    )
                  }
                  disableError
                />
              </View>
            </View>

            <LineSeperator extraStyle="my-5" />

            <View className="flex-row justify-center gap-4">
              {/* <TouchableOpacity
              onPress={saveDetails}
              className="border border-primaryPurple px-6 py-3 rounded-full bg-primaryPurple"
            >
              <Text className="text-primaryWhite font-semibold">Save</Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={handleCancel}
              className="border border-primaryPurple px-6 py-3 rounded-full bg-primaryBg"
            >
              <Text className="text-primaryPurple">Clear</Text>
            </TouchableOpacity> */}
              <SaveActionButton
                disabled={!diff}
                onPress={saveDetails}
                onCancel={handleCancel}
              />
            </View>
          </View>
          <View className="mt-9"></View>
        </View>
      )}

      <DatePicker
        modal
        open={dobDateModal}
        date={dob ? moment(dob, "YYYY-MM-DD").toDate() : new Date()}
        onConfirm={(date) => {
          setDobDateModal(false);
          dispatch(
            setPatientDetails({
              dob: moment(date).format("YYYY-MM-DD"),
            })
          );
        }}
        onCancel={() => setDobDateModal(false)}
        mode={"date"}
        maximumDate={new Date()}
      />

      <DatePicker
        modal
        open={procedureDateModal}
        date={
          procedure_date
            ? moment(procedure_date, "YYYY-MM-DD").toDate()
            : new Date()
        }
        onConfirm={(date) => {
          setProcedureDateModal(false);
          dispatch(
            setPatientDetails({
              procedure_date: moment(date).format("YYYY-MM-DD"),
            })
          );
        }}
        onCancel={() => {
          setProcedureDateModal(false);
        }}
        mode={"date"}
        minimumDate={new Date()}
      />

      <DatePicker
        modal
        open={procedureTimeModal}
        date={
          procedure_time
            ? moment(procedure_time, "HH:mm:ss").toDate()
            : new Date()
        }
        onConfirm={(date) => {
          setProcedureTimeModal(false);
          dispatch(
            setPatientDetails({
              procedure_time: moment(date).format("HH:mm:ss"),
            })
          );
        }}
        onCancel={() => {
          setProcedureTimeModal(false);
        }}
        mode={"time"}
      />
      <PopupModal
        show={modalVisible}
        msg={popupMsg}
        status="warning"
        onClose={() => setModalVisible(false)}
      />

      {/* Custom Bottom Modal for Adding Referring Provider */}
      <CustomBottomModal
        ref={addReferringProviderBottomSheetRef}
        height="90%"
        backgroundColor="#f8f9fa"
        onClose={() => {
          // Optional: Add any cleanup logic here
        }}
      >
        <AddReferringProvider
          onSuccess={handleAddReferringProvider}
          onCancel={() => addReferringProviderBottomSheetRef.current?.close()}
          bottomSheetRef={addReferringProviderBottomSheetRef}
        />
      </CustomBottomModal>

      {/* Custom Bottom Modal for Adding PCP Provider */}
      <CustomBottomModal
        ref={addPcpProviderBottomSheetRef}
        height="90%"
        backgroundColor="#f8f9fa"
        onClose={() => {
          // Optional: Add any cleanup logic here
        }}
      >
        <AddPcpProvider
          onSuccess={handleAddPcpProvider}
          onCancel={() => addPcpProviderBottomSheetRef.current?.close()}
          bottomSheetRef={addPcpProviderBottomSheetRef}
        />
      </CustomBottomModal>
    </>
  );
};

export default AddPatient;
