interface AbstractorPatientCase {
  procedure_date: string;
  patient_id: string;
  patient_name: string;
  dob: string;
  referring_provider: string;
}

interface AbstractorInitialState {
  schedules: AbstractorPatientCase[] | null;
  selectedPatient: AbstractorPatientCase | null;
  loaders: {
    scheduleLoader: boolean;
  };
  errors: {
    scheduleError: string | null;
  };
}

export type { AbstractorInitialState };
