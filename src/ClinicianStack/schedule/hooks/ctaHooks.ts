import { useSelector } from "react-redux";
import { RootState } from "../../../store";
import { current } from "@reduxjs/toolkit";

const useLoaderAndError = () => {
  const loader = useSelector(
    (state: RootState) => state.coordinator.schedule.cta.loading
  );
  const error = useSelector(
    (state: RootState) => state.coordinator.schedule.cta.error
  );
  return { loader, error };
};

const useTruplanLink = () => {
  const truplanLink = useSelector(
    (state: RootState) => state.coordinator.schedule.cta.truplanPdfLink
  );

  return truplanLink;
};

const useCTADetails = () => {
  const {
    max_diameter,
    area,
    avg_diameter,
    comments,
    depth,
    laa_cross_section_url,
    laa_long_axis_url,
    min_diameter,
    perimeter,
    study,
    notes,
  } = useSelector(
    (state: RootState) => state.coordinator.schedule.cta.ctaDetails || {}
  );

  return {
    max_diameter,
    area,
    avg_diameter,
    comments,
    depth,
    laa_cross_section_url,
    laa_long_axis_url,
    min_diameter,
    perimeter,
    study,
    notes,
  };
};

const useApiCtaDetails = () => {
  const {
    max_diameter,
    area,
    avg_diameter,
    comments,
    depth,
    laa_cross_section_url,
    laa_long_axis_url,
    min_diameter,
    perimeter,
    study,
    notes,
  } = useSelector(
    (state: RootState) => state.coordinator.schedule.cta.apiCtaDetails || {}
  );

  return {
    max_diameter,
    area,
    avg_diameter,
    comments,
    depth,
    laa_cross_section_url,
    laa_long_axis_url,
    min_diameter,
    perimeter,
    study,
    notes,
  };
};

const findDiff = () => {
  const ctaDetails = useCTADetails();
  const apiCtaDetails = useApiCtaDetails();

  let temp: boolean = false;

  if (JSON.stringify(ctaDetails) !== JSON.stringify(apiCtaDetails)) {
    temp = true;
  } else {
    temp = false;
  }

  const payload = {
    notes: ctaDetails.notes,
  };

  return {
    hasdiff: temp,
    currentValues: payload,
  };
};

export { useLoaderAndError, useCTADetails, useTruplanLink, findDiff };
