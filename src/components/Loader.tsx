import * as React from 'react';
import {View, ActivityIndicator} from 'react-native';

// Define the props for the Loader component
interface LoaderProps {
  size?: 'small' | 'large'; // Size of the loader ('small' or 'large')
  color?: string; // Color of the loader
}

// Functional component for Loader with default props
const Loader: React.FunctionComponent<LoaderProps> = ({
  size = 'large',
  color = '#8143d9', // Default color (purple shade)
}) => {
  return (
    <View
      style={{
        flex: 1,
        justifyContent: 'center', // Center vertically
        alignItems: 'center', // Center horizontally
        position: 'absolute',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        zIndex: 50, // Ensure loader appears above other content
      }}>
      <ActivityIndicator size={size} color={color} />
    </View>
  );
};

export default Loader;
