import { createAsyncThunk } from "@reduxjs/toolkit";
import api from "../../../../api/api";
import { IPostOpSchema } from "./types";

const fetchPostOpDetails = createAsyncThunk(
  "postOp/fetchPostOpDetails",
  async ({ caseId }: { caseId: string }, thunkAPI) => {
    try {
      const response = await api.private.get(
        api.routes.privateRoutes.rep.post_op_get(caseId)
      );

      if (response.data && response.data.result) {
        return response.data.result;
      } else {
        return thunkAPI.rejectWithValue(
          'Invalid response format: Missing "result" field'
        );
      }
    } catch (error: any) {
      if (error.response) {
        if (error.response.status === 404) {
        }
        return thunkAPI.rejectWithValue(
          error.response.data.error_description ||
            "Failed to fetch Post-Op Details"
        );
      } else if (error.request) {
        console.error("No response received:", error.request);
        return thunkAPI.rejectWithValue("No response received from the server");
      } else {
        console.error("Error occurred:", error.message);
        return thunkAPI.rejectWithValue(
          error.message || "An unknown error occurred"
        );
      }
    }
  }
);

const putPostOpDetails = createAsyncThunk(
  "postOp/putPostOpDetails",
  async ({ caseId, payload }: { caseId: string; payload: any }, thunkAPI) => {
    try {
      const response = await api.private.put(
        api.routes.privateRoutes.rep.post_op_put(caseId),
        payload
      );

      if (response.data) {
        return response.data.result;
      } else {
        return thunkAPI.rejectWithValue(
          "Invalid response format: Missing data"
        );
      }
    } catch (error) {
      console.error("Error updating post-op details:", error.response);
      return thunkAPI.rejectWithValue("Failed to update post-op details");
    }
  }
);

export { fetchPostOpDetails, putPostOpDetails };
