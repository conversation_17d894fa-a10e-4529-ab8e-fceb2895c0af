import { useSelector } from "react-redux";
import { RootState } from "../../../store";

const useAnesthesiaDetails = () => {
  const { anesthesiaDetails } = useSelector(
    (state: RootState) => state.rep.anesthesia
  );
  return anesthesiaDetails;
};

const useAnesthesiaUserDetails = () => {
  const { userDetails } = useSelector(
    (state: RootState) => state.rep.anesthesia
  );
  return userDetails;
};

const useLoaderAndError = () => {
  const loader = useSelector(
    (state: RootState) => state.rep.anesthesia.loading
  );
  const error = useSelector((state: RootState) => state.rep.anesthesia.error);
  return { loader, error };
};

const findAnesthesiaDiff = () => {
  const anesthesiaDetails = useAnesthesiaDetails();
  const userDetails = useAnesthesiaUserDetails();

  const diff = {
    anesthesia_type:
      anesthesiaDetails?.anesthesia?.selected.id ===
      userDetails?.anesthesia?.selected.id,

    imaging_type:
      anesthesiaDetails?.imaging?.selected.id ===
      userDetails?.imaging?.selected.id,

    catheter_type:
      anesthesiaDetails?.catheter?.selected.id ===
      userDetails?.catheter?.selected.id,

    fluid_bolus:
      anesthesiaDetails?.fluid_bolus?.selected.id ===
      userDetails?.fluid_bolus?.selected.id,

    la_pressure: anesthesiaDetails?.la_pressure === userDetails?.la_pressure,
  };

  const currentValues = {
    anesthesia_type: userDetails?.anesthesia?.selected.id,
    procedure_imaging_type: userDetails?.imaging?.selected.id,
    catheter_type: userDetails?.catheter?.selected.id
      ? userDetails?.catheter?.selected.id
      : null,
    fluid_bolus: userDetails?.fluid_bolus?.selected.id,
    la_pressure: userDetails?.la_pressure,
  };

  return {
    hasDiff: Object.values(diff).includes(false),
    currentValues: currentValues,
  };
};

export {
  useAnesthesiaDetails,
  useAnesthesiaUserDetails,
  useLoaderAndError,
  findAnesthesiaDiff,
};
