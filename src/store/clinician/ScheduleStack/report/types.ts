interface study {
  viewer_link: string;
  uuid: string;
  modality: string;
}

interface preopCTA {
  max_diameter: number;
  min_diameter: number;
  avg_diameter: number;
  area: number;
  perimeter: number;
  depth: number;
  comments: string;
  laa_cross_section_url: string;
  laa_long_axis_url: string;
  study: [study] | [];
}

interface IinitialState {
  ctaDetails: preopCTA;
  truplanPdfLink: string | null;
  loading: boolean;
  error: string | null;
}
export type { IinitialState };
