import { useSelector } from "react-redux";
import { RootState } from "../../../store";
import { ImplantingPhysician } from "../../../store/rep/ScheduleStack/addPhysician/types";

const useLoaderAndError = () => {
  const loader = useSelector(
    (state: RootState) => state.rep.settings.implantingPhysicians.loading
  );

  const error = useSelector(
    (state: RootState) => state.rep.settings.implantingPhysicians.error
  );
  return { loader, error };
};

const useProcedureTypes = () => {
  const procedureTypes = useSelector(
    (state: RootState) => state.rep.settings.implantingPhysicians.procedureTypes
  );
  return procedureTypes;
};
const useImplantingPhysiciansDetails = () => {
  const { implantingPhysicianDetails } = useSelector(
    (state: RootState) => state.rep.settings.implantingPhysicians
  );
  return implantingPhysicianDetails;
};

const useUserImplantingPhysiciansDetails = () => {
  const { userDetails } = useSelector(
    (state: RootState) => state.rep.settings.implantingPhysicians
  );
  return userDetails;
};
const usePhysicianList = () => {
  const { implantingPhysicianList } = useSelector(
    (state: RootState) => state.rep.settings.implantingPhysicians
  );
  return implantingPhysicianList;
};
const useProviderCredentials = () => {
  const providerCredentials = useSelector(
    (state: RootState) =>
      state.rep.settings.implantingPhysicians.providerCredentials
  );
  return providerCredentials;
};

const useProviderExperience = () => {
  const providerExperience = useSelector(
    (state: RootState) =>
      state.rep.settings.implantingPhysicians.providerExperience
  );
  return providerExperience;
};

const useActiveTab = (): "sites" | "implanting-physicians" => {
  return useSelector(
    (state: RootState) => state.rep.settings.implantingPhysicians.activeTab
  );
};

const useAddPhysicianModalState = (): boolean => {
  return useSelector(
    (state: RootState) =>
      state.rep.settings.implantingPhysicians.showAddPhysicianModal
  );
};

const useSelectedSite = () => {
  return useSelector(
    (state: RootState) => state.rep.settings.implantingPhysicians.selectedSite
  );
};

const useSelectedPhysician = () => {
  return useSelector(
    (state: RootState) =>
      state.rep.settings.implantingPhysicians.selectedPhysician
  );
};

const formatPostImplantingPhysician = (data: ImplantingPhysician) => {
  const credentials = useProviderCredentials();
  const experiences = useProviderExperience();
  const first_name = data?.first_name || "";
  const last_name = data?.last_name || "";
  const middle_name = data?.middle_name || "";
  const credential = data?.credential || "";
  const credential_options =
    credentials?.map((option: any) => ({
      label: option.name,
      value: option.name,
    })) || [];
  const npi_number = data?.npi_number || "";
  const email_id = data?.email_id || "";
  const phone_number = data?.phone_number || "";
  const fax_number = data?.fax_number || "";
  const address = data?.address || "";
  const city = data?.city || "";
  const state = data?.state || "";
  const zip_code = data?.zip_code || "";
  const experience = data?.experience || "";
  const experience_options =
    experiences?.map((option: any) => ({
      label: option.name,
      value: option.value,
    })) || [];
  return {
    first_name,
    last_name,
    middle_name,
    credential,
    credential_options,
    npi_number,
    email_id,
    phone_number,
    fax_number,
    address,
    city,
    state,
    zip_code,
    experience,
    experience_options,
  };
};

const findPostImplantingPhysicianDiff = () => {
  const userDetails = useUserImplantingPhysiciansDetails();
  const physicianDetails = useImplantingPhysiciansDetails();

  const diff = JSON.stringify(userDetails) !== JSON.stringify(physicianDetails);

  const currentValues = {
    first_name: userDetails?.first_name || null,
    last_name: userDetails?.last_name || null,
    middle_name: userDetails?.middle_name || null,
    credential: userDetails?.credential || null,
    npi_number: userDetails?.npi_number || null,
    email_id: userDetails?.email_id || null,
    phone_number: userDetails?.phone_number || null,
    fax_number: userDetails?.fax_number || null,
    address: userDetails?.address || null,
    city: userDetails?.city || null,
    state: userDetails?.state || null,
    zip_code: userDetails?.zip_code || null,
    experience: userDetails?.experience || null,
  };

  return {
    hasDiff: diff,
    currentValues: currentValues,
  };
};

const useSiteListGlobal = () => {
  return useSelector(
    (state: RootState) => state.rep.settings.implantingPhysicians.siteList
  );
};

const useSiteSelectedGlobal = () => {
  return useSelector(
    (state: RootState) => state.rep.settings.implantingPhysicians.siteSelected
  );
};

const usePopupMsgGlobal = () => {
  return useSelector(
    (state: RootState) => state.rep.settings.implantingPhysicians.popupMsg
  );
};

const useModalVisibleGlobal = () => {
  return useSelector(
    (state: RootState) => state.rep.settings.implantingPhysicians.modalVisible
  );
};

export {
  useActiveTab,
  useLoaderAndError,
  useSelectedPhysician,
  useSelectedSite,
  useImplantingPhysiciansDetails,
  useUserImplantingPhysiciansDetails,
  formatPostImplantingPhysician,
  findPostImplantingPhysicianDiff,
  useAddPhysicianModalState,
  useProcedureTypes,
  //new
  useSiteListGlobal,
  useSiteSelectedGlobal,
  usePopupMsgGlobal,
  useModalVisibleGlobal,
  usePhysicianList,
};
