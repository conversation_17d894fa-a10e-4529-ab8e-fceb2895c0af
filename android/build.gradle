// buildscript {
//     ext {
//         buildToolsVersion = "34.0.0"
//         minSdkVersion = 23
//         compileSdkVersion = 34
//         targetSdkVersion = 34
//         ndkVersion = "26.1.10909125"
//         kotlinVersion = "1.9.22"
//         gradlePluginVersion = "8.6.0"
//         reactNativeGradlePluginVersion = "0.75.4" 
//     }

//     repositories {
//         google()
//         mavenCentral()
//     }

//     dependencies {
//         classpath "com.android.tools.build:gradle:$gradlePluginVersion"
//         classpath("com.facebook.react:react-native-gradle-plugin:$reactNativeGradlePluginVersion")
//         classpath("org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlinVersion")
//     }
// }
buildscript {
    ext {
        buildToolsVersion = "34.0.0"
        minSdkVersion = 23
        compileSdkVersion = 34
        targetSdkVersion = 34
        ndkVersion = "26.1.10909125"
        kotlinVersion = "1.9.24"
       
    }
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath("com.android.tools.build:gradle")
        classpath("com.facebook.react:react-native-gradle-plugin")
        classpath("org.jetbrains.kotlin:kotlin-gradle-plugin:${kotlinVersion}")
    }
}

apply plugin: "com.facebook.react.rootproject"

allprojects {
    configurations.all {
        resolutionStrategy {
            force "androidx.core:core:1.13.1"
            force "androidx.core:core-ktx:1.13.1"
        }
    }
}
