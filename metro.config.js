// const {getDefaultConfig, mergeConfig} = require('@react-native/metro-config');

// /**
//  * Metro configuration
//  * https://reactnative.dev/docs/metro
//  *
//  * @type {import('metro-config').MetroConfig}
//  */
// const config = {
//   transformer: {
//     babelTransformerPath: require.resolve('react-native-svg-transformer'),
//   },
//   resolver: {
//     assetExts: [
//       // Exclude svg from asset extensions
//       'png',
//       'jpg',
//       'jpeg',
//       'gif',
//       'bmp',
//       'svg',
//     ],
//     sourceExts: ['js', 'jsx', 'ts', 'tsx', 'json', 'wasm', 'svg'], // Add 'svg' to source extensions
//   },
// };

// module.exports = mergeConfig(getDefaultConfig(__dirname), config);

const { getDefaultConfig, mergeConfig } = require("@react-native/metro-config");
const { withNativeWind } = require("nativewind/metro");

const { withSentryConfig } = require("@sentry/react-native/metro");

/**
 * Metro configuration
 * https://reactnative.dev/docs/metro
 *
 * @type {import('metro-config').MetroConfig}
 */

const config = mergeConfig(getDefaultConfig(__dirname), {
  transformer: {
    // Use react-native-svg-transformer to handle SVG files
    babelTransformerPath: require.resolve("react-native-svg-transformer"),
  },
  resolver: {
    assetExts: [
      // Exclude svg from asset extensions
      "png",
      "jpg",
      "jpeg",
      "gif",
      "bmp",
      // 'svg' should be removed from here
    ],
    sourceExts: [
      "js",
      "jsx",
      "ts",
      "tsx",
      "json",
      "wasm",
      "svg", // Add 'svg' to source extensions
    ],
  },
});

module.exports = withSentryConfig(
  withSentryConfig(withNativeWind(config, { input: "./global.css" }))
);

// module.exports = mergeConfig(getDefaultConfig(__dirname), config);
