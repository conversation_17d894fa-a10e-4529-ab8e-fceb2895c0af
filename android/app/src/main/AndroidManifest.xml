<manifest xmlns:android="http://schemas.android.com/apk/res/android">

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <application
      android:name=".MainApplication"
      android:label="@string/app_name"
      android:icon="@mipmap/ic_launcher"
      android:roundIcon="@mipmap/ic_launcher_round"
      android:allowBackup="false"
      android:usesCleartextTraffic="true"
      android:theme="@style/AppTheme"
      android:supportsRtl="true">
      <activity
        android:name=".MainActivity"
        android:label="@string/app_name"
        android:configChanges="keyboard|keyboardHidden|orientation|screenLayout|screenSize|smallestScreenSize|uiMode"
        android:launchMode="singleTask"
        android:windowSoftInputMode="stateAlwaysHidden|adjustPan"
        android:screenOrientation="portrait"
        android:exported="true">
        <intent-filter android:autoVerify="true">
            <action android:name="android.intent.action.MAIN" />
            <category android:name="android.intent.category.LAUNCHER" />
        </intent-filter>
        <intent-filter>
          <action android:name="android.intent.action.VIEW" />
          <category android:name="android.intent.category.DEFAULT" />
          <category android:name="android.intent.category.BROWSABLE" />
          <data android:scheme="atriai" />
           <data
            android:scheme="https"
            android:host="dev-scheduler.cormetrix.com"
            android:pathPrefix="/login"
             />
             <data
            android:scheme="https"
            android:host="staging-scheduler.cormetrix.com"
            android:pathPrefix="/login"
             />
             <data
            android:scheme="https"
            android:host="scheduler.cormetrix.com"
            android:pathPrefix="/login"
             />
        </intent-filter>
      </activity>
    </application>
</manifest>
