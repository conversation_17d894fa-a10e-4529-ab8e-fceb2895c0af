import React, { useEffect, useState } from "react";
import { ActivityIndicator, Alert, View } from "react-native";
import WebView from "react-native-webview";
import Loader from "../../../components/Loader";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { useNavigation } from "@react-navigation/native";
import { Enviromnent, SchedulerUrl } from "../../../api/config";

const SchedulerHome: React.FC<{}> = () => {
  const [url, setUrl] = useState<string>("");
  const [token, setToken] = useState<string | null>("");
  const [loader, setLoader] = useState<boolean>(false);
  const [refreshToken, setRefreshToken] = useState<string | null>("");
  const [WebViewLoader, setWebViewLoader] = useState<boolean>(true);
  const navigation = useNavigation();
  useEffect(() => {
    const fetchToken = async () => {
      try {
        setLoader(true);
        const storedToken = await AsyncStorage.getItem("accessToken");
        const refresh = await AsyncStorage.getItem("refreshToken");
        setToken(storedToken);
        setRefreshToken(refresh);
        setUrl(`${SchedulerUrl[Enviromnent]}/mobile_index.html`);
        // setUrl("http://********:5501/mobile_index.html");
      } catch (error) {
        console.error(error);
      } finally {
        setLoader(false);
      }
    };

    fetchToken();
  }, []);

  if (loader) {
    return <Loader />;
  }

  const injectedJavaScript = `
    (function() {
      localStorage.setItem("access_token", "${token}");
      localStorage.setItem("refresh_token", "${refreshToken}");

      document.getElementById('logoutNav').style.backgroundColor = 'purple'  
    })();
  `;

  const jsAfterLoaded = `
  `;

  return (
    <View style={{ flex: 1 }}>
      {WebViewLoader && (
        <View
          className="flex-1 absolute w-full h-screen items-center justify-center z-10 "
          style={{
            backgroundColor: "rgba(255,255, 255, 0.4)",
          }}
        >
          <ActivityIndicator color={"#8143d9"} size={"large"} />
        </View>
      )}
      <WebView
        source={{
          uri: url,
        }}
        scalesPageToFit={true}
        webviewDebuggingEnabled={true}
        injectedJavaScriptBeforeContentLoadedForMainFrameOnly={false}
        style={{ flex: 1 }}
        originWhitelist={["*"]}
        injectedJavaScriptBeforeContentLoaded={injectedJavaScript}
        injectedJavaScript={jsAfterLoaded}
        injectedJavaScriptObject={{ customValue: "data" }}
        javaScriptEnabled={true}
        onMessage={(val) => {
          const data = JSON.parse(val?.nativeEvent.data);
          if (data.screen === "ScheduleTab") {
            navigation.navigate("ScheduleTab");
          }
        }}
        incognito={true}
        cacheEnabled={false}
        onError={(err) => console.error(err)}
        pullToRefreshEnabled={true}
        onLoad={() => {
          setWebViewLoader(false);
        }}
      />
    </View>
  );
};

export default SchedulerHome;
