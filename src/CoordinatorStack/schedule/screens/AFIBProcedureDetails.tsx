import * as React from "react";
import ScreenWrapper from "../../../components/ScreenWrapper";
import SearchablePicker from "../../../components/SearchablePicker";
import { Text, TouchableOpacity, View } from "react-native";
import Heading from "../../../components/Heading";
import CustomCard from "../../../components/CustomCard";
import CustomInput from "../../../components/CustomTextInput";
import LineSeperator from "../../../components/LineSeperator";
import globalStyles from "../../../styles/GlobalStyles";
import { useCoordinatorSelectedPatient } from "../hooks/schedulesHooks";
import Loader from "../../../components/Loader";
import Error from "../../../components/Error";
import { useDispatch } from "react-redux";
import { AppDispatch } from "../../../store";
import { useFocusEffect } from "@react-navigation/native";
import SaveActionButton from "../../../components/SaveActionButton";
import {
  parseIntInput,
  formatAfibProcedureData,
  replaceUndefinedWithNull,
} from "../../../utils";

// NEW START
import { parseFloatInput } from "../../../utils";
import {
  useLoaderAndError,
  useAfibProcedureUserDetails,
  findAfibProcedureDiff,
  useAfibProcedureDetails,
} from "../hooks/afibAbliationProcedureDetailsHooks";
// import {
//   fetchAfibAblationProedure,
//   putAfibAblationProcedure,
// } from "../../../store/Cooordinator/ScheduleStack/afibAblation/procedureDetails/thunk";
import {
  fetchAfibAblationProedure,
  putAfibAblationProcedure,
} from "../../../store/coordinator/ScheduleStack/afibAblation/procedureDetails/thunk";
import { setAfibProcedureDetails } from "../../../store/rep/ScheduleStack/afibAblation/procedureDetails";
import { useAfibBAsicDetails } from "../hooks/afibAblationBasicHooks";
import { setAfibBAsicUserDetails } from "../../../store/rep/ScheduleStack/afibAblation/basic";
import ToggleButton from "../../../components/ToggleButton";
import PopupModal from "../../../components/Popup";

// NEW END

interface AFIBProcedureDetailsProps {
  heading: string;
}

const AFIBProcedureDetails: React.FunctionComponent<
  AFIBProcedureDetailsProps
> = () => {
  // NEW START
  const dispatch = useDispatch<AppDispatch>();

  const [modalVisible, setModalVisible] = React.useState(false);

  const [popupMsg, setPopupMsg] = React.useState<string[]>([]);

  const selectedPatient = useCoordinatorSelectedPatient();
  const { loader, error } = useLoaderAndError();

  const userDetails = useAfibProcedureUserDetails();
  const afibDetails = useAfibProcedureDetails();

  const validator = () => {
    setPopupMsg([]);

    var temp = false;

    if (
      sheath_utilized_selected === null ||
      sheath_utilized_selected === undefined ||
      sheath_utilized_selected === ""
    ) {
      setPopupMsg((prev) => [...prev, "Please Select Sheath Utilized"]);
      temp = true;
    }

    if (act === null || act === undefined) {
      setPopupMsg((prev) => [...prev, "Please Enter ACT Value"]);
      temp = true;
    }

    if (
      pvi_selected_id === null ||
      pvi_selected_id === undefined ||
      pvi_selected_id === ""
    ) {
      setPopupMsg((prev) => [
        ...prev,
        "Please Select Pulmonaty Vein Isolation",
      ]);
      temp = true;
    }

    if (
      cathered_selected_id === null ||
      cathered_selected_id === undefined ||
      cathered_selected_id === ""
    ) {
      setPopupMsg((prev) => [...prev, "Please Select Catherer Used"]);
      temp = true;
    }

    if (
      add_lesion_sets_selected === null ||
      add_lesion_sets_selected === undefined ||
      add_lesion_sets_selected === ""
    ) {
      setPopupMsg((prev) => [...prev, "Please Select Catherer Used"]);
      temp = true;
    }

    if (
      (complications_toggler === "Yes" && complication_selected_id === null) ||
      complication_selected_id === undefined ||
      complication_selected_id === ""
    ) {
      setPopupMsg((prev) => [...prev, "Please Select Complications"]);
      temp = true;
    }

    if (
      fluoroscopy?.total_fluoro_time === null ||
      fluoroscopy?.total_fluoro_time === undefined ||
      fluoroscopy?.total_fluoro_time?.toString() === ""
    ) {
      setPopupMsg((prev) => [...prev, "Please Enter Total Fluoro Time"]);
      temp = true;
    }

    if (
      fluoroscopy?.contrast === null ||
      fluoroscopy?.contrast === undefined ||
      fluoroscopy?.contrast?.toString() === ""
    ) {
      setPopupMsg((prev) => [...prev, "Please Enter Contrast Value"]);
      temp = true;
    }

    if (
      complications_toggler === "Yes" &&
      complication_selected_name?.toLowerCase() === "other" &&
      !complication_other
    ) {
      setPopupMsg((prev) => [...prev, "Please Enter Other Complications"]);
      temp = true;
    }

    if (temp) {
      setModalVisible(true);
      return false;
    } else {
      return true;
    }
  };

  const {
    act,
    pvi_options,
    pvi_selected_id,
    sheat_utilized_options,
    sheath_utilized_selected,
    pvi_data,
    cathered_selected_id,
    fluoroscopy,
    add_lesion_sets_selected,
    add_lesion_sets_options,
    complications_toggler,
    complication_selected_id,
    complication_other,
    complications_type,
    cathered_selected_name,
    other_selected_value,
    complication_selected_name,
  } = formatAfibProcedureData(userDetails);

  useFocusEffect(
    React.useCallback(() => {
      const fetchDetails = async () => {
        if (selectedPatient?.case_id) {
          const res = await dispatch(
            fetchAfibAblationProedure({
              case_id: selectedPatient?.case_id,
            })
          );
        }
      };
      fetchDetails();
    }, [dispatch, selectedPatient?.case_id])
  );

  const cathered_opt = pvi_data
    ?.find((item: any) => item.option.id === pvi_selected_id)
    ?.option?.catheter_used.map((catheter: any) => ({
      label: catheter.name,
      value: catheter.id,
    }));

  const [catherId, setCahtherId] = React.useState(cathered_selected_id);
  const [catherOptions, setCatherOptions] = React.useState(cathered_opt);

  React.useEffect(() => {
    const cathered_opt = pvi_data
      ?.find((item: any) => item.option.id === pvi_selected_id)
      ?.option?.catheter_used.map((catheter: any) => ({
        label: catheter.name,
        value: catheter.id,
      }));

    setCatherOptions(cathered_opt);

    const cathered_id = pvi_data?.find(
      (item: any) => item.option.id === pvi_selected_id
    )?.option?.catheter_used[0]?.id;
    setCahtherId(cathered_id);
  }, [pvi_selected_id, catherId]);

  // NEW END
  const diff = findAfibProcedureDiff();

  const saveDetails = async () => {
    try {
      if (validator()) {
        const res = await dispatch(
          putAfibAblationProcedure({
            case_id: selectedPatient?.case_id,
            payload: replaceUndefinedWithNull(diff.currentValues),
          })
        );
        if (res.payload) {
          if (selectedPatient?.case_id) {
            const refetchRes = await dispatch(
              fetchAfibAblationProedure({
                case_id: selectedPatient?.case_id,
              })
            );
          }
        }
      }
    } catch (err) {
      console.error(err);
    }
  };

  const handleCancel = () => {
    // Reset the fields to the initial data
    dispatch(
      setAfibProcedureDetails({
        ...userDetails,
        ...afibDetails,
      })
    );
  };

  const handleRefresh = async () => {
    if (!selectedPatient?.case_id) return;
    await dispatch(
      fetchAfibAblationProedure({
        case_id: selectedPatient?.case_id,
      })
    );
  };

  if (loader) {
    return <Loader />;
  }

  if (error) {
    return <Error message={error} />;
  }

  return (
    <ScreenWrapper direction="column" onRefresh={handleRefresh}>
      <CustomCard extraStyle="mb-9">
        {/* <SaveActionButton disabled={!diff.hasDiff} onPress={saveDetails} /> */}

        <View>
          <Heading
            text="Sheath Utilized"
            size="sub-heading"
            showSeperator={false}
            extraStyle="pb-3"
          />
          <SearchablePicker
            items={sheat_utilized_options || []}
            placeholder="Type"
            width={"100%"}
            value={sheath_utilized_selected || ""}
            onValueChange={(option) =>
              dispatch(
                setAfibProcedureDetails({
                  sheath_utilized: {
                    ...userDetails?.sheath_utilized,
                    selected: {
                      id: option.value,
                      name: option.label,
                    },
                  },
                })
              )
            }
          />
        </View>

        <LineSeperator extraStyle={globalStyles.lineseperator} />

        <View>
          <Heading
            text="ACT (sec)"
            size="sub-heading"
            showSeperator={false}
            extraStyle="pb-3"
          />
          <CustomInput
            inputValue={act?.toString() || ""}
            onInputChange={(value) =>
              parseIntInput(value, (updatedValue) => {
                dispatch(
                  setAfibProcedureDetails({
                    act_sec: updatedValue,
                  })
                );
              })
            }
            placeholder="sec"
            keyboardType="numeric"
          />
        </View>

        <LineSeperator extraStyle={globalStyles.lineseperator} />
        <Heading
          text="Lesion Sets"
          size="heading"
          showSeperator={false}
          extraStyle="pb-3"
        />

        <View className="bg-primaryBg p-3 rounded-lg">
          <Heading
            text="Pulmonary Vein Isolation (PVI)"
            size="sub-heading"
            showSeperator={false}
            extraStyle="pb-3"
          />
          <View className="bg-primaryBg rounded-md gap-y-4">
            <SearchablePicker
              items={pvi_options || []}
              placeholder="Type"
              width={"100%"}
              value={pvi_selected_id || ""}
              onValueChange={(option) => {
                const updatedSelected = {
                  id: option.value,
                  name: option.label,
                  catheter_used:
                    userDetails?.pvi_lesioin_sets?.selected?.catheter_used,
                };

                dispatch(
                  setAfibProcedureDetails({
                    pvi_lesioin_sets: {
                      ...userDetails?.pvi_lesioin_sets,
                      selected: updatedSelected,
                    },
                  })
                );
              }}
            />
            <View>
              <Heading
                showSeperator={false}
                size="label"
                text="Catheter system used"
              />
              <SearchablePicker
                items={cathered_opt || []}
                placeholder="Catheter system used"
                value={cathered_selected_id || ""}
                width={"100%"}
                onValueChange={(option) => {
                  const updatedSelected = {
                    catheter_used:
                      userDetails?.pvi_lesioin_sets?.selected?.catheter_used.map(
                        (item) => ({
                          ...item,
                          id: (item.id = option.value),
                          name: (item.name = option.label),
                        })
                      ),
                  };
                  dispatch(
                    setAfibProcedureDetails({
                      pvi_lesioin_sets: {
                        ...userDetails?.pvi_lesioin_sets,
                        selected: {
                          ...userDetails?.pvi_lesioin_sets?.selected,
                          catheter_used: updatedSelected.catheter_used,
                        },
                      },
                    })
                  );
                }}
              />
            </View>
          </View>

          {cathered_selected_name?.toLowerCase() === "other" && (
            <View className="mt-3">
              <CustomInput
                inputValue={other_selected_value || ""}
                onInputChange={(val) => {
                  const updatedSelected = {
                    catheter_used:
                      userDetails?.pvi_lesioin_sets?.selected?.catheter_used.map(
                        (item) => ({
                          ...item,
                          csu_pvi_other_complications: val,
                        })
                      ),
                  };
                  dispatch(
                    setAfibProcedureDetails({
                      pvi_lesioin_sets: {
                        ...userDetails?.pvi_lesioin_sets,
                        selected: {
                          ...userDetails?.pvi_lesioin_sets?.selected,
                          catheter_used: updatedSelected.catheter_used,
                        },
                      },
                    })
                  );
                }}
                placeholder="Other"
              />
            </View>
          )}
        </View>

        <View className="bg-primaryBg p-3 rounded-lg mt-3">
          <Heading
            text="Additional Lesion Sets"
            size="sub-heading"
            showSeperator={false}
            extraStyle="pb-3"
          />
          <View className="bg-primaryBg rounded-md gap-y-4">
            <SearchablePicker
              items={add_lesion_sets_options || []}
              placeholder="Type"
              width={"100%"}
              value={add_lesion_sets_selected || ""}
              onValueChange={(option) => {
                const updatedSelected = {
                  id: option.value,
                  name: option.label,
                };

                dispatch(
                  setAfibProcedureDetails({
                    add_lesioin_sets: {
                      ...userDetails?.add_lesioin_sets,
                      selected: updatedSelected,
                    },
                  })
                );
              }}
            />
          </View>
        </View>

        <LineSeperator extraStyle="my-5" />

        <View className="flex-row justify-between items-center mb-2">
          <Heading
            text="Complications"
            color="black"
            size="sub-heading"
            showSeperator={false}
            extraStyle="mt-1"
          />
          <ToggleButton
            messages={["Yes", "No"]}
            selected={complications_toggler}
            invertColor
            setSelected={(value) =>
              dispatch(
                setAfibProcedureDetails({
                  complication: {
                    ...userDetails?.complication,
                    selected: {
                      ...userDetails?.complication?.selected,
                      complication_present: value === "Yes" ? true : false,
                    },
                  },
                })
              )
            }
          />
        </View>
        {complications_toggler === "Yes" && (
          <>
            <SearchablePicker
              items={complications_type}
              placeholder="Complications"
              value={complication_selected_id}
              onValueChange={(option) =>
                dispatch(
                  setAfibProcedureDetails({
                    complication: {
                      ...userDetails?.complication,
                      selected: {
                        ...userDetails?.complication?.selected,
                        id: option.value,
                      },
                    },
                  })
                )
              }
            />

            {complication_selected_name?.toLocaleLowerCase() === "other" && (
              <View className="mt-3">
                <CustomInput
                  inputValue={complication_other}
                  error={!complication_other}
                  onInputChange={(val: string) => {
                    dispatch(
                      setAfibProcedureDetails({
                        complication: {
                          ...userDetails?.complication,
                          selected: {
                            ...userDetails?.complication?.selected,
                            complication_other: val,
                          },
                        },
                      })
                    );
                  }}
                  placeholder="Other"
                />
              </View>
            )}
          </>
        )}

        <LineSeperator extraStyle="my-5" />

        <Heading
          text="Fluoroscopy & Contrast"
          size="heading"
          extraStyle="pb-3"
          showSeperator={false}
        />

        <View className="bg-primaryBg p-3 rounded-lg ">
          <View className="mb-6">
            <Heading
              text="Total Fluoro Time (min)"
              size="sub-heading"
              extraStyle="pb-3"
              showSeperator={false}
            />
            <CustomInput
              inputValue={fluoroscopy?.total_fluoro_time?.toString()}
              onInputChange={(value) =>
                parseFloatInput(value, (updatedValue: number) => {
                  dispatch(
                    setAfibProcedureDetails({
                      fluroscopy: {
                        ...userDetails?.fluroscopy,
                        total_fluoro_time: updatedValue,
                      },
                    })
                  );
                })
              }
              placeholder="mg/dL"
              keyboardType="numeric"
            />
          </View>

          {/* <View className="mb-6">
            <Heading
              text="Total Fluoro (mSv)"
              size="sub-heading"
              extraStyle="pb-3"
              showSeperator={false}
            />
            <CustomInput
              inputValue={fluoroscopy?.total_fluoro?.toString() || ""}
              onInputChange={(value) =>
                parseFloatInput(value, (updatedValue: number) => {
                  dispatch(
                    setAfibProcedureDetails({
                      fluroscopy: {
                        ...userDetails?.fluroscopy,
                        total_fluoro: updatedValue,
                      },
                    })
                  );
                })
              }
              placeholder="min"
              keyboardType="numeric"
            />
          </View> */}

          <View>
            <Heading
              text="Contrast (mL)"
              size="sub-heading"
              extraStyle="pb-3"
              showSeperator={false}
            />
            <CustomInput
              inputValue={fluoroscopy?.contrast?.toString() || ""}
              onInputChange={(value) =>
                parseFloatInput(value, (updatedValue: number) => {
                  dispatch(
                    setAfibProcedureDetails({
                      fluroscopy: {
                        ...userDetails?.fluroscopy,
                        contrast: updatedValue,
                      },
                    })
                  );
                })
              }
              placeholder="mGy"
              keyboardType="numeric"
            />
          </View>
        </View>
        <View className="flex-row justify-center gap-4 mt-6">
          <TouchableOpacity
            onPress={saveDetails}
            className={`border 
                              ${
                                !diff?.hasDiff
                                  ? `bg-primaryGray border-primaryGray`
                                  : `bg-primaryPurple border-primaryPurple`
                              }
                               px-6 py-3 rounded-full`}
            disabled={!diff?.hasDiff}
          >
            <Text className="text-primaryWhite font-semibold">Save</Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={handleCancel}
            className="border border-primaryPurple px-6 py-3 rounded-full bg-primaryBg"
          >
            <Text className="text-primaryPurple">Cancel</Text>
          </TouchableOpacity>
        </View>
      </CustomCard>

      <View className="mb-9 "></View>

      <PopupModal
        show={modalVisible}
        msg={popupMsg}
        status="warning"
        onClose={() => setModalVisible(false)}
      />
    </ScreenWrapper>
  );
};

export default AFIBProcedureDetails;
