import React, { useState, useRef } from "react";
import {
  Text,
  View,
  ActivityIndicator,
  StyleSheet,
  ScrollView,
} from "react-native";
import { CalendarProvider, ExpandableCalendar } from "react-native-calendars";
import { useDispatch } from "react-redux";
import { AppDispatch } from "../../../store";
import { fetchAbstractorSchedules } from "../../../store/abstractor/ScheduleStack/schedule/thunk";
import moment from "moment";
import PatientCard from "../components/PatientCard";
import ScreenWrapper from "../../../components/ScreenWrapper";
import BottomSheetComponent, {
  BottomSheetRefProps,
} from "../../../components/BottomSheetComponent";
import {
  useAbstractorSchedules,
  useLoadersState,
} from "../hooks/abstractorSchedulesHooks";

interface IScheduleScreenProps {}

const ScheduleScreen: React.FunctionComponent<IScheduleScreenProps> = () => {
  const abstractorSchedules = useAbstractorSchedules();
  const [date, setDate] = useState(new Date().toISOString().split("T")[0]);
  const dispatch = useDispatch<AppDispatch>();
  const { scheduleLoader } = useLoadersState();

  const bottomSheetRef = useRef<BottomSheetRefProps>(null);

  const fetchEvents = async (selectedDate: Date) => {
    const procedure_date = moment(selectedDate).format("YYYY-MM-DD");
    await dispatch(
      fetchAbstractorSchedules({
        procedure_date: procedure_date,
      })
    );
  };

  React.useEffect(() => {
    fetchEvents(new Date(date));
  }, [date]);

  return (
    <>
      <CalendarProvider
        date={date.toString()}
        onDateChanged={(date) => setDate(date)}
        style={styles.calendarProvider}
      >
        <ExpandableCalendar
          firstDay={1}
          onDayPress={(day) => setDate(day.dateString)}
          theme={styles.calendarTheme}
        />
        <ScrollView className="mt-4 px-4">
          {scheduleLoader ? (
            <View className="mt-48 flex-1 justify-center items-center">
              <ActivityIndicator size="large" color="#8143d9" />
            </View>
          ) : (
            <View>
              {abstractorSchedules && abstractorSchedules.length > 0 ? (
                abstractorSchedules.map((item) => (
                  <View key={item.patient_id} className="mb-4">
                    <PatientCard patient={item} />
                  </View>
                ))
              ) : (
                <View className="mt-4 flex-1 justify-center items-center p-4">
                  <Text className="text-gray-500 text-lg">No Patients</Text>
                </View>
              )}
            </View>
          )}
        </ScrollView>
      </CalendarProvider>
    </>
  );
};

export default ScheduleScreen;

const styles = StyleSheet.create({
  calendarProvider: {
    backgroundColor: "#f8f6ff",
  },
  calendarTheme: {
    selectedDayBackgroundColor: "#8143d9",
    todayTextColor: "#8143d9",
    arrowColor: "#8143d9",
    monthTextColor: "#8143d9",
    textDayFontWeight: "500",
    textMonthFontWeight: "bold",
    textDayHeaderFontWeight: "500",
  },
  loader: {
    position: "absolute",
    top: 0,
    left: 0,
    width: "100%",
    height: "100%",
    backgroundColor: "white",
    justifyContent: "center",
    alignItems: "center",
  },
  emptyItem: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 10,
  },
  emptyText: {
    color: "#999",
    fontSize: 16,
  },
});
