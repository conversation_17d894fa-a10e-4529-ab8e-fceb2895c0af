import React from "react";
import { TouchableOpacity, Text, View, StyleSheet } from "react-native";
import Icon from "react-native-vector-icons/MaterialCommunityIcons";

const colorMap = {
  yellow: "#FFD700",
  green: "#16a34a",
  red: "#dc2626",
  purple: "#8143d9",
};

type CustomCheckboxProps = {
  value: boolean;
  onChange?: (val: boolean) => void;
  labelYes?: string;
  labelNo?: string;
  yesColor?: string;
  noColor?: string;
  showLabel?: boolean;
  size?: number;
};

const CustomCheckbox: React.FC<CustomCheckboxProps> = ({
  value,
  onChange,
  labelYes = "Yes",
  labelNo = "No",
  yesColor = "green",
  noColor = "red",
  showLabel = false,
  size = 35,
}) => {
  const getColor = (color: string) => colorMap[color] || color;

  return (
    <TouchableOpacity
      style={[
        styles.checkbox,
        value
          ? { backgroundColor: getColor(yesColor) }
          : {
              borderWidth: 2,
              borderColor: getColor(noColor),
              backgroundColor: "transparent",
            },
        { width: size, height: size },
      ]}
      onPress={() => onChange?.(!value)}
      activeOpacity={0.7}
    >
      <Icon
        name={value ? "check" : "close"}
        size={size * 0.6}
        color={value ? "white" : getColor(noColor)}
      />
      {showLabel && (
        <Text style={styles.label}>{value ? labelYes : labelNo}</Text>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  checkbox: {
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 50,
  },
  label: {
    marginTop: 5,
    fontSize: 14,
    color: "black",
    textAlign: "center",
  },
});

export default CustomCheckbox;
