import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import MessagesScreen from './screens/MessagesScreen';

const MessageStack = createStackNavigator();

export default function MessageScreenStack(): React.JSX.Element {
  return (
    <MessageStack.Navigator>
      <MessageStack.Screen name="Message" component={MessagesScreen} />
    </MessageStack.Navigator>
  );
}

