import {createStackNavigator} from '@react-navigation/stack';
import Home from '../RepStack/home/<USER>/HomeScreen';
import LoginScreen from './screens/LoginScreen';

const Auth = createStackNavigator();

const AuthStack = () => {
  return (
    <Auth.Navigator
      initialRouteName="LoginScreen"
      screenOptions={{
        headerShown: false,
      }}>
      <Auth.Screen name="LoginScreen" component={LoginScreen} />
    </Auth.Navigator>
  );
};

export default AuthStack;
