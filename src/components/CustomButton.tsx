import React from "react";
import { TouchableOpacity } from "react-native";
import Heading from "./Heading";

interface CustomButtonProps {
  className?: string; // Additional custom classes
  color?: string; // Text color for the button text
  value: string; // Button label
  onPress: () => void; // Action for the button press
}

const CustomButton: React.FC<CustomButtonProps> = ({
  className = "",
  color = "white", // Default text color
  value,
  onPress,
}) => {
  const defaultClasses =
    "flex-row bg-primaryPurple justify-center items-center p-4 rounded-lg";
  const combinedClasses = `${defaultClasses} ${className}`; // Merge default and custom classes

  return (
    <TouchableOpacity
      onPress={onPress}
      className={combinedClasses}
    >
      <Heading
        text={value}
        size="sub-heading"
        color={color}
        showSeperator={false}
      />
    </TouchableOpacity>
  );
};

export default CustomButton;
