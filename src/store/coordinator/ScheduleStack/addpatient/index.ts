import { createSlice } from "@reduxjs/toolkit";
import initialState from "./initialstate";
import { fetchReferringProviders } from "../../../common/addReferringProvider/thunk";
import {
  // fetchReferringProviders,
  fetchImplantingPhysicians,
  fetchRationale,
  postPatientDetails,
} from "./thunk";

const postPatientSlice = createSlice({
  name: "postPatient",
  initialState: initialState.postPatient,
  reducers: {
    setPatientDetails: (state, action) => {
      state.userDetails = {
        ...state.userDetails,
        ...action.payload,
      };
    },
    resetPatientDetails: (state) => {
      state.userDetails = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(postPatientDetails.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(postPatientDetails.fulfilled, (state, action) => {
        state.loading = false;
        state.patientDetails = action.payload;
        state.userDetails = action.payload;
      })
      .addCase(postPatientDetails.rejected, (state, action) => {
        state.loading = false;
        state.patientDetails = null;
        state.error = action.payload as string;
      });
  },
});

const referringProvidersSlice = createSlice({
  name: "referringProviders",
  initialState: initialState.referringProviders,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchReferringProviders.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchReferringProviders.fulfilled, (state, action) => {
        state.loading = false;
        state.referringProviders = action.payload;
      })
      .addCase(fetchReferringProviders.rejected, (state, action) => {
        state.loading = false;
        state.referringProviders = [];
        state.error = action.payload as string;
      });
  },
});

const implantingPhysiciansSlice = createSlice({
  name: "implantingPhysicians",
  initialState: initialState.implantingPhysicians,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchImplantingPhysicians.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchImplantingPhysicians.fulfilled, (state, action) => {
        state.loading = false;
        state.implantingPhysicians = action.payload;
      })
      .addCase(fetchImplantingPhysicians.rejected, (state, action) => {
        state.loading = false;
        state.implantingPhysicians = [];
        state.error = action.payload as string;
      });
  },
});

const rationaleSlice = createSlice({
  name: "rationale",
  initialState: initialState.rationale,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchRationale.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchRationale.fulfilled, (state, action) => {
        state.loading = false;
        state.rationale = action.payload;
      })
      .addCase(fetchRationale.rejected, (state, action) => {
        state.loading = false;
        state.rationale = [];
        state.error = action.payload as string;
      });
  },
});
export const { setPatientDetails, resetPatientDetails } =
  postPatientSlice.actions;
export default {
  postPatientSlice,
  referringProvidersSlice,
  implantingPhysiciansSlice,
  rationaleSlice,
};
