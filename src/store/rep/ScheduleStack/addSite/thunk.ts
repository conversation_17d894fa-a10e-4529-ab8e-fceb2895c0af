import { createAsyncThunk } from "@reduxjs/toolkit";
import { Site } from "./types";
import api from "../../../../api/api";

// Fetch all sites
export const fetchSites = createAsyncThunk(
  "addsite/fetchSites",
  async (_, thunkAPI) => {
    try {
      const response = await api.private.get(
        api.routes.privateRoutes.rep.sites
      );

      if (response.data && response.data.result) {
        return response.data.result;
      }
    } catch (error: any) {
      console.error("Error caught during API call:", error.response);
      if (error.response) {
        if (error.response.status === 400) {
        }
        return thunkAPI.rejectWithValue("Failed to fetch sites");
      } else if (error.request) {
        console.error("No response received:", error.request);
        return thunkAPI.rejectWithValue("No response received from the server");
      } else {
        console.error("Error occurred:", error.message);
        return thunkAPI.rejectWithValue("An unknown error occurred");
      }
    }
  }
);

export const fetchSpecificSite = createAsyncThunk(
  "addsite/fetchSpecificSite",
  async (site_id: string, thunkAPI) => {
    try {
      const response = await api.private.get(
        api.routes.privateRoutes.rep.site_details_get(site_id)
      );

      if (response.data && response.data.result) {
        return response.data.result;
      } else {
        throw new Error('Invalid response format: Missing "result" field');
      }
    } catch (error: any) {
      console.error("Error caught during API call:", error.response);
      if (error.response) {
        if (error.response.status === 400) {
          return thunkAPI.rejectWithValue(error.response.data.message);
        }
        return thunkAPI.rejectWithValue("Failed to fetch site details");
      } else if (error.request) {
        console.error("No response received:", error.request);
        return thunkAPI.rejectWithValue("No response received from the server");
      } else {
        console.error("Error occurred:", error.message);
        return thunkAPI.rejectWithValue("An unknown error occurred");
      }
    }
  }
);

export const putSite = createAsyncThunk(
  "addSite/putSite",
  async (
    { site_id, payload }: { site_id: string; payload: Site },
    thunkAPI
  ) => {
    try {
      const response = await api.private.put(
        api.routes.privateRoutes.rep.site_details_put(site_id),
        payload
      );

      if (response.data && response.data.result) {
        return response.data.result;
      } else {
        throw new Error('Invalid response format: Missing "result" field');
      }
    } catch (error: any) {
      console.error("Error caught during API call:", error.response?.message);
      if (error.response) {
        if (error.response.status === 400) {
          return thunkAPI.rejectWithValue(error.response.data.message);
        }
        return thunkAPI.rejectWithValue("Failed to update site");
      } else if (error.request) {
        console.error("No response received:", error.request);
        return thunkAPI.rejectWithValue("No response received from the server");
      } else {
        console.error("Error occurred:", error.message);
        return thunkAPI.rejectWithValue("An unknown error occurred");
      }
    }
  }
);
// Add a new site
export const postSite = createAsyncThunk(
  "addSite/putPostSite",
  async (payload: Site, thunkAPI) => {
    try {
      const response = await api.private.post(
        api.routes.privateRoutes.rep.sites,
        payload
      );

      if (response.data && response.data.result) {
        return response.data.result;
      }
    } catch (error: any) {
      console.error("Error caught during API call:", error.response.message);
      if (error.response) {
        if (error.response.status === 400) {
        }
        return thunkAPI.rejectWithValue("Failed to add site");
      } else if (error.request) {
        console.error("No response received:", error.request);
        return thunkAPI.rejectWithValue("No response received from the server");
      } else {
        console.error("Error occurred:", error.message);
        return thunkAPI.rejectWithValue("An unknown error occurred");
      }
    }
  }
);
