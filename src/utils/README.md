# App Version Management

This document explains how the app version is managed across different environments (DEV, STAGING, PROD).

## Overview

The app uses a centralized version management system that allows for:
- Different version numbers for each environment
- Consistent display of version information across all screens
- Easy updates to version numbers

## How It Works

The version management is handled by the `appVersion.ts` utility:

```typescript
import { version as packageVersion } from '../../package.json';
import { Enviromnent } from '../api/config';

interface VersionConfig {
  DEV: string;
  STAGING: string;
  PROD: string;
}

// Define specific version numbers for each environment
const environmentVersions: VersionConfig = {
  DEV: packageVersion, // Using package.json version for DEV
  STAGING: packageVersion, // Using package.json version for STAGING
  PROD: '1.0.0', // Custom version for PROD
};

export const getAppVersion = (): string => {
  // Get the version for the current environment
  const currentVersion = environmentVersions[Enviromnent];
  
  // For non-PROD environments, prefix with environment name
  if (Enviromnent !== 'PROD') {
    return `${Enviromnent}-${currentVersion}`;
  }
  
  // For PROD, just return the version number
  return currentVersion;
};
```

## How to Update Versions

### For DEV and STAGING Environments

The DEV and STAGING environments use the version from `package.json`. To update these versions:

1. Update the `version` field in `package.json`
2. The changes will automatically be reflected in DEV and STAGING environments

### For PROD Environment

The PROD environment uses a custom version number. To update the PROD version:

1. Open `src/utils/appVersion.ts`
2. Update the PROD version in the `environmentVersions` object:

```typescript
const environmentVersions: VersionConfig = {
  DEV: packageVersion,
  STAGING: packageVersion,
  PROD: '1.0.1', // Update this value
};
```

## How to Use in Components

To display the app version in a component:

```typescript
import { getAppVersion } from '../utils/appVersion';

// Then in your component:
<Text>App Version: {getAppVersion()}</Text>
```

## Switching Environments

To switch between environments:

1. Open `src/api/config.ts`
2. Change the `Enviromnent` constant:

```typescript
// Set to "DEV", "STAGING", or "PROD"
export const Enviromnent: Environment = "DEV";
```
