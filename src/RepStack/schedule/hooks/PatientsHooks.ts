import { useSelector } from "react-redux";
import { RootState } from "../../../store";
import { AppDispatch } from "../../../store";
import { setSelectedPatient } from "../../../store/rep/ScheduleStack/patients";

// const dispatch = useDispatch<AppDispatch>();

const useSiteDetails = () => {
  const selectedDate = useSelector(
    (state: RootState) => state.patients.selectedDate
  );
  const selectedHospitalName = useSelector(
    (state: RootState) => state.patients.selectedHospitalName
  );
  const selectedHospitalId = useSelector(
    (state: RootState) => state.patients.selectedHospitalId
  );
  const implantingPhysicianName = useSelector(
    (state: RootState) => state.patients.implantingPhysicianName
  );

  const implantingPhysicianImage = useSelector(
    (state: RootState) => state.patients.implantingPhysicianImage
  );

  const hospitalImage = useSelector(
    (state: RootState) => state.patients.hospitalImage
  );

  return {
    selectedDate,
    selectedHospitalName,
    selectedHospitalId,
    implantingPhysicianName,
    implantingPhysicianImage,
    hospitalImage,
  };
};

const usePatientCases = () => {
  const patientCases = useSelector(
    (state: RootState) => state.patients.patients
  );
  return patientCases;
};

const useSelectedPatient = () => {
  const selectedPatient = useSelector(
    (state: RootState) => state.patients.selectedPatient
  );
  return selectedPatient;
};

export { useSiteDetails, usePatientCases, useSelectedPatient };
