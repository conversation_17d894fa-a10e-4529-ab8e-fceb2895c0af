import { useSelector } from "react-redux";
import { RootState } from "../../../store";

const useAbstractorSchedules = () => {
  const { schedules } = useSelector(
    (state: RootState) => state.abstractor.schedule.schedules
  );
  return schedules;
};

const useLoadersState = () => {
  const scheduleLoader = useSelector(
    (state: RootState) =>
      state.abstractor.schedule.schedules.loaders.scheduleLoader
  );
  return { scheduleLoader };
};

const useAbstractorSelectedPatient = () => {
  const { selectedPatient } = useSelector(
    (state: RootState) => state.abstractor.schedule.schedules
  );
  return selectedPatient;
};

export {
  useAbstractorSchedules,
  useLoadersState,
  useAbstractorSelectedPatient,
};
