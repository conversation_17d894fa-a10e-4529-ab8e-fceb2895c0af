import { createSlice, PayloadAction, createAsyncThunk } from "@reduxjs/toolkit";
import { fetchCases } from "./thunk";
import initialState from "./initialstate";

const patientSlice = createSlice({
  name: "patients",
  initialState,
  reducers: {
    setSiteDetails: (state, action: PayloadAction<siteDetails>) => {
      state.selectedDate = action.payload.selectedDate;
      state.selectedHospitalId = action.payload.selectedHospitalId;
      state.selectedHospitalName = action.payload.selectedHospitalName;
      state.implantingPhysicianName = action.payload.implantingPhysicianName;
      state.implantingPhysicianImage = action.payload.implantingPhysicianImage;
      state.hospitalImage = action.payload.hospitalImage;
    },

    setSelectedPatient: (state, action: PayloadAction<Patient | null>) => {
      state.selectedPatient = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchCases.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchCases.fulfilled, (state, action) => {
        state.loading = false;
        state.patients = action.payload;
      })
      .addCase(fetchCases.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
        state.patients = [];
      });
  },
});

export const { setSiteDetails, setSelectedPatient } = patientSlice.actions;
export default patientSlice.reducer;
