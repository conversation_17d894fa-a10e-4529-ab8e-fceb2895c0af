interface PatientCase {
  case_id: string;
  patient: {
    afib_ablation: boolean;
    age: number;
    anticoagulation: string | null;
    cha2ds2_vasc: {
      score: number;
      calculation: {
        congestive_heart_failure: number;
        hypertension: number;
        age: number;
        diabetes_mellitus: number;
        stroke_tia_thromboembolic_event: number;
        vascular_disease: number;
        sex: number;
      };
    };
    cta: boolean;
    tee: boolean;
    id: string;
    name: string;
    rationale: string;
    referring_provider: any;
    sex: string;
  };
  procedure_date: string;
  procedure_time: string;
}

interface IinitialState {
  schedules: PatientCase[] | null;
  selectedPatient: PatientCase | null;
  chadScore: {
    congestive_heart_fail: boolean;
    lv_dysfunc: boolean;
    hypertension: boolean;
    diabetes_mellitus: boolean;
    stroke: boolean;
    tia: boolean;
    thromboembolic_event: boolean;
    vascular_disease: boolean;
  } | null;
  loaders: {
    scheduleLoader: boolean;
    chadScoreLoader: boolean;
  };
  errors: {
    scheduleError: string | null;
    chadScoreError: string | null;
  };
}

export type { IinitialState };
