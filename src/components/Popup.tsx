import React from "react";
import {
  Modal,
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  FlatList,
} from "react-native";
import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons";

// Extend props type to include children
interface PopupModalProps {
  show?: boolean;
  msg?: string[];
  status: "warning" | "info" | "success" | "error";
  onClose: () => void;
  children?: React.ReactNode;
}

const PopupModal: React.FC<PopupModalProps> = ({
  show = false,
  msg = [],
  status,
  onClose,
  children,
}) => {
  // Determine icon and color based on status
  const getStatusIcon = (status: string) => {
    switch (status) {
      case "warning":
        return { name: "alert-circle-outline", color: "#FFA726" };
      case "info":
        return { name: "information-outline", color: "#42A5F5" };
      case "success":
        return { name: "check-circle-outline", color: "#66BB6A" };
      case "error":
        return { name: "alert-circle-outline", color: "#EF5350" };
      default:
        return { name: "information-outline", color: "#42A5F5" };
    }
  };

  const { name, color } = getStatusIcon(status);

  return (
    <Modal
      visible={show}
      transparent
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.overlay}>
        <View style={styles.modalContainer}>
          <MaterialCommunityIcons name={name} size={48} color={color} />
          <FlatList
            data={msg}
            keyExtractor={(item, index) => index.toString()}
            renderItem={({ item }) => (
              <Text style={styles.message}>{item}</Text>
            )}
          />
          {/* Render additional children if provided, otherwise show the default Close button */}
          {children ? (
            children
          ) : (
            <TouchableOpacity style={styles.closeButton} onPress={onClose}>
              <Text style={styles.closeButtonText}>Close</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>
    </Modal>
  );
};

// Styles
const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "center",
    alignItems: "center",
  },
  modalContainer: {
    width: Dimensions.get("window").width * 0.8,
    backgroundColor: "white",
    borderRadius: 10,
    padding: 20,
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  message: {
    fontSize: 16,
    textAlign: "center",
    marginVertical: 5,
    color: "black",
  },
  closeButton: {
    backgroundColor: "#8143d9",
    borderRadius: 5,
    paddingVertical: 10,
    paddingHorizontal: 20,
    marginTop: 15,
    alignSelf: "center",
  },
  closeButtonText: {
    color: "white",
    fontSize: 14,
    fontWeight: "bold",
  },
});

export default PopupModal;
