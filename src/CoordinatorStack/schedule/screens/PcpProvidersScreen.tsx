import React, { useCallback, useEffect, useState } from "react";
import { ScrollView, RefreshControl } from "react-native";
import { useDispatch } from "react-redux";
import { useFocusEffect } from "@react-navigation/native";
import { AppDispatch } from "../../../store";
import {
  fetchPcpProviders,
  fetchPcpProviderById,
} from "../../../store/common/addPcpProvider/thunk";
import {
  resetProviderDetails,
  showAddPcpProviderModal,
  setSelectedProvider,
} from "../../../store/common/addPcpProvider";
import PcpProviderList from "../components/PcpProviderList";

const PcpProvidersScreen: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const [refreshing, setRefreshing] = useState(false);

  const fetchData = async () => {
    await dispatch(fetchPcpProviders());
  };

  // 🔁 Fetch on first load
  useEffect(() => {
    fetchData();
  }, []);

  // 🔁 Auto reset and refetch on tab focus
  useFocusEffect(
    useCallback(() => {
      dispatch(resetProviderDetails());
      dispatch(setSelectedProvider(""));
      dispatch(showAddPcpProviderModal(false));
      fetchData();
      return () => {
        // Optional cleanup
      };
    }, [dispatch])
  );

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchData();
    setRefreshing(false);
  };

  const handleAddProvider = () => {
    dispatch(setSelectedProvider(""));
    dispatch(resetProviderDetails());
    dispatch(showAddPcpProviderModal(true));
  };

  const handleProviderSelect = async (providerId: string) => {
    dispatch(setSelectedProvider(providerId));
    if (providerId) {
      await dispatch(fetchPcpProviderById(providerId));
    }
  };

  return (
    <ScrollView
      className="flex-1"
      refreshControl={
        <RefreshControl
          refreshing={refreshing}
          onRefresh={handleRefresh}
          colors={["#8143d9"]}
          tintColor={"#8143d9"}
        />
      }
    >
      <PcpProviderList
        onAddPress={handleAddProvider}
        onProviderSelect={handleProviderSelect}
      />
    </ScrollView>
  );
};

export default PcpProvidersScreen;
