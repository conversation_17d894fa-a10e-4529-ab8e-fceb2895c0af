import { useSelector } from "react-redux";
import { RootState } from "../../../store";

const useLoaderAndError = () => {
  const loader = useSelector(
    (state: RootState) => state.clinician.laaoProcedure.laaoimplant.loading
  );
  const error = useSelector(
    (state: RootState) => state.clinician.laaoProcedure.laaoimplant.error
  );
  return { loader, error };
};

const useLaaoImplantDetails = () => {
  const { laaoImplantDetails } = useSelector(
    (state: RootState) => state.clinician.laaoProcedure.laaoimplant
  );
  return laaoImplantDetails;
};

const useLaaoImplantUserDetails = () => {
  const { userDetails } = useSelector(
    (state: RootState) => state.clinician.laaoProcedure.laaoimplant
  );
  return userDetails;
};

const findLaaoImplantDiff = () => {
  const laaoImplantDetails = useLaaoImplantDetails();
  const userDetails = useLaaoImplantUserDetails();

  // const diff = {
  //   suitability_tug:
  //     laaoImplantDetails?.suitability_tug === userDetails?.suitability_tug
  //       ? "none"
  //       : userDetails?.suitability_tug,

  //   partial_recaptures:
  //     laaoImplantDetails?.partial_recaptures === userDetails?.partial_recaptures
  //       ? "none"
  //       : userDetails?.partial_recaptures,

  //   no_partial_recaptures:
  //     laaoImplantDetails?.no_partial_recaptures ===
  //     userDetails?.no_partial_recaptures
  //       ? "none"
  //       : userDetails?.no_partial_recaptures,

  //   partial_recaptures_manipulation:
  //     laaoImplantDetails?.partial_recaptures_manipulation ===
  //     userDetails?.partial_recaptures_manipulation
  //       ? "none"
  //       : userDetails?.partial_recaptures_manipulation,

  //   device_deployed:
  //     laaoImplantDetails?.device_deployed === userDetails?.device_deployed
  //       ? "none"
  //       : userDetails?.device_deployed,

  //   device_not_deployed_rationale:
  //     laaoImplantDetails?.device_not_deployed_rationale ===
  //     userDetails?.device_not_deployed_rationale
  //       ? "none"
  //       : userDetails?.device_not_deployed_rationale,

  //   case_aborted:
  //     laaoImplantDetails?.case_aborted === userDetails?.case_aborted
  //       ? "none"
  //       : userDetails?.case_aborted,

  //   case_aborted_rationale:
  //     laaoImplantDetails?.case_aborted_rationale ===
  //     userDetails?.case_aborted_rationale
  //       ? "none"
  //       : userDetails?.case_aborted_rationale,

  //   product_chargeable:
  //     laaoImplantDetails?.product_chargeable === userDetails?.product_chargeable
  //       ? "none"
  //       : userDetails?.product_chargeable,

  //   product_not_chargeable_rationale:
  //     laaoImplantDetails?.product_not_chargeable_rationale ===
  //     userDetails?.product_not_chargeable_rationale
  //       ? "none"
  //       : userDetails?.product_not_chargeable_rationale,

  //   implant_access_sheath:
  //     laaoImplantDetails?.implant_access_sheath?.selected.id ===
  //     userDetails?.implant_access_sheath?.selected.id
  //       ? "none"
  //       : userDetails?.implant_access_sheath.selected.id,

  //   device_type:
  //     laaoImplantDetails?.device?.selected.id ===
  //     userDetails?.device?.selected.id
  //       ? "none"
  //       : userDetails?.device?.selected.id,

  //   device_size:
  //     laaoImplantDetails?.device?.selected?.device_size ===
  //     userDetails?.device?.selected?.device_size
  //       ? "none"
  //       : userDetails?.device?.selected?.device_size,
  // };

  // const nonNullDiff = Object.entries(diff).reduce((acc, [key, value]) => {
  //   if (value !== "none" && value !== undefined) {
  //     acc[key] = value;
  //   }
  //   return acc;
  // }, {});

  const diff =
    JSON.stringify(userDetails) !== JSON.stringify(laaoImplantDetails);
  const currentValues = {
    suitability_tug: userDetails?.suitability_tug,

    partial_recaptures: userDetails?.partial_recaptures,

    no_partial_recaptures: userDetails?.no_partial_recaptures,

    partial_recaptures_manipulation:
      userDetails?.partial_recaptures_manipulation,

    device_deployed: userDetails?.device_deployed,

    device_not_deployed_rationale: userDetails?.device_not_deployed_rationale,

    case_aborted: userDetails?.case_aborted,

    case_aborted_rationale: userDetails?.case_aborted_rationale,

    product_chargeable: userDetails?.product_chargeable,

    product_not_chargeable_rationale:
      userDetails?.product_not_chargeable_rationale,

    implant_access_sheath: userDetails?.implant_access_sheath.selected.id,

    device_type: userDetails?.device?.selected.id,

    device_size: userDetails?.device?.selected?.device_size,

    complication_present:
      userDetails?.complication?.selected?.complication_present,

    complication_other: userDetails?.complication?.selected?.complication_other,

    complication_id: userDetails?.complication?.selected?.id,
  };

  // return {
  //   hasDiff: Object.values(nonNullDiff).some((value) => value !== "none"),
  //   currentValues: nonNullDiff,
  // };
  return {
    hasDiff: diff,
    currentValues: currentValues,
  };
};

export {
  useLoaderAndError,
  useLaaoImplantDetails,
  useLaaoImplantUserDetails,
  findLaaoImplantDiff,
};
