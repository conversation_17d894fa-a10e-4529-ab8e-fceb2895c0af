import React, { useState } from "react";
import { View, Text, TouchableOpacity } from "react-native";
import CustomCard from "../../../components/CustomCard";
import SearchablePicker from "../../../components/SearchablePicker";
import CustomInput from "../../../components/CustomTextInput";
import DatePicker from "react-native-date-picker";
import moment from "moment";
import ScreenWrapper from "../../../components/ScreenWrapper";
import LineSeperator from "../../../components/LineSeperator";
import Heading from "../../../components/Heading";

const JetCompressionScreen: React.FunctionComponent = () => {
  const jetOptions = [
    { label: "Jet A", value: "Jet A" },
    { label: "Jet B", value: "Jet B" },
    { label: "Jet C", value: "Jet C" },
  ];

  const complicationsOptions = [
    { label: "Complication 1", value: "Complication 1" },
    { label: "Complication 2", value: "Complication 2" },
    { label: "Complication 3", value: "Complication 3" },
  ];

  const [selectedJet, setSelectedJet] = useState<string | null>(
    jetOptions[0].value
  );
  const [compressionPercentage, setCompressionPercentage] =
    useState<string>("50");
  const [open, setOpen] = useState<boolean>(false);
  const [time, setTime] = useState<Date>(new Date());
  const [selectedComplications, setSelectedComplications] = useState<
    string | null
  >(complicationsOptions[0].value);

  const handleSave = () => {
    // Logic for saving data
  };

  const handleCancel = () => {
    // Reset fields
    setSelectedJet(jetOptions[0].value);
    setCompressionPercentage("");
    setOpen(false);
    setTime(new Date());
    setSelectedComplications(complicationsOptions[0].value);
  };

  return (
    <ScreenWrapper direction="column">
      <CustomCard>
        <View className="rounded-md p-3">
          {/* Jet Picker */}
          <View className="mb-3">
            <Heading
              text="Jet Compression"
              size="sub-heading"
              showSeperator={false}
            />
            <View className="mt-2 flex-1">
              <SearchablePicker
                placeholder="Select Jet"
                items={jetOptions}
                value={selectedJet}
                onValueChange={(item) => setSelectedJet(item.value)}
              />
            </View>
          </View>
          <LineSeperator extraStyle="my-5" />

          {/* Compression Percentage */}
          <View className="mb-3">
            <Heading
              text="Compression Percentage"
              size="sub-heading"
              showSeperator={false}
            />
            <View className="mt-2 flex-1">
              <CustomInput
                inputValue={compressionPercentage}
                onInputChange={setCompressionPercentage}
                keyboardType="numeric"
                placeholder="Compression"
              />
            </View>
          </View>
          <LineSeperator extraStyle="my-5" />

          {/* End Time DatePicker */}
          <View className="mb-3">
            <Heading text="End Time" size="sub-heading" showSeperator={false} />
            <View className="mt-2 flex-1">
              <TouchableOpacity
                onPress={() => setOpen(true)}
                className="bg-primaryWhite border border-primaryPurple h-[45px] rounded-md justify-center px-3"
              >
                <Text className="text-primaryBlack">
                  {moment(time).format("hh:mm A")}
                </Text>
              </TouchableOpacity>
            </View>
          </View>
          <LineSeperator extraStyle="my-5" />

          {/* Complications Picker */}
          <View className="mb-3">
            <Heading
              text="Complications"
              size="sub-heading"
              showSeperator={false}
            />
            <View className="mt-2 flex-1">
              <SearchablePicker
                placeholder="Select Complications"
                items={complicationsOptions}
                value={selectedComplications}
                onValueChange={(item) => setSelectedComplications(item.value)}
              />
            </View>
          </View>
          <LineSeperator extraStyle="mt-5" />

          {/* Action Buttons */}
          <View className="flex-row justify-center gap-4 mt-6">
            <TouchableOpacity
              onPress={handleSave}
              className="border border-primaryPurple px-6 py-3 rounded-full bg-primaryPurple"
            >
              <Text className="text-primaryWhite font-semibold">Save</Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={handleCancel}
              className="border border-primaryPurple px-6 py-3 rounded-full bg-primaryBg"
            >
              <Text className="text-primaryPurple">Cancel</Text>
            </TouchableOpacity>
          </View>

          {/* DatePicker Modal */}
          <DatePicker
            modal
            open={open}
            date={time}
            mode={"time"}
            onConfirm={(date) => {
              setOpen(false);
              setTime(date);
            }}
            onCancel={() => setOpen(false)}
          />
        </View>
      </CustomCard>
      <View className="mt-9"></View>
    </ScreenWrapper>
  );
};

export default JetCompressionScreen;
