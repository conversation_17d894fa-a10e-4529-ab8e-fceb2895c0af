import { useSelector } from "react-redux";
import { RootState } from "../../../store";
import {
  PcpProvider,
  CredentialOption,
} from "../../../store/common/addPcpProvider/types";

const usePcpLoaderAndError = () => {
  const loader = useSelector(
    (state: RootState) => state.common.pcpProviders.loading
  );

  const error = useSelector(
    (state: RootState) => state.common.pcpProviders.error
  );
  return { loader, error };
};

const usePcpProviderDetails = () => {
  const { providerDetails } = useSelector(
    (state: RootState) => state.common.pcpProviders
  );
  return providerDetails;
};

const useUserPcpProviderDetails = () => {
  const { userDetails } = useSelector(
    (state: RootState) => state.common.pcpProviders
  );
  return userDetails;
};

const useActiveTab = (): "referring-providers" | "pcp-providers" => {
  return useSelector((state: RootState) => state.common.pcpProviders.activeTab);
};

// Fixed: Changed function name to match export
const useShowAddPcpProviderModal = (): boolean => {
  return useSelector(
    (state: RootState) => state.common.pcpProviders.showAddPcpProviderModal // Fixed field name
  );
};

const useSelectedPcpProvider = (): string => {
  // Fixed: Should return string (ID), not PcpProvider
  return useSelector(
    (state: RootState) => state.common.pcpProviders.selectedProvider
  );
};

const formatPostPcpProvider = (data: PcpProvider | null) => {
  // Fixed: Handle null case
  if (!data) {
    return {
      first_name: "",
      last_name: "",
      middle_name: "",
      credential: "",
      npi_number: "",
      email_id: "",
      phone_number: "",
      fax_number: "",
      address: "",
      city: "",
      state: "",
      zip_code: "",
      hospital_system: "",
    };
  }

  const first_name = data?.first_name || "";
  const last_name = data?.last_name || "";
  const middle_name = data?.middle_name || "";
  const credential = data?.credential || "";
  const npi_number = data?.npi_number || "";
  const email_id = data?.email_id || "";
  const phone_number = data?.phone_number || "";
  const fax_number = data?.fax_number || "";
  const address = data?.address || "";
  const city = data?.city || "";
  const state = data?.state || "";
  const zip_code = data?.zip_code || "";
  const hospital_system = data?.hospital_system || "";

  return {
    first_name,
    last_name,
    middle_name,
    credential,
    npi_number,
    email_id,
    phone_number,
    fax_number,
    address,
    city,
    state,
    zip_code,
    hospital_system,
  };
};

const findPostPcpProviderDiff = () => {
  const userDetails = useUserPcpProviderDetails();
  const providerDetails = usePcpProviderDetails();

  const diff = JSON.stringify(userDetails) !== JSON.stringify(providerDetails);

  const currentValues = {
    first_name: userDetails?.first_name || "",
    last_name: userDetails?.last_name || "",
    middle_name: userDetails?.middle_name || "",
    credential: userDetails?.credential || "",
    npi_number: userDetails?.npi_number || "",
    email_id: userDetails?.email_id || "",
    phone_number: userDetails?.phone_number || "",
    fax_number: userDetails?.fax_number || "",
    address: userDetails?.address || "",
    city: userDetails?.city || "",
    state: userDetails?.state || "",
    zip_code: userDetails?.zip_code || "",
    hospital_system: userDetails?.hospital_system || "",
  };

  return {
    hasDiff: diff,
    currentValues,
  };
};

const usePcpCredentialOptions = (): CredentialOption[] => {
  return useSelector(
    (state: RootState) => state.common.pcpProviders.credentialOptions
  );
};

export {
  useActiveTab,
  usePcpLoaderAndError,
  useShowAddPcpProviderModal, // Fixed: Updated export name
  usePcpProviderDetails,
  useUserPcpProviderDetails,
  useSelectedPcpProvider,
  formatPostPcpProvider,
  findPostPcpProviderDiff,
  usePcpCredentialOptions,
};
