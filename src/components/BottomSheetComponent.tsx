import React, { forwardRef, useImperativeHandle, useRef } from "react";
import { StyleSheet, View } from "react-native";
import BottomSheet, {
  BottomSheetScrollView,
  BottomSheetView,
} from "@gorhom/bottom-sheet";

export interface BottomSheetComponentProps {
  children: React.ReactNode;
  snapPoints: (string | number)[];
  backgroundColor?: string;
  onClose?: () => void;
  onSnapTo?: (index: number) => void;
  onChange?: (index: number) => void; // New prop for onChange
  style?: any;
  scrollEnabled?: boolean;
}

export interface BottomSheetRefProps {
  open: () => void;
  close: () => void;
}

const BottomSheetComponent = forwardRef<
  BottomSheetRefProps,
  BottomSheetComponentProps
>(
  (
    {
      children,
      snapPoints,
      backgroundColor = "white",
      onClose,
      onSnapTo,
      onChange, // Destructuring the onChange prop
      style,
      scrollEnabled = true,
    },
    ref
  ) => {
    const bottomSheetRef = useRef<BottomSheet>(null);

    useImperativeHandle(ref, () => ({
      open: () => bottomSheetRef.current?.snapToIndex(0),
      close: () => bottomSheetRef.current?.close(),
    }));

    const handleSnapChange = (index: number) => {
      if (onSnapTo) {
        onSnapTo(index);
      }
      if (onChange) {
        onChange(index); // Call the onChange prop if defined
      }
    };

    return (
      <BottomSheet
        ref={bottomSheetRef}
        snapPoints={snapPoints}
        enablePanDownToClose
        index={-1}
        backgroundStyle={{ backgroundColor }}
        onClose={onClose}
        onChange={handleSnapChange} // Passing handleSnapChange to BottomSheet
        keyboardBehavior="interactive"
        keyboardBlurBehavior="restore"
      >
        {scrollEnabled ? (
          <BottomSheetScrollView
            style={[styles.contentContainer, style, { backgroundColor }]}
          >
            {children}
          </BottomSheetScrollView>
        ) : (
          <BottomSheetView
            style={[styles.contentContainer, style, { backgroundColor }]}
          >
            {children}
          </BottomSheetView>
        )}
      </BottomSheet>
    );
  }
);

const styles = StyleSheet.create({
  contentContainer: {
    flex: 1,
  },
});

export default BottomSheetComponent;
