import React, { useState } from "react";
import { <PERSON>, Button, StyleSheet } from "react-native";
import DatePicker from "react-native-date-picker";
import SearchablePicker from "../../../components/SearchablePicker";
import { useDispatch } from "react-redux";
import { AppDispatch } from "../../../store";
import Loader from "../../../components/Loader";
import {
  useSiteList,
  usePatientsList,
  useLoadersState,
  useAddCaseModal,
} from "../hooks/schedulesHooks";
import {
  fetchSiteList,
  fetchPatientsList,
  putRepCase,
} from "../../../store/rep/ScheduleStack/schedules/thunk";
import CustomText from "../../../components/CustomText";
import Heading from "../../../components/Heading";
import LineSeperator from "../../../components/LineSeperator";

interface AddCaseBottomSheetProps {
  onSuccess?: () => void;
}

const AddCaseBottomSheet: React.FC<AddCaseBottomSheetProps> = ({
  onSuccess,
}) => {
  const dispatch = useDispatch<AppDispatch>();
  const modalVisible = useAddCaseModal();
  const [date, setDate] = useState(new Date());

  // State variables
  const [siteList, setSiteList] = useState<{ label: string; value: number }[]>(
    []
  );
  const [siteSelected, setSiteSelected] = useState<string>("");
  const [patients, setPatients] = useState<{ label: string; value: number }[]>(
    []
  );

  const [selectedPatient, setSelectedPatient] = useState<string>(0);

  const siteListData = useSiteList();
  const patientsList = usePatientsList();
  const { patientLoader } = useLoadersState();

  // Helper functions
  const unMount = () => {
    setSiteList([]);
    setSiteSelected("");
    setPatients([]);
    setSelectedPatient("");
  };

  const cleanState = () => {
    setSiteSelected("");
    setPatients([]);
    setSelectedPatient("");
  };

  const fetchPatientList = async () => {
    try {
      const response = await dispatch(
        fetchPatientsList({
          site_id: siteSelected,
          case_date: date.toISOString().split("T")[0],
        })
      );

      if (response.payload && Array.isArray(response.payload)) {
        setPatients(
          response.payload.map((patient) => ({
            label: patient.patient.name,
            value: patient.case_id,
          }))
        );
      }
    } catch (error) {
      console.error("Error fetching patient list:", error);
    }
  };

  const handleAddCase = async () => {
    try {
      await dispatch(putRepCase({ case_id: selectedPatient }));
      cleanState();
      // Call the onSuccess callback if provided
      if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error("Error adding case:", error);
    }
  };

  React.useEffect(() => {
    if (modalVisible) {
      dispatch(fetchSiteList());
    }

    return () => {
      unMount();
    };
  }, [dispatch, modalVisible]);

  React.useEffect(() => {
    if (siteListData && Array.isArray(siteListData)) {
      setSiteList(
        siteListData.map((site) => ({
          label: site.name,
          value: site.id,
        }))
      );
    }
  }, [siteListData]);

  React.useEffect(() => {
    if (siteSelected) {
      fetchPatientList();
    }
  }, [siteSelected, date]);
  return (
    <View style={styles.container}>
      {/* <Heading size="heading" text="Add Case" /> */}
      <View className="flex">
        <CustomText
          value={"Add Case"}
          color="text-primaryPurple"
          className="text-2xl font-bold text-center mb-2"
        />
      </View>

      <LineSeperator color="primaryPurple" extraStyle="my-2" />
      {/* Styled DatePicker */}
      <View style={styles.datePickerContainer}>
        <DatePicker
          date={date}
          onDateChange={setDate}
          mode="date"
          theme="light"
        />
      </View>

      <LineSeperator color="primaryPurple" extraStyle="my-2" />

      <View style={styles.picker}>
        <SearchablePicker
          placeholder={"Select site"}
          items={siteList}
          value={siteSelected}
          onValueChange={(value) => setSiteSelected(value.value)}
        />
      </View>

      {patientLoader ? <Loader /> : <></>}

      {siteSelected && patients.length > 0 ? (
        <View style={styles.picker}>
          <SearchablePicker
            placeholder={"Select patient"}
            items={patients}
            value={selectedPatient?.toString()}
            onValueChange={(value) => setSelectedPatient(value.value)}
          />
        </View>
      ) : null}

      {siteSelected && patients.length === 0 && !patientLoader ? (
        <View style={styles.input}>
          <CustomText value={"No Patients found for selected site and date"} />
        </View>
      ) : null}

      {selectedPatient ? (
        <View style={styles.addButton}>
          <Button title="Add Case" onPress={handleAddCase} />
        </View>
      ) : null}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    // flex: 1,
    // backgroundColor: "#121212", // Background color for dark mode
    padding: 16,
  },
  datePickerContainer: {
    display: "flex",
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#f8f6ff", // Dark mode background
    borderRadius: 8,
    // padding: 8,
    // marginBottom: 16,
  },
  datePicker: {
    color: "black",
    // width: "100%",
  },
  picker: {
    marginVertical: 8,
  },
  addButton: {
    marginTop: 16,
  },
  input: {
    marginTop: 8,
    marginBottom: 10,
    borderRadius: 6,
    fontSize: 16,
    lineHeight: 20,
    borderColor: "#8143d9",
    borderWidth: 1,
    padding: 12,
    // backgroundColor: "rgba(151, 151, 151, 0.25)",
  },
});

export default AddCaseBottomSheet;
