import * as React from "react";
import { View } from "react-native";

interface ILineSeperatorProps {
  extraStyle?: string;
  color?: string;
}

const LineSeperator: React.FunctionComponent<ILineSeperatorProps> = (props) => {
  return (
    <View
      className={`h-[2px] ${
        props.color ? `bg-${props.color}` : "bg-primaryBg"
      } ${props.extraStyle}`}
    ></View>
  );
};

export default LineSeperator;
