// import React, { useEffect, useState } from "react";
// import { LayoutChangeEvent, StyleSheet, Text } from "react-native";
// import Animated, {
//   Easing,
//   Extrapolation,
//   interpolate,
//   useAnimatedStyle,
//   useSharedValue,
//   withDelay,
//   withSpring,
// } from "react-native-reanimated";
// import MCI from "react-native-vector-icons/MaterialCommunityIcons";
// import { useDispatch } from "react-redux";
// import { hideToaster } from "../store/toaster";

// interface Props {
//   show: boolean;
//   message: string;
//   icon: "info" | "success" | "error";
//   duration?: number;
//   onHide?: () => void;
// }

// const CustomToaster: React.FC<Props> = ({
//   show,
//   message,
//   icon,
//   duration = 5000,
//   onHide,
// }) => {
//   const transY = useSharedValue(0);
//   const transX = useSharedValue(0);
//   const [toastHeight, setToastHeight] = useState(0);
//   const [textLength, setTextLength] = useState(0);
//   const dispatch = useDispatch();

//   useEffect(() => {
//     if (show) {
//       transX.value = textLength + 12;
//       transY.value = withSpring(80, { damping: 20, stiffness: 100 });

//       transX.value = withDelay(
//         duration,
//         withSpring(0, { damping: 20, stiffness: 100 })
//       );

//       const hideTimeout = setTimeout(() => {
//         hideToast();
//       }, 1000);

//       const revertState = setTimeout(() => {
//         dispatch(hideToaster());
//         onHide?.();
//       }, 1500);

//       return () => {
//         clearTimeout(hideTimeout);
//         clearTimeout(revertState);
//       };
//     } else {
//       hideToast();
//     }
//   }, [show, textLength, toastHeight]);

//   const rView = useAnimatedStyle(() => ({
//     transform: [{ translateY: transY.value }],
//     opacity: interpolate(
//       transY.value,
//       [-toastHeight, 80],
//       [0, 1],
//       Extrapolation.CLAMP
//     ),
//   }));

//   const rOuterView = useAnimatedStyle(() => ({
//     transform: [{ translateX: -Math.max(transX.value, 1) / 2 }],
//   }));

//   const rInnerView = useAnimatedStyle(() => ({
//     transform: [{ translateX: transX.value }],
//   }));

//   const rText = useAnimatedStyle(() => ({
//     opacity: interpolate(transX.value, [0, textLength], [1, 0]),
//   }));

//   const hideToast = () => {
//     transX.value = withSpring(textLength + 12, { damping: 20, stiffness: 100 });
//     transY.value = withDelay(
//       duration,
//       withSpring(-toastHeight, { damping: 20, stiffness: 100 })
//     );
//   };

//   const generateIcon = () => {
//     const iconProps = { size: 30, color: "#fff" };
//     switch (icon) {
//       case "success":
//         return <MCI name="check-circle" {...iconProps} />;
//       case "error":
//         return <MCI name="alert-circle" {...iconProps} />;
//       default:
//         return <MCI name="information" {...iconProps} />;
//     }
//   };

//   const generateBackgroundColor = () => {
//     switch (icon) {
//       case "success":
//         return "#4CAF50"; // Green
//       case "error":
//         return "#F44336"; // Red
//       default:
//         return "#2196F3"; // Blue
//     }
//   };

//   const handleTextLayout = (event: LayoutChangeEvent) => {
//     setTextLength(Math.floor(event.nativeEvent.layout.width));
//   };

//   const handleViewLayout = (event: LayoutChangeEvent) => {
//     setToastHeight(event.nativeEvent.layout.height);
//   };

//   if (!show) return null;

//   return (
//     <Animated.View
//       onLayout={handleViewLayout}
//       style={[styles.container, rView]}
//     >
//       <Animated.View style={[styles.outerContainer, rOuterView]}>
//         <Animated.View
//           style={[
//             styles.innerContainer,
//             rInnerView,
//             { backgroundColor: generateBackgroundColor() },
//           ]}
//         >
//           {generateIcon()}
//           <Animated.Text
//             onLayout={handleTextLayout}
//             style={[styles.text, rText]}
//           >
//             {message}
//           </Animated.Text>
//         </Animated.View>
//       </Animated.View>
//     </Animated.View>
//   );
// };

// const styles = StyleSheet.create({
//   container: {
//     position: "absolute",
//     top: 30,
//     left: 0,
//     right: 0,
//     zIndex: 100,
//     alignItems: "center",
//   },
//   outerContainer: {
//     overflow: "hidden",
//     borderRadius: 25,
//   },
//   innerContainer: {
//     flexDirection: "row",
//     alignItems: "center",
//     justifyContent: "center",
//     paddingHorizontal: 20,
//     paddingVertical: 12,
//     borderRadius: 25,
//   },
//   text: {
//     color: "white",
//     fontWeight: "500",
//     fontSize: 16,
//     marginLeft: 10,
//     textAlign: "center",
//     lineHeight: 22,
//     letterSpacing: 0.4,
//     flexShrink: 1,
//   },
// });

// export default CustomToaster;

// import React, { useEffect, useState } from "react";
// import { LayoutChangeEvent, StyleSheet, Text, View } from "react-native";
// import Animated, {
//   Easing,
//   useAnimatedStyle,
//   useSharedValue,
//   withSpring,
//   withTiming,
// } from "react-native-reanimated";
// import MCI from "react-native-vector-icons/MaterialCommunityIcons";
// import { useDispatch } from "react-redux";
// import { hideToaster } from "../store/toaster";

// const TOAST_DURATION = 3000;

// const ICONS = {
//   success: "check-circle",
//   error: "alert-circle",
//   info: "information",
// };

// const COLORS = {
//   success: "#4CAF50",
//   error: "#F44336",
//   info: "#2196F3",
// };

// const CustomToaster = ({
//   show,
//   message,
//   icon = "info",
//   duration = TOAST_DURATION,
//   onHide,
// }) => {
//   const transY = useSharedValue(-100);
//   const opacity = useSharedValue(0);
//   const scale = useSharedValue(0.8);
//   const [toastHeight, setToastHeight] = useState(0);
//   const dispatch = useDispatch();

//   useEffect(() => {
//     if (show) {
//       transY.value = withTiming(0, {
//         duration: 900,
//         easing: Easing.out(Easing.exp),
//       });
//       opacity.value = withTiming(1, {
//         duration: 900,
//         easing: Easing.out(Easing.exp),
//       });
//       scale.value = withSpring(1, { damping: 12, stiffness: 100 });

//       const timeout = setTimeout(() => hideToast(), duration);
//       return () => clearTimeout(timeout);
//     }
//   }, [show]);

//   const hideToast = () => {
//     transY.value = withTiming(-toastHeight, {
//       duration: 600,
//       easing: Easing.in(Easing.exp),
//     });
//     opacity.value = withTiming(0, {
//       duration: 400,
//       easing: Easing.in(Easing.exp),
//     });
//     scale.value = withTiming(0.8, {
//       duration: 400,
//       easing: Easing.inOut(Easing.ease),
//     });
//     setTimeout(() => {
//       dispatch(hideToaster());
//       onHide?.();
//     }, 600);
//   };

//   const animatedStyle = useAnimatedStyle(() => ({
//     transform: [{ translateY: transY.value }, { scale: scale.value }],
//     opacity: opacity.value,
//   }));

//   if (!show) return null;

//   return (
//     <Animated.View
//       style={[styles.container, animatedStyle]}
//       onLayout={(event) => setToastHeight(event.nativeEvent.layout.height)}
//     >
//       <View style={[styles.toast, { backgroundColor: COLORS[icon] }]}>
//         <MCI name={ICONS[icon]} size={28} color="#fff" style={styles.icon} />
//         <Text style={styles.text}>{message}</Text>
//       </View>
//     </Animated.View>
//   );
// };

// const styles = StyleSheet.create({
//   container: {
//     position: "absolute",
//     top: 50,
//     left: 20,
//     right: 20,
//     zIndex: 1000,
//     alignItems: "center",
//   },
//   toast: {
//     flexDirection: "row",
//     alignItems: "center",
//     paddingHorizontal: 16,
//     paddingVertical: 12,
//     borderRadius: 12,
//     shadowColor: "#000",
//     shadowOffset: { width: 0, height: 4 },
//     shadowOpacity: 0.3,
//     shadowRadius: 6,
//     elevation: 5,
//   },
//   icon: {
//     marginRight: 10,
//   },
//   text: {
//     color: "white",
//     fontSize: 16,
//     fontWeight: "500",
//     flexShrink: 1,
//   },
// });

// export default CustomToaster;

//v-3

import React, { useEffect, useState } from "react";
import { LayoutChangeEvent, Modal, StyleSheet, Text, View } from "react-native";
import Animated, {
  Easing,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  withTiming,
} from "react-native-reanimated";
import MCI from "react-native-vector-icons/MaterialCommunityIcons";
import { useDispatch } from "react-redux";
import { hideToaster } from "../store/toaster";

const TOAST_DURATION = 3000;

const ICONS: { [key: string]: string } = {
  success: "check-circle",
  error: "alert-circle",
  info: "information",
};

const COLORS: { [key: string]: string } = {
  success: "#4CAF50",
  error: "#F44336",
  info: "#2196F3",
};

interface CustomToasterProps {
  show: boolean;
  message: string;
  icon?: "success" | "error" | "info";
  duration?: number;
  onHide?: () => void;
}

const CustomToaster: React.FC<CustomToasterProps> = ({
  show,
  message,
  icon = "info",
  duration = TOAST_DURATION,
  onHide,
}) => {
  const transY = useSharedValue(-100);
  const opacity = useSharedValue(0);
  const scale = useSharedValue(0.8);
  const [toastHeight, setToastHeight] = useState(0);
  const dispatch = useDispatch();

  useEffect(() => {
    if (show) {
      transY.value = withTiming(0, {
        duration: 900,
        easing: Easing.out(Easing.exp),
      });
      opacity.value = withTiming(1, {
        duration: 900,
        easing: Easing.out(Easing.exp),
      });
      scale.value = withSpring(1, { damping: 12, stiffness: 100 });

      const timeout = setTimeout(() => hideToast(), duration);
      return () => clearTimeout(timeout);
    }
  }, [show]);

  const hideToast = () => {
    transY.value = withTiming(-toastHeight, {
      duration: 600,
      easing: Easing.in(Easing.exp),
    });
    opacity.value = withTiming(0, {
      duration: 400,
      easing: Easing.in(Easing.exp),
    });
    scale.value = withTiming(0.8, {
      duration: 400,
      easing: Easing.inOut(Easing.ease),
    });
    setTimeout(() => {
      dispatch(hideToaster());
      onHide?.();
    }, 600);
  };

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: transY.value }, { scale: scale.value }],
    opacity: opacity.value,
  }));

  if (!show) return null;

  return (
    <Modal transparent animationType="none" visible={show}>
      <View style={styles.modalOverlay}>
        <Animated.View
          style={[styles.container, animatedStyle]}
          onLayout={(event: LayoutChangeEvent) =>
            setToastHeight(event.nativeEvent.layout.height)
          }
        >
          <View style={[styles.toast, { backgroundColor: COLORS[icon] }]}>
            <MCI
              name={ICONS[icon]}
              size={28}
              color="#fff"
              style={styles.icon}
            />
            <Text style={styles.text}>{message}</Text>
          </View>
        </Animated.View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,

    justifyContent: "flex-start",
  },
  container: {
    position: "absolute",
    top: 50,
    left: 20,
    right: 20,
    zIndex: 9999,
    alignItems: "center",
  },
  toast: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 6,
    elevation: 5,
  },
  icon: {
    marginRight: 10,
  },
  text: {
    color: "white",
    fontSize: 16,
    fontWeight: "500",
    flexShrink: 1,
  },
});

export default CustomToaster;
