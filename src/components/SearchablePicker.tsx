// import React, { useState, useEffect, useRef } from "react";
// import { StyleSheet } from "react-native";
// import { Picker } from "react-native-ui-lib";

// interface PickerItem {
//   label: any;
//   value: any;
// }

// interface SearchablePickerProps {
//   items: PickerItem[];
//   placeholder: string;
//   value?: any; // Use the `value` as a string (selected value)
//   onValueChange?: (selectedItem: PickerItem) => void; // Expect the full item with label and value
//   floatingLabel?: boolean;
//   width?: any;
//   height?: any;
//   disable?: boolean;
//   error?: boolean;
//   disableError?: boolean;
// }

// const SearchablePicker: React.FC<SearchablePickerProps> = ({
//   items,
//   placeholder,
//   value,
//   onValueChange,
//   floatingLabel = false,
//   width,
//   height,
//   disable = true,
//   error = false,
//   disableError = false,
// }) => {
//   const [selectedItem, setSelectedItem] = useState<string | undefined>(value);
//   const [searchValue, setSearchValue] = useState<string>("");
//   const [filteredOptions, setFilteredOptions] = useState<PickerItem[]>(items);
//   const [isPickerOpen, setIsPickerOpen] = useState<boolean>(false);
//   const selectedOptionRef = useRef<PickerItem | null>(null);

//   // Find and store the selected option object based on value
//   useEffect(() => {
//     if (value !== undefined && items && items.length > 0) {
//       const option = items.find((item) => item.value === value);
//       if (option) {
//         selectedOptionRef.current = option;
//       }
//     }
//   }, [value, items]);

//   // Filter options based on search text
//   useEffect(() => {
//     const filtered = items?.filter(
//       (option) =>
//         typeof option?.label === "string" &&
//         option.label.toLowerCase().includes(searchValue.toLowerCase())
//     );
//     setFilteredOptions(filtered);
//   }, [searchValue, items]);

//   // Update selected item when value prop changes
//   useEffect(() => {
//     setSelectedItem(value);
//   }, [value]);

//   // Reset search value when picker is closed or opened
//   useEffect(() => {
//     if (!isPickerOpen) {
//       // When picker is closed, reset search value
//       setSearchValue("");
//     } else {
//       // When picker is opened, reset filtered options to show all items
//       setFilteredOptions(items);
//     }
//   }, [isPickerOpen, items]);

//   const handleSearchChange = (searchText: string) => {
//     setSearchValue(searchText);
//   };

//   const handleValueChange = (item: any) => {
//     // Only update if a valid item is selected (not the "No options available" item)
//     if (item !== null) {
//       const selectedOption = items.find((option) => option.value === item);
//       if (selectedOption) {
//         setSelectedItem(item);
//         selectedOptionRef.current = selectedOption;
//         if (onValueChange) {
//           onValueChange(selectedOption);
//         }
//       }
//     } else {
//       // If null is selected (from "No options available"), restore the previous selection
//       if (selectedOptionRef.current) {
//         // Restore the previous selection
//         setSelectedItem(selectedOptionRef.current.value);

//         // Reset search value to ensure we show all options again
//         setSearchValue("");

//         // No need to call onValueChange here as we're just restoring the previous value
//         // and not actually changing the selection
//       }
//     }

//     // Always close the picker when a selection is made or canceled
//     setIsPickerOpen(false);
//   };

//   // Generate picker items based on filtered options
//   const getPickerItems = () => {
//     // If we have filtered options, show them
//     if (filteredOptions?.length > 0) {
//       return filteredOptions.map((option) => ({
//         label: option.label,
//         value: option.value,
//       }));
//     }
//     // If search returned no results but we have a selected item, include it at the top
//     else if (searchValue && selectedOptionRef.current) {
//       return [
//         {
//           label: selectedOptionRef.current.label,
//           value: selectedOptionRef.current.value,
//         },
//         { label: "No matching options", value: null },
//       ];
//     }
//     // If no search text or the picker was just opened, show all items
//     else if ((!searchValue || isPickerOpen) && items?.length > 0) {
//       // Show all available items
//       return items.map((option) => ({
//         label: option.label,
//         value: option.value,
//       }));
//     }
//     // Fallback for when there are no options at all
//     else {
//       return [{ label: "No options available", value: null }];
//     }
//   };

//   // Handle picker open event
//   const handlePickerOpen = () => {
//     setIsPickerOpen(true);
//     // Reset search and show all options when picker is opened
//     setSearchValue("");
//     setFilteredOptions(items);
//   };

//   return (
//     <Picker
//       placeholder={placeholder}
//       floatingPlaceholder={floatingLabel}
//       placeholderTextColor="#999"
//       value={selectedItem}
//       enableModalBlur={false}
//       onChange={(item) => handleValueChange(item)}
//       onPress={handlePickerOpen}
//       onDismiss={() => setIsPickerOpen(false)}
//       style={[
//         styles.picker,
//         { width: width || "auto", height: height || 45 },

//         !disable
//           ? { borderColor: "#888888" }
//           : value
//           ? error
//             ? { borderColor: "red" }
//             : { borderColor: "#8143d9" }
//           : disableError
//           ? { borderColor: "#8143d9" }
//           : { borderColor: "red" },
//       ]}
//       topBarProps={{ title: "Select an Option" }}
//       showSearch
//       searchPlaceholder="Search..."
//       searchStyle={styles.searchStyle}
//       floatingPlaceholderStyle={styles.placeholderStyle}
//       onSearchChange={handleSearchChange}
//       items={getPickerItems()}
//       editable={disable}
//     />
//   );
// };

// const styles = StyleSheet.create({
//   picker: {
//     backgroundColor: "white",
//     borderWidth: 1,
//     borderRadius: 4,
//     padding: 12,
//     flexDirection: "row",
//     justifyContent: "space-between",
//     alignItems: "center",
//     marginTop: 5,
//   },
//   searchStyle: {
//     color: "#0059ff",
//     fontSize: 16,
//   },
//   placeholderStyle: {
//     fontSize: 16,
//     paddingTop: 5,
//     paddingBottom: 5,
//     color: "#000000",
//     justifyContent: "center",
//     alignItems: "center",
//     fontWeight: "500",
//   },
// });

// export default SearchablePicker;

import React, { useState, useEffect, useRef } from "react";
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  Modal,
  TextInput,
  FlatList,
  SafeAreaView,
} from "react-native";
import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons";

interface PickerItem {
  label: any;
  value: any;
}

interface SearchablePickerProps {
  items?: PickerItem[];
  placeholder: string;
  value?: any;
  onValueChange?: (selectedItem: PickerItem) => void;
  floatingLabel?: boolean;
  width?: any;
  height?: any;
  disable?: boolean;
  error?: boolean;
  disableError?: boolean;
  // New props for context-based add functionality
  screenContext?:
    | "site"
    | "referring_provider"
    | "pcp_provider"
    | "implanting_physician"
    | "credential";
  onAddPress?: (context: string) => void;
  showAddInSearch?: boolean; // Controls visibility of add button in search modal
}

const SearchablePicker: React.FC<SearchablePickerProps> = ({
  items,
  placeholder,
  value,
  onValueChange,
  floatingLabel = false,
  width,
  height,
  disable = true,
  error = false,
  disableError = false,
  screenContext,
  onAddPress,
  showAddInSearch = false,
}) => {
  const [selectedItem, setSelectedItem] = useState<PickerItem | null>(null);
  const [searchValue, setSearchValue] = useState<string>("");
  const [filteredOptions, setFilteredOptions] = useState<PickerItem[]>(
    items || []
  );
  const [isModalVisible, setIsModalVisible] = useState<boolean>(false);
  const searchInputRef = useRef<TextInput>(null);

  // Find selected item based on value
  useEffect(() => {
    if (value !== undefined && value !== null && value !== "") {
      if (items && Array.isArray(items) && items.length > 0) {
        const option = items.find((item) => item && item.value === value);
        setSelectedItem(option || null);
      } else {
        // If items are not loaded yet but we have a value, keep the current selectedItem
        // This handles the case where a new provider is added and we have the ID but items haven't refreshed
        if (!selectedItem || selectedItem.value !== value) {
          // Create a temporary item to show the value until items are loaded
          setSelectedItem({ label: `Loading...`, value: value });
        }
      }
    } else {
      setSelectedItem(null);
    }
  }, [value, items]);

  // Filter options based on search text
  useEffect(() => {
    if (!items || !Array.isArray(items)) {
      setFilteredOptions([]);
      return;
    }

    const filtered = items.filter(
      (option) =>
        option &&
        typeof option?.label === "string" &&
        option.label.toLowerCase().includes(searchValue.toLowerCase())
    );
    setFilteredOptions(filtered || []);
  }, [searchValue, items]);

  const openModal = () => {
    setIsModalVisible(true);
    setSearchValue("");
    setFilteredOptions(items || []);
    // Focus the search input after modal opens
    setTimeout(() => {
      searchInputRef.current?.focus();
    }, 100);
  };

  const closeModal = () => {
    setIsModalVisible(false);
    setSearchValue("");
  };

  const handleItemSelect = (item: PickerItem) => {
    setSelectedItem(item);
    if (onValueChange) {
      onValueChange(item);
    }
    closeModal();
  };

  const handleAddPress = () => {
    // Prevent multiple rapid clicks
    if (!onAddPress || !screenContext) return;

    // Close modal first
    closeModal();

    // Add a small delay to ensure modal is closed before opening the add provider sheet
    setTimeout(() => {
      onAddPress(screenContext);
    }, 100);
  };

  // Get context-specific button text and icon
  const getAddButtonConfig = () => {
    switch (screenContext) {
      case "site":
        return {
          text: "Add New Site",
          icon: "hospital-building",
        };
      case "referring_provider":
        return {
          text: "Add New Referring Provider",
          icon: "doctor",
        };
      case "pcp_provider":
        return {
          text: "Add New PCP Provider",
          icon: "account-heart",
        };
      case "implanting_physician":
        return {
          text: "Add New Implanting Physician",
          icon: "medical-bag",
        };
      // case "credential":
      //   return {
      //     text: "Add New Credential",
      //     icon: "certificate",
      //   };
      default:
        return {
          text: "NO OPTIONS",
          // icon: "plus-circle-outline",
        };
    }
  };

  const renderItem = ({ item }: { item: PickerItem }) => (
    <TouchableOpacity
      style={[
        styles.listItem,
        selectedItem?.value === item.value && styles.selectedItem,
      ]}
      onPress={() => handleItemSelect(item)}
    >
      <Text style={styles.listItemText}>{item.label}</Text>
    </TouchableOpacity>
  );

  const renderEmptyComponent = () => {
    const addButtonConfig = getAddButtonConfig();

    return (
      <View style={styles.emptyContainer}>
        <Text style={styles.emptyText}>No matching options</Text>
        {screenContext && onAddPress && (
          <TouchableOpacity style={styles.addButton} onPress={handleAddPress}>
            <MaterialCommunityIcons
              name={addButtonConfig.icon}
              size={20}
              color="#8143d9"
              style={styles.addButtonIcon}
            />
            <Text style={styles.addButtonText}>{addButtonConfig.text}</Text>
          </TouchableOpacity>
        )}
      </View>
    );
  };

  const getDisplayText = () => {
    if (selectedItem) {
      return selectedItem.label;
    }
    return "";
  };

  const getBorderColor = () => {
    if (!disable) return "#888888";
    if (value) {
      return error ? "red" : "#8143d9";
    }
    return disableError ? "#8143d9" : "red";
  };

  return (
    <View style={{ width: width || "auto" }}>
      {floatingLabel && selectedItem && (
        <Text style={styles.floatingLabel}>{placeholder}</Text>
      )}

      <TouchableOpacity
        style={[
          styles.picker,
          {
            width: width || "auto",
            height: height || 45,
            borderColor: getBorderColor(),
          },
        ]}
        onPress={openModal}
        disabled={!disable}
      >
        <Text
          style={[styles.pickerText, !selectedItem && styles.placeholderText]}
        >
          {getDisplayText() || placeholder}
        </Text>
        {/* <Text style={styles.dropdownIcon}>▼</Text> */}
      </TouchableOpacity>

      <Modal
        visible={isModalVisible}
        animationType="slide"
        transparent={false}
        onRequestClose={closeModal}
      >
        <SafeAreaView style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <TouchableOpacity onPress={closeModal} style={styles.cancelButton}>
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
            <Text style={styles.modalTitle}>Select an Option</Text>
            <View style={styles.placeholder} />
            <>
              {showAddInSearch && screenContext && onAddPress && (
                <TouchableOpacity
                  style={styles.searchAddButton}
                  onPress={handleAddPress}
                >
                  <MaterialCommunityIcons
                    name="plus-circle"
                    size={24}
                    color="#8143d9"
                  />
                </TouchableOpacity>
              )}
            </>
          </View>

          <View style={styles.searchContainer}>
            <View style={styles.searchInputWrapper}>
              <TextInput
                ref={searchInputRef}
                style={[
                  styles.searchInput,
                  showAddInSearch && styles.searchInputWithButton,
                ]}
                placeholder="Search..."
                value={searchValue}
                onChangeText={setSearchValue}
                autoFocus={true}
                clearButtonMode="while-editing"
              />
            </View>
          </View>

          <FlatList
            data={filteredOptions}
            renderItem={renderItem}
            keyExtractor={(item, index) => `${item.value}-${index}`}
            style={styles.list}
            ListEmptyComponent={renderEmptyComponent}
          />
        </SafeAreaView>
      </Modal>
    </View>
  );
};

const styles = StyleSheet.create({
  picker: {
    backgroundColor: "white",
    borderWidth: 1,
    borderRadius: 4,
    padding: 6,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginTop: 5,
  },
  pickerText: {
    fontSize: 16,
    color: "#000000",
    flex: 1,
  },
  placeholderText: {
    color: "#999",
  },
  dropdownIcon: {
    fontSize: 12,
    color: "#666",
  },
  floatingLabel: {
    fontSize: 12,
    color: "#8143d9",
    fontWeight: "500",
    marginBottom: -5,
    marginLeft: 8,
    backgroundColor: "white",
    alignSelf: "flex-start",
    paddingHorizontal: 4,
    zIndex: 1,
  },
  modalContainer: {
    flex: 1,
    backgroundColor: "white",
  },
  modalHeader: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#e0e0e0",
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#000",
  },
  cancelButton: {
    padding: 8,
  },
  cancelButtonText: {
    fontSize: 16,
    color: "#8143d9",
  },
  placeholder: {
    width: 60,
  },
  searchContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#e0e0e0",
  },
  searchInputWrapper: {
    flexDirection: "row",
    alignItems: "center",
    position: "relative",
  },
  searchInput: {
    fontSize: 16,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderWidth: 1,
    borderColor: "#ddd",
    borderRadius: 8,
    backgroundColor: "#f9f9f9",
    flex: 1,
  },
  searchInputWithButton: {
    paddingRight: 50,
  },
  searchAddButton: {
    position: "absolute",
    right: 10,
    padding: 4,
    justifyContent: "center",
    alignItems: "center",
  },
  list: {
    flex: 1,
  },
  listItem: {
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
  },
  selectedItem: {
    backgroundColor: "#f0f8ff",
  },
  listItemText: {
    fontSize: 16,
    color: "#000",
  },
  emptyContainer: {
    padding: 32,
    alignItems: "center",
  },
  emptyText: {
    fontSize: 16,
    color: "#666",
    marginBottom: 16,
  },
  addButton: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#f8f4ff",
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: "#8143d9",
    borderStyle: "dashed",
  },
  addButtonIcon: {
    marginRight: 8,
  },
  addButtonText: {
    fontSize: 16,
    color: "#8143d9",
    fontWeight: "500",
  },
});

export default SearchablePicker;
