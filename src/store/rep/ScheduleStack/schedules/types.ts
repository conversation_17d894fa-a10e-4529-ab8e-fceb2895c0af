interface IschedulesProps {
  schedules: any[];
  siteList: any[];
  patientList: any[];
  loaders: {
    agendaLoader: boolean;
    patientLoader: boolean;
    siteLoader: boolean;
  };
  chadScore: {
    congestive_heart_fail: boolean;
    lv_dysfunc: boolean;
    hypertension: boolean;
    diabetes_mellitus: boolean;
    stroke: boolean;
    tia: boolean;
    thromboembolic_event: boolean;
    vascular_disease: boolean;
  } | null;
  errors: {
    scheduleError: string | null;
    patientListError: string | null;
    siteListError: string | null;
  };
  selectedDate: string;
  selectedSite: string;
  showAddCaseModal: boolean;
  showAddPatientModal: boolean;
}

interface IfetchPatientsListParams {
  site_id: number;
  case_date: string;
}

interface IfetchSchedulesParams {
  from_date: string;
  to_date: string;
}

export type {
  IschedulesProps,
  IfetchPatientsListParams,
  IfetchSchedulesParams,
};
