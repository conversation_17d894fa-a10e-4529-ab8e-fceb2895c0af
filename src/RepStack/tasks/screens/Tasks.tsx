import * as React from "react";
import { <PERSON>, FlatList, RefreshControl } from "react-native";
import CustomText from "../../../components/CustomText";
import CustomInput from "../../../components/CustomTextInput";
import RepTaskCard from "../Components/TaskCard";
import CustomTabView from "../../../components/CustomTabView";
import { useRepTasks, useLoaderAndError } from "../hooks/tasksHooks";
import { useFocusEffect } from "@react-navigation/native";
import Loader from "../../../components/Loader";
import Error from "../../../components/Error";
import { fetchRepTasks } from "../../../store/rep/Tasks/tasks/thunk";
import { useDispatch } from "react-redux";
import { AppDispatch } from "../../../store";
import { ITask } from "../../../store/rep/Tasks/tasks/types";

interface ITasksProps {}

const Tasks: React.FunctionComponent<ITasksProps> = (props) => {
  const dispatch = useDispatch<AppDispatch>();
  const [refreshing, setRefreshing] = React.useState(false);

  useFocusEffect(
    React.useCallback(() => {
      const fetchTasks = async () => {
        try {
          const response = await dispatch(fetchRepTasks());

          if (response.payload) {
          }
        } catch (error) {
          console.error("Error fetching tasks:", error);
        }
      };
      fetchTasks();
    }, [])
  );

  const { error, loader } = useLoaderAndError();

  const repTasks = useRepTasks();

  const tabs = [
    { label: "All", value: "All" },
    { label: "Due", value: "Due" },
    { label: "Completed", value: "Completed" },
  ];

  const [selectedTopic, setSelectedTopic] = React.useState(tabs[0].value);
  const [searchQuery, setSearchQuery] = React.useState("");

  const filterTasks = (): ITask[] | null => {
    if (!repTasks) {
      return null;
    }

    // Filter tasks based on the selected topic
    let filteredTasks: ITask[] = repTasks;

    // Apply topic-based filtering
    switch (selectedTopic) {
      case "All":
        // Show all non-completed tasks
        filteredTasks = repTasks.filter(
          (task) => task.task_status.toLowerCase() !== "completed"
        );
        break;
      case "Due":
        // Show only pending tasks
        filteredTasks = repTasks.filter(
          (task) => task.task_status.toLowerCase() === "pending"
        );
        break;
      case "Completed":
        // Show only completed tasks
        filteredTasks = repTasks.filter(
          (task) => task.task_status.toLowerCase() === "completed"
        );
        break;
    }

    // Apply search query filtering
    if (searchQuery.trim()) {
      filteredTasks = filteredTasks.filter((task) =>
        task.patient.name.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    return filteredTasks;
  };

  const handleRefresh = async () => {
    setRefreshing(true);

    try {
      await fetchRepTasks();
    } catch (error) {
      console.error("Error refreshing schedules:", error);
    } finally {
      setRefreshing(false);
    }
  };

  if (loader) {
    return <Loader />;
  }

  if (error) {
    return <Error message={error} />;
  }

  return (
    <View className="flex-1 p-4 bg-primaryBg">
      <CustomInput
        placeholder="Search Patient"
        inputValue={searchQuery}
        onInputChange={setSearchQuery}
        searchIcon={{
          icon: true,
          iconPress: () => {},
        }}
      />
      <View className="mt-3">
        <CustomTabView
          options={tabs}
          onSelect={(option) => setSelectedTopic(option)}
        />
      </View>

      <FlatList
        data={filterTasks()}
        showsVerticalScrollIndicator={false}
        renderItem={({ item }) => (
          <View className="mb-3">
            <RepTaskCard info={item} />
          </View>
        )}
        ListEmptyComponent={() => {
          return (
            <View className="flex-1 justify-center items-center h-[250px] ">
              <CustomText value="No tasks found" />
            </View>
          );
        }}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={["#8143d9"]}
            tintColor={"#8143d9"}
          />
        }
      />
    </View>
  );
};

export default Tasks;
