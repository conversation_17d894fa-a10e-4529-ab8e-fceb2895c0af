import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../../store";
import { IProfile } from "../../../store/coordinator/MyProfile/MyProfileScreen/types";
import { fetchProfile } from "../../../store/coordinator/MyProfile/MyProfileScreen/thunk";

const useLoaderAndError = () => {
  const loader = useSelector(
    (state: RootState) => state.coordinator.profile.loading
  );
  const error = useSelector(
    (state: RootState) => state.coordinator.profile.error
  );
  return { loader, error };
};

const useProfileDetails = () => {
  const { profileDetails } = useSelector(
    (state: RootState) => state.coordinator.profile
  );

  return profileDetails;
};

const formatProfileDetails = (data: IProfile | null) => {
  const user_id = data?.user_id || null;
  const name = data?.name || null;
  const role = data?.role || null;
  const email_id = data?.email_id || null;
  const site_id = data?.org?.id || null;
  const site_name = data?.org?.name || null;

  return {
    user_id,
    name,
    role,
    email_id,
    site_id,
    site_name,
  };
};

export { useProfileDetails, formatProfileDetails, useLoaderAndError };
