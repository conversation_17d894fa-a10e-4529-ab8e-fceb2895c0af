import * as React from "react";
import { useState } from "react";
import { View, Text, TouchableOpacity } from "react-native";
import DatePicker from "react-native-date-picker";
import { useNavigation } from "@react-navigation/native";

import moment from "moment";
import CustomInput from "../../../components/CustomTextInput";
import CustomText from "../../../components/CustomText";
import Heading from "../../../components/Heading";
import ToggleButton from "../../../components/ToggleButton";
import CustomCard from "../../../components/CustomCard";
import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons";
import {
  usePatientDemographics,
  useUserDetails,
  usePatientDemographicsDiff,
  usePreopDetails,
} from "../hooks/preopHooks";
import SearchablePicker from "../../../components/SearchablePicker";
import { setPreopUserDetails } from "../../../store/clinician/ScheduleStack/preop";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch } from "../../../store";
import { fetchProfile } from "../../../store/clinician/MyProfile/MyProfileScreen/thunk";
import { putPreopDetails } from "../../../store/clinician/ScheduleStack/preop/thunk";
import { useFocusEffect } from "@react-navigation/native";
import {
  formatProfileDetails,
  useProfileDetails,
} from "../../my-profile/hooks/profileHooks";
import { fetchPreopDetails } from "../../../store/clinician/ScheduleStack/preop/thunk";
import LineSeperator from "../../../components/LineSeperator";
import { parseCharInput, parseFloatInput } from "../../../utils";
import PopupModal from "../../../components/Popup";
import SaveActionButton from "../../../components/SaveActionButton";
import { simpleAutoSave } from "../../../services/simpleAutoSave";
import { setSaveStatus, setLastSavedData } from "../../../store/services";

interface IPatientDemographicsCardProps {}

const PatientDemographicsCard: React.FunctionComponent<
  IPatientDemographicsCardProps
> = () => {
  const dispatch = useDispatch<AppDispatch>();
  const [date, setDate] = useState(new Date());
  const [open, setOpen] = useState(false);
  const navigation = useNavigation();
  const [selectedGender, setSelectedGender] = useState<string | null>(null);
  const userProfileDetails = useProfileDetails();
  const { site_id } = formatProfileDetails(userProfileDetails);
  const data = usePatientDemographics();
  const userDetails = useUserDetails();
  const preopDetails = usePreopDetails();
  const diff = usePatientDemographicsDiff();
  // Create a ref to track if a save is in progress
  const saveInProgressRef = React.useRef(false);

  // Create a ref to track the last saved data hash
  const lastSavedDataHashRef = React.useRef("");
  const [modalVisible, setModalVisible] = React.useState(false);

  const [popupMsg, setPopupMsg] = React.useState<string[]>([]);

  useFocusEffect(
    React.useCallback(() => {
      const fetchDetails = async () => {
        const res = await dispatch(fetchProfile());
      };
      fetchDetails();
    }, [dispatch])
  );

  const validator = () => {
    setPopupMsg([]);

    var temp = false;
    if (
      patientData?.first_name === "" ||
      patientData?.first_name === null ||
      patientData?.first_name === undefined
    ) {
      setPopupMsg((prev) => [...prev, "Please Enter First Name"]);
      temp = true;
    }

    if (
      patientData?.last_name === null ||
      patientData?.last_name === undefined ||
      patientData?.last_name === ""
    ) {
      setPopupMsg((prev) => [...prev, "Please Enter Last Name"]);
      temp = true;
    }

    if (
      patientData?.dateOfBirth === null ||
      patientData?.dateOfBirth === undefined ||
      patientData?.dateOfBirth === ""
    ) {
      setPopupMsg((prev) => [...prev, "Please Enter DOB"]);
      temp = true;
    }

    if (temp) {
      setModalVisible(true);
      return false;
    } else {
      return true;
    }
  };

  const saveDetails = async (): Promise<void> => {
    try {
      // Run validation
      const isValid = validator();

      if (!isValid) {
        dispatch(
          setSaveStatus({
            screenId: "preop",
            status: "validation_failed",
          })
        );
        setLocalSaveStatus("validation_failed");
        throw new Error("Validation failed");
      }

      // Avoid saving if already in progress
      if (saveInProgressRef.current) {
        return;
      }

      const dataCopy = { ...diff.currentValues };
      const currentDataHash = JSON.stringify(dataCopy);

      // If the data hasn't changed since the last save, skip saving
      if (currentDataHash === lastSavedDataHashRef.current) {
        return;
      }

      // Mark that a save is in progress
      saveInProgressRef.current = true;

      const res = await dispatch(
        putPreopDetails({
          case_id: userDetails?.case_id,
          payload: dataCopy,
        })
      );

      if (res.payload && userDetails?.case_id) {
        const refetchRes = await dispatch(
          fetchPreopDetails({
            case_id: userDetails.case_id,
          })
        );

        if (refetchRes.payload) {
          dispatch(setPreopUserDetails(refetchRes.payload));
          // Optionally update the hash if refetch confirms a good save
          lastSavedDataHashRef.current = currentDataHash;
        }
      }

      saveInProgressRef.current = false;
    } catch (err) {
      console.error("Save error:", err);
      saveInProgressRef.current = false;
      throw err;
    }
  };

  const patientData = {
    first_name: data?.first_name,
    middle_name: data?.middle_name,
    last_name: data?.last_name,
    dateOfBirth: data?.dob,
    gender: data?.sex,
    pcp_selected_id: data?.pcp_selected_id,
    pcp_options: data?.pcp_options,
    referring_provider_selected_id: data?.referring_provider_id,
    referring_provider_options: data?.referring_provider_options,
  };

  const saveStatus = useSelector(
    (state: any) => state.common?.autoSave?.["preop"]?.status
  );

  const [localSaveStatus, setLocalSaveStatus] = React.useState<string>("");

  const effectiveSaveStatus = saveStatus || localSaveStatus;

  // Function to check if all required fields are filled (excluding referring provider and PCP provider)
  const areRequiredFieldsFilled = () => {
    return (
      patientData?.first_name &&
      patientData?.first_name !== "" &&
      patientData?.last_name &&
      patientData?.last_name !== "" &&
      patientData?.patient_dob &&
      patientData?.patient_dob !== "" &&
      patientData?.patient_sex &&
      patientData?.patient_sex !== "" &&
      patientData?.procedure_date &&
      patientData?.procedure_date !== "" &&
      patientData?.procedure_time &&
      patientData?.procedure_time !== "" &&
      patientData?.site?.selected?.id &&
      patientData?.site?.selected?.id !== "" &&
      patientData?.implanting_physician?.selected?.id &&
      patientData?.implanting_physician?.selected?.id !== ""
    );
  };

  // Track if user has made changes to prevent auto-navigation on initial load
  const [hasUserMadeChanges, setHasUserMadeChanges] = React.useState(false);

  // Track when user makes changes to any field
  React.useEffect(() => {
    if (diff.hasDiff && !hasUserMadeChanges) {
      setHasUserMadeChanges(true);
    }
  }, [diff.hasDiff, hasUserMadeChanges]);

  // Add effect to navigate to LAAO Procedure screen when all required fields are filled and saved
  React.useEffect(() => {
    if (
      effectiveSaveStatus === "saved" &&
      areRequiredFieldsFilled() &&
      hasUserMadeChanges
    ) {
      // Add a small delay to ensure the user sees the "saved" message briefly
      const timer = setTimeout(() => {
        (navigation as any).navigate("LAAO Procedure");
      }, 1500);

      return () => clearTimeout(timer);
    }
  }, [
    effectiveSaveStatus,
    navigation,
    patientData?.first_name,
    patientData?.last_name,
    patientData?.patient_dob,
    patientData?.patient_sex,
    patientData?.procedure_date,
    patientData?.procedure_time,
    patientData?.site?.selected?.id,
    patientData?.implanting_physician?.selected?.id,
    hasUserMadeChanges,
  ]);

  /**
   * Derive the save message and styling based on the save status
   */
  const { saveMessage, messageStyle, iconName, iconColor } =
    React.useMemo(() => {
      let message = "";
      let style = "text-primaryPurple italic";
      let icon = "alert-circle-outline";
      let color = "#8143d9";

      switch (effectiveSaveStatus) {
        case "changes_detected":
          message = "Changes detected, saving soon...";
          style = "text-primaryBlack italic";
          icon = "alert-circle-outline";
          color = "#f5c542"; // yellow
          break;
        case "saving":
          message = "Saving... Please don't close the app";
          style = "text-primaryPurple font-bold italic";
          icon = "alert-circle-outline";
          color = "#8143d9"; // purple
          break;
        case "saved":
          message = "All changes saved";
          style = "text-green-3 italic";
          icon = "check-circle-outline";
          color = "#28a745"; // green
          break;
        case "validation_failed":
          message = "Validation failed. Please check your inputs.";
          style = "text-red-3 font-bold italic";
          icon = "alert-circle-outline";
          color = "#d93025"; // red
          break;
        case "error":
          message = "Error saving changes. Please try again.";
          style = "text-red-3 font-bold italic";
          icon = "alert-circle-outline";
          color = "#d93025"; // red
          break;
      }

      return {
        saveMessage: message,
        messageStyle: style,
        iconName: icon,
        iconColor: color,
      };
    }, [effectiveSaveStatus]);
  /**
   * Ref to track previous diff values to prevent unnecessary saves
   */
  const prevDiffRef = React.useRef<string>("");

  /**
   * Ref to track if initial data has been loaded
   * This prevents autosave from triggering before data is fetched
   */
  const initialDataLoadedRef = React.useRef<boolean>(false);

  /**
   * Effect to set initialDataLoaded when data is fetched
   */
  React.useEffect(() => {
    // Check if we have valid data
    const hasValidData = userDetails?.case_id !== undefined;

    if (hasValidData && !initialDataLoadedRef.current) {
      initialDataLoadedRef.current = true;
    }
  }, [userDetails]);

  /**
   * Effect to trigger autosave when data changes
   * This uses our new simpleAutoSave service with local state fallback
   */
  React.useEffect(() => {
    // Only proceed if initial data has been loaded and there are actual differences
    if (!initialDataLoadedRef.current) {
      return;
    }

    // Check if case_id exists to prevent API calls with undefined ID
    if (!userDetails?.case_id) {
      return;
    }

    // Only proceed if there are actual differences
    if (diff.hasDiff) {
      // Check if we have any undefined values in critical fields
      const hasUndefinedValues = Object.values(diff.currentValues).every(
        (val) => val === undefined
      );
      if (hasUndefinedValues) {
        return;
      }

      // Convert current values to string for comparison
      const currentDiffString = JSON.stringify(diff.currentValues);

      // Only trigger save if the values have actually changed
      if (currentDiffString !== prevDiffRef.current) {
        // Update the ref with current values
        prevDiffRef.current = currentDiffString;

        // Create a custom wrapper for setSaveStatus that updates both Redux and local state
        const setStatusWithFallback = (payload: {
          screenId: string;
          status: string;
        }) => {
          // Update local state as a fallback
          setLocalSaveStatus(payload.status);
          // Also dispatch to Redux
          return dispatch(setSaveStatus(payload));
        };

        // Trigger the simple autosave with the current values
        simpleAutoSave(
          "preop", // Screen ID
          diff.currentValues, // Data to save
          saveDetails, // Save function
          4000, // 4 second delay before saving
          dispatch, // Redux dispatch
          setStatusWithFallback, // Custom action to set save status with fallback
          setLastSavedData // Action to set last saved data
        );
      } else {
        console.log("No actual changes in diff values, skipping autosave");
      }
    }
  }, [
    diff.hasDiff,
    diff.currentValues,
    dispatch,
    setLocalSaveStatus,
    userDetails,
  ]);

  const handleCancel = () => {
    dispatch(
      setPreopUserDetails({
        ...userDetails,
        ...preopDetails,
      })
    );
  };

  return (
    <>
      <View className="p-3">
        {saveMessage && (
          <View
            style={{
              flexDirection: "row",
              justifyContent: "flex-end",
              alignItems: "center",
              marginVertical: 4,
            }}
          >
            <MaterialCommunityIcons
              name={iconName}
              color={iconColor}
              size={20}
            />
            <Text
              className={`text-sm ${messageStyle}`}
              style={{ marginLeft: 6 }}
            >
              {saveMessage}
            </Text>
          </View>
        )}
        <View className="gap-y-2">
          <Text className="text-primaryBlack font-medium ">First Name</Text>
          <View className="flex-1">
            <CustomInput
              inputValue={patientData?.first_name}
              error={!patientData?.first_name}
              onInputChange={(value) => {
                parseCharInput(value, (parsedValue) => {
                  dispatch(
                    setPreopUserDetails({
                      patient: {
                        ...userDetails?.patient,
                        first_name: parsedValue,
                      },
                    })
                  );
                });
              }}
              height={38}
            />
          </View>

          <LineSeperator extraStyle="mt-2 mb-1" />

          <Text className="text-primaryBlack font-medium ">Middle Name</Text>
          <View className="flex-1">
            <CustomInput
              inputValue={patientData?.middle_name}
              onInputChange={(value) => {
                parseCharInput(value, (parsedValue) => {
                  dispatch(
                    setPreopUserDetails({
                      patient: {
                        ...userDetails?.patient,
                        middle_name: parsedValue,
                      },
                    })
                  );
                });
              }}
              placeholder="Middle Name"
              height={38}
            />
          </View>

          <LineSeperator extraStyle="mt-2 mb-1" />

          <Text className="text-primaryBlack font-medium ">Last Name</Text>
          <View className="flex-1">
            <CustomInput
              inputValue={patientData?.last_name}
              error={!patientData?.last_name}
              onInputChange={(value) => {
                parseCharInput(value, (parsedValue) => {
                  dispatch(
                    setPreopUserDetails({
                      patient: {
                        ...userDetails?.patient,
                        last_name: parsedValue,
                      },
                    })
                  );
                });
              }}
              height={38}
            />
          </View>

          <LineSeperator extraStyle="mt-2 mb-1" />

          <Text className="text-primaryBlack font-medium ">Date of Birth</Text>
          <View className="flex-1">
            <TouchableOpacity
              onPress={() => setOpen(true)}
              className={`bg-primaryWhite border h-[38px] rounded-md justify-center px-3 ${
                patientData?.dateOfBirth
                  ? "border-primaryPurple"
                  : "border-red-3"
              }`}
            >
              <Text className="text-primaryBlack">
                {moment(patientData?.dateOfBirth).format("MM-DD-YYYY")}
              </Text>
            </TouchableOpacity>
          </View>

          <LineSeperator extraStyle="mt-2 mb-1" />
          {/* 
          <View className="flex-row justify-between items-center ">
            <Text className="text-primaryBlack font-medium ">Gender</Text>
            <ToggleButton
              messages={["Female", "Male"]}
              selected={patientData?.gender === "M" ? "Male" : "Female"}
              setSelected={(value) => {
                dispatch(
                  setPreopUserDetails({
                    patient: {
                      ...userDetails?.patient,
                      sex: value === "Male" ? "M" : "F",
                    },
                  })
                );
              }}
              yesIconName="human-female"
              noIconName="human-male"
              disabled={false}
              width={"w-[77px]"}
              invertColor
              customColors={["#8143d9", "#8143d9"]}
              customToggler
              isBox={false}
            />
          </View> */}

          <Text className="text-primaryBlack font-medium ">Gender</Text>
          <View className="flex-1">
            <SearchablePicker
              placeholder="Select Gender"
              items={[
                { label: "Male", value: "M" },
                { label: "Female", value: "F" },
              ]}
              value={patientData?.gender || ""}
              onValueChange={(value) => {
                dispatch(
                  setPreopUserDetails({
                    patient: {
                      ...userDetails?.patient,
                      sex: value.value,
                    },
                  })
                );
              }}
              height={38}
            />
          </View>

          <LineSeperator extraStyle="mt-2 mb-1" />

          <Text className="text-primaryBlack font-medium ">PCP</Text>
          <View className="flex-1">
            <SearchablePicker
              items={patientData?.pcp_options || []}
              value={patientData?.pcp_selected_id || ""}
              placeholder="Select"
              onValueChange={(option) => {
                dispatch(
                  setPreopUserDetails({
                    patient: {
                      ...userDetails?.patient,
                      pcp: {
                        ...userDetails?.patient?.pcp,
                        selected: {
                          ...userDetails?.patient?.pcp?.selected,
                          id: option.value,
                        },
                      },
                    },
                  })
                );
              }}
              height={38}
              disableError
            />
          </View>

          <LineSeperator extraStyle="mt-2 mb-1" />

          <Text className="text-primaryBlack font-medium ">
            Referring Provider
          </Text>
          <View className="flex-1">
            <SearchablePicker
              items={patientData?.referring_provider_options || []}
              value={patientData?.referring_provider_selected_id || ""}
              placeholder="Select"
              onValueChange={(option) => {
                dispatch(
                  setPreopUserDetails({
                    patient: {
                      ...userDetails?.patient,
                      referring_provider: {
                        ...userDetails?.patient?.referring_provider,
                        selected: {
                          ...userDetails?.patient?.referring_provider?.selected,
                          id: option.value,
                        },
                      },
                    },
                  })
                );
              }}
              height={38}
              disableError
            />
          </View>
        </View>

        <LineSeperator extraStyle="my-5" />

        <View className="flex-row justify-center gap-4 ">
          {/* <TouchableOpacity
            onPress={handleSave}
            className={`border 
              ${
                !diff?.hasDiff
                  ? `bg-primaryGray border-primaryGray`
                  : `bg-primaryPurple border-primaryPurple`
              }
               px-6 py-3 rounded-full`}
            disabled={!diff?.hasDiff}
          >
            <Text className="text-primaryWhite font-semibold">Save</Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={handleCancel}
            className="border border-primaryPurple px-6 py-3 rounded-full bg-primaryBg"
          >
            <Text className="text-primaryPurple">Cancel</Text>
          </TouchableOpacity> */}

          {/* <SaveActionButton
            disabled={!diff.hasDiff}
            onPress={saveDetails}
            onCancel={handleCancel}
          /> */}
        </View>
      </View>
      <DatePicker
        modal
        open={open}
        date={
          patientData?.dateOfBirth
            ? moment(patientData.dateOfBirth, "YYYY-MM-DD").toDate()
            : new Date()
        }
        mode="date"
        maximumDate={new Date()}
        onConfirm={(date) => {
          setOpen(false);
          dispatch(
            setPreopUserDetails({
              patient: {
                ...userDetails?.patient,
                dob: moment(date).format("YYYY-MM-DD"),
              },
            })
          );
        }}
        onCancel={() => setOpen(false)}
      />

      <PopupModal
        show={modalVisible}
        msg={popupMsg}
        status="warning"
        onClose={() => setModalVisible(false)}
      />
    </>
  );
};
export default PatientDemographicsCard;
