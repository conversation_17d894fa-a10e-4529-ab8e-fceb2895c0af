import React, { useState } from "react";
import { View, Text, TouchableOpacity } from "react-native";
import CustomCard from "../../../components/CustomCard";
import { usePreopDetails } from "../hooks/preopHooks";
import { useNavigation } from "@react-navigation/native";

interface IPrelaacImagingCardProps {}

const PrelaacImagingCard: React.FunctionComponent<
  IPrelaacImagingCardProps
> = () => {
  const navigation = useNavigation();
  const { patient } = usePreopDetails();

  return (
    <View className="p-3">
      <View className="flex-row items-center justify-between gap-2">
        <TouchableOpacity
          className={`${
            patient?.cta ? `bg-green-2` : `bg-secondaryGray`
          } rounded items-center w-[40%] p-2 shadow-sm`}
          onPress={() => {
            navigation.navigate("CTA");
          }}
          disabled={patient?.cta ? false : true}
        >
          <Text
            className={`
                                        ${
                                          patient?.cta
                                            ? `text-green-3`
                                            : `text-primaryWhite`
                                        }
                                        `}
          >
            {"Pre-Op CTA"}
          </Text>
        </TouchableOpacity>

        <TouchableOpacity
          className={`${
            patient?.tee ? `bg-green-2` : `bg-secondaryGray`
          } rounded items-center w-[40%] p-2 shadow-sm`}
          // onPress={() => {
          //   navigation.navigate("TEE", {
          //     morphology: morphData,
          //     clotData: morphData,
          //     angles: ["0", "90", "45", "135"],
          //     diameters: ["26 mm", "19 mm", "19 mm", "18 mm"],
          //     depths: ["37 mm", "30 mm", "35 mm", "26 mm"],
          //   });
          // }}
          disabled={patient?.tee ? false : true}
        >
          <Text
            className={`
                                        ${
                                          patient?.tee
                                            ? `text-green-3`
                                            : `text-primaryWhite`
                                        }
                                        `}
          >
            {"Pre-Op TEE"}
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default PrelaacImagingCard;
