import React, { useState } from "react";
import { View, Text, ScrollView, ActivityIndicator } from "react-native";
import { useDispatch } from "react-redux";
import { AppDispatch } from "../../../store";
import {
  fetchSites,
  postSite,
} from "../../../store/rep/ScheduleStack/addSite/thunk";
import {
  useLoaderAndError,
  formatPostSite,
  useSitesDetails,
  useUserSitesDetails,
  findPostSiteDiff,
} from "../hooks/addSiteHooks";
import {
  setSiteDetails,
  resetSiteDetails,
  showAddSiteModal,
} from "../../../store/rep/ScheduleStack/addSite";
import CustomBottomSheetTextInput from "../../../components/CustomBottomSheetTextInput";
import LineSeperator from "../../../components/LineSeperator";
import SaveActionButton from "../../../components/SaveActionButton";
import Loader from "../../../components/Loader";
import { parseCharInput, parseIntInput } from "../../../utils";
import PopupModal from "../../../components/Popup";
import { setSelectedProvider } from "../../../store/common/addReferringProvider";
import { fetchSiteList } from "../../../store/rep/ScheduleStack/schedules/thunk";
import { setSelectedSite } from "../../../store/rep/ScheduleStack/addSite";
import SiteList from "./SiteList";
import { useSiteList } from "../hooks/schedulesHooks";

interface AddSiteProps {
  onSuccess?: (newSiteId: string) => void;
  onCancel?: () => void;
  bottomSheetRef?: React.RefObject<any>;
}

const AddSite: React.FC<AddSiteProps> = ({
  onSuccess,
  onCancel,
  bottomSheetRef,
}) => {
  const dispatch = useDispatch<AppDispatch>();
  const siteListData = useSiteList();
  const [popupMsg, setPopupMsg] = React.useState<string[]>([]);

  const [modalVisible, setModalVisible] = React.useState(false);

  const userDetails = useUserSitesDetails();
  const {
    site_name,
    site_account_id,
    site_address,
    site_city,
    site_state,
    site_zipcode,
  } = formatPostSite(userDetails);
  const { loader, error } = useLoaderAndError();
  const diff = findPostSiteDiff();

  const validator = () => {
    setPopupMsg([]);

    var temp = false;
    if (site_name === "" || site_name === null || site_name === undefined) {
      setPopupMsg((prev) => [...prev, "Please Enter Site Name"]);
      temp = true;
    }

    if (site_city === "" || site_city === null || site_city === undefined) {
      setPopupMsg((prev) => [...prev, "Please Enter Site City"]);
      temp = true;
    }

    if (site_state === "" || site_state === null || site_state === undefined) {
      setPopupMsg((prev) => [...prev, "Please Enter Site State"]);
      temp = true;
    }

    if (temp) {
      setModalVisible(true);
      return false;
    } else {
      return true;
    }
  };

  // const saveDetails = async () => {
  //   try {
  //     if (validator()) {
  //       const res = await dispatch(postSite(diff.currentValues));

  //       if (res) {
  //         onSuccess?.();
  //         await dispatch(fetchSiteList());
  //         const lastSiteId = siteListData[siteListData.length - 1]?.id;
  //         console.log("lastSiteId:", lastSiteId);
  //         await dispatch(setSelectedSite(lastSiteId));
  //       }
  //     }
  //   } catch (err) {
  //     console.error(err);
  //   }
  // };

  const saveDetails = async () => {
    try {
      if (validator()) {
        const res = await dispatch(postSite(diff.currentValues)).unwrap();

        if (res) {
          // Use site_id from response directly
          const newSiteId = res.site_id;
          onSuccess?.(newSiteId);

          // Optional: refresh site list
          await dispatch(fetchSiteList());

          // Set the newly created site as selected
          await dispatch(setSelectedSite(newSiteId));
        }
      }
    } catch (err) {
      console.error(err);
    }
  };

  const handleCancel = () => {
    dispatch(resetSiteDetails());
    dispatch(showAddSiteModal(false));
    bottomSheetRef?.current?.close();
  };

  return (
    <>
      {loader ? (
        <View className="mt-64">
          <Loader />
        </View>
      ) : (
        <View className="p-3 bg-primaryWhite">
          <View className="rounded-md p-3">
            <Text className="text-primaryPurple text-center font-semibold text-lg mb-4">
              Add Site
            </Text>

            {error && (
              <View className="bg-red-100 p-3 rounded-md mb-4">
                <Text className="text-red-700">{error}</Text>
              </View>
            )}

            <View className="flex-row justify-center gap-4">
              <SaveActionButton
                disabled={!diff}
                onPress={saveDetails}
                onCancel={handleCancel}
              />
            </View>

            <LineSeperator extraStyle="my-5" />

            <View className="flex-row justify-between items-center">
              <Text className="text-primaryBlack font-medium w-[40%]">
                Hospital Name <Text className="text-red-3 text-lg">*</Text>
              </Text>
              <View className="flex-1">
                <CustomBottomSheetTextInput
                  inputValue={site_name}
                  placeholder="Hospital Name"
                  onInputChange={(value) => {
                    parseCharInput(value, (parsedValue) => {
                      dispatch(
                        setSiteDetails({
                          name: parsedValue,
                        })
                      );
                    });
                  }}
                />
              </View>
            </View>

            <LineSeperator extraStyle="my-5" />

            <View className="flex-row justify-between items-center">
              <Text className="text-primaryBlack font-medium w-[40%]">
                Account ID
              </Text>
              <View className="flex-1">
                <CustomBottomSheetTextInput
                  inputValue={site_account_id?.toString()}
                  placeholder="Account ID"
                  onInputChange={(value) => {
                    parseIntInput(value, (parsedValue) => {
                      dispatch(
                        setSiteDetails({
                          account: parsedValue,
                        })
                      );
                    });
                  }}
                  keyboardType="numeric"
                />
              </View>
            </View>

            <LineSeperator extraStyle="my-5" />

            <View className="flex-row justify-between items-center">
              <Text className="text-primaryBlack font-medium w-[40%]">
                Address
              </Text>
              <View className="flex-1">
                <CustomBottomSheetTextInput
                  inputValue={site_address}
                  placeholder="Address"
                  onInputChange={(value) => {
                    dispatch(
                      setSiteDetails({
                        address: value,
                      })
                    );
                  }}
                />
              </View>
            </View>

            <LineSeperator extraStyle="my-5" />

            <View className="flex-row justify-between items-center">
              <Text className="text-primaryBlack font-medium w-[40%]">
                City <Text className="text-red-3 text-lg">*</Text>
              </Text>
              <View className="flex-1">
                <CustomBottomSheetTextInput
                  inputValue={site_city}
                  placeholder="City"
                  onInputChange={(value) => {
                    parseCharInput(value, (parsedValue) => {
                      dispatch(
                        setSiteDetails({
                          city: parsedValue,
                        })
                      );
                    });
                  }}
                />
              </View>
            </View>

            <LineSeperator extraStyle="my-5" />

            <View className="flex-row justify-between items-center">
              <Text className="text-primaryBlack font-medium w-[40%]">
                State <Text className="text-red-3 text-lg">*</Text>
              </Text>
              <View className="flex-1">
                <CustomBottomSheetTextInput
                  inputValue={site_state}
                  placeholder="State"
                  onInputChange={(value) => {
                    parseCharInput(value, (parsedValue) => {
                      dispatch(
                        setSiteDetails({
                          state: parsedValue,
                        })
                      );
                    });
                  }}
                />
              </View>
            </View>
            <LineSeperator extraStyle="my-5" />

            <View className="flex-row justify-between items-center">
              <Text className="text-primaryBlack font-medium w-[40%]">
                Zip Code
              </Text>
              <View className="flex-1">
                <CustomBottomSheetTextInput
                  inputValue={site_zipcode?.toString()}
                  placeholder="Zip Code"
                  onInputChange={(value) => {
                    parseIntInput(value, (parsedValue) => {
                      dispatch(
                        setSiteDetails({
                          zip_code: parsedValue,
                        })
                      );
                    });
                  }}
                  keyboardType="numeric"
                />
              </View>
            </View>
            <LineSeperator extraStyle="my-5" />
          </View>
        </View>
      )}
      <PopupModal
        show={modalVisible}
        msg={popupMsg}
        status="warning"
        onClose={() => setModalVisible(false)}
      />
    </>
  );
};

export default AddSite;
