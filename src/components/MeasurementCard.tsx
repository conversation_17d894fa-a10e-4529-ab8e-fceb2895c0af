import React from "react";
import { View, Image, TouchableOpacity } from "react-native";
import CustomText from "../components/CustomText";

type Measurement = {
  image: any; // Replace `any` with the appropriate type for the image source
  label: string;
  link?: string;
};

type MeasurementCardProps = {
  measurements: Measurement[];
};

const MeasurementCard: React.FC<MeasurementCardProps> = ({ measurements }) => {
  return (
    <View className="mt-5">
      <View className="flex-row justify-between px-2 flex-wrap">
        {measurements.map((measurement, index) => (
          <View key={index} className="relative w-[45%] mb-4">
            <Image
              source={measurement.image}
              className="rounded-b-md w-full h-32 mb-2"
              resizeMode="cover"
            />
            <TouchableOpacity
              onPress={() => {
                if (measurement.link) {
                }
              }}
              className="rounded-b-md absolute bottom-0 w-full bg-primaryBg px-2 py-1"
            >
              <CustomText
                value={measurement.label}
                className="text-primaryPurple text-sm font-semibold text-center"
              />
            </TouchableOpacity>
          </View>
        ))}
      </View>
    </View>
  );
};

export default MeasurementCard;
