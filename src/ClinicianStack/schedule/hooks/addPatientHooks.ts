import { useSelector } from "react-redux";
import { RootState } from "../../../store";
import {
  IPostPatient,
  IReferringProvider,
  IImplantingPhysician,
} from "../../../store/coordinator/ScheduleStack/addpatient/types";

const useLoaderAndErrorPostPatient = () => {
  const loader = useSelector(
    (state: RootState) => state.clinician.postPatient.loading
  );
  const error = useSelector(
    (state: RootState) => state.clinician.postPatient.error
  );
  return { loader, error };
};

const useLoaderAndErrorReferringProvider = () => {
  const loader = useSelector(
    (state: RootState) => state.common.referringProviders.loading
  );
  const error = useSelector(
    (state: RootState) => state.common.referringProviders.error
  );
  return { loader, error };
};

const useLoaderAndErrorImplantingPhysician = () => {
  const loader = useSelector(
    (state: RootState) => state.clinician.implantingPhysicians.loading
  );
  const error = useSelector(
    (state: RootState) => state.clinician.implantingPhysicians.error
  );
  return { loader, error };
};

const useReferringProviderDetails = () => {
  const referringProviders = useSelector(
    (state: RootState) => state.common.referringProviders.providerDetails
  );
  // If providerDetails is an array, transform it
  // If it's null, return an empty array
  if (Array.isArray(referringProviders)) {
    return referringProviders.map((provider: IReferringProvider) => ({
      value: provider.id,
      label: provider.name,
    }));
  } else {
    // If it's not an array, it might be a single object or null
    return [];
  }
};

const usePostPatientUserDetails = () => {
  const { userDetails } = useSelector(
    (state: RootState) => state.clinician.postPatient
  );
  return userDetails;
};

const usePostPatientDetails = () => {
  const { patientDetails } = useSelector(
    (state: RootState) => state.clinician.postPatient
  );
  return patientDetails;
};

const findPostPatientDiff = () => {
  const userDetails = usePostPatientUserDetails();
  const procedureDetails = usePostPatientDetails();

  const diff = JSON.stringify(userDetails) !== JSON.stringify(procedureDetails);

  const currentValues = {
    first_name: userDetails?.first_name || null,
    last_name: userDetails?.last_name || null,
    middle_name: userDetails?.middle_name || null,
    dob: userDetails?.dob || null,
    sex: userDetails?.sex || null,
    mrn: userDetails?.mrn || null,
    ssn: userDetails?.ssn || null,
    referring_providers: Array.isArray(userDetails?.referring_providers)
      ? userDetails.referring_providers
      : userDetails?.referring_providers
      ? [userDetails.referring_providers]
      : [],
    rationale: userDetails?.rationale_selected_id || null,
    rationale_other: userDetails?.rationale_other || null,
    procedure_date: userDetails?.procedure_date || null,
    procedure_time: userDetails?.procedure_time || null,
    implanting_physician: userDetails?.implanting_physician_id || null,
  };

  return {
    hasDiff: diff,
    currentValues: currentValues,
  };
};

const formatReferringProvider = (data: IReferringProvider[] | null) => {
  return {
    referringProviders: data || [],
  };
};

const formatTimeToHHMMSS = (timestamp: string): string => {
  if (!timestamp) {
    throw new Error("Invalid timestamp provided.");
  }

  const timePart = timestamp.split("T")[1]?.split(".")[0];
  if (!timePart) {
    throw new Error("Invalid timestamp format.");
  }

  return timePart;
};

const formatPostPatient = (data: IPostPatient) => {
  const first_name = data?.first_name || "";
  const last_name = data?.last_name || "";
  const middle_name = data?.middle_name || "";
  const dob = data?.dob || null;
  const sex = data?.sex || "";
  const mrn = data?.mrn || "";
  const ssn = data?.ssn || "";
  const referring_provider = data?.referring_provider_id || "";
  const rationale = data?.rationale_selected_id || "";
  const rationale_other = data?.rationale_other || "";
  const procedure_date = data?.procedure_date || null;
  const procedure_time = data?.procedure_time || null;
  const implanting_physician = data?.implanting_physician_id || "";

  return {
    first_name,
    last_name,
    middle_name,
    dob,
    sex,
    mrn,
    ssn,
    referring_provider,
    rationale,
    rationale_other,
    procedure_date,
    procedure_time,
    implanting_physician,
  };
};

const useImplantingPhysicianDetails = () => {
  const { implantingPhysicians } = useSelector(
    (state: RootState) => state.clinician.implantingPhysicians
  );
  return implantingPhysicians;
};

const formatImplantingPhysician = (data: IImplantingPhysician[] | null) => {
  return {
    implantingPhysicians: data || [],
  };
};

export {
  useLoaderAndErrorPostPatient,
  useLoaderAndErrorReferringProvider,
  useLoaderAndErrorImplantingPhysician,
  useReferringProviderDetails,
  usePostPatientUserDetails,
  usePostPatientDetails,
  findPostPatientDiff,
  formatPostPatient,
  formatReferringProvider,
  useImplantingPhysicianDetails,
  formatImplantingPhysician,
};
