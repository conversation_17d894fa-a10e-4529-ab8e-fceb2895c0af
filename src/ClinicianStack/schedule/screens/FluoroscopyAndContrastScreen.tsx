import * as React from "react";
import ScreenWrapper from "../../../components/ScreenWrapper";
import { Text, View } from "react-native";
import CustomInput from "../../../components/CustomTextInput";
import CustomCard from "../../../components/CustomCard";
import Heading from "../../../components/Heading";
import { parseFloatInput, parseIntInput } from "../../../utils";
import LineSeperator from "../../../components/LineSeperator";
import {
  fetchFluoroscopyDetails,
  putFluoroscopyDetails,
} from "../../../store/rep/ScheduleStack/laaoProcedures/fluoroscopy/thunk";
import { useSelectedPatient } from "../hooks/PatientsHooks";
import { AppDispatch } from "../../../store";
import { useDispatch, useSelector } from "react-redux";
import { formatFluoroscopyOptions } from "../../../utils";
import {
  useFluoroscopyUserDetails,
  useLoaderAndError,
  findFluoroscopyDiff,
} from "../hooks/fluoroscopyHooks";
import { setFluoroscopyUserDetails } from "../../../store/rep/ScheduleStack/laaoProcedures/fluoroscopy";
import { useFocusEffect } from "@react-navigation/native";
import Loader from "../../../components/Loader";
import SaveActionButton from "../../../components/SaveActionButton";
import { simpleAutoSave } from "../../../services/simpleAutoSave";
import { setSaveStatus, setLastSavedData } from "../../../store/services";

interface IFluoroscopyAndContrastScreenProps {}

const FluoroscopyAndContrastScreen: React.FunctionComponent<
  IFluoroscopyAndContrastScreenProps
> = (props) => {
  // NEW START

  const dispatch = useDispatch<AppDispatch>();
  const selectedPatient = useSelectedPatient();
  const userDetails = useFluoroscopyUserDetails();
  const { loader, error } = useLoaderAndError();
  const diff = findFluoroscopyDiff();
  const saveInProgressRef = React.useRef(false);
  // Create a ref to track the last saved data hash
  const lastSavedDataHashRef = React.useRef("");
  const { creatinine_value, fluoro_time, fluoro_total, total_contrast } =
    formatFluoroscopyOptions(userDetails);

  const saveDetails = async (): Promise<void> => {
    try {
      // // Run validation
      // const isValid = validator();

      // // If validation fails, update status and throw error
      // if (!isValid) {
      //   dispatch(
      //     setSaveStatus({
      //       screenId: "fluoroscopy",
      //       status: "validation_failed",
      //     })
      //   );
      //   setLocalSaveStatus("validation_failed");
      //   throw new Error("Validation failed");
      // }

      // Avoid saving if already in progress
      if (saveInProgressRef.current) {
        return;
      }

      const dataCopy = { ...diff.currentValues };
      const currentDataHash = JSON.stringify(dataCopy);

      // If the data hasn't changed since the last save, skip saving
      if (currentDataHash === lastSavedDataHashRef.current) {
        return;
      }

      // Mark that a save is in progress
      saveInProgressRef.current = true;

      let result;

      if (diff.hasDiff) {
        result = await dispatch(
          putFluoroscopyDetails({
            case_details_id: userDetails?.case_detail_id,
            payload: dataCopy,
          })
        );

        // Update the last saved data hash
        lastSavedDataHashRef.current = currentDataHash;

        // Refetch if applicable
        if (selectedPatient?.case_id) {
          const refetchRes = await dispatch(
            fetchFluoroscopyDetails({ case_id: selectedPatient.case_id })
          );
          // if (refetchRes.payload) {
          //   dispatch(setFluoroscopyUserDetails(refetchRes.payload));
          // }
        }
      }

      // Reset the save in progress flag
      saveInProgressRef.current = false;
    } catch (err) {
      saveInProgressRef.current = false;
      throw err;
    }
  };

  useFocusEffect(
    React.useCallback(() => {
      const fetchDetails = async () => {
        if (selectedPatient?.case_id) {
          const res = await dispatch(
            fetchFluoroscopyDetails({ case_id: selectedPatient.case_id })
          );

          if (res.payload) {
            dispatch(setFluoroscopyUserDetails(res.payload));
          }
        }
      };
      fetchDetails();
    }, [dispatch, selectedPatient?.case_id])
  );

  const saveStatus = useSelector(
    (state: any) => state.common?.autoSave?.["fluoroscopy"]?.status
  );

  const [localSaveStatus, setLocalSaveStatus] = React.useState<string>("");

  const effectiveSaveStatus = saveStatus || localSaveStatus;

  /**
   * Derive the save message and styling based on the save status
   */
  const { saveMessage, messageStyle } = React.useMemo(() => {
    let message = "";
    let style = "text-primaryPurple italic";

    switch (effectiveSaveStatus) {
      case "changes_detected":
        message = "Changes detected, saving soon...";
        style = "text-amber-500 italic";
        break;
      case "saving":
        message = "⚠️ Saving... Please don't close the app";
        style = "text-amber-600 font-bold italic";
        break;
      case "saved":
        message = "✅ All changes saved";
        style = "text-green-600 italic";
        break;
      case "validation_failed":
        message = "⚠️ Validation failed. Please check your inputs.";
        style = "text-red-600 font-bold italic";
        break;
      case "error":
        message = "❌ Error saving changes. Please try again.";
        style = "text-red-600 font-bold italic";
        break;
    }

    return { saveMessage: message, messageStyle: style };
  }, [effectiveSaveStatus, saveStatus, localSaveStatus]);

  /**
   * Ref to track previous diff values to prevent unnecessary saves
   */
  const prevDiffRef = React.useRef<string>("");

  /**
   * Ref to track if initial data has been loaded
   * This prevents autosave from triggering before data is fetched
   */
  const initialDataLoadedRef = React.useRef<boolean>(false);

  /**
   * Effect to set initialDataLoaded when data is fetched
   */
  React.useEffect(() => {
    // Check if we have valid data
    const hasValidData = userDetails?.case_detail_id !== undefined;

    if (hasValidData && !initialDataLoadedRef.current) {
      initialDataLoadedRef.current = true;
    }
  }, [userDetails]);

  /**
   * Effect to trigger autosave when data changes
   * This uses our new simpleAutoSave service with local state fallback
   */
  React.useEffect(() => {
    // Only proceed if initial data has been loaded and there are actual differences
    if (!initialDataLoadedRef.current) {
      return;
    }

    // Check if case_detail_id exists to prevent API calls with undefined ID
    if (!userDetails?.case_detail_id) {
      return;
    }

    // Only proceed if there are actual differences
    if (diff.hasDiff) {
      // Check if we have any undefined values in critical fields
      const hasUndefinedValues = Object.values(diff.currentValues).every(
        (val) => val === undefined
      );
      if (hasUndefinedValues) {
        return;
      }

      // Convert current values to string for comparison
      const currentDiffString = JSON.stringify(diff.currentValues);

      // Only trigger save if the values have actually changed
      if (currentDiffString !== prevDiffRef.current) {
        // Update the ref with current values
        prevDiffRef.current = currentDiffString;

        // Create a custom wrapper for setSaveStatus that updates both Redux and local state
        const setStatusWithFallback = (payload: {
          screenId: string;
          status: string;
        }) => {
          // Update local state as a fallback
          setLocalSaveStatus(payload.status);
          // Also dispatch to Redux
          return dispatch(setSaveStatus(payload));
        };

        // Trigger the simple autosave with the current values
        simpleAutoSave(
          "fluoroscopy", // Screen ID
          diff.currentValues, // Data to save
          saveDetails, // Save function
          4000, // 4 second delay before saving
          dispatch, // Redux dispatch
          setStatusWithFallback, // Custom action to set save status with fallback
          setLastSavedData // Action to set last saved data
        );
      } else {
        console.log("No actual changes in diff values, skipping autosave");
      }
    }
  }, [
    diff.hasDiff,
    diff.currentValues,
    dispatch,
    setLocalSaveStatus,
    userDetails,
  ]);

  const handleRefresh = async () => {
    if (!selectedPatient?.case_id) return;
    const refetchRes = await dispatch(
      fetchFluoroscopyDetails({ case_id: selectedPatient.case_id })
    );
    if (refetchRes.payload) {
      dispatch(setFluoroscopyUserDetails(refetchRes.payload));
    }
  };

  // NEW END

  if (loader) {
    return <Loader />;
  }

  return (
    <ScreenWrapper onRefresh={handleRefresh}>
      <CustomCard extraStyle="">
        {saveMessage && (
          <Text className={`text-sm text-right my-1 ${messageStyle}`}>
            {saveMessage}
          </Text>
        )}
        <SaveActionButton disabled={!diff.hasDiff} onPress={saveDetails} />

        <View>
          <Heading
            text="Creatinine Value (mg/dL)"
            size="sub-heading"
            extraStyle="pb-3"
            showSeperator={false}
          />
          <CustomInput
            inputValue={creatinine_value?.toString()}
            onInputChange={(value) =>
              parseFloatInput(value, (updatedValue) => {
                dispatch(
                  setFluoroscopyUserDetails({
                    creatinine_value: updatedValue,
                  })
                );
              })
            }
            placeholder="mg/dL"
            keyboardType="numeric"
          />
        </View>

        <LineSeperator extraStyle="my-5" />

        <View>
          <Heading
            text="Fluoro Time (min)"
            size="sub-heading"
            extraStyle="pb-3"
            showSeperator={false}
          />
          <CustomInput
            inputValue={fluoro_time?.toString()}
            onInputChange={(value) =>
              parseIntInput(value, (updatedValue) => {
                dispatch(
                  setFluoroscopyUserDetails({
                    fluoro_time: updatedValue,
                  })
                );
              })
            }
            placeholder="min"
            keyboardType="numeric"
          />
        </View>

        {/* <LineSeperator extraStyle="my-5" /> */}

        {/* <View>
          <Heading
            text="Fluoro total (mGy)"
            size="sub-heading"
            extraStyle="pb-3"
            showSeperator={false}
          />
          <CustomInput
            inputValue={fluoro_total?.toString()}
            onInputChange={(value) =>
              parseIntInput(value, (updatedValue) => {
                dispatch(
                  setFluoroscopyUserDetails({
                    fluoro_total: updatedValue,
                  })
                );
              })
            }
            placeholder="mGy"
            keyboardType="numeric"
          />
        </View> */}

        <LineSeperator extraStyle="my-5" />

        <View>
          <Heading
            text="Total Contrast (mL)"
            size="sub-heading"
            extraStyle="pb-3"
            showSeperator={false}
          />
          <CustomInput
            inputValue={total_contrast?.toString()}
            onInputChange={(value) =>
              parseIntInput(
                value,
                (updatedValue) => {
                  dispatch(
                    setFluoroscopyUserDetails({
                      total_contrast: updatedValue,
                    })
                  );
                },
                0,
                300
              )
            }
            placeholder="mL"
            keyboardType="numeric"
          />
        </View>
        <View className="mb-3 "></View>
      </CustomCard>
    </ScreenWrapper>
  );
};

export default FluoroscopyAndContrastScreen;
