interface CaseDetail {
  case_detail_id: string;
  case_id: string;
  suitability_tug: boolean;
  partial_recaptures: boolean;
  no_partial_recaptures: number;
  partial_recaptures_manipulation: string;
  device_deployed: boolean;
  device_not_deployed_rationale: string;
  case_aborted: boolean;
  case_aborted_rationale: string;
  product_chargeable: boolean;
  product_not_chargeable_rationale: string;
  implant_access_sheath: {
    selected: {
      id: string;
      name: string;
    };
    options: {
      id: string;
      name: string;
    }[];
  };
  device: {
    selected: {
      id: string;
      name: string;
      device_size: number[];
    };
    options: {
      id: string;
      name: string;
      device_size: number[];
    }[];
  };
  complication: {
    selected: {
      id: string;
      name: string;
      complication_other: string;
      complication_present: boolean;
    };
    options: {
      id: string;
      name: string;
    }[];
  };
}

interface IinitialState {
  laaoImplantDetails: CaseDetail | null;
  userDetails: CaseDetail | null;
  loading: boolean;
  error: any;
}

export type { IinitialState };
