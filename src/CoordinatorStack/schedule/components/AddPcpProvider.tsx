import React, { useEffect, useState } from "react";
import { View, Text, ScrollView, ActivityIndicator } from "react-native";
import { useDispatch } from "react-redux";
import { AppDispatch } from "../../../store";
import {
  createPcpProvider,
  fetchPcpProviders,
  lookupProviderByNPI,
} from "../../../store/common/addPcpProvider/thunk";
import { useFocusEffect } from "@react-navigation/native";

import {
  usePcpLoaderAndError,
  formatPostPcpProvider,
  usePcpProviderDetails,
  useUserPcpProviderDetails,
  findPostPcpProviderDiff,
  usePcpCredentialOptions,
} from "../hooks/addPcpProviderHooks";
import {
  setProviderDetails,
  resetProviderDetails,
  showAddPcpProviderModal,
  setSelectedProvider,
} from "../../../store/common/addPcpProvider";
import CustomBottomSheetTextInput from "../../../components/CustomBottomSheetTextInput";
import LineSeperator from "../../../components/LineSeperator";
import SaveActionButton from "../../../components/SaveActionButton";
import Loader from "../../../components/Loader";
import {
  parseCharInput,
  parseIntInput,
  parseNPIInput,
  isValidNPI,
} from "../../../utils";
import PopupModal from "../../../components/Popup";
import { fetchCredentialOptions } from "../../../store/common/addPcpProvider/thunk";
import SearchablePicker from "../../../components/SearchablePicker";

interface AddPcpProviderProps {
  onSuccess?: (newProviderId: string) => void;
  onCancel?: () => void;
  bottomSheetRef?: React.RefObject<any>;
}

const AddPcpProvider: React.FC<AddPcpProviderProps> = ({
  onSuccess,
  onCancel,
  bottomSheetRef,
}) => {
  const dispatch = useDispatch<AppDispatch>();
  const [popupMsg, setPopupMsg] = React.useState<string[]>([]);

  const [modalVisible, setModalVisible] = React.useState(false);
  const [isLookingUpNPI, setIsLookingUpNPI] = React.useState(false);
  const [npiLookupMessage, setNpiLookupMessage] = React.useState<string>("");
  const npiLookupTimeoutRef = React.useRef<NodeJS.Timeout | null>(null);
  const credentialOptionsData = usePcpCredentialOptions(); // Added this hook

  const userDetails = useUserPcpProviderDetails();
  const {
    first_name,
    last_name,
    middle_name,
    credential,
    npi_number,
    email_id,
    phone_number,
    fax_number,
    address,
    city,
    state,
    zip_code,
    hospital_system,
  } = formatPostPcpProvider(userDetails);
  const { loader, error } = usePcpLoaderAndError();
  const diff = findPostPcpProviderDiff();
  // Fetch credential options when component mounts
  useEffect(() => {
    dispatch(fetchCredentialOptions());
  }, [dispatch]);

  // Also fetch when screen is focused
  useFocusEffect(
    React.useCallback(() => {
      dispatch(fetchCredentialOptions());
    }, [dispatch])
  );
  const validator = () => {
    setPopupMsg([]);

    var temp = false;
    if (first_name === "" || first_name === null || first_name === undefined) {
      setPopupMsg((prev) => [...prev, "Please Enter First Name"]);
      temp = true;
    }

    if (last_name === "" || last_name === null || last_name === undefined) {
      setPopupMsg((prev) => [...prev, "Please Enter Last Name"]);
      temp = true;
    }

    if (npi_number === "" || npi_number === null || npi_number === undefined) {
      setPopupMsg((prev) => [...prev, "Please Enter NPI Number"]);
      temp = true;
    }

    if (temp) {
      setModalVisible(true);
      return false;
    } else {
      return true;
    }
  };

  const credentialOptions = React.useMemo(() => {
    if (
      Array.isArray(credentialOptionsData) &&
      credentialOptionsData.length > 0
    ) {
      return credentialOptionsData.map((provider) => ({
        label: provider.name,
        value: provider.id,
      }));
    }
    return [];
  }, [credentialOptionsData]);

  // Function to find credential ID by matching the credential text
  const findCredentialIdByText = React.useCallback(
    (credentialText: string) => {
      if (!credentialText || !credentialOptions.length) return null;

      const matchedCredential = credentialOptions.find(
        (option) => option.label.toLowerCase() === credentialText.toLowerCase()
      );

      return matchedCredential ? matchedCredential.value : null;
    },
    [credentialOptions]
  );

  // NPI lookup function with debouncing
  const handleNPILookup = React.useCallback(
    async (npi: string) => {
      if (!isValidNPI(npi)) return;

      // Clear any existing timeout and message
      if (npiLookupTimeoutRef.current) {
        clearTimeout(npiLookupTimeoutRef.current);
      }
      setNpiLookupMessage("");

      // Set a debounced timeout for the lookup
      npiLookupTimeoutRef.current = setTimeout(async () => {
        setIsLookingUpNPI(true);
        try {
          const result = await dispatch(lookupProviderByNPI(npi));

          if (result.payload) {
            const providerData = result.payload;

            // Find the credential ID if credential is provided
            let credentialId = null;
            if (providerData.credential) {
              credentialId = findCredentialIdByText(providerData.credential);
            }

            // Auto-populate all fields
            dispatch(
              setProviderDetails({
                first_name: providerData.first_name || "",
                last_name: providerData.last_name || "",
                middle_name: providerData.middle_name || "",
                credential: credentialId || "",
                npi_number: providerData.npi_number || npi,
                email_id: providerData.email_id || "",
                phone_number: providerData.phone_number || "",
                fax_number: providerData.fax_number || "",
                address: providerData.address || "",
                city: providerData.city || "",
                state: providerData.state || "",
                zip_code: providerData.zip_code || "",
                hospital_system: providerData.hospital_system || "",
              })
            );
            setNpiLookupMessage(""); // Clear any previous message
          } else {
            // Handle case where result.payload is null/undefined
            setNpiLookupMessage("No provider found");
          }
        } catch (error: any) {
          console.error("NPI lookup failed:", error);

          // Check if it's a "Not Found" error
          if (
            error?.message?.includes("No provider found") ||
            error?.response?.data?.error === "Not Found"
          ) {
            setNpiLookupMessage("No provider found");
          } else {
            // For other errors, don't show message, just let them continue manually
            setNpiLookupMessage("");
          }
        } finally {
          setIsLookingUpNPI(false);
        }
      }, 500); // 500ms debounce
    },
    [dispatch, findCredentialIdByText]
  );

  // Cleanup timeout on unmount
  React.useEffect(() => {
    return () => {
      if (npiLookupTimeoutRef.current) {
        clearTimeout(npiLookupTimeoutRef.current);
      }
    };
  }, []);

  const saveDetails = async () => {
    try {
      if (validator()) {
        const res = await dispatch(createPcpProvider(diff?.currentValues));

        if (res?.payload) {
          // Get the newly created provider ID and data
          const newProviderId = res.payload.id;

          // Construct complete provider object to send back
          // const providerData = {
          //   id: newProviderId,
          //   name: `${first_name || ""} ${middle_name ? middle_name + " " : ""}${
          //     last_name || ""
          //   }`,
          // };
          // Set the selected provider in the store
          dispatch(setSelectedProvider(newProviderId));

          // Let the parent component handle setting the value in state
          onSuccess?.(newProviderId);
          dispatch(fetchPcpProviders());
          dispatch(showAddPcpProviderModal(false));
          // Close the bottom sheet after successful save
          bottomSheetRef?.current?.close();
        }
      }
    } catch (err) {
      console.error(err);
    }
  };

  const handleCancel = () => {
    dispatch(resetProviderDetails());
    dispatch(showAddPcpProviderModal(false));
    // Call the parent's onCancel function to close the bottom sheet
    onCancel?.();
  };

  return (
    <>
      {loader ? (
        <View className="mt-64">
          <Loader />
        </View>
      ) : (
        <View className="p-3 bg-primaryWhite">
          <View className="rounded-md p-3">
            <Text className="text-primaryPurple text-center font-semibold text-lg mb-4">
              Add PCP Provider
            </Text>

            {error && (
              <View className="bg-red-100 p-3 rounded-md mb-4">
                <Text className="text-red-700">{error}</Text>
              </View>
            )}

            <View className="flex-row justify-center gap-4">
              <SaveActionButton
                disabled={!diff}
                onPress={saveDetails}
                onCancel={handleCancel}
              />
            </View>
            <LineSeperator extraStyle="my-5" />

            <View className="flex-row justify-between items-center">
              <Text className="text-primaryBlack font-medium w-[40%]">
                NPI Number <Text className="text-red-3 text-lg">*</Text>
                {isLookingUpNPI && (
                  <Text className="text-blue-500 text-xs ml-1">
                    Looking up...
                  </Text>
                )}
                {npiLookupMessage && (
                  <Text className="text-red-500 text-xs ml-1">
                    {npiLookupMessage}
                  </Text>
                )}
              </Text>
              <View className="flex-1">
                <CustomBottomSheetTextInput
                  inputValue={npi_number}
                  placeholder="NPI Number (10 digits)"
                  onInputChange={(value) => {
                    parseNPIInput(
                      value,
                      (parsedValue) => {
                        dispatch(
                          setProviderDetails({
                            npi_number: parsedValue,
                          })
                        );
                      },
                      handleNPILookup
                    );
                  }}
                  keyboardType="numeric"
                />
              </View>
            </View>

            <LineSeperator extraStyle="my-5" />

            <View className="flex-row justify-between items-center">
              <Text className="text-primaryBlack font-medium w-[40%]">
                First Name <Text className="text-red-3 text-lg">*</Text>
              </Text>
              <View className="flex-1">
                <CustomBottomSheetTextInput
                  inputValue={first_name}
                  placeholder="First Name"
                  onInputChange={(value) => {
                    parseCharInput(value, (parsedValue) => {
                      dispatch(
                        setProviderDetails({
                          first_name: parsedValue,
                        })
                      );
                    });
                  }}
                />
              </View>
            </View>

            <LineSeperator extraStyle="my-5" />

            <View className="flex-row justify-between items-center">
              <Text className="text-primaryBlack font-medium w-[40%]">
                Last Name <Text className="text-red-3 text-lg">*</Text>
              </Text>
              <View className="flex-1">
                <CustomBottomSheetTextInput
                  inputValue={last_name}
                  placeholder="Last Name"
                  onInputChange={(value) => {
                    parseCharInput(value, (parsedValue) => {
                      dispatch(
                        setProviderDetails({
                          last_name: parsedValue,
                        })
                      );
                    });
                  }}
                />
              </View>
            </View>

            <LineSeperator extraStyle="my-5" />

            <View className="flex-row justify-between items-center">
              <Text className="text-primaryBlack font-medium w-[40%]">
                Middle Name
              </Text>
              <View className="flex-1">
                <CustomBottomSheetTextInput
                  inputValue={middle_name}
                  placeholder="Middle Name"
                  onInputChange={(value) => {
                    parseCharInput(value, (parsedValue) => {
                      dispatch(
                        setProviderDetails({
                          middle_name: parsedValue,
                        })
                      );
                    });
                  }}
                />
              </View>
            </View>

            <LineSeperator extraStyle="my-5" />

            <View className="flex-row justify-between items-center">
              <Text className="text-primaryBlack font-medium w-[40%]">
                Credential
              </Text>
              <View className="flex-1">
                <SearchablePicker
                  value={credential}
                  placeholder="Select Credential"
                  items={credentialOptions.map((option) => {
                    return {
                      label: option.label,
                      value: option.value,
                    };
                  })}
                  onValueChange={(selectedItem) => {
                    dispatch(
                      setProviderDetails({
                        credential: selectedItem.value,
                      })
                    );
                  }}
                  floatingLabel={false}
                  disable={true}
                  error={false}
                  disableError
                />
              </View>
            </View>

            <LineSeperator extraStyle="my-5" />

            <View className="flex-row justify-between items-center">
              <Text className="text-primaryBlack font-medium w-[40%]">
                Email ID
              </Text>
              <View className="flex-1">
                <CustomBottomSheetTextInput
                  inputValue={email_id}
                  placeholder="Email ID"
                  onInputChange={(value) => {
                    dispatch(
                      setProviderDetails({
                        email_id: value,
                      })
                    );
                  }}
                  keyboardType="email-address"
                />
              </View>
            </View>

            <LineSeperator extraStyle="my-5" />

            <View className="flex-row justify-between items-center">
              <Text className="text-primaryBlack font-medium w-[40%]">
                Phone Number
              </Text>
              <View className="flex-1">
                <CustomBottomSheetTextInput
                  inputValue={phone_number}
                  placeholder="Phone Number"
                  onInputChange={(value) => {
                    dispatch(
                      setProviderDetails({
                        phone_number: value,
                      })
                    );
                  }}
                  keyboardType="phone-pad"
                />
              </View>
            </View>

            <LineSeperator extraStyle="my-5" />

            <View className="flex-row justify-between items-center">
              <Text className="text-primaryBlack font-medium w-[40%]">
                Fax Number
              </Text>
              <View className="flex-1">
                <CustomBottomSheetTextInput
                  inputValue={fax_number}
                  placeholder="Fax Number"
                  onInputChange={(value) => {
                    dispatch(
                      setProviderDetails({
                        fax_number: value,
                      })
                    );
                  }}
                  keyboardType="phone-pad"
                />
              </View>
            </View>

            <LineSeperator extraStyle="my-5" />

            <View className="flex-row justify-between items-center">
              <Text className="text-primaryBlack font-medium w-[40%]">
                Address
              </Text>
              <View className="flex-1">
                <CustomBottomSheetTextInput
                  inputValue={address}
                  placeholder="Address"
                  onInputChange={(value) => {
                    dispatch(
                      setProviderDetails({
                        address: value,
                      })
                    );
                  }}
                />
              </View>
            </View>

            <LineSeperator extraStyle="my-5" />

            <View className="flex-row justify-between items-center">
              <Text className="text-primaryBlack font-medium w-[40%]">
                City
              </Text>
              <View className="flex-1">
                <CustomBottomSheetTextInput
                  inputValue={city}
                  placeholder="City"
                  onInputChange={(value) => {
                    parseCharInput(value, (parsedValue) => {
                      dispatch(
                        setProviderDetails({
                          city: parsedValue,
                        })
                      );
                    });
                  }}
                />
              </View>
            </View>

            <LineSeperator extraStyle="my-5" />

            <View className="flex-row justify-between items-center">
              <Text className="text-primaryBlack font-medium w-[40%]">
                State
              </Text>
              <View className="flex-1">
                <CustomBottomSheetTextInput
                  inputValue={state}
                  placeholder="State"
                  onInputChange={(value) => {
                    parseCharInput(value, (parsedValue) => {
                      dispatch(
                        setProviderDetails({
                          state: parsedValue,
                        })
                      );
                    });
                  }}
                />
              </View>
            </View>

            <LineSeperator extraStyle="my-5" />

            <View className="flex-row justify-between items-center">
              <Text className="text-primaryBlack font-medium w-[40%]">
                Zip Code
              </Text>
              <View className="flex-1">
                <CustomBottomSheetTextInput
                  inputValue={zip_code?.toString()}
                  placeholder="Zip Code"
                  onInputChange={(value) => {
                    parseIntInput(value, (parsedValue) => {
                      dispatch(
                        setProviderDetails({
                          zip_code: parsedValue,
                        })
                      );
                    });
                  }}
                  keyboardType="numeric"
                />
              </View>
            </View>

            <LineSeperator extraStyle="my-5" />

            <View className="flex-row justify-between items-center">
              <Text className="text-primaryBlack font-medium w-[40%]">
                Hospital System
              </Text>
              <View className="flex-1">
                <CustomBottomSheetTextInput
                  inputValue={hospital_system}
                  placeholder="Hospital System"
                  onInputChange={(value) => {
                    dispatch(
                      setProviderDetails({
                        hospital_system: value,
                      })
                    );
                  }}
                />
              </View>
            </View>

            <LineSeperator extraStyle="my-5" />

            {/* <View className="flex-row justify-center gap-4">
              <SaveActionButton
                disabled={!diff.hasDiff}
                onPress={saveDetails}
                onCancel={handleCancel}
              />
            </View> */}
          </View>
        </View>
      )}
      <PopupModal
        show={modalVisible}
        msg={popupMsg}
        status="warning"
        onClose={() => setModalVisible(false)}
      />
    </>
  );
};

export default AddPcpProvider;
