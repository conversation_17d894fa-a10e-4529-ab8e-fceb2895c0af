import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import initialState from "./initialstate";

const chatBotSlice = createSlice({
  name: "chatbot",
  initialState,
  reducers: {
    setChatBotState: (state, action: PayloadAction<any>) => {
      state.screenPath = action.payload.screenPath;
      state.currentScreen = action.payload.currentScreen;
      state.isOpened = action.payload.isOpened;
    },
    setPatientDetails: (state, action: PayloadAction<any>) => {
      state.caseId = action.payload.caseId;
      state.patientName = action.payload.patientName;
    },
    setPatientName: (state, action: PayloadAction<any>) => {
      state.patientName = action.payload.patientName;
    },
  },
  extraReducers: (builder) => {},
});

export const { setChatBotState, setPatientDetails, setPatientName } =
  chatBotSlice.actions;

export {};

export default chatBotSlice.reducer;
