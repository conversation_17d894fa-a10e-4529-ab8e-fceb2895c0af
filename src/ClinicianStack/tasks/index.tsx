import * as React from "react";
import { createStackNavigator } from "@react-navigation/stack";
import Tasks from "./screens/Tasks";

interface ITasksScreenStackProps {}

const TasksScreenStack: React.FunctionComponent<ITasksScreenStackProps> = (
  props
) => {
  const TasksStack = createStackNavigator();

  return (
    <TasksStack.Navigator>
      <TasksStack.Screen name="Tasks" component={Tasks} />
    </TasksStack.Navigator>
  );
};

export default TasksScreenStack;
