// thunk.ts
import { createAsyncThunk } from "@reduxjs/toolkit";
import api from "../../../api/api";

const fetchDashboardDetails = createAsyncThunk(
  "tasks/fetchDashboardDetails",
  async (_, thunkAPI) => {
    try {
      const response = await api.private.get(
        api.routes.privateRoutes.common.dashboard_get
      );

      if (response.data && response.data.result) {
        return response.data.result[0];
      } else {
        return thunkAPI.rejectWithValue(
          'Invalid response format: Missing "result" field'
        );
      }
    } catch (error: any) {
      if (error.response) {
        if (error.response.status === 404) {
        }
        return thunkAPI.rejectWithValue("Failed to fetch Dashboard Details");
      } else if (error.request) {
        console.error("No response received:", error.request);
        return thunkAPI.rejectWithValue("No response received from the server");
      } else {
        console.error("Error occurred:", error.message);
        return thunkAPI.rejectWithValue(
          error.message || "An unknown error occurred"
        );
      }
    }
  }
);

export { fetchDashboardDetails };
