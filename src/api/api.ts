// api.ts
import privateApiService from "./private/privateApi";
import publicApiService from "./public/publicApi";
import privateRoutes from "./private/routes";
import publicRoutes from "./public/routes";

const routes = {
  patientsList: (siteId: number, caseDate: string) =>
    `/api/mobile/schedule/sites/${siteId}/cases/${caseDate}`,
};

class ApiService {
  public private = privateApiService;
  public public = publicApiService;
  public routes = {
    publicRoutes: publicRoutes,
    privateRoutes: privateRoutes,
  };
}

export default new ApiService();
