// types.ts
interface ITask {
  task_id: string;
  assigner: {
    id: string;
    name: string;
  };
  due_date: string;
  due_time: string;
  assigned_date: string;
  assigned_time: string;
  patient: {
    id: string;
    name: string;
    age: number;
  };
  task_type: {
    id: string;
    name: string;
    sub_type: {
      id: string;
      name: string;
    };
  };
  auto_complete: boolean;
  task_status: string;
}

export type { ITask };
