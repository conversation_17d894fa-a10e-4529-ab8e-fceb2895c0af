import { createAsyncThunk } from "@reduxjs/toolkit";
import api from "../../../../api/api";

const fetchCoordinatorSchedules = createAsyncThunk(
  "coordinator/schedule/fetchCoordinatorSchedules",
  async (
    {
      scheduleType,
      case_date,
      to_date,
    }: {
      scheduleType: string;
      case_date: string;
      to_date?: string;
    },
    thunkAPI
  ) => {
    try {
      const params: Record<string, string> = {
        type: scheduleType,
        case_date,
      };

      if (to_date && scheduleType === "completed") {
        params["completed_start_date"] = case_date;
        params["completed_end_date"] = to_date;
      }

      if (to_date && scheduleType === "procedure") {
        params["to_date"] = to_date;
      }

      const response = await api.private.get(
        api.routes.privateRoutes.coordinator.schedule,
        {
          params,
        }
      );

      if (response.data && response.data.result) {
        return response.data.result;
      } else {
        return thunkAPI.rejectWithValue(
          'Invalid response format: Missing "result" field'
        );
      }
    } catch (error: any) {
      console.error(
        "Error caught during API call:",
        error.response.data || error
      );
      return thunkAPI.rejectWithValue(
        error.response?.data?.message || "Failed to fetch coordinator schedules"
      );
    }
  }
);

const putChadScoreDetails = createAsyncThunk(
  "tasks/putChadScoreDetails",
  async ({ case_id, payload }: { case_id: string; payload: any }, thunkAPI) => {
    try {
      const response = await api.private.put(
        api.routes.privateRoutes.coordinator.chad_score_put(case_id),
        payload
      );

      if (response.data && response.data.result) {
        return response.data.result;
      } else {
        return thunkAPI.rejectWithValue(
          'Invalid response format: Missing "result" field'
        );
      }
    } catch (error: any) {
      if (error.response) {
        console.error("Error updating CHAD Score:", error.response.data);
        return thunkAPI.rejectWithValue(
          error.response.data.error_description || "Failed to update CHAD Score"
        );
      } else if (error.request) {
        console.error("No response received:", error.request);
        return thunkAPI.rejectWithValue("No response received from the server");
      } else {
        console.error("Error occurred:", error.message);
        return thunkAPI.rejectWithValue(
          error.message || "An unknown error occurred"
        );
      }
    }
  }
);
const putHasbledScoreDetails = createAsyncThunk(
  "tasks/putHasbledScoreDetails",
  async ({ case_id, payload }: { case_id: string; payload: any }, thunkAPI) => {
    try {
      const response = await api.private.put(
        api.routes.privateRoutes.coordinator.hasbled_score_put(case_id),
        payload
      );

      if (response.data && response.data.result) {
        return response.data.result;
      } else {
        return thunkAPI.rejectWithValue(
          'Invalid response format: Missing "result" field'
        );
      }
    } catch (error: any) {
      if (error.response) {
        console.error("Error updating HASBLED Score:", error.response.data);
        return thunkAPI.rejectWithValue(
          error.response.data.error_description ||
            "Failed to update HASBLED Score"
        );
      } else if (error.request) {
        console.error("No response received:", error.request);
        return thunkAPI.rejectWithValue("No response received from the server");
      } else {
        console.error("Error occurred:", error.message);
        return thunkAPI.rejectWithValue(
          error.message || "An unknown error occurred"
        );
      }
    }
  }
);

export {
  fetchCoordinatorSchedules,
  putChadScoreDetails,
  putHasbledScoreDetails,
};
