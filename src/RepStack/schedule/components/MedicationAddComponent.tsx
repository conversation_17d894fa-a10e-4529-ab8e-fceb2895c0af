import React, { useEffect, useState } from "react";
import { View, Text, TouchableOpacity } from "react-native";
import SearchablePicker from "../../../components/SearchablePicker";
import { MatIcon } from "../../../utils";

const MedicationPlan = ({
  apiData,
  selected,
  onMedicationChange,
  onQuantityChange,
  onAddMedication,
  onDeleteMedication,
}) => {
  const [localSelected, setLocalSelected] = useState([]);

  // Update localSelected when selected is updated asynchronously
  useEffect(() => {
    setLocalSelected(selected);
  }, [selected]);

  const getPickerItems = () => {
    return apiData.map((item) => ({
      label: item.name,
      value: item.value,
    }));
  };

  return (
    <View className="p-2 bg-primaryBg">
      {localSelected.map((item, index) => {
        const selectedMed = apiData.find((med) => med.value === item.value);

        return (
          <View
            key={index}
            style={{
              flexDirection: "row",
              alignItems: "center",
              marginBottom: 8,
              justifyContent: "space-between",
            }}
          >
            {/* Medication Picker */}
            <View style={{ flex: 2, marginHorizontal: 4 }}>
              <SearchablePicker
                placeholder="Select Drug"
                items={getPickerItems()}
                value={item.value}
                onValueChange={(value) => onMedicationChange(index, value)}
              />
            </View>

            {/* Quantity Picker */}
            <View style={{ flex: 2, marginHorizontal: 4 }}>
              <SearchablePicker
                placeholder="Dose"
                items={
                  selectedMed?.quantity.map((qty) => ({
                    label: String(qty),
                    value: String(qty),
                  })) || []
                }
                value={item?.quantity?.toString()}
                onValueChange={(value) => onQuantityChange(index, value.value)}
              />
            </View>

            {/* Display Metric */}
            <View
              style={{
                flex: 1,
                marginHorizontal: 4,
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              <Text style={{ color: "black" }}>
                {selectedMed?.metric || ""}
              </Text>
            </View>

            {/* Delete Button */}
            <View
              style={{
                alignItems: "center",
                justifyContent: "center",
              }}
            >
              <TouchableOpacity onPress={() => onDeleteMedication(index)}>
                {MatIcon("trash-can-outline", "red", 26)}
              </TouchableOpacity>
            </View>
          </View>
        );
      })}
    </View>
  );
};

export default MedicationPlan;
