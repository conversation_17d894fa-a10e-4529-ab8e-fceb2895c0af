import { Middleware } from "@reduxjs/toolkit";
import { showToaster } from "./toaster";

const ErrorHandler: Middleware = (storeAPI) => {
  return (next) => (action) => {
    if (
      action.type.toLowerCase().includes("chad") ||
      action.type.toLowerCase().includes("hasbled") ||
      action.type.toLowerCase().includes("putpostsite") ||
      action.type.toLowerCase().includes("putpostimplantingphysician") ||
      action.type.toLowerCase().includes("putpostpatientdetails") ||
      action.type.toLowerCase().includes("putpostreferringprovider") ||
      (action.type.toLowerCase().includes("postpcpprovider") &&
        action.type.endsWith("/fulfilled"))
    ) {
      storeAPI.dispatch(
        showToaster({
          show: true,
          message: "Saved Successfully",
          icon: "success",
          // duration: 1000,
        })
      );
    }

    if (action.type.includes("put") && action.type.endsWith("/rejected")) {
      storeAPI.dispatch(
        showToaster({
          show: true,
          message: "failed to save",
          icon: "error",
          // duration: 5000
        })
      );
    }

    if (action.type.endsWith("/rejected")) {
      storeAPI.dispatch(
        showToaster({
          show: true,
          message: action.payload ? action.payload : "",
          icon: "error",
          // duration: 5000,
        })
      );
    }
    return next(action);
  };
};

export default ErrorHandler;
