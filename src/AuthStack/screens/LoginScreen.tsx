import React from "react";
import {
  Image,
  Text,
  TouchableOpacity,
  View,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
  SafeAreaView,
} from "react-native";
import CustomInput from "../../components/CustomTextInput";
import Heading from "../../components/Heading";
import { login } from "../../store/auth";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "../../store";
import CormetrixLogo from "../../../assests/logo.png";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import globalStyles from "../../styles/GlobalStyles";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { loginSuccess } from "../../store/auth";
import {
  authorize,
  AuthorizeResult,
  AuthConfiguration,
} from "react-native-app-auth";
import { authUrl, Enviromnent } from "../../api/config";
import { getAppVersion } from "../../utils/appVersion";

interface ILoginScreenProps {}

const config: AuthConfiguration = {
  issuer: authUrl[Enviromnent],
  clientId: "cormetrix-api",
  redirectUrl: "atriai://login",
  scopes: ["openid", "profile", "email", "offline_access"],
  additionalParameters: {
    prompt: "login",
    authorizationEndpoint: `${authUrl[Enviromnent]}/protocol/openid-connect/auth`,
    tokenEndpoint: `${authUrl[Enviromnent]}/protocol/openid-connect/token`,
    revocationEndpoint: `${authUrl[Enviromnent]}/protocol/openid-connect/logout`,
  },
};

const LoginScreen: React.FunctionComponent<ILoginScreenProps> = () => {
  const [email, setEmail] = React.useState("");
  const [password, setPassword] = React.useState("");
  const [loader, setLoader] = React.useState(false);

  const loginError = useSelector((state: RootState) => state.auth.errorStatus);
  const dispatch = useDispatch<AppDispatch>();

  const loginInit = async () => {
    setLoader(true);
    try {
      const res = await authorize(config);
      if (res && res.accessToken) {
        await AsyncStorage.setItem("accessToken", res?.accessToken);
        await AsyncStorage.setItem("refreshToken", res?.refreshToken);
        dispatch(loginSuccess());
      } else {
        console.error("no access token");
      }
    } catch (err) {
      console.error("login screen error", err);
    } finally {
      setLoader(false);
    }
  };

  if (loader) {
    return (
      <View className="flex-1 justify-center items-center bg-white">
        <ActivityIndicator size="large" color="#8143d9" />
      </View>
    );
  }

  return (
    <SafeAreaView className="flex-1 bg-white">
      <KeyboardAwareScrollView>
        <View className="mt-[50%] flex justify-between px-5 py-5">
          {/* Logo */}
          <View className="flex w-full h-36 mx-auto justify-center items-center">
            <Image source={CormetrixLogo} className="w-full h-full" />
          </View>

          <View className={`${globalStyles.containers.flex_center} py-12`}>
            <Heading
              text="Sign in to your account"
              size="sub-heading"
              showSeperator={false}
              extraStyle="text-center mb-4"
            />
          </View>
          {/*
          <View className="mt-24">
            <Heading text="User Name" size="label" showSeperator={false} />
            <CustomInput
              inputValue={email}
              onInputChange={setEmail}
              placeholder="Enter your user name"
            />
            <Heading
              text="Password"
              size="label"
              showSeperator={false}
              extraStyle="mt-4"
            />
            <CustomInput
              inputValue={password}
              onInputChange={setPassword}
              placeholder="Enter your password"
              secureTextEntry={true}
            />
            <TouchableOpacity className="mt-2">
              <Text className="text-primaryPurple text-sm font-medium text-left">
                Forgot Password?
              </Text>
            </TouchableOpacity>
            <View className="flex-1 justify-center"></View>
          </View> */}

          <View className="flex-1 justify-center items-center mt-5">
            <TouchableOpacity
              className="bg-primaryPurple rounded-lg px-4 py-2 w-[30%]"
              onPress={loginInit}
            >
              <Text className="text-primaryWhite text-center font-bold text-base">
                Login
              </Text>
            </TouchableOpacity>
          </View>

          {/* App Version */}
          <View className="mt-4">
            <Text className="text-primaryPurple text-center text-sm">
              App Version: {getAppVersion()}
            </Text>
          </View>

          {loginError && (
            <View className="mt-4">
              <Text className="text-red-3 text-center text-sm">
                Error during login
              </Text>
            </View>
          )}
        </View>
      </KeyboardAwareScrollView>
    </SafeAreaView>
  );
};

export default LoginScreen;
