import { useSelector } from "react-redux";
import { RootState } from "../../../store";
import { PcpProvider } from "../../../store/common/addPcpProvider/types";

// Loader and Error
const usePcpLoaderAndError = () => {
  const loader = useSelector(
    (state: RootState) => state.common.pcpProviders.loading
  );

  const error = useSelector(
    (state: RootState) => state.common.pcpProviders.error
  );
  return { loader, error };
};

// Provider details from API
const usePcpProviderDetails = (): PcpProvider | null => {
  return useSelector(
    (state: RootState) => state.common.pcpProviders.providerDetails
  );
};

// Modified/Editable form values
const useUserPcpProviderDetails = (): PcpProvider | null => {
  return useSelector(
    (state: RootState) => state.common.pcpProviders.userDetails
  );
};

// Active tab
const useActiveTab = (): "referring-providers" | "pcp-providers" => {
  return useSelector((state: RootState) => state.common.pcpProviders.activeTab);
};

// Modal state
const useAddPcpProviderModalState = (): boolean => {
  return useSelector(
    (state: RootState) => state.common.pcpProviders.showAddPcpProviderModal
  );
};

// Selected provider
const useSelectedPcpProvider = (): string => {
  return useSelector(
    (state: RootState) => state.common.pcpProviders.selectedProvider
  );
};

// Format data for POST API
const formatPostPcpProvider = (data: PcpProvider) => {
  return {
    provider_name: `${data.first_name} ${data.middle_name || ""} ${
      data.last_name
    }`.trim(),
    provider_credential: data.credential || "",
    provider_email: data.email_id || "",
    provider_phone: data.phone_number || "",
    provider_npi: data.npi_number || "",
    provider_fax: data.fax_number || "",
    provider_address: data.address || "",
    provider_city: data.city || "",
    provider_state: data.state || "",
    provider_zip: data.zip_code || "",
    hospital_system: data.hospital_system || "",
  };
};

// Diff check between form & original
const findPostPcpProviderDiff = () => {
  const userDetails = useUserPcpProviderDetails();
  const providerDetails = usePcpProviderDetails();

  const hasDiff =
    JSON.stringify(userDetails) !== JSON.stringify(providerDetails);

  const currentValues = {
    first_name: userDetails?.first_name || "",
    middle_name: userDetails?.middle_name || "",
    last_name: userDetails?.last_name || "",
    credential: userDetails?.credential || "",
    email_id: userDetails?.email_id || "",
    phone_number: userDetails?.phone_number || "",
    fax_number: userDetails?.fax_number || "",
    npi_number: userDetails?.npi_number || "",
    address: userDetails?.address || "",
    city: userDetails?.city || "",
    state: userDetails?.state || "",
    zip_code: userDetails?.zip_code || "",
    hospital_system: userDetails?.hospital_system || "",
  };

  return {
    hasDiff,
    currentValues,
  };
};

const usePcpCredentialOptions = (): string[] => {
  return useSelector(
    (state: RootState) => state.common.pcpProviders.credentialOptions
  );
};

export {
  useActiveTab,
  usePcpLoaderAndError,
  useAddPcpProviderModalState,
  usePcpProviderDetails,
  useUserPcpProviderDetails,
  useSelectedPcpProvider,
  formatPostPcpProvider,
  findPostPcpProviderDiff,
};
