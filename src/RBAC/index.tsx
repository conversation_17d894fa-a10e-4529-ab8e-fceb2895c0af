import * as React from "react";
import { useSelector, useDispatch } from "react-redux";
import { AppDispatch } from "../store";
import AuthStack from "../AuthStack";
import { RootState } from "../store";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { loginSuccess, loginFailure, logout } from "../store/auth";
import { ActivityIndicator, Text, View, Alert } from "react-native";
import RepStack from "../RepStack";
import ClinicianStack from "../ClinicianStack";
import AbstractorStack from "../AbstractorStack";
import { jwtDecode } from "jwt-decode";
import CoordinatorStack from "../CoordinatorStack";
import CustomToaster from "../components/CustomToaster";
import {
  useNavigationContainerRef,
  NavigationContainerRef,
  NavigationContainer,
} from "@react-navigation/native";
import { setChatBotState } from "../store/chatbot";
import { fetchDashboardDetails } from "../store/common/dashboard/thunk";
import { setToken } from "../store/common/dashboard";
import Loader from "../components/Loader";

interface IRBACProps {}

interface CustomJwtPayload {
  realm_access?: {
    roles: string[];
  };
}

const RBAC: React.FunctionComponent<IRBACProps> = (props) => {
  const [stack, setStack] = React.useState<React.ReactNode>(null);
  const [loading, setLoading] = React.useState(true);
  const isLoggedIn = useSelector(
    (state: RootState) => state.auth.isAuthenticated
  );
  const dispatch = useDispatch<AppDispatch>();
  const navigationRef = useNavigationContainerRef<NavigationContainerRef>();
  const data = useSelector((state: RootState) => state.toaster);
  const { duration, show, message, icon } = data;

  React.useEffect(() => {
    const checkAccessToken = async () => {
      try {
        if (isLoggedIn) {
          setStack(<Loader color="#FFFFFF" />);
        }

        const accessToken = await AsyncStorage.getItem("accessToken");

        if (accessToken !== null) {
          const roles =
            jwtDecode<CustomJwtPayload>(accessToken)?.realm_access?.roles;

          dispatch(fetchDashboardDetails());
          dispatch(setToken(accessToken));

          if (roles?.includes("REP") || roles?.includes("REP_ADVISOR")) {
            dispatch(loginSuccess());
            const role = roles.includes("REP_ADVISOR") ? "REP_ADVISOR" : "REP";
            setStack(<RepStack role={role} />);
          } else if (roles?.includes("CLINICAL_COORDINATOR")) {
            dispatch(loginSuccess());
            setStack(<CoordinatorStack />);
          } else if (roles?.includes("CLINICIAN")) {
            dispatch(loginSuccess());
            setStack(<ClinicianStack />);
          } else {
            // Handle unauthorized access
            dispatch(logout());
            setStack(<AuthStack />);
            Alert.alert(
              "Unauthorized Access",
              "Please log in with an authorized account."
            );
          }
        } else {
          setStack(<AuthStack />);
        }
      } catch (error) {
        console.error("Failed to retrieve access token:", error);
        setStack(<AuthStack />);
      } finally {
        setLoading(false);
      }
    };

    checkAccessToken();
  }, [dispatch, isLoggedIn]);

  // React.useEffect(() => {
  //   const listener = navigationRef.addListener("state", () => {
  //     const routes = navigationRef.getCurrentRoute();
  //     const currentScreen = routes?.name || "Unknown";

  //     dispatch(
  //       setChatBotState({
  //         currentScreen,
  //         isOpened: false,
  //       })
  //     );
  //   });

  //   return () => {
  //     navigationRef.removeListener("state", listener);
  //   };
  // }, [navigationRef, dispatch]);

  React.useEffect(() => {
    const listener = navigationRef.addListener("state", () => {
      const getFullPath = (route) => {
        if (!route) return "Unknown";

        const { name, state } = route;
        if (state?.routes?.length > 0) {
          // Recursively find the nested route
          const nestedRoute = state.routes[state.index || 0];
          return `${name}/${getFullPath(nestedRoute)}`;
        }

        return name; // Return the name of the current route
      };

      const routes = navigationRef.getRootState();
      const currentRoute = routes?.routes[routes.index || 0];
      const screenPath = getFullPath(currentRoute);

      const currenScreenRef = navigationRef.getCurrentRoute();
      const currentScreen = currenScreenRef?.name || "Unknown";

      dispatch(
        setChatBotState({
          screenPath,
          currentScreen,
          isOpened: false,
        })
      );
    });

    return () => {
      navigationRef.removeListener("state", listener);
    };
  }, [navigationRef, dispatch]);

  const linking = {
    prefixes: ["atriai://"],
    config: {
      screens: {
        login: "LoginScreen",
      },
    },
  };

  if (loading) {
    return (
      <View style={{ flex: 1, justifyContent: "center", alignItems: "center" }}>
        <ActivityIndicator size="large" color="#0000ff" />
      </View>
    );
  }

  return (
    <>
      <CustomToaster
        show={show}
        message={message}
        duration={duration}
        icon={icon}
      />
      <NavigationContainer
        linking={linking}
        ref={navigationRef}
        fallback={<Text>Loading...</Text>}
      >
        {stack}
      </NavigationContainer>
    </>
  );
};

export default RBAC;
