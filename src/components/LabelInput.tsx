import * as React from 'react';
import {View, Text, TextInput} from 'react-native';

interface ILabelInputProps {
  label: string;
  value: string;
  editable: boolean;
  isFullWidth?: boolean;
}

const LabelInput: React.FunctionComponent<ILabelInputProps> = props => {
  return (
    <View
      style={{
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'space-between',
        marginVertical: 5,
        width: props.isFullWidth ? '100%' : '48%',
      }}>
      <Text style={{flex: 1}}>{props.label}</Text>
      <TextInput
        value={props.value}
        editable={props.editable}
        style={{
          borderBottomWidth: 1,
          borderColor: 'black',
          flex: 2,
        }}
      />
    </View>
  );
};

export default LabelInput;
