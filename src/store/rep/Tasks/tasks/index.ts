import { createSlice } from "@reduxjs/toolkit";
import initialState from "./initialstate";
import { fetchRepTasks } from "./thunk";

const taskSlice = createSlice({
  name: "tasks",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchRepTasks.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchRepTasks.fulfilled, (state, action) => {
        state.loading = false;
        state.tasks = action.payload;
      })
      .addCase(fetchRepTasks.rejected, (state, action) => {
        state.loading = false;
        state.tasks = null;
        state.error = action.payload as string;
      });
  },
});

export default taskSlice.reducer;
