import { useSelector } from "react-redux";
import { RootState } from "../../../store";

const useRepTasks = () => {
  const { tasks } = useSelector((state: RootState) => state.rep.tasks);
  return tasks;
};

const useLoaderAndError = () => {
  const loader = useSelector((state: RootState) => state.rep.tasks.loading);
  const error = useSelector((state: RootState) => state.rep.tasks.error);
  return { loader, error };
};

export { useRepTasks, useLoaderAndError };
