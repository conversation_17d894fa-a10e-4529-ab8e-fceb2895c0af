import React, { useState, useRef } from "react";
import { useNavigation } from "@react-navigation/native";
import {
  View,
  Text,
  Image,
  TouchableOpacity,
  StyleSheet,
  Button,
  FlatList,
  Platform,
} from "react-native";
import { useFocusEffect } from "@react-navigation/native";
import Heading from "../../../components/Heading";
import ScreenWrapper from "../../../components/ScreenWrapper";
import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons";
import moment from "moment";
import ToggleButton from "../../../components/ToggleButton";
import CustomInput from "../../../components/CustomTextInput";
import CustomCard from "../../../components/CustomCard";
import LineSeperator from "../../../components/LineSeperator";
import DatePicker from "react-native-date-picker";
import SearchablePicker from "../../../components/SearchablePicker";
import CustomText from "../../../components/CustomText";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";
import { parseIntInput } from "../../../utils";
import SaveActionButton from "../../../components/SaveActionButton";
import {
  useLoaderAndError,
  usePostOpUserDetails,
  usePostOpDetails,
  findPostOpDiff,
  formatPostOpData,
  useMedicationData,
} from "../hooks/postopHooks";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../../store";
import {
  fetchPostOpDetails,
  putPostOpDetails,
} from "../../../store/rep/ScheduleStack/postop/thunk";
import { AppDispatch } from "../../../store";
import { useSelectedPatient } from "../hooks/PatientsHooks";
import { get } from "react-native/Libraries/TurboModule/TurboModuleRegistry";
import { setPostOpDetails } from "../../../store/rep/ScheduleStack/postop";
import Loader from "../../../components/Loader";
import Error from "../../../components/Error";
import OptionSelector from "../../../components/OptionSelector";
import { Linking } from "react-native";
import MedicationPlan from "../components/MedicationAddComponent";
import SaveActionV2 from "../../../components/SaveActionButtonV2";
import { Dimensions } from "react-native";
import BottomSheetComponent, {
  BottomSheetRefProps,
} from "../../../components/BottomSheetComponent";
import CHADCard from "../../../components/CHADCard";
import MedicationBottomSheetComponent from "../../components/MedicationBottomSheetComponent";
import {
  deleteMedicationById,
  resetMedication,
} from "../../../store/rep/ScheduleStack/postop";
import PopupModal from "../../../components/Popup";
import { simpleAutoSave } from "../../../services/simpleAutoSave";
import { setSaveStatus, setLastSavedData } from "../../../store/services";

interface PostOpProps {
  heading: string;
}

const PostOpScreen: React.FunctionComponent<PostOpProps> = () => {
  const dispatch = useDispatch<AppDispatch>();
  const bottomRef = useRef<BottomSheetRefProps>(null);

  const [medicationKey, setMedicationKey] = useState<any>(Date.now());

  const selectedPatient = useSelectedPatient();
  const { error, loader } = useLoaderAndError();
  const navigation = useNavigation();

  const [medicationSelectedId, setMedicationSelectedId] = useState<
    string | null
  >("");
  const [deleteConfirmationVisible, setDeleteConfirmationVisible] =
    useState(false);
  const [medToDelete, setMedToDelete] = useState<string | null>(null);
  const userDetails = usePostOpUserDetails();
  const postOpDetails = usePostOpDetails();
  const { med_data } = useMedicationData();
  const medicationBottomSheetRef = useRef(null);
  const saveInProgressRef = React.useRef(false);
  // Create a ref to track the last saved data hash
  const lastSavedDataHashRef = React.useRef("");
  const handleClose = () => {
    if (medicationBottomSheetRef.current) {
      medicationBottomSheetRef.current.stateCleanUp();
    }
  };

  const handleMedOpen = (id?: string) => {
    setMedicationKey(Date.now());
    if (id) {
      setMedicationSelectedId(id);
    } else {
      setMedicationSelectedId(null);
    }
    bottomRef.current?.open();
  };

  const handleMedClose = () => {
    bottomRef.current?.close();
  };

  // NEW START
  const {
    anticoagulation_selected,
    discharge_plan_selected,
    discharge_plan_options,
    anticipated_45_days_follow_up_date,
    follow_ups_45_days,
    follow_ups_1_yr,
  } = formatPostOpData(userDetails);

  const [selected, setSelected] = useState([]);

  const [medDeleted, setMedDeleted] = useState(false);

  const [initialUpdate, setInitialUpdate] = useState(false);

  const [medDiff, setMedDiff] = useState(false);

  React.useEffect(() => {
    if (
      anticoagulation_selected &&
      selected.length === 0 &&
      !medDeleted &&
      !initialUpdate
    ) {
      setSelected(anticoagulation_selected);
      setInitialUpdate(true);
    }
  }, [anticoagulation_selected, medDeleted]);

  const handleAddMedication = () => {
    handleMedOpen();
  };

  // NEW END

  const resetState = () => {
    setMedDeleted(false);
    setInitialUpdate(false);
    setMedDiff(false);
    setSelected([]);
    setMedDiff(false);
  };

  useFocusEffect(
    React.useCallback(() => {
      const fetchDetails = async () => {
        if (selectedPatient?.case_id) {
          resetState();

          const res = await dispatch(
            fetchPostOpDetails({ caseId: selectedPatient?.case_id })
          );
        }
      };

      fetchDetails();
    }, [dispatch, selectedPatient?.case_id])
  );

  const diff = findPostOpDiff();

  const saveDetails = async (dataToSave: any): Promise<void> => {
    try {
      // // Run validation
      // const isValid = validator();

      // // If validation fails, update status and throw error
      // if (!isValid) {
      //   dispatch(
      //     setSaveStatus({
      //       screenId: "postOp",
      //       status: "validation_failed",
      //     })
      //   );
      //   setLocalSaveStatus("validation_failed");
      //   throw new Error("Validation failed");
      // }

      // Avoid saving if already in progress
      if (saveInProgressRef.current) {
        return;
      }

      const dataCopy = { ...(dataToSave || diff.currentValues) };
      const currentDataHash = JSON.stringify(dataCopy);

      if (currentDataHash === lastSavedDataHashRef.current) {
        return;
      }

      saveInProgressRef.current = true;

      if (!selectedPatient?.case_id) {
        saveInProgressRef.current = false;
        throw new Error("Missing case_id");
      }

      await dispatch(
        putPostOpDetails({
          caseId: selectedPatient.case_id,
          payload: dataCopy,
        })
      );

      lastSavedDataHashRef.current = currentDataHash;

      const refetchRes = await dispatch(
        fetchPostOpDetails({ caseId: selectedPatient.case_id })
      );

      if (refetchRes.payload) {
        resetState();
      }

      saveInProgressRef.current = false;
    } catch (err) {
      saveInProgressRef.current = false;
      throw err;
    }
  };

  const handleCancel = () => {
    dispatch(
      setPostOpDetails({
        ...userDetails,
        ...postOpDetails,
      })
    );
    dispatch(resetMedication());
  };

  const saveStatus = useSelector(
    (state: any) => state.common?.autoSave?.["postOp"]?.status
  );

  const [localSaveStatus, setLocalSaveStatus] = React.useState<string>("");

  const effectiveSaveStatus = saveStatus || localSaveStatus;

  // Track if user has made changes to prevent auto-navigation on initial load
  const [hasUserMadeChanges, setHasUserMadeChanges] = React.useState(false);

  // Track when user makes changes to any field
  React.useEffect(() => {
    if (diff.hasDiff && !hasUserMadeChanges) {
      setHasUserMadeChanges(true);
    }
  }, [diff.hasDiff, hasUserMadeChanges]);

  // Add effect to navigate to Case Synopsis tab when save is successful
  // React.useEffect(() => {
  //   if (effectiveSaveStatus === "saved" && hasUserMadeChanges) {
  //     // Add a small delay to ensure the user sees the "saved" message briefly
  //     const timer = setTimeout(() => {
  //       // Navigate to the Case Synopsis main tab
  //       (navigation as any).navigate("Case Synopsis");
  //     }, 1000);

  //     return () => clearTimeout(timer);
  //   }
  // }, [effectiveSaveStatus, navigation, hasUserMadeChanges]);

  /**
   * Derive the save message and styling based on the save status
   */
  const { saveMessage, messageStyle, iconName, iconColor } =
    React.useMemo(() => {
      let message = "";
      let style = "text-primaryPurple italic";
      let icon = "alert-circle-outline";
      let color = "#8143d9";

      switch (effectiveSaveStatus) {
        case "changes_detected":
          message = "Changes detected, saving soon...";
          style = "text-primaryBlack italic";
          icon = "alert-circle-outline";
          color = "#f5c542"; // yellow
          break;
        case "saving":
          message = "Saving... Please don't close the app";
          style = "text-primaryPurple font-bold italic";
          icon = "alert-circle-outline";
          color = "#8143d9"; // purple
          break;
        case "saved":
          message = "All changes saved";
          style = "text-green-3 italic";
          icon = "check-circle-outline";
          color = "#28a745"; // green
          break;
        case "validation_failed":
          message = "Validation failed. Please check your inputs.";
          style = "text-red-3 font-bold italic";
          icon = "alert-circle-outline";
          color = "#d93025"; // red
          break;
        case "error":
          message = "Error saving changes. Please try again.";
          style = "text-red-3 font-bold italic";
          icon = "alert-circle-outline";
          color = "#d93025"; // red
          break;
      }

      return {
        saveMessage: message,
        messageStyle: style,
        iconName: icon,
        iconColor: color,
      };
    }, [effectiveSaveStatus]);

  /**
   * Ref to track previous diff values to prevent unnecessary saves
   */
  const prevDiffRef = React.useRef<string>("");

  /**
   * Ref to track if initial data has been loaded
   * This prevents autosave from triggering before data is fetched
   */
  const initialDataLoadedRef = React.useRef<boolean>(false);

  /**
   * Effect to set initialDataLoaded when data is fetched
   */
  React.useEffect(() => {
    // Check if we have valid data
    const hasValidData =
      selectedPatient?.case_id !== undefined && userDetails !== null;

    if (hasValidData && !initialDataLoadedRef.current) {
      initialDataLoadedRef.current = true;
    }
  }, [userDetails, selectedPatient]);

  /**
   * Effect to trigger autosave when data changes
   * This uses our new simpleAutoSave service with local state fallback
   */
  React.useEffect(() => {
    // Only proceed if initial data has been loaded and there are actual differences
    if (!initialDataLoadedRef.current) {
      return;
    }

    // Check if case_detail_id exists to prevent API calls with undefined ID
    if (!userDetails?.case_id) {
      return;
    }

    // Only proceed if there are actual differences
    if (diff.hasDiff) {
      // Check if we have any undefined values in critical fields
      const hasUndefinedValues = Object.values(diff.currentValues).every(
        (val) => val === undefined
      );
      if (hasUndefinedValues) {
        return;
      }

      // Convert current values to string for comparison
      const currentDiffString = JSON.stringify(diff.currentValues);

      // Only trigger save if the values have actually changed
      if (currentDiffString !== prevDiffRef.current) {
        // Update the ref with current values
        prevDiffRef.current = currentDiffString;

        // Create a custom wrapper for setSaveStatus that updates both Redux and local state
        const setStatusWithFallback = (payload: {
          screenId: string;
          status: string;
        }) => {
          // Update local state as a fallback
          setLocalSaveStatus(payload.status);
          // Also dispatch to Redux
          return dispatch(setSaveStatus(payload));
        };

        // Trigger the simple autosave with the current values
        simpleAutoSave(
          "postOp", // Screen ID
          diff.currentValues, // Data to save
          saveDetails, // Save function
          4000, // 4 second delay before saving
          dispatch, // Redux dispatch
          setStatusWithFallback, // Custom action to set save status with fallback
          setLastSavedData // Action to set last saved data
        );
      } else {
        console.log("No actual changes in diff values, skipping autosave");
      }
    }
  }, [
    diff.hasDiff,
    diff.currentValues,
    dispatch,
    setLocalSaveStatus,
    userDetails,
  ]);
  const [anticoagulationDateModal, setAnticoagulationDateModal] =
    useState(false);
  const [date45DaysModal, setDate45DaysModal] = useState(false);
  const [date1YearModal, setDate1YearModal] = useState(false);

  const [med_ids, setMedIds] = React.useState();

  if (loader) {
    return <Loader />;
  }

  if (error) {
    return <Error message={error} />;
  }

  const renderMed = ({ item, index }) => {
    return (
      <>
        <View className="flex-row justify-between items-center p-2">
          <View className="flex-row items-center">
            <Text className="text-md font-bold text-primaryPurple">
              {item.med_name}
            </Text>
            <Text className="text-sm text-primaryBlack">
              {" - "}
              {item.med_dose} {item.dosing_frequency}
              {item.period?.toLowerCase() !== "indefinitely" ? " x" : " -"}{" "}
              {item.period_count} {item.period}
            </Text>
          </View>

          <View className="flex-row space-x-4 items-center">
            {/* Edit Button */}
            <TouchableOpacity
              onPress={() => {
                handleMedOpen(item.id);
              }}
              className="px-4 py-2"
            >
              <MaterialCommunityIcons name="pencil" size={20} color="#3b82f6" />
            </TouchableOpacity>

            {/* Delete Button */}
            <TouchableOpacity
              onPress={() => {
                setMedToDelete(item.id);
                setDeleteConfirmationVisible(true);
              }}
              className="px-4 py-2"
            >
              <MaterialCommunityIcons
                name="trash-can"
                size={20}
                color="#ef4444"
              />
            </TouchableOpacity>
          </View>
        </View>

        {/* Add LineSeperator except for the last item */}
        {index < med_data.length - 1 && <LineSeperator color="primaryWhite" />}
      </>
    );
  };

  const renderEmpty = () => {
    return (
      <View className="flex-row justify-center">
        <CustomText value={"None"} />
      </View>
    );
  };

  const handleRefresh = async () => {
    resetState();
    if (!selectedPatient?.case_id) return;
    await dispatch(fetchPostOpDetails({ caseId: selectedPatient?.case_id }));
  };

  return (
    <>
      <ScreenWrapper direction="column" onRefresh={handleRefresh}>
        <CustomCard>
          {saveMessage && (
            <View
              style={{
                flexDirection: "row",
                justifyContent: "flex-end",
                alignItems: "center",
                marginVertical: 4,
              }}
            >
              <MaterialCommunityIcons
                name={iconName}
                color={iconColor}
                size={20}
              />
              <Text
                className={`text-sm ${messageStyle}`}
                style={{ marginLeft: 6 }}
              >
                {saveMessage}
              </Text>
            </View>
          )}
          {/* {diff.hasDiff && (
        <View
          style={{
            position: "absolute",
            bottom: 60, // Adjust this value to place it slightly above the bottom bar
            left: 0,
            right: 0,
            zIndex: 10,
            alignItems: "center", // Centers the button horizontally
          }}
        >
          <SaveActionV2 disabled={!diff.hasDiff} onPress={saveDetails} />
        </View>
      )} */}

          <View className="gap-4 mt-2">
            <View className="gap-3">
              <View className="gap-2">
                <View className="flex-row justify-between">
                  <Heading
                    text="Anticoagulation/Antiplatelet Plan"
                    size="sub-heading"
                    showSeperator={false}
                  />
                  <MaterialCommunityIcons
                    name="plus-circle-outline"
                    color="#8143d9"
                    size={25}
                    onPress={handleAddMedication}
                  />
                </View>

                {med_data && med_data.length > 0 ? (
                  <View className="bg-primaryBg rounded-md">
                    <FlatList
                      data={med_data}
                      renderItem={renderMed}
                      keyExtractor={(item) => item.id}
                      scrollEnabled={false}
                    />
                  </View>
                ) : (
                  <View className="py-2">
                    {/* <Text className="text-gray-500 italic">
                      No data available
                    </Text> */}
                  </View>
                )}
              </View>
            </View>
            <LineSeperator extraStyle="mt-2" />

            <Heading
              text="Discharge Plan"
              size="sub-heading"
              color="black"
              showSeperator={false}
              extraStyle=""
            />

            <View className="bg-primaryBg rounded-md p-3">
              <OptionSelector
                options={discharge_plan_options?.map((option) => ({
                  label: option.label,
                  value: option.value,
                  icon:
                    option.label === "Same day discharge"
                      ? "clock-outline"
                      : "bed",
                }))}
                selected={discharge_plan_selected}
                onSelect={(option) => {
                  dispatch(
                    setPostOpDetails({
                      discharge_plan: {
                        ...userDetails?.discharge_plan,
                        selected: {
                          id: option?.value,
                          name: option?.label,
                        },
                      },
                    })
                  );
                }}
              />
            </View>
            {/* <LineSeperator extraStyle="my-5" /> */}

            {/* <View className="gap-2 ">
              <Heading
                text="Anticipated 45 days follow up"
                size="sub-heading"
                extraStyle="mb-2"
                showSeperator={false}
              />

              <TouchableOpacity
                onPress={() => setAnticoagulationDateModal(true)}
                className="border border-primaryPurple p-4 rounded-lg"
              >
                <CustomText
                  value={
                    anticipated_45_days_follow_up_date
                      ? moment(anticipated_45_days_follow_up_date).format(
                          "MM-DD-YYYY"
                        )
                      : "Select Date"
                  }
                  className="text-primaryBlack text-md"
                />
              </TouchableOpacity>
            </View> */}

            {/* <LineSeperator extraStyle="my-5" /> */}

            {/* <View className="gap-2 rounded-lg p-2 bg-primaryBg ">
              <Heading
                text="45 day follow up imaging"
                size="sub-heading"
                color="black"
                showSeperator={false}
                extraStyle="bg-primaryBg pb-2 mt-3 rounded"
              />

              <View className="flex-row justify-between rounded-md px-3">
                <TouchableOpacity
                  className={`rounded items-center w-[40%] p-2 ${
                    follow_ups_45_days?.cta_link
                      ? `bg-green-2`
                      : `bg-primaryGray`
                  } shadow-sm`}
                  disabled={follow_ups_45_days?.cta_link ? false : true}
                  onPress={() =>
                    navigation.navigate("WebViewer", {
                      link: follow_ups_45_days?.cta_link,
                    })
                  }
                >
                  <Text
                    className={`
                    ${
                      follow_ups_45_days?.cta_link
                        ? `text-green-3`
                        : `text-primaryWhite`
                    }
                    `}
                  >
                    {"Post-Op CTA"}
                  </Text>
                </TouchableOpacity>

                <TouchableOpacity
                  className={`rounded items-center w-[40%] p-2 ${
                    follow_ups_45_days?.tee_link
                      ? `bg-green-2`
                      : `bg-primaryGray`
                  } shadow-sm`}
                  disabled={follow_ups_45_days?.tee_link ? false : true}
                  onPress={() =>
                    navigation.navigate("WebViewer", {
                      link: follow_ups_45_days?.tee_link,
                    })
                  }
                >
                  <Text
                    className={`
                    ${
                      follow_ups_45_days?.tee_link
                        ? `text-green-3`
                        : `text-primaryWhite`
                    }
                    `}
                  >
                    {"Post-Op TEE"}
                  </Text>
                </TouchableOpacity>
              </View>
              <LineSeperator extraStyle="my-4" color="primaryWhite" />

              <View className="bg-primaryBg rounded-lg">
                <View className="flex-row justify-between items-center">
                  <CustomText
                    value="Completed"
                    className="text-primaryBlack text-md font-semibold"
                  />
                  <ToggleButton
                    messages={["Yes", "No"]}
                    selected={follow_ups_45_days.completed}
                    setSelected={(value) => {
                      dispatch(
                        setPostOpDetails({
                          follow_ups_45_days: {
                            ...userDetails?.follow_ups_45_days,
                            completed: value === "Yes" ? true : false,
                          },
                        })
                      );
                    }}
                  />
                </View>
                <LineSeperator extraStyle="my-5" color="primaryWhite" />

                {follow_ups_45_days.completed == "Yes" && (
                  <>
                    <View className="flex-row justify-between items-center">
                      <View className="flex-1">
                        <CustomText
                          value="Date"
                          className="text-primaryBlack text-md font-semibold"
                        />
                      </View>
                      <View className="flex-1">
                        <TouchableOpacity
                          onPress={() => setDate45DaysModal(true)}
                          className="border border-primaryPurple p-4 rounded-lg bg-primaryWhite"
                        >
                          <CustomText
                            value={
                              follow_ups_45_days?.date
                                ? moment(follow_ups_45_days?.date).format(
                                    "MM-DD-YYYY"
                                  )
                                : "Select Date"
                            }
                            className="text-primaryBlack text-md"
                          />
                        </TouchableOpacity>
                      </View>
                    </View>
                    <LineSeperator extraStyle="my-5" color="primaryWhite" />
                  </>
                )}

                <View className="flex-row justify-between items-center">
                  <CustomText
                    value="Peri device leak"
                    className="text-primaryBlack text-md font-semibold"
                  />
                  <ToggleButton
                    messages={["Yes", "No"]}
                    selected={follow_ups_45_days.peri_device_leak}
                    invertColor
                    setSelected={(value) => {
                      dispatch(
                        setPostOpDetails({
                          follow_ups_45_days: {
                            ...userDetails?.follow_ups_45_days,
                            peri_device_leak: value === "Yes" ? true : false,
                            width:
                              value === "No" ? null : follow_ups_45_days.width,
                          },
                        })
                      );
                    }}
                  />
                </View>
                <LineSeperator extraStyle="my-5" color="primaryWhite" />

                {follow_ups_45_days.peri_device_leak === "Yes" && (
                  <>
                    <View className="flex-row justify-between items-center">
                      <View className="flex-1">
                        <CustomText
                          value="Width (mm)"
                          className="text-primaryBlack text-md font-semibold"
                        />
                      </View>
                      <View className="flex-1">
                        <CustomInput
                          inputValue={
                            follow_ups_45_days.width?.toString() || ""
                          }
                          onInputChange={(value) =>
                            parseIntInput(value, (updatedValue) => {
                              dispatch(
                                setPostOpDetails({
                                  follow_ups_45_days: {
                                    ...userDetails?.follow_ups_45_days,
                                    width: updatedValue,
                                  },
                                })
                              );
                            })
                          }
                          placeholder="in mm"
                          keyboardType="numeric"
                        />
                      </View>
                    </View>
                    <LineSeperator extraStyle="my-5" color="primaryWhite" />
                  </>
                )}

                <View className="flex-row justify-between items-center mb-3">
                  <CustomText
                    value="Device related thrombus"
                    className="text-primaryBlack text-md font-semibold"
                  />
                  <ToggleButton
                    messages={["Yes", "No"]}
                    selected={follow_ups_45_days.thrombus}
                    invertColor
                    setSelected={(value) => {
                      dispatch(
                        setPostOpDetails({
                          follow_ups_45_days: {
                            ...userDetails?.follow_ups_45_days,
                            thrombus: value === "Yes" ? true : false,
                          },
                        })
                      );
                    }}
                  />
                </View>
              </View>
            </View> */}

            {/* <LineSeperator extraStyle="my-5" /> */}

            {/* <View className="gap-2 rounded-lg p-2 bg-primaryBg">
              <Heading
                text="1 year follow up images"
                size="sub-heading"
                color="black"
                showSeperator={false}
                extraStyle="bg-primaryBg pb-2 mt-3 rounded"
              />

              <View className="flex-row justify-between rounded-md px-3 ">
                <TouchableOpacity
                  className={`rounded items-center w-[40%] p-2 ${
                    follow_ups_1_yr?.cta_link ? `bg-green-2` : `bg-primaryGray`
                  } shadow-sm`}
                  disabled={follow_ups_1_yr?.cta_link ? false : true}

                  onPress={() =>
                    navigation.navigate("WebViewer", {
                      link: follow_ups_1_yr?.cta_link,
                    })
                  }
                >
                  <Text
                    className={`
                    ${
                      follow_ups_45_days?.cta
                        ? `text-green-3`
                        : `text-primaryWhite`
                    }
                    `}
                  >
                    {"Post-Op CTA"}
                  </Text>
                </TouchableOpacity>

                <TouchableOpacity
                  className={`rounded items-center w-[40%] p-2 ${
                    follow_ups_1_yr?.tee_link ? `bg-green-2` : `bg-primaryGray`
                  } shadow-sm`}
                  disabled={follow_ups_1_yr?.tee_link ? false : true}
                  onPress={() =>
                    navigation.navigate("WebViewer", {
                      link: follow_ups_1_yr?.tee_link,
                    })
                  }
                >
                  <Text
                    className={`
                    ${
                      follow_ups_45_days?.tee_link
                        ? `text-green-3`
                        : `text-primaryWhite`
                    }
                    `}
                  >
                    {"Post-Op TEE"}
                  </Text>
                </TouchableOpacity>
              </View>
              <LineSeperator extraStyle="my-4" color="primaryWhite" />

              <View className="bg-primaryBg  rounded-lg">
                <View className="flex-row justify-between items-center">
                  <CustomText
                    value="Completed"
                    className="text-primaryBlack text-md font-semibold"
                  />
                  <ToggleButton
                    messages={["Yes", "No"]}
                    selected={follow_ups_1_yr.completed}
                    setSelected={(value) => {
                      dispatch(
                        setPostOpDetails({
                          follow_ups_1_yr: {
                            ...userDetails?.follow_ups_1_yr,
                            completed: value === "Yes" ? true : false,
                          },
                        })
                      );
                    }}
                  />
                </View>
                <LineSeperator extraStyle="my-5" color="primaryWhite" />

                {follow_ups_1_yr.completed === "Yes" && (
                  <>
                    <View className="flex-row justify-between items-center">
                      <View className="flex-1">
                        <CustomText
                          value="Date"
                          className="text-primaryBlack text-md font-semibold"
                        />
                      </View>
                      <View className="flex-1">
                        <TouchableOpacity
                          onPress={() => setDate1YearModal(true)}
                          className="border border-primaryPurple bg-primaryWhite p-4 rounded-lg"
                        >
                          <CustomText
                            value={
                              follow_ups_1_yr?.date
                                ? moment(follow_ups_1_yr.date).format(
                                    "MM-DD-YYYY"
                                  )
                                : "Select Date"
                            }
                            className="text-primaryBlack text-md"
                          />
                        </TouchableOpacity>
                      </View>
                    </View>
                    <LineSeperator extraStyle="my-5" color="primaryWhite" />
                  </>
                )}

                <View className="flex-row justify-between items-center">
                  <CustomText
                    value="Peri device leak"
                    className="text-primaryBlack text-md font-semibold"
                  />
                  <ToggleButton
                    messages={["Yes", "No"]}
                    selected={follow_ups_1_yr.peri_device_leak}
                    invertColor
                    setSelected={(value) => {
                      dispatch(
                        setPostOpDetails({
                          follow_ups_1_yr: {
                            ...userDetails?.follow_ups_1_yr,
                            peri_device_leak: value === "Yes" ? true : false,
                            width:
                              value === "No" ? null : follow_ups_1_yr.width,
                          },
                        })
                      );
                    }}
                  />
                </View>
                <LineSeperator extraStyle="my-5" color="primaryWhite" />

                {follow_ups_1_yr.peri_device_leak === "Yes" && (
                  <>
                    <View className="flex-row justify-between items-center">
                      <View className="flex-1">
                        <CustomText
                          value="Width (mm)"
                          className="text-primaryBlack text-md font-semibold"
                        />
                      </View>
                      <View className="flex-1">
                        <CustomInput
                          inputValue={follow_ups_1_yr.width?.toString() || ""}
                          onInputChange={(value) =>
                            parseIntInput(value, (updatedValue) => {
                              dispatch(
                                setPostOpDetails({
                                  follow_ups_1_yr: {
                                    ...userDetails?.follow_ups_1_yr,
                                    width: updatedValue,
                                  },
                                })
                              );
                            })
                          }
                          placeholder="in mm"
                          keyboardType="numeric"
                        />
                      </View>
                    </View>
                    <LineSeperator extraStyle="my-5" color="primaryWhite" />
                  </>
                )}

                <View className="flex-row justify-between items-center mb-3">
                  <CustomText
                    value="Device related thrombus"
                    className="text-primaryBlack text-md font-semibold"
                  />
                  <ToggleButton
                    messages={["Yes", "No"]}
                    selected={follow_ups_1_yr.thrombus}
                    invertColor
                    setSelected={(value) => {
                      dispatch(
                        setPostOpDetails({
                          follow_ups_1_yr: {
                            ...userDetails?.follow_ups_1_yr,
                            thrombus: value === "Yes" ? true : false,
                          },
                        })
                      );
                    }}
                  />
                </View>
              </View>
            </View> */}
          </View>
          <LineSeperator extraStyle="my-5" />

          {/* <View className="flex-row items-center justify-center">
            <SaveActionButton
              disabled={!(medDiff || diff.hasDiff)}
              onPress={saveDetails}
              onCancel={handleCancel}
            />
          </View> */}
          <View className="mt-3"></View>
        </CustomCard>
        <View className="mt-9"></View>
        <DatePicker
          modal
          open={anticoagulationDateModal}
          date={
            anticipated_45_days_follow_up_date &&
            moment(
              anticipated_45_days_follow_up_date,
              "YYYY-MM-DD",
              true
            ).isValid()
              ? moment(
                  anticipated_45_days_follow_up_date,
                  "YYYY-MM-DD"
                ).toDate()
              : new Date()
          }
          onConfirm={(date) => {
            setAnticoagulationDateModal(false);
            dispatch(
              setPostOpDetails({
                anticipated_45_days_follow_up_date:
                  moment(date).format("YYYY-MM-DD"),
              })
            );
          }}
          onCancel={() => {
            setAnticoagulationDateModal(false);
          }}
          mode={"date"}
        />

        {/* 45 days follow up */}
        <DatePicker
          modal
          open={date45DaysModal}
          date={
            follow_ups_45_days?.date &&
            moment(follow_ups_45_days?.date, "YYYY-MM-DD", true).isValid()
              ? moment(follow_ups_45_days?.date, "YYYY-MM-DD")?.toDate()
              : new Date()
          }
          onConfirm={(date) => {
            setDate45DaysModal(false);

            dispatch(
              setPostOpDetails({
                follow_ups_45_days: {
                  ...userDetails?.follow_ups_45_days,
                  date: moment(date).format("YYYY-MM-DD"),
                },
              })
            );
          }}
          onCancel={() => {
            setDate45DaysModal(false);
          }}
          mode={"date"}
        />

        {/* 1 yr follow up */}
        <DatePicker
          modal
          open={date1YearModal}
          date={
            follow_ups_1_yr?.date &&
            moment(follow_ups_1_yr.date, "YYYY-MM-DD", true).isValid()
              ? moment(follow_ups_1_yr.date, "YYYY-MM-DD").toDate()
              : new Date()
          }
          onConfirm={(date) => {
            setDate1YearModal(false);

            dispatch(
              setPostOpDetails({
                follow_ups_1_yr: {
                  ...userDetails?.follow_ups_1_yr,
                  date: moment(date).format("YYYY-MM-DD"),
                },
              })
            );
          }}
          onCancel={() => {
            setDate1YearModal(false);
          }}
          mode={"date"}
        />
      </ScreenWrapper>
      <BottomSheetComponent
        ref={bottomRef}
        snapPoints={["75%", "85%"]}
        backgroundColor="white"
        onClose={() => handleClose()}
      >
        {
          <View className="flex-1">
            <MedicationBottomSheetComponent
              ref={medicationBottomSheetRef}
              medicineId={medicationSelectedId || ""}
              bottomSheetClose={handleMedClose}
              medKey={medicationKey}
            />
          </View>
        }
      </BottomSheetComponent>
      <PopupModal
        show={deleteConfirmationVisible}
        msg={["Are you sure you want to delete this medication?"]}
        status="warning"
        onClose={() => setDeleteConfirmationVisible(false)}
      >
        <View className="flex-row justify-end mt-4">
          <TouchableOpacity
            onPress={() => setDeleteConfirmationVisible(false)}
            style={{
              paddingHorizontal: 16,
              paddingVertical: 8,
              backgroundColor: "#D1D5DB",
              borderRadius: 6,
              marginRight: 8,
            }}
          >
            <Text style={{ color: "black" }}>Cancel</Text>
          </TouchableOpacity>

          <TouchableOpacity
            onPress={() => {
              if (medToDelete) {
                dispatch(deleteMedicationById(medToDelete));
                setDeleteConfirmationVisible(false);
                setMedToDelete(null);
                setMedDeleted(true);
              }
            }}
            style={{
              paddingHorizontal: 16,
              paddingVertical: 8,
              backgroundColor: "#EF4444",
              borderRadius: 6,
            }}
          >
            <Text style={{ color: "white" }}>Delete</Text>
          </TouchableOpacity>
        </View>
      </PopupModal>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
  },
  row: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
  },
  picker: {
    flex: 1,
    marginRight: 10,
  },
});

export default PostOpScreen;
