// PreOpTEEScreen.js
import React from "react";
import { View, TouchableOpacity, Image, Text } from "react-native";
import Heading from "../../../components/Heading";
import ScreenWrapper from "../../../components/ScreenWrapper";
import OptionSelector from "../../../components/OptionSelector";
import CustomCard from "../../../components/CustomCard";
import LineSeperator from "../../../components/LineSeperator";
import { useCoordinatorSelectedPatient } from "../hooks/schedulesHooks";
import { AppDispatch } from "../../../store";
import { useDispatch } from "react-redux";
import { useFocusEffect } from "@react-navigation/native";
import Loader from "../../../components/Loader";
import {
  findTEEDiff,
  useLoaderAndError,
  useteeDetails,
  useTEEUserDetails,
} from "../hooks/teeHooks";
import {
  fetchTEEDetails,
  putTEEDetails,
} from "../../../store/coordinator/ScheduleStack/tee/thunk";
import { formatLaaAnatomyOptions } from "../../../utils";
import { useNavigation } from "@react-navigation/native";
import SaveActionButton from "../../../components/SaveActionButton";
import CustomText from "../../../components/CustomText";
import globalStyles from "../../../styles/GlobalStyles";
import { setTEEUserDetails } from "../../../store/coordinator/ScheduleStack/tee";

interface IPreOpTEEScreenProps {
  route: {
    params: {
      morphology: string[];
      angles: string[];
      diameters: string[];
      depths: string[];
    };
  };
}

const PreOpTEEScreen: React.FunctionComponent<IPreOpTEEScreenProps> = ({
  route,
}) => {
  // NEW START
  const dispatch = useDispatch<AppDispatch>();
  const selectedPatient = useCoordinatorSelectedPatient();
  const { loader, error } = useLoaderAndError();
  const navigation = useNavigation();

  const userDetails = useTEEUserDetails();

  const { morphology_selected, morphologyOptions, study } =
    formatLaaAnatomyOptions(userDetails);

  const diff = findTEEDiff();

  const saveDetails = async () => {
    try {
      const res = await dispatch(
        putTEEDetails({
          case_details_id: userDetails?.case_detail_id,
          payload: diff.currentValues,
        })
      );

      if (res.payload) {
        if (selectedPatient?.case_id) {
          const refetchRes = await dispatch(
            fetchTEEDetails({ case_id: selectedPatient.case_id })
          );
          if (refetchRes.payload) {
            dispatch(setTEEUserDetails(refetchRes.payload));
          }
        }
      }
    } catch (err) {
      console.error(err);
    }
  };

  useFocusEffect(
    React.useCallback(() => {
      const fetchDetails = async () => {
        if (selectedPatient?.case_id) {
          const res = await dispatch(
            fetchTEEDetails({ case_id: selectedPatient.case_id })
          );

          if (res.payload) {
            dispatch(setTEEUserDetails(res.payload));
          }
        }
      };
      fetchDetails();
    }, [dispatch, selectedPatient?.case_id])
  );

  const handleRefresh = async () => {
    if (!selectedPatient?.case_id) return;
    const refetchRes = await dispatch(
      fetchTEEDetails({ case_id: selectedPatient.case_id })
    );
    if (refetchRes.payload) {
      dispatch(setTEEUserDetails(refetchRes.payload));
    }
  };

  if (loader) {
    return <Loader />;
  }

  return (
    <ScreenWrapper direction="column" onRefresh={handleRefresh}>
      <CustomCard>
        <SaveActionButton disabled={!diff.hasDiff} onPress={saveDetails} />

        <View className="mt-2 gap-4 ">
          <Heading
            text="Morphology"
            size="sub-heading"
            color={"black"}
            showSeperator={false}
          />
          <View className="p-3 rounded-md bg-primaryBg">
            <OptionSelector
              options={morphologyOptions}
              selected={morphology_selected}
              onSelect={(option) =>
                dispatch(
                  setTEEUserDetails({
                    morphology: {
                      ...userDetails.morphology,
                      selected: {
                        id: option.value,
                        name: option.label,
                      },
                    },
                  })
                )
              }
            />
          </View>
        </View>

        <LineSeperator extraStyle="my-5" />

        <View>
          <View>
            <Heading
              text="Measurements"
              size="sub-heading"
              color={"black"}
              showSeperator={false}
              extraStyle="mb-4"
            />

            <View className={`${globalStyles.containers.flex_between} px-2`}>
              <CustomText
                value={"0°: W 26 mm, D 37 mm"}
                className="mb-3 text-primaryPurple font-semibold"
              />
              <CustomText
                value={"45°: W 19 mm, D 35 mm"}
                className="mb-3 text-primaryPurple font-semibold"
              />
              <CustomText
                value={"90°: W 26 mm, D 37 mm"}
                className="mb-3 text-primaryPurple font-semibold"
              />
              <CustomText
                value={"135°: W 26 mm, D 37 mm"}
                className="mb-3 text-primaryPurple font-semibold"
              />
            </View>

            {study?.map((item, index) => (
              <TouchableOpacity
                key={index}
                className="px-5 py-3 rounded-md bg-primaryPurple shadow-md mb-2 w-[45%] mt-3"
                onPress={() =>
                  navigation.navigate("WebViewer", { link: item.viewer_link })
                }
              >
                <Text className="text-primaryWhite">{`TEE Study ${
                  index + 1
                }`}</Text>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        <View className="mb-3"></View>
      </CustomCard>

      <View className="mt-9"></View>
    </ScreenWrapper>
  );
};

export default PreOpTEEScreen;
