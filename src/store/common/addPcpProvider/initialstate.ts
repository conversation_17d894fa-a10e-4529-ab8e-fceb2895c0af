import { PcpProvider } from "./types";
const initialState = {
  providerListDetails: [] as PcpProvider[],
  providerDetails: null as PcpProvider | null,
  userDetails: null as PcpProvider | null,
  activeTab: "pcp-providers" as
    | "pcp-providers"
    | "referring-providers"
    | "implanting-physicians"
    | "sites",

  showAddPcpProviderModal: false,
  selectedProvider: "",
  credentialOptions: [] as string[], // <- NEW
  loading: false,
  error: null as string | null,
};
export default initialState;

// import { PcpProvider } from "./types";

// const initialState = {
//   providerListDetails: [] as PcpProvider[], // For dropdown options
//   providerDetails: null as PcpProvider | null, // Selected PCP provider's original details
//   userDetails: null as PcpProvider | null, // For form editing (current state)
//   activeTab: "pcp-providers" as "pcp-providers" | "referring-providers",
//   showAddPcpProviderModal: false,
//   selectedProvider: "", // Stores the ID of the selected PCP provider
//   loading: false,
//   error: null as string | null,
// };

// export default initialState;
