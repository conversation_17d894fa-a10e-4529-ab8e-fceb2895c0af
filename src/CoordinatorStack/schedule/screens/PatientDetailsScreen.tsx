import * as React from "react";
import CoordinatorTopBarStack from "../components/CoordinatorTopBarStack";
import { useNavigation } from "@react-navigation/native";
import { resetTransseptalState } from "../../../store/rep/ScheduleStack/laaoProcedures/transseptalpuncture";
import { useDispatch } from "react-redux";
import { AppDispatch } from "../../../store";

interface PatientDetailsScreenProps {
  heading: string;
}

const PatientDetailsScreen: React.FunctionComponent<
  PatientDetailsScreenProps
> = (props) => {
  const dispatch = useDispatch<AppDispatch>();
  const navigation = useNavigation();

  return <CoordinatorTopBarStack />;
};

export default PatientDetailsScreen;
