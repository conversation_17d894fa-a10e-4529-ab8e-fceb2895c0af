import { createSlice } from "@reduxjs/toolkit";
import initialState from "./initialstate";
import { fetchCoordinatorTasks } from "./thunk";

const taskSlice = createSlice({
  name: "tasks",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchCoordinatorTasks.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchCoordinatorTasks.fulfilled, (state, action) => {
        state.loading = false;
        state.tasks = action.payload;
      })
      .addCase(fetchCoordinatorTasks.rejected, (state, action) => {
        state.loading = false;
        state.tasks = null;
        state.error = action.payload as string;
      });
  },
});

export default taskSlice.reducer;
