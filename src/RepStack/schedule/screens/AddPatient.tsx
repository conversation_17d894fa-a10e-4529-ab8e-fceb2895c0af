import React, { useState, useRef } from "react";
import { View, Text, TouchableOpacity, ScrollView } from "react-native";
import { useFocusEffect } from "@react-navigation/native";
import CustomCard from "../../../components/CustomCard";
import SaveActionButton from "../../../components/SaveActionButton";
import LineSeperator from "../../../components/LineSeperator";
import SearchablePicker from "../../../components/SearchablePicker";
import DatePicker from "react-native-date-picker";
import moment from "moment";
import CustomInput from "../../../components/CustomTextInput";
import CustomBottomSheetTextInput from "../../../components/CustomBottomSheetTextInput";
import CustomText from "../../../components/CustomText";
import { useDispatch } from "react-redux";
import { AppDispatch } from "../../../store";
import AddReferringProvider from "../components/AddReferringProvider";
import AddPcpProvider from "../components/AddPcpProvider";
import CustomBottomModal, {
  CustomBottomModalRefProps,
} from "../../../components/CustomBottomModal";

import {
  formatProvider,
  useReferringProviderDetails,
  usePcpProviderDetails,
  findPostPatientDiff,
  usePostPatientUserDetails,
  formatPostPatient,
  useImplantingPhysicianDetails,
  formatImplantingPhysician,
  useLoaderAndErrorPostPatient,
} from "../hooks/addPatientHooks.ts";
import {
  parseCharInput,
  parseSSNInput,
  formatRationale,
  useRationaleDetails,
  parseMRNInput,
} from "../../../utils.tsx";
import { fetchReferringProviders } from "../../../store/common/addReferringProvider/thunk.ts";
import { resetProviderDetails as resetReferringProviderDetails } from "../../../store/common/addReferringProvider";
import { fetchPcpProviders } from "../../../store/common/addPcpProvider/thunk.ts";
import { resetProviderDetails as resetPcpProviderDetails } from "../../../store/common/addPcpProvider";
import {
  fetchImplantingPhysicians,
  postPatientDetails,
  fetchRationale,
} from "../../../store/rep/ScheduleStack/addpatient/thunk";
import {
  setPatientDetails,
  resetPatientDetails,
} from "../../../store/coordinator/ScheduleStack/addpatient";
import PopupModal from "../../../components/Popup";
import { useSiteList } from "../hooks/schedulesHooks.ts";
import { fetchSiteList } from "../../../store/rep/ScheduleStack/schedules/thunk.ts";
import Loader from "../../../components/Loader.tsx";

interface IAddPatientProps {
  onClose: () => void;
  bottomSheetRef: React.RefObject<any>;
  onSave: () => void;
}

const AddPatient: React.FunctionComponent<IAddPatientProps> = ({
  onClose,
  bottomSheetRef,
  onSave,
}) => {
  const dispatch = useDispatch<AppDispatch>();
  const { loader, error } = useLoaderAndErrorPostPatient();
  const useReferringProviderDetail = useReferringProviderDetails();
  const useImplantingPhysicianDetail = useImplantingPhysicianDetails();
  const useRationaleDetail = useRationaleDetails();
  const userDetails = usePostPatientUserDetails();
  const [popupMsg, setPopupMsg] = React.useState<string[]>([]);
  const [modalVisible, setModalVisible] = React.useState(false);
  const [rationaleSelectedName, setRationaleSelectedName] = React.useState("");
  const [siteList, setSiteList] = useState<{ label: string; value: number }[]>(
    []
  );

  // Simplified state management for add provider modal
  const [isAddProviderOpen, setIsAddProviderOpen] = useState(false);
  const [providerContext, setProviderContext] = useState<
    "referring_provider" | "pcp_provider" | null
  >(null);
  const addProviderBottomSheetRef =
    React.useRef<CustomBottomModalRefProps>(null);

  // Store the previous provider counts to detect new additions
  const [previousRefProviderCount, setPreviousRefProviderCount] = useState(0);
  const [previousPcpProviderCount, setPreviousPcpProviderCount] = useState(0);

  const validator = () => {
    setPopupMsg([]);

    var temp = false;

    if (!siteSelected) {
      setPopupMsg((prev) => [...prev, "Please Select a Site"]);
      temp = true;
    }
    if (first_name === "" || first_name === null || first_name === undefined) {
      setPopupMsg((prev) => [...prev, "Please Enter First Name"]);
      temp = true;
    }

    if (last_name === "" || last_name === null || last_name === undefined) {
      setPopupMsg((prev) => [...prev, "Please Enter Last Name"]);
      temp = true;
    }

    if (sex === "" || sex === null || sex === undefined) {
      setPopupMsg((prev) => [...prev, "Please Enter Gender"]);
      temp = true;
    }

    if (dob === "" || dob === null || dob === undefined) {
      setPopupMsg((prev) => [...prev, "Please Enter DOB"]);
      temp = true;
    }

    if (mrn === "" || mrn === null || mrn === undefined) {
      setPopupMsg((prev) => [...prev, "Please Enter MRN Value"]);
      temp = true;
    }

    if (
      implanting_physician === "" ||
      implanting_physician === null ||
      implanting_physician === undefined
    ) {
      setPopupMsg((prev) => [...prev, "Please Select Implanting Physician"]);
      temp = true;
    }

    if (
      procedure_date === "" ||
      procedure_date === null ||
      procedure_date === undefined
    ) {
      setPopupMsg((prev) => [...prev, "Please Enter Procedure Date"]);
      temp = true;
    }

    if (
      procedure_time === "" ||
      procedure_time === null ||
      procedure_time === undefined
    ) {
      setPopupMsg((prev) => [...prev, "Please Enter Procedure Time"]);
      temp = true;
    }

    if (temp) {
      setModalVisible(true);
      return false;
    } else {
      return true;
    }
  };

  const {
    siteSelected,
    first_name,
    last_name,
    middle_name,
    dob,
    sex,
    mrn,
    ssn,
    referring_provider,
    pcp_provider,
    rationale,
    rationale_other,
    procedure_date,
    procedure_time,
    implanting_physician,
  } = formatPostPatient(userDetails);

  // Get referring provider data
  const refProviders = useReferringProviderDetails();
  const pcpProviders = usePcpProviderDetails();

  const { providers: refProviderOptions } = formatProvider(refProviders);
  const { providers: pcpOptions } = formatProvider(pcpProviders);

  const { implantingPhysicians } = formatImplantingPhysician(
    useImplantingPhysicianDetail
  );
  const { rationaleOptions } = formatRationale(useRationaleDetail);
  const siteListData = useSiteList();

  // Function to auto-select the last added provider
  const autoSelectNewProvider = (
    providerType: "referring_provider" | "pcp_provider",
    currentProviders: any[]
  ) => {
    if (currentProviders && currentProviders.length > 0) {
      // Get the last provider (newest one)
      const lastProvider = currentProviders[currentProviders.length - 1];

      if (providerType === "referring_provider") {
        dispatch(
          setPatientDetails({
            referring_providers: lastProvider.value,
          })
        );
      } else if (providerType === "pcp_provider") {
        dispatch(
          setPatientDetails({
            pcp_providers: lastProvider.value,
          })
        );
      }
    }
  };

  // Effect to detect new referring provider additions and auto-select
  React.useEffect(() => {
    if (refProviderOptions && refProviderOptions.length > 0) {
      // If this is the first load, just set the count
      if (previousRefProviderCount === 0) {
        setPreviousRefProviderCount(refProviderOptions.length);
      }
      // If count increased, a new provider was added
      else if (refProviderOptions.length > previousRefProviderCount) {
        autoSelectNewProvider("referring_provider", refProviderOptions);
        setPreviousRefProviderCount(refProviderOptions.length);
      }
    }
  }, [refProviderOptions]);

  // Effect to detect new PCP provider additions and auto-select
  React.useEffect(() => {
    if (pcpOptions && pcpOptions.length > 0) {
      // If this is the first load, just set the count
      if (previousPcpProviderCount === 0) {
        setPreviousPcpProviderCount(pcpOptions.length);
      }
      // If count increased, a new provider was added
      else if (pcpOptions.length > previousPcpProviderCount) {
        autoSelectNewProvider("pcp_provider", pcpOptions);
        setPreviousPcpProviderCount(pcpOptions.length);
      }
    }
  }, [pcpOptions]);

  const fetchDetails = async () => {
    try {
      const refProviderAction = await dispatch(fetchReferringProviders());
      if (fetchReferringProviders.fulfilled.match(refProviderAction)) {
        console.log(
          "Successfully fetched referring providers:",
          refProviderAction.payload?.length || 0
        );
      } else {
        console.error(
          "Failed to fetch referring providers:",
          refProviderAction.error
        );
      }

      const pcpProviderAction = await dispatch(fetchPcpProviders());
      if (fetchPcpProviders.fulfilled.match(pcpProviderAction)) {
        console.log(
          "Successfully fetched PCP providers:",
          pcpProviderAction.payload?.length || 0
        );
      } else {
        console.error(
          "Failed to fetch PCP providers:",
          pcpProviderAction.error
        );
      }

      await dispatch(fetchRationale());
      await dispatch(fetchSiteList());
    } catch (error) {
      console.error("Error in fetchDetails:", error);
    }
  };

  // Fetch implanting physicians when site is selected and screen is focused
  useFocusEffect(
    React.useCallback(() => {
      if (siteSelected) {
        dispatch(fetchImplantingPhysicians(siteSelected))
          .then((result) => {
            console.log("Implanting physicians fetch result:", result);
          })
          .catch((error) => {
            console.error("Error fetching implanting physicians:", error);
          });
      } else {
        console.log("No site selected, skipping implanting physicians fetch");
      }
    }, [siteSelected])
  );

  // Fetch initial data when screen is focused
  useFocusEffect(
    React.useCallback(() => {
      fetchDetails();
    }, [])
  );

  // Update site list when siteListData changes
  React.useEffect(() => {
    if (siteListData && Array.isArray(siteListData)) {
      setSiteList(
        siteListData.map((site) => ({
          label: site.name,
          value: site.id,
        }))
      );
    }
  }, [siteListData]);

  const [dobDateModal, setDobDateModal] = React.useState(false);
  const [procedureDateModal, setProcedureDateModal] = React.useState(false);
  const [procedureTimeModal, setProcedureTimeModal] = React.useState(false);
  const diff = findPostPatientDiff();

  const saveDetails = async () => {
    try {
      if (validator()) {
        const res = await dispatch(
          postPatientDetails({
            site_id: siteSelected,
            payload: diff.currentValues,
          })
        );

        if (res) {
          bottomSheetRef.current?.close();
          onSave();
        }
      }
    } catch (err) {
      console.error(err);
    }
  };

  const handleCancel = () => {
    dispatch(resetPatientDetails());
    bottomSheetRef.current?.close();
  };

  // Handle opening add provider modal
  const handleAddProvider = (
    context: "referring_provider" | "pcp_provider"
  ) => {
    // Store current counts before opening modal
    if (context === "referring_provider") {
      setPreviousRefProviderCount(refProviderOptions?.length || 0);
    } else if (context === "pcp_provider") {
      setPreviousPcpProviderCount(pcpOptions?.length || 0);
    }

    // If already open, close it first to reset states
    if (isAddProviderOpen) {
      handleCloseAddProvider();

      return;
    }

    // If not already open, just open with the given context
    openProviderSheet(context);
  };

  // Helper function to open provider sheet with given context
  const openProviderSheet = (
    context: "referring_provider" | "pcp_provider"
  ) => {
    // Reset provider data based on context before opening
    if (context === "referring_provider") {
      dispatch(resetReferringProviderDetails());
    } else if (context === "pcp_provider") {
      dispatch(resetPcpProviderDetails());
    }

    // Set the context firstopenProviderSheet
    setProviderContext(context);

    // Set isAddProviderOpen to true to render the component with empty data
    setIsAddProviderOpen(true);

    // Open the bottom sheet first
    addProviderBottomSheetRef.current?.open();
  };

  // Handle closing add provider modal
  const handleCloseAddProvider = () => {
    setIsAddProviderOpen(false);

    // Reset provider form data based on context
    if (providerContext === "referring_provider") {
      dispatch(resetReferringProviderDetails());
    } else if (providerContext === "pcp_provider") {
      dispatch(resetPcpProviderDetails());
    }

    // Reset context after data reset
    setProviderContext(null);

    // Close the bottom sheet
    addProviderBottomSheetRef.current?.close();
  };

  // Handle successful provider addition
  const handleProviderSuccess = (newProviderId) => {
    // If a new provider ID was returned, directly set it as selected
    if (newProviderId) {
      if (providerContext === "referring_provider") {
        // Immediately set the provider ID in the state
        dispatch(
          setPatientDetails({
            referring_providers: newProviderId,
          })
        );

        // Fetch providers list in background
        dispatch(fetchReferringProviders());

        // Close and reset after successful addition
        handleCloseAddProvider();
      } else if (providerContext === "pcp_provider") {
        // Immediately set the provider ID in the state
        dispatch(
          setPatientDetails({
            pcp_providers: newProviderId,
          })
        );

        // Fetch providers list in background
        dispatch(fetchPcpProviders());

        // Close and reset after successful addition
        handleCloseAddProvider();
      }
    }
  };
  console.log("Selected site:", siteSelected);
  console.log("Payload:", diff.currentValues);

  return (
    <>
      {loader ? (
        <View className="mt-64">
          <Loader />
        </View>
      ) : (
        <View className="p-3 bg-primaryWhite">
          <View className="rounded-md p-3">
            <Text className="text-primaryPurple text-center font-semibold text-lg mb-4">
              Add Patient
            </Text>

            <View className="flex-row justify-between items-center ">
              <Text className="text-primaryBlack font-medium w-[40%]">
                Site <Text className="text-red-3 text-lg">*</Text>
              </Text>
              <View className="flex-1">
                <SearchablePicker
                  placeholder={"Site"}
                  items={siteList}
                  value={siteSelected}
                  onValueChange={(value) => {
                    dispatch(
                      setPatientDetails({
                        site_selected: value.value,
                        implanting_physician_id: "",
                      })
                    );
                  }}
                />
              </View>
            </View>
            <LineSeperator extraStyle="my-5" />
            <View className="flex-row justify-between items-center">
              <View className="w-[40%]">
                <Text className="text-primaryBlack font-medium ">
                  Implanting{"  "}
                  <Text className="ml-2 text-red-3 text-lg">*</Text>
                </Text>
                <Text className="text-primaryBlack font-medium ">
                  Physician
                </Text>
              </View>
              <View className="flex-1">
                <SearchablePicker
                  placeholder="Select"
                  items={implantingPhysicians.map((physician) => ({
                    label: physician?.label,
                    value: physician?.value,
                  }))}
                  value={implanting_physician}
                  onValueChange={(value) =>
                    dispatch(
                      setPatientDetails({
                        implanting_physician_id: value.value,
                      })
                    )
                  }
                  disable={siteSelected === "" ? false : true}
                  error={!implanting_physician}
                />
              </View>
            </View>
            <LineSeperator extraStyle="my-5" />

            {/* Patient Details */}
            <View className="flex-row justify-between items-center ">
              <Text className="text-primaryBlack font-medium w-[40%]">
                First Name <Text className="text-red-3 text-lg">*</Text>
              </Text>
              <View className="flex-1">
                <CustomBottomSheetTextInput
                  inputValue={first_name}
                  placeholder="First Name"
                  onInputChange={(value) => {
                    parseCharInput(value, (parsedValue) => {
                      dispatch(
                        setPatientDetails({
                          first_name: parsedValue,
                        })
                      );
                    });
                  }}
                />
              </View>
            </View>
            <LineSeperator extraStyle="my-5" />

            <View className="flex-row justify-between items-center ">
              <Text className="text-primaryBlack font-medium w-[40%]">
                Middle Name
              </Text>
              <View className="flex-1">
                <CustomInput
                  inputValue={middle_name}
                  placeholder="Middle Name"
                  onInputChange={(value) =>
                    parseCharInput(value, (parsedValue) => {
                      dispatch(
                        setPatientDetails({
                          middle_name: parsedValue,
                        })
                      );
                    })
                  }
                />
              </View>
            </View>
            <LineSeperator extraStyle="my-5" />

            <View className="flex-row justify-between items-center ">
              <Text className="text-primaryBlack font-medium w-[40%]">
                Last Name <Text className="text-red-3 text-lg">*</Text>
              </Text>
              <View className="flex-1">
                <CustomInput
                  inputValue={last_name}
                  onInputChange={(value) =>
                    parseCharInput(value, (parsedValue) => {
                      dispatch(
                        setPatientDetails({
                          last_name: parsedValue,
                        })
                      );
                    })
                  }
                  placeholder="Last Name"
                />
              </View>
            </View>

            <LineSeperator extraStyle="my-5" />

            <View className="flex-row justify-between items-center ">
              <Text className="text-primaryBlack font-medium w-[40%]">
                Gender <Text className="text-red-3 text-lg">*</Text>
              </Text>
              <View className="flex-1">
                <SearchablePicker
                  placeholder="Select"
                  items={[
                    { label: "Male", value: "M" },
                    { label: "Female", value: "F" },
                  ]}
                  value={sex}
                  onValueChange={(value) =>
                    dispatch(setPatientDetails({ sex: value.value }))
                  }
                  disableError
                />
              </View>
            </View>
            <LineSeperator extraStyle="my-5" />

            <View className="flex-row justify-between items-center ">
              <Text className="text-primaryBlack font-medium w-[40%]">
                Date of Birth <Text className="text-red-3 text-lg">*</Text>
              </Text>
              <TouchableOpacity
                onPress={() => {
                  setDobDateModal(true);
                }}
                className="flex-1 border border-primaryPurple pl-2 pr-4 py-4 rounded-lg"
              >
                <CustomText
                  value={dob ? moment(dob).format("MM/DD/YYYY") : "Select"}
                  className="text-primaryGray text-md"
                />
              </TouchableOpacity>
            </View>
            <LineSeperator extraStyle="my-5" />

            <View className="flex-row justify-between items-center">
              <Text className="text-primaryBlack font-medium w-[40%]">SSN</Text>
              <View className="flex-1">
                <CustomInput
                  inputValue={
                    ssn && ssn.length === 9
                      ? ssn.replace(/^(\d{3})(\d{2})(\d{4})$/, "$1-$2-$3")
                      : ssn
                  }
                  placeholder="SSN"
                  onInputChange={(value) =>
                    parseSSNInput(value, () => {
                      dispatch(
                        setPatientDetails({
                          ssn: value.replace(/[^0-9]/g, ""),
                        })
                      );
                    })
                  }
                  keyboardType="numeric"
                />
              </View>
            </View>
            <LineSeperator extraStyle="my-5" />

            <View className="flex-row justify-between items-center">
              <Text className="text-primaryBlack font-medium w-[40%]">
                MRN <Text className="text-red-3 text-lg">*</Text>
              </Text>
              <View className="flex-1">
                <CustomInput
                  inputValue={mrn}
                  placeholder="MRN"
                  onInputChange={(value) =>
                    parseMRNInput(value, (cleanedValue) =>
                      dispatch(
                        setPatientDetails({
                          mrn: cleanedValue,
                        })
                      )
                    )
                  }
                />
              </View>
            </View>

            <LineSeperator extraStyle="my-5" />
            <View className="flex-row justify-between items-center">
              <Text className="text-primaryBlack font-medium w-[40%]">
                Referring Provider
              </Text>
              <View className="flex-1">
                <SearchablePicker
                  placeholder="Select"
                  items={refProviderOptions?.map((provider) => ({
                    label: provider.label,
                    value: provider.value,
                  }))}
                  value={referring_provider}
                  onValueChange={(value) =>
                    dispatch(
                      setPatientDetails({
                        referring_providers: value.value,
                      })
                    )
                  }
                  disableError
                  screenContext="referring_provider"
                  onAddPress={() => handleAddProvider("referring_provider")}
                  showAddInSearch={true}
                />
              </View>
            </View>
            <LineSeperator extraStyle="my-5" />

            <View className="flex-row justify-between items-center">
              <Text className="text-primaryBlack font-medium w-[40%]">
                PCP Provider
              </Text>
              <View className="flex-1">
                <SearchablePicker
                  placeholder="Select"
                  items={pcpOptions}
                  value={pcp_provider}
                  onValueChange={(option) => {
                    dispatch(
                      setPatientDetails({
                        pcp_providers: option.value,
                      })
                    );
                  }}
                  disableError
                  screenContext="pcp_provider"
                  onAddPress={() => handleAddProvider("pcp_provider")}
                  showAddInSearch={true}
                />
              </View>
            </View>

            <LineSeperator extraStyle="my-5" />

            <View className="flex-row justify-between items-center ">
              <Text className="text-primaryBlack font-medium w-[40%]">
                Rationale
              </Text>
              <View className="flex-1">
                <View className="flex-1">
                  <SearchablePicker
                    placeholder="Select"
                    items={rationaleOptions.map((item) => ({
                      label: item.name,
                      value: item.id,
                    }))}
                    value={rationale}
                    onValueChange={(value) => {
                      setRationaleSelectedName(value.label);
                      dispatch(
                        setPatientDetails({
                          rationale_selected_id: value.value,
                        })
                      );
                    }}
                    disableError
                  />
                </View>

                {rationaleSelectedName?.toLowerCase() === "other" && (
                  <View className="mt-4 flex-1">
                    <CustomInput
                      placeholder="Other"
                      inputValue={rationale_other || ""}
                      onInputChange={(val) => {
                        dispatch(
                          setPatientDetails({
                            rationale_other: val,
                          })
                        );
                      }}
                    />
                  </View>
                )}
              </View>
            </View>

            <LineSeperator extraStyle="my-5" />

            <View className="flex-row justify-between items-center ">
              <Text className="text-primaryBlack font-medium w-[40%]">
                Procedure Date <Text className="text-red-3 text-lg">*</Text>
              </Text>
              <TouchableOpacity
                onPress={() => {
                  setProcedureDateModal(true);
                }}
                className="flex-1 border border-primaryPurple pl-2 pr-4 py-4 rounded-lg"
              >
                <CustomText
                  value={
                    procedure_date
                      ? moment(procedure_date).format("MM/DD/YYYY")
                      : "Select"
                  }
                  className="text-primaryGray text-md"
                />
              </TouchableOpacity>
            </View>

            <LineSeperator extraStyle="my-5" />

            <View className="flex-row justify-between items-center ">
              <Text className="text-primaryBlack font-medium w-[40%]">
                Procedure Time <Text className="text-red-3 text-lg">*</Text>
              </Text>
              <TouchableOpacity
                onPress={() => {
                  setProcedureTimeModal(true);
                }}
                className="flex-1 border border-primaryPurple pl-2 pr-4 py-4 rounded-lg"
              >
                <CustomText
                  value={
                    procedure_time
                      ? moment(procedure_time, "HH:mm:ss").format("HH:mm")
                      : "Select"
                  }
                  className="text-primaryGray text-md"
                />
              </TouchableOpacity>
            </View>

            <LineSeperator extraStyle="my-5" />

            <View className="flex-row justify-center gap-4">
              <SaveActionButton
                disabled={!diff}
                onPress={saveDetails}
                onCancel={handleCancel}
              />
            </View>
          </View>
          <View className="mt-9"></View>
        </View>
      )}

      <DatePicker
        modal
        open={dobDateModal}
        date={dob ? moment(dob, "YYYY-MM-DD").toDate() : new Date()}
        onConfirm={(date) => {
          setDobDateModal(false);
          dispatch(
            setPatientDetails({
              dob: moment(date).format("YYYY-MM-DD"),
            })
          );
        }}
        onCancel={() => setDobDateModal(false)}
        mode={"date"}
        maximumDate={new Date()}
      />
      <DatePicker
        modal
        open={procedureDateModal}
        date={
          procedure_date
            ? moment(procedure_date, "YYYY-MM-DD").toDate()
            : new Date()
        }
        onConfirm={(date) => {
          setProcedureDateModal(false);
          dispatch(
            setPatientDetails({
              procedure_date: moment(date).format("YYYY-MM-DD"),
            })
          );
        }}
        onCancel={() => {
          setProcedureDateModal(false);
        }}
        mode={"date"}
        minimumDate={new Date()}
      />

      <DatePicker
        modal
        open={procedureTimeModal}
        date={
          procedure_time
            ? moment(procedure_time, "HH:mm:ss").toDate()
            : new Date()
        }
        onConfirm={(date) => {
          setProcedureTimeModal(false);
          dispatch(
            setPatientDetails({
              procedure_time: moment(date).format("HH:mm:ss"),
            })
          );
        }}
        onCancel={() => {
          setProcedureTimeModal(false);
        }}
        mode={"time"}
      />
      <PopupModal
        show={modalVisible}
        msg={popupMsg}
        status="warning"
        onClose={() => setModalVisible(false)}
      />

      {/* Custom Bottom Modal for adding a new provider */}
      <CustomBottomModal
        ref={addProviderBottomSheetRef}
        height="90%"
        backgroundColor="#f8f9fa"
        onClose={() => {
          // Reset provider data based on context
          if (providerContext === "referring_provider") {
            dispatch(resetReferringProviderDetails());
          } else if (providerContext === "pcp_provider") {
            dispatch(resetPcpProviderDetails());
          }

          // Make sure to reset all states when the sheet is closed
          setIsAddProviderOpen(false);
          setProviderContext(null);

          // Force a re-render to ensure components mount with fresh data next time
          setTimeout(() => {
            // This ensures the component is fully unmounted before potentially being remounted
            setIsAddProviderOpen(false);
          }, 100);
        }}
      >
        {isAddProviderOpen && providerContext === "referring_provider" && (
          <AddReferringProvider
            bottomSheetRef={addProviderBottomSheetRef}
            onSuccess={handleProviderSuccess}
          />
        )}
        {isAddProviderOpen && providerContext === "pcp_provider" && (
          <AddPcpProvider
            bottomSheetRef={addProviderBottomSheetRef}
            onSuccess={handleProviderSuccess}
          />
        )}
      </CustomBottomModal>
    </>
  );
};

export default AddPatient;
