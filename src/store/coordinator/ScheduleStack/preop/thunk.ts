// thunk.ts
import { createAsyncThunk } from "@reduxjs/toolkit";
import api from "../../../../api/api";
import { Alert } from "react-native";
import ReactNativeBlobUtil from "react-native-blob-util";

const fetchPreopDetails = createAsyncThunk(
  "tasks/fetchPreopDetails",
  async ({ case_id }: { case_id: string }, thunkAPI) => {
    try {
      const response = await api.private.get(
        api.routes.privateRoutes.coordinator.preop_get(case_id)
      );

      if (response.data && response.data.result) {
        return response.data.result;
      } else {
        return thunkAPI.rejectWithValue(
          'Invalid response format: Missing "result" field'
        );
      }
    } catch (error: any) {
      if (error.response) {
        if (error.response.status === 404) {
        }
        return thunkAPI.rejectWithValue(
          error.response.data.error_description || "Failed to fetch Afib Basics"
        );
      } else if (error.request) {
        console.error("No response received:", error.request);
        return thunkAPI.rejectWithValue("No response received from the server");
      } else {
        console.error("Error occurred:", error.message);
        return thunkAPI.rejectWithValue(
          error.message || "An unknown error occurred"
        );
      }
    }
  }
);

const puttruplanStatus = createAsyncThunk(
  "schedule/puttruplanStatus",
  async ({ case_id, payload }: { case_id: string; payload: any }, thunkAPI) => {
    try {
      console.info(
        "puttruplanStatus:",
        "case_id:",
        case_id,
        "payload:",
        payload
      );
      const response = await api.private.put(
        api.routes.privateRoutes.coordinator.preop_put(case_id),
        payload
      );
      if (response.data && response.data.result) {
        return response.data.result;
      } else {
        return thunkAPI.rejectWithValue(
          'Invalid response format: Missing "result" field'
        );
      }
    } catch (error: any) {
      console.error(
        "Error caught during API call:",
        error.response.data || error
      );
      return thunkAPI.rejectWithValue("Failed to PUT truplanStatus");
    }
  }
);

const uploadPDF = createAsyncThunk(
  "schedule/uploadPDF",
  async ({ case_id, file }: { case_id: string; file: any }, thunkAPI) => {
    try {
      const dispatch = thunkAPI.dispatch;
      const url = api.private.post(
        api.routes.privateRoutes.rep.truplan_pdf_post(case_id)
      );

      const uploadResponse = await ReactNativeBlobUtil.fetch(
        "PUT",
        (
          await url
        ).data.result.blob_url,
        {
          "x-ms-blob-type": "BlockBlob",
          "Content-Type": "application/pdf",
        },
        ReactNativeBlobUtil.wrap(file[0].uri)
      );

      if (
        uploadResponse.respInfo.status >= 200 &&
        uploadResponse.respInfo.status < 300
      ) {
        // ✅ Ensure we **await** the `dispatch` call
        await dispatch(
          puttruplanStatus({
            case_id: case_id,
            payload: { truplan_upload_status: "In Progress" },
          })
        ).unwrap(); // Unwraps to throw if rejected

        Alert.alert("File Uploaded Successfully");
      } else {
        return thunkAPI.rejectWithValue(
          "File upload failed with status: " + uploadResponse.respInfo.status
        );
      }
    } catch (error: any) {
      console.error(error);
      console.error("Error caught during API call:", error.response?.data);
      return thunkAPI.rejectWithValue(
        error.response?.data?.message || "Failed to upload PDF"
      );
    }
  }
);

const putPreopDetails = createAsyncThunk(
  "tasks/putPreopDetails",
  async ({ case_id, payload }: { case_id: string; payload: any }, thunkAPI) => {
    try {
      const response = await api.private.put(
        api.routes.privateRoutes.coordinator.preop_put(case_id),
        payload
      );

      if (response.data && response.data.result) {
        return response.data.result;
      }
    } catch (error: any) {
      if (error.response) {
        if (error.response.status === 404) {
          return thunkAPI.rejectWithValue("Data not found (404)");
        }
        return thunkAPI.rejectWithValue(
          error.response.message || "Unknown error occurred"
        );
      } else if (error.request) {
        console.error("No response received:", error.request);
        return thunkAPI.rejectWithValue("No response received from the server");
      } else {
        console.error("Error occurred:", error.message);
        return thunkAPI.rejectWithValue(
          error.message || "An unknown error occurred"
        );
      }
    }
  }
);

const putPreopHistoryDetails = createAsyncThunk(
  "tasks/putPreopHistoryDetails",
  async ({ case_id, payload }: { case_id: string; payload: any }, thunkAPI) => {
    try {
      const response = await api.private.put(
        api.routes.privateRoutes.coordinator.preop_history_put(case_id),
        payload
      );

      if (response.data && response.data.result) {
        return response.data.result;
      }
    } catch (error: any) {
      if (error.response) {
        if (error.response.status === 404) {
        }
        return thunkAPI.rejectWithValue;
      } else if (error.request) {
        console.error("No response received:", error.request);
        return thunkAPI.rejectWithValue("No response received from the server");
      } else {
        console.error("Error occurred:", error.message);
        return thunkAPI.rejectWithValue(
          error.message || "An unknown error occurred"
        );
      }
    }
  }
);

const putConsultVisit = createAsyncThunk(
  "tasks/putConsultVisit",
  async ({ case_id, payload }: { case_id: string; payload: any }, thunkAPI) => {
    try {
      const response = await api.private.post(
        api.routes.privateRoutes.coordinator.pre_op_consult_visit_post(case_id),
        payload
      );

      if (response.data && response.data.result) {
        return response.data.result;
      }
    } catch (error: any) {
      if (error.response) {
        console.error("Response error:", error.response);
        return thunkAPI.rejectWithValue("Error from server");
      } else if (error.request) {
        console.error("No response received:", error.request);
        return thunkAPI.rejectWithValue("No response received from the server");
      } else {
        console.error("Error occurred:", error.message);
        return thunkAPI.rejectWithValue("An unknown error occurred");
      }
    }
  }
);

const putPreopTesting = createAsyncThunk(
  "tasks/putPreopTesting",
  async ({ case_id, payload }: { case_id: string; payload: any }, thunkAPI) => {
    try {
      const response = await api.private.put(
        api.routes.privateRoutes.coordinator.preop_testing_put(case_id),
        payload
      );

      if (response.data && response.data.result) {
        return response.data.result;
      }
    } catch (error: any) {
      if (error.response) {
        if (error.response.status === 404) {
        }
        return thunkAPI.rejectWithValue;
      } else if (error.request) {
        console.error("No response received:", error.request);
        return thunkAPI.rejectWithValue("No response received from the server");
      } else {
        console.error("Error occurred:", error.message);
        return thunkAPI.rejectWithValue(
          error.message || "An unknown error occurred"
        );
      }
    }
  }
);

export {
  fetchPreopDetails,
  putPreopDetails,
  putPreopHistoryDetails,
  putPreopTesting,
  putConsultVisit,
  puttruplanStatus,
  uploadPDF,
};
