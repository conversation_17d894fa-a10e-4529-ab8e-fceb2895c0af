import React from "react";
import { View, TouchableOpacity, Image, Text } from "react-native";
import Heading from "../../../components/Heading";
import ScreenWrapper from "../../../components/ScreenWrapper";
import OptionSelector from "../../../components/OptionSelector";
import CustomCard from "../../../components/CustomCard";
import LineSeperator from "../../../components/LineSeperator";
import { useClinicianSelectedPatient } from "../hooks/schedulesHooks";
import { AppDispatch } from "../../../store";
import { useDispatch, useSelector } from "react-redux";
import { useFocusEffect } from "@react-navigation/native";
import Loader from "../../../components/Loader";
import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons";
import {
  useLoaderAndError,
  useLaaAnatomyUserDetails,
  findLaaoAnatomyDiff,
  useLaaAnatomyDetails,
} from "../hooks/laaanatomyHooks";
import {
  fetchLaaAnatomyDetails,
  putLaaAnatomyDetails,
} from "../../../store/clinician/ScheduleStack/laaanatomy/thunk";
import {
  setDepthForAngle,
  setLaaAnatomyUserDetails,
  setWidthForAngle,
} from "../../../store/clinician/ScheduleStack/laaanatomy";
// import { setLaaAnatomyUserDetails } from "../../../store/rep/ScheduleStack/laaoProcedures/laaanatomy";
import { formatLaaAnatomyOptions, parseFloatInput } from "../../../utils";
import { useNavigation } from "@react-navigation/native";
import SaveActionButton from "../../../components/SaveActionButton";
import CustomText from "../../../components/CustomText";
import globalStyles from "../../../styles/GlobalStyles";
import CustomInput from "../../../components/CustomTextInput";
import LAAAnatomyMeasurements from "../components/LAAAnatomyMeasurements";
import PopupModal from "../../../components/Popup";
import { simpleAutoSave } from "../../../services/simpleAutoSave";
import { setLastSavedData, setSaveStatus } from "../../../store/services";

interface ILAAAnatomyScreenProps {
  route: {
    params: {
      morphology: string[];
      angles: string[];
      diameters: string[];
      depths: string[];
    };
  };
}

const LAAAnatomyScreen: React.FunctionComponent<ILAAAnatomyScreenProps> = ({
  route,
}) => {
  const [modalVisible, setModalVisible] = React.useState(false);
  const [popupMsg, setPopupMsg] = React.useState<string[]>([]);
  const dispatch = useDispatch<AppDispatch>();
  const selectedPatient = useClinicianSelectedPatient();
  const { loader, error } = useLoaderAndError();
  const navigation = useNavigation();

  const userDetails = useLaaAnatomyUserDetails();
  const laaAnatomyDetails = useLaaAnatomyDetails();

  const { morphology_selected, morphologyOptions, study } =
    formatLaaAnatomyOptions(userDetails);

  const diff = findLaaoAnatomyDiff();

  const transformedData = userDetails?.tee_measurement?.reduce(
    (acc, { angle, width, depth }) => {
      acc[angle] = {
        label: `${angle}°`,
        w: width,
        d: depth,
        onWChange: (value) => {
          if (Number(value) > 50) return;
          parseFloatInput(value, (updatedValue) => {
            dispatch(
              setWidthForAngle({
                angle,
                width: updatedValue,
              })
            );
          });
        },
        onDChange: (value) => {
          if (Number(value) > 100) return;
          parseFloatInput(value, (updatedValue) => {
            dispatch(
              setDepthForAngle({
                angle,
                depth: updatedValue,
              })
            );
          });
        },
      };
      return acc;
    },
    {}
  );

  const validator = () => {
    setPopupMsg([]);

    let invalidWidth = false;
    let invalidDiameter = false;
    userDetails?.tee_measurement?.forEach((item) => {
      if (item.width === null || item.width === undefined || item.width === 0) {
        invalidWidth = true;
      }
      if (item.depth === null || item.depth === undefined || item.depth === 0) {
        invalidDiameter = true;
      }
    });

    if (invalidWidth) {
      setPopupMsg((prev) => [...prev, "Please Enter a Valid Width"]);
    }
    if (invalidDiameter) {
      setPopupMsg((prev) => [...prev, "Please Enter a Valid Diameter"]);
    }

    if (invalidWidth || invalidDiameter) {
      setModalVisible(true);
      return false;
    } else {
      return true;
    }
  };

  const saveDetails = async (): Promise<void> => {
    try {
      // Run validation
      const isValid = validator();

      // If validation fails, update status and throw error
      if (!isValid) {
        dispatch(
          setSaveStatus({
            screenId: "laaAnatomy",
            status: "validation_failed",
          })
        );
        setLocalSaveStatus("validation_failed");
        throw new Error("Validation failed");
      }

      const dataCopy = { ...diff.currentValues };

      // Save LAA anatomy data
      const res = await dispatch(
        putLaaAnatomyDetails({
          case_details_id: userDetails?.case_detail_id,
          payload: dataCopy,
        })
      );

      // Refetch if applicable
      if (res.payload && selectedPatient?.case_id) {
        const refetchRes = await dispatch(
          fetchLaaAnatomyDetails({ case_id: selectedPatient.case_id })
        );
        if (refetchRes.payload) {
          dispatch(setLaaAnatomyUserDetails(refetchRes.payload));
        }
      }
    } catch (err) {
      throw err;
    }
  };

  const handleCancel = () => {
    dispatch(
      setLaaAnatomyUserDetails({
        ...userDetails,
        ...laaAnatomyDetails,
      })
    );
  };

  useFocusEffect(
    React.useCallback(() => {
      const fetchDetails = async () => {
        if (selectedPatient?.case_id) {
          const res = await dispatch(
            fetchLaaAnatomyDetails({ case_id: selectedPatient.case_id })
          );

          if (res.payload) {
            dispatch(setLaaAnatomyUserDetails(res.payload));
          }
        }
      };
      fetchDetails();
    }, [dispatch, selectedPatient?.case_id])
  );

  const saveStatus = useSelector(
    (state: any) => state.common?.autoSave?.["laaAnatomy"]?.status
  );

  const [localSaveStatus, setLocalSaveStatus] = React.useState<string>("");

  const effectiveSaveStatus = saveStatus || localSaveStatus;

  // Track if user has made changes to prevent auto-navigation on initial load
  const [hasUserMadeChanges, setHasUserMadeChanges] = React.useState(false);

  // Track when user makes changes to any field
  React.useEffect(() => {
    if (diff.hasDiff && !hasUserMadeChanges) {
      setHasUserMadeChanges(true);
    }
  }, [diff.hasDiff, hasUserMadeChanges]);

  // Add effect to navigate to Anesthesia screen when save is successful
  // React.useEffect(() => {
  //   if (effectiveSaveStatus === "saved" && hasUserMadeChanges) {
  //     // Add a small delay to ensure the user sees the "saved" message briefly
  //     const timer = setTimeout(() => {
  //       (navigation as any).navigate("Anesthesia");
  //     }, 1000);

  //     return () => clearTimeout(timer);
  //   }
  // }, [effectiveSaveStatus, navigation, hasUserMadeChanges]);

  /**
   * Derive the save message and styling based on the save status
   */
  const { saveMessage, messageStyle, iconName, iconColor } =
    React.useMemo(() => {
      let message = "";
      let style = "text-primaryPurple italic";
      let icon = "alert-circle-outline";
      let color = "#8143d9";

      switch (effectiveSaveStatus) {
        case "changes_detected":
          message = "Changes detected, saving soon...";
          style = "text-primaryBlack italic";
          icon = "alert-circle-outline";
          color = "#f5c542"; // yellow
          break;
        case "saving":
          message = "Saving... Please don't close the app";
          style = "text-primaryPurple font-bold italic";
          icon = "alert-circle-outline";
          color = "#8143d9"; // purple
          break;
        case "saved":
          message = "All changes saved";
          style = "text-green-3 italic";
          icon = "check-circle-outline";
          color = "#28a745"; // green
          break;
        case "validation_failed":
          message = "Validation failed. Please check your inputs.";
          style = "text-red-3 font-bold italic";
          icon = "alert-circle-outline";
          color = "#d93025"; // red
          break;
        case "error":
          message = "Error saving changes. Please try again.";
          style = "text-red-3 font-bold italic";
          icon = "alert-circle-outline";
          color = "#d93025"; // red
          break;
      }

      return {
        saveMessage: message,
        messageStyle: style,
        iconName: icon,
        iconColor: color,
      };
    }, [effectiveSaveStatus]);
  /**
   * Ref to track previous diff values to prevent unnecessary saves
   */
  const prevDiffRef = React.useRef<string>("");

  /**
   * Ref to track if initial data has been loaded
   * This prevents autosave from triggering before data is fetched
   */
  const initialDataLoadedRef = React.useRef<boolean>(false);

  /**
   * Effect to set initialDataLoaded when data is fetched
   */
  React.useEffect(() => {
    // Check if we have valid data
    const hasValidData = userDetails?.case_detail_id !== undefined;

    if (hasValidData && !initialDataLoadedRef.current) {
      initialDataLoadedRef.current = true;
    }
  }, [userDetails]);

  /**
   * Effect to trigger autosave when data changes
   * This uses our new simpleAutoSave service with local state fallback
   */
  React.useEffect(() => {
    // Only proceed if initial data has been loaded and there are actual differences
    if (!initialDataLoadedRef.current) {
      return;
    }

    // Check if case_detail_id exists to prevent API calls with undefined ID
    if (!userDetails?.case_detail_id) {
      return;
    }

    // Only proceed if there are actual differences
    if (diff.hasDiff) {
      // Check if we have any undefined values in critical fields
      const hasUndefinedValues = Object.values(diff.currentValues).every(
        (val) => val === undefined
      );
      if (hasUndefinedValues) {
        return;
      }

      // Convert current values to string for comparison
      const currentDiffString = JSON.stringify(diff.currentValues);

      // Only trigger save if the values have actually changed
      if (currentDiffString !== prevDiffRef.current) {
        // Update the ref with current values
        prevDiffRef.current = currentDiffString;

        // Create a custom wrapper for setSaveStatus that updates both Redux and local state
        const setStatusWithFallback = (payload: {
          screenId: string;
          status: string;
        }) => {
          // Update local state as a fallback
          setLocalSaveStatus(payload.status);
          // Also dispatch to Redux
          return dispatch(setSaveStatus(payload));
        };

        // Trigger the simple autosave with the current values
        simpleAutoSave(
          "laaAnatomy", // Screen ID
          diff.currentValues, // Data to save
          saveDetails, // Save function
          4000, // 4 second delay before saving
          dispatch, // Redux dispatch
          setStatusWithFallback, // Custom action to set save status with fallback
          setLastSavedData // Action to set last saved data
        );
      } else {
        console.log("No actual changes in diff values, skipping autosave");
      }
    }
  }, [
    diff.hasDiff,
    diff.currentValues,
    dispatch,
    setLocalSaveStatus,
    userDetails,
  ]);

  const handleRefresh = async () => {
    if (!selectedPatient?.case_id) return;
    const refetchRes = await dispatch(
      fetchLaaAnatomyDetails({ case_id: selectedPatient.case_id })
    );
    if (refetchRes.payload) {
      dispatch(setLaaAnatomyUserDetails(refetchRes.payload));
    }
  };
  if (loader) {
    return <Loader />;
  }
  return (
    <ScreenWrapper direction="column" onRefresh={handleRefresh}>
      <CustomCard>
        {saveMessage && (
          <View
            style={{
              flexDirection: "row",
              justifyContent: "flex-end",
              alignItems: "center",
              marginVertical: 4,
            }}
          >
            <MaterialCommunityIcons
              name={iconName}
              color={iconColor}
              size={20}
            />
            <Text
              className={`text-sm ${messageStyle}`}
              style={{ marginLeft: 6 }}
            >
              {saveMessage}
            </Text>
          </View>
        )}
        <View className="mt-2 gap-4">
          <Heading
            text="Morphology"
            size="sub-heading"
            color={"black"}
            showSeperator={false}
          />
          <View className="p-3 rounded-md bg-primaryBg">
            <OptionSelector
              options={morphologyOptions}
              selected={morphology_selected}
              onSelect={(option) =>
                dispatch(
                  setLaaAnatomyUserDetails({
                    morphology: {
                      ...userDetails.morphology,
                      selected: {
                        id: option.value,
                        name: option.label,
                      },
                    },
                  })
                )
              }
            />
          </View>
        </View>
        <LineSeperator extraStyle="mt-5 mb-3" />
        <View>
          <Heading
            text="Measurements"
            size="sub-heading"
            color={"black"}
            showSeperator={false}
            extraStyle="mb-4"
          />
          {transformedData && <LAAAnatomyMeasurements data={transformedData} />}
          {study?.map((item, index) => (
            <TouchableOpacity
              key={index}
              className="px-5 py-3 rounded-md bg-primaryPurple shadow-md mb-2 w-[45%] mt-3"
              onPress={() =>
                navigation.navigate("WebViewer", { link: item.viewer_link })
              }
            >
              <Text className="text-primaryWhite">{`TEE Study ${
                index + 1
              }`}</Text>
            </TouchableOpacity>
          ))}
        </View>
        <LineSeperator extraStyle="my-5" />

        {/* <View className="flex-row items-center justify-center">
          <SaveActionButton
            disabled={!diff.hasDiff}
            onPress={saveDetails}
            onCancel={handleCancel}
          />
        </View> */}
        <View className="mb-3"></View>
      </CustomCard>
      <View className="mt-9"></View>
      <PopupModal
        show={modalVisible}
        msg={popupMsg}
        status="warning"
        onClose={() => setModalVisible(false)}
      />
    </ScreenWrapper>
  );
};

export default LAAAnatomyScreen;
