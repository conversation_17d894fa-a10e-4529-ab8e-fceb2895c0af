// TEEScreen.js

import React from "react";
import { View, TouchableOpacity, Image, Text, ScrollView } from "react-native";
import ScreenWrapper from "../../../components/ScreenWrapper";
import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons";
import Heading from "../../../components/Heading";
import zeroDegImage from "../../../../assests/0deg.png";
import fortyfiveDegImage from "../../../../assests/45deg.png";
import ninetyDegImage from "../../../../assests/90deg.png";
import onethirtyfiveDegImage from "../../../../assests/135deg.png";
import OptionSelector from "../../../components/OptionSelector";
import CustomCard from "../../../components/CustomCard";
import LineSeperator from "../../../components/LineSeperator";

const sampleUri2 = {
  img1: zeroDegImage,
  img2: ninetyDegImage,
  // img3: ninetyDegImage,
  img3: fortyfiveDegImage,
  img4: onethirtyfiveDegImage,
};

interface ITEEScreenProps {
  route: {
    params: {
      morphology: string[];
      angles: string[];
      diameters: string[];
      depths: string[];
    };
  };
}

// Function to return the MaterialCommunityIcons component
function MatIcon(name: string, color: string, size: number) {
  return (
    <TouchableOpacity className="">
      <Text>
        {" "}
        <MaterialCommunityIcons name={name} color={color} size={size} />
      </Text>
    </TouchableOpacity>
  );
}

const TEEScreen: React.FunctionComponent<ITEEScreenProps> = ({ route }) => {
  const { angles, diameters, depths, morphology } = route.params;
  const sampleUrisArray = Object.values(sampleUri2); // Convert sampleUri object to array for mapping
  const numericDiameters = diameters.map((d) =>
    parseInt(d.replace(" mm", ""), 10)
  );

  // Find the maximum diameter
  const maxDiameter = Math.max(...numericDiameters);

  const morphologyData = ["Windsock", "Chicken Wing", "Cactus", "Cauliflower"];
  const [morphology1, setMorphology1] = React.useState<string>("Windsock");
  const handleRefresh = async () => {};
  return (
    <ScreenWrapper direction="column" onRefresh={handleRefresh}>
      {/* Morphology Section */}
      <CustomCard>
        <View className="mt-2">
          <Heading
            text="Morphology"
            size="sub-heading"
            color={"black"}
            showSeperator={false}
          />
          <OptionSelector
            options={morphologyData}
            selected={morphology1}
            onSelect={(value) => setMorphology1(value)}
          />
        </View>

        <LineSeperator extraStyle="my-5" />

        <View>
          <Heading
            text="Measurements"
            size="sub-heading"
            color={"black"}
            showSeperator={false}
          />
          <View className="flex-row rounded-lg bg-primaryWhite flex-wrap mt-2">
            {sampleUrisArray.map((imageUri, index) => {
              if (index % 2 === 0) {
                return (
                  <View key={index} className="flex-1 justify-between">
                    <TouchableOpacity key={index} className="p-1 w-[100%]">
                      {/* <View className="flex-row justify-end pb-2">
                      {MatIcon('plus-circle', '#8143d9', 20)}
                    </View> */}
                      <View className="flex-row justify-between">
                        <View className="flex-row mb-2">
                          <Text className="text-primaryPurple text-[12px] font-bold">
                            {`${angles[index]}°: W ${diameters[index]}`},{" "}
                            {`D ${depths[index]}`}
                          </Text>
                        </View>
                      </View>
                      <Image
                        source={imageUri}
                        className="w-[100%] h-[120px] object-fit"
                      />
                    </TouchableOpacity>

                    {/* Render the next image if it exists */}
                    {sampleUrisArray[index + 1] && (
                      <TouchableOpacity
                        key={index + 1}
                        className="p-1 pt-4 w-[100%]"
                      >
                        {/* <View className="flex-row pb-2 justify-end">
                        {MatIcon('plus-circle', '#8143d9', 20)}
                      </View> */}
                        <View className="flex-row justify-between">
                          <View className="flex-row mb-2">
                            <Text className="text-primaryPurple text-[12px] text-md font-bold ">
                              {`${angles[index + 1]}°: W ${
                                diameters[index + 1]
                              }`}
                              , {`D ${depths[index + 1]}`}
                            </Text>
                            {/* <Text className="text-primaryPurple text-left text-md font-bold">
                            {`D ${depths[index + 1]}`}
                          </Text> */}
                          </View>
                        </View>
                        <Image
                          source={sampleUrisArray[index + 1]}
                          className="w-[100%] h-[120px] object-fit"
                        />
                      </TouchableOpacity>
                    )}
                  </View>
                );
              }
            })}
          </View>
        </View>

        <View className="mt-3"></View>
      </CustomCard>

      <View className="mt-9"></View>
    </ScreenWrapper>
  );
};

export default TEEScreen;
