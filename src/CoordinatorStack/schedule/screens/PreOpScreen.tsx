import React, { useState, useRef } from "react";
import { View, TouchableOpacity, Animated, Text, Alert } from "react-native";
import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons";
import PatientDemographicsCard from "../components/PatientDemographicsCard";
import globalStyles from "../../../styles/GlobalStyles";
import { pick, types } from "react-native-document-picker";

import SiteDetailsCard from "../components/SiteDetailsCard";
import HistoryCard from "../components/HistoryCard";
import ConsultVisitCard from "../components/ConsultVisitCard";
import ProcedureDateCard from "../components/ProcedureDateCard";
import PreOpTestingCard from "../components/PreOpTesingCard";
import PreOpImagingCard from "../components/PreOpImagingCard";
import ScreenWrapper from "../../../components/ScreenWrapper";
import LineSeperator from "../../../components/LineSeperator";
import CustomCard from "../../../components/CustomCard";
import { uploadPDF } from "../../../store/rep/ScheduleStack/patientDetails/thunk";

// NEW START
import { fetchPreopDetails } from "../../../store/coordinator/ScheduleStack/preop/thunk";
import { useFocusEffect } from "@react-navigation/native";
import { AppDispatch } from "../../../store";
import { useDispatch } from "react-redux";
import { useCoordinatorSelectedPatient } from "../hooks/schedulesHooks";
import {
  useHistory,
  useLoaderAndError,
  useUserDetails,
  useTruplanStatus,
} from "../hooks/preopHooks";
import { useLoaderAndError as useProcedureDetailsLoaderAndError } from "../hooks/procedureDetailsHooks";
import Loader from "../../../components/Loader";
import Error from "../../../components/Error";
import { fetchImplantingPhysicians } from "../../../store/coordinator/ScheduleStack/addpatient/thunk";
import { fetchProcedureDetails } from "../../../store/coordinator/ScheduleStack/procedure/thunk";
import BottomSheetComponent, {
  BottomSheetRefProps,
} from "../../../components/BottomSheetComponent";
import MedicationBottomSheetComponent from "../../components/MedicationBottomSheetComponent";
import CHADCard from "../../../components/CHADCard";
import { putChadScoreDetails } from "../../../store/rep/ScheduleStack/schedules/thunk";
import HASBLEDCard from "../../../components/HASBLEDCard";
import { putHasbledScoreDetails } from "../../../store/coordinator/ScheduleStack/schedule/thunk";
import CustomText from "../../../components/CustomText";
import { useLoadersState } from "../../../RepStack/schedule/hooks/schedulesHooks";
import AddReferringProvider from "../components/AddReferringProvider";
import AddPcpProvider from "../components/AddPcpProvider";
import {
  fetchReferringProviders,
  fetchCredentialOptions as fetchReferringCredentialOptions,
} from "../../../store/common/addReferringProvider/thunk";
import {
  fetchPcpProviders,
  fetchCredentialOptions as fetchPcpCredentialOptions,
} from "../../../store/common/addPcpProvider/thunk";
import { setPreopUserDetails } from "../../../store/coordinator/ScheduleStack/preop";

// NEW END

interface IPreOpScreenProps {}

type MenuOptions =
  | "PatientDemographics"
  // | "SiteDetails"
  | "History"
  | "ConsultVisit"
  | "PreOpTesting"
  | "PreOpImaging"
  | "ScheduleProcedure";

interface AnimatedState {
  open: boolean;
  animation: Animated.Value;
}

const PreOpScreen: React.FunctionComponent<IPreOpScreenProps> = (props) => {
  // NEW START

  const dispatch = useDispatch<AppDispatch>();

  const selectedPatient = useCoordinatorSelectedPatient();

  const bottomRef = useRef<BottomSheetRefProps>(null);
  const medicationBottomSheetRef = useRef(null);
  const [disableTruPlan, setDisableTruPlan] = React.useState(false);
  const userDetails = useUserDetails();
  const chadScoreRef = useRef<BottomSheetRefProps>(null);
  const hasBledRef = useRef<BottomSheetRefProps>(null);

  // Provider addition bottom sheet refs and state
  const addReferringProviderRef = useRef<BottomSheetRefProps>(null);
  const addPcpProviderRef = useRef<BottomSheetRefProps>(null);
  const addSiteRef = useRef<BottomSheetRefProps>(null);
  const addImplantingPhysicianRef = useRef<BottomSheetRefProps>(null);
  const [addReferringProviderVisible, setAddReferringProviderVisible] =
    useState(false);
  const [addPcpProviderVisible, setAddPcpProviderVisible] = useState(false);
  const [addSiteVisible, setAddSiteVisible] = useState(false);
  const [addImplantingPhysicianVisible, setAddImplantingPhysicianVisible] =
    useState(false);

  // Store previous provider counts to detect new additions
  const [previousRefProviderCount, setPreviousRefProviderCount] = useState(0);
  const [previousPcpProviderCount, setPreviousPcpProviderCount] = useState(0);
  const [lastAddedReferringProvider, setLastAddedReferringProvider] =
    useState<string>();
  const [lastAddedPcpProvider, setLastAddedPcpProvider] = useState<string>();
  const [lastAddedSite, setLastAddedSite] = useState<string>();
  const [lastAddedImplantingPhysician, setLastAddedImplantingPhysician] =
    useState<string>();
  const { chad, hasbled } = useHistory();
  const { truplan_upload_status, cta_status, study } = useTruplanStatus();
  // const [chad, setChad] = useState<any>(
  //   selectedPatient?.patient?.cha2ds2_vasc?.calculation
  // );
  const { agendaLoader } = useLoadersState();

  const [medicationKey, setMedicationKey] = useState<number | null>(null);
  const [medicationSelectedId, setMedicationSelectedId] = useState<
    string | null
  >(null);

  const handleClose = () => {
    if (medicationBottomSheetRef.current) {
      medicationBottomSheetRef.current.stateCleanUp();
    }
  };

  const handleBottomSheetClose = (ref) => {
    ref.current?.close();
  };

  const handleMedOpen = (id?: string) => {
    setMedicationKey(Date.now());
    if (id) {
      setMedicationSelectedId(id);
    } else {
      setMedicationSelectedId(null);
    }
    bottomRef.current?.open();
  };

  const handleMedClose = () => {
    bottomRef.current?.close();
  };

  useFocusEffect(
    React.useCallback(() => {
      const fetchDetails = async () => {
        if (selectedPatient?.case_id) {
          await dispatch(fetchImplantingPhysicians());
          const res1 = await dispatch(
            fetchPreopDetails({
              case_id: selectedPatient?.case_id,
            })
          );
          const res2 = await dispatch(
            fetchProcedureDetails({
              case_id: selectedPatient?.case_id,
            })
          );
        }
      };
      fetchDetails();
    }, [
      dispatch,
      selectedPatient?.case_id,
      selectedPatient?.patient?.cha2ds2_vasc?.calculation,
    ])
  );
  // Provider sheet handlers
  const handleOpenReferringProviderSheet = () => {
    dispatch(fetchReferringCredentialOptions());
    addReferringProviderRef.current?.open();
  };

  const handleOpenPcpProviderSheet = () => {
    dispatch(fetchPcpCredentialOptions());
    addPcpProviderRef.current?.open();
  };

  const handleCloseReferringProviderSheet = () => {
    addReferringProviderRef.current?.close();
  };

  const handleClosePcpProviderSheet = () => {
    addPcpProviderRef.current?.close();
  };

  const menuItems: Record<MenuOptions, React.ReactNode> = {
    PatientDemographics: (
      <PatientDemographicsCard
        onOpenReferringProviderSheet={handleOpenReferringProviderSheet}
        onOpenPcpProviderSheet={handleOpenPcpProviderSheet}
        lastAddedReferringProvider={lastAddedReferringProvider}
        lastAddedPcpProvider={lastAddedPcpProvider}
      />
    ),
    // SiteDetails: <SiteDetailsCard />,
    History: (
      <HistoryCard
        onMedOpen={handleMedOpen}
        onMedClose={handleMedClose}
        medicationBottomSheetRef={medicationBottomSheetRef}
        chadBottomSheetRef={chadScoreRef}
        hasBledBottomSheetRef={hasBledRef}
      />
    ),
    ConsultVisit: <ConsultVisitCard />,
    PreOpTesting: <PreOpTestingCard />,
    PreOpImaging: <PreOpImagingCard />,
    ScheduleProcedure: <ProcedureDateCard />,
  };

  // NEW END
  const [expandedMenu, setExpandedMenu] = useState<MenuOptions | null>(null);
  const animatedStates = useRef<Record<MenuOptions, AnimatedState>>({
    PatientDemographics: { open: false, animation: new Animated.Value(0) },
    // SiteDetails: { open: false, animation: new Animated.Value(0) },
    History: { open: false, animation: new Animated.Value(0) },
    ConsultVisit: { open: false, animation: new Animated.Value(0) },
    PreOpTesting: { open: false, animation: new Animated.Value(0) },
    PreOpImaging: { open: false, animation: new Animated.Value(0) },
    ScheduleProcedure: { open: false, animation: new Animated.Value(0) },
  }).current;

  const toggleMenu = (menu: MenuOptions) => {
    // Collapse all other menus
    if (expandedMenu && expandedMenu !== menu) {
      Animated.timing(animatedStates[expandedMenu].animation, {
        toValue: 0,
        duration: 400,
        useNativeDriver: true,
      }).start();
    }

    const animation = animatedStates[menu].animation;
    const isOpen = expandedMenu === menu;

    Animated.timing(animation, {
      toValue: isOpen ? 0 : 1,
      duration: 400,
      useNativeDriver: true,
    }).start();

    setExpandedMenu(isOpen ? null : menu);
  };

  const uploader = async () => {
    const PdfFile = await pick({
      allowMultiSelection: false,
      type: types.pdf,
      mode: "import",
      presentationStyle: "formSheet",
    });

    if (PdfFile) {
      const uploadResponse = await dispatch(
        uploadPDF({
          case_id: selectedPatient?.case_id,
          file: PdfFile,
        })
      );

      const response = await dispatch(
        fetchPreopDetails({ case_id: selectedPatient?.case_id })
      );
      setDisableTruPlan(true);
    } else {
      Alert.alert("Please Select PDF File");
    }
  };
  const saveChadScoreDetails = async (updatedValues: any) => {
    handleBottomSheetClose(chadScoreRef);
    try {
      // First, save the CHAD score details
      const res = await dispatch(
        putChadScoreDetails({
          case_id: selectedPatient?.case_id,
          payload: updatedValues,
        })
      );

      const result = await dispatch(
        fetchPreopDetails({
          case_id: selectedPatient?.case_id,
        })
      );
    } catch (error) {
      console.error("Error updating CHAD Score:", error);
    }
  };
  const saveHasbledDetails = async (updatedValues: any) => {
    handleBottomSheetClose(hasBledRef);
    try {
      const res = await dispatch(
        putHasbledScoreDetails({
          case_id: selectedPatient?.case_id,
          payload: updatedValues,
        })
      );
      const result = await dispatch(
        fetchPreopDetails({
          case_id: selectedPatient?.case_id,
        })
      );
    } catch (error) {
      console.error("Error updating CHAD Score:", error);
    }
  };

  // Provider addition handlers
  const handleAddReferringProviderSuccess = async (newProviderId: any) => {
    if (newProviderId) {
      try {
        // Set the last added referring provider for passing to the child component
        setLastAddedReferringProvider(newProviderId);

        // Close the sheet immediately
        handleCloseReferringProviderSheet();

        // FIRST: Fetch updated preop details to get latest provider options
        await dispatch(
          fetchPreopDetails({
            case_id: selectedPatient?.case_id,
          })
        );

        // THEN: Set the new provider ID in Redux state (this will trigger AutoSave)
        dispatch(
          setPreopUserDetails({
            ...userDetails,
            patient: {
              ...userDetails?.patient,
              referring_provider: {
                ...userDetails?.patient?.referring_provider,
                selected: {
                  ...userDetails?.patient?.referring_provider?.selected,
                  id: newProviderId,
                },
              },
            },
          })
        );

        // Also fetch updated provider list for future use
        dispatch(fetchReferringProviders());
      } catch (error) {
        console.error("Error updating referring provider:", error);
        handleCloseReferringProviderSheet();
      }
    } else {
      handleCloseReferringProviderSheet();
    }
  };

  const handleAddPcpProviderSuccess = async (newProviderId: any) => {
    if (newProviderId) {
      try {
        // Set the last added PCP provider for passing to the child component
        setLastAddedPcpProvider(newProviderId);

        // Close the sheet immediately
        handleClosePcpProviderSheet();

        // FIRST: Fetch updated preop details to get latest provider options
        await dispatch(
          fetchPreopDetails({
            case_id: selectedPatient?.case_id,
          })
        );

        // THEN: Set the new PCP provider ID in Redux state (this will trigger AutoSave)
        dispatch(
          setPreopUserDetails({
            patient: {
              ...userDetails?.patient,
              pcp: {
                ...userDetails?.patient?.pcp,
                selected: {
                  ...userDetails?.patient?.pcp?.selected,
                  id: newProviderId,
                },
              },
            },
          })
        );

        // Optionally fetch updated PCP list (if such an action exists)
        dispatch(fetchPcpProviders?.());
      } catch (error) {
        console.error("Error updating PCP provider:", error);
        handleClosePcpProviderSheet();
      }
    } else {
      handleClosePcpProviderSheet();
    }
  };

  const { loader } = useLoaderAndError();
  const { loader: schedule_procedure_loader } =
    useProcedureDetailsLoaderAndError();
  const handleRefresh = async () => {
    if (!selectedPatient?.case_id) return;
    await dispatch(fetchImplantingPhysicians());
    await dispatch(
      fetchPreopDetails({
        case_id: selectedPatient?.case_id,
      })
    );
    await dispatch(
      fetchProcedureDetails({
        case_id: selectedPatient?.case_id,
      })
    );
  };

  if (loader || schedule_procedure_loader) {
    return <Loader />;
  }

  return (
    <>
      <ScreenWrapper direction="column" onRefresh={handleRefresh}>
        {agendaLoader ? (
          <View className="mt-64 flex-1 justify-center items-center">
            <Loader />
          </View>
        ) : (
          <CustomCard>
            <View className=" py-2">
              {Object.keys(menuItems).map((itemKey) => {
                const key = itemKey as MenuOptions;
                return (
                  <View key={key} className="">
                    <TouchableOpacity
                      onPress={() => toggleMenu(key)}
                      className=" flex-row justify-between items-center py-3 px-3"
                    >
                      <Text className="text-lg font-medium text-primaryBlack">
                        {key.replace(/([A-Z])/g, " $1").trim()}
                      </Text>
                      <Animated.View
                        style={{
                          transform: [
                            {
                              rotate: animatedStates[
                                key
                              ]?.animation.interpolate({
                                inputRange: [0, 1],
                                outputRange: ["0deg", "180deg"],
                              }),
                            },
                          ],
                        }}
                      >
                        <MaterialCommunityIcons
                          name="arrow-down-drop-circle-outline"
                          color="#8143d9"
                          size={25}
                        />
                      </Animated.View>
                    </TouchableOpacity>

                    {expandedMenu === key && (
                      <View style={{ overflow: "hidden" }}>
                        <Animated.View
                          style={{
                            transform: [
                              {
                                translateY: animatedStates[
                                  key
                                ]?.animation.interpolate({
                                  inputRange: [0, 1],
                                  outputRange: [-200, 0],
                                }),
                              },
                            ],
                            opacity: animatedStates[key]?.animation.interpolate(
                              {
                                inputRange: [0, 1],
                                outputRange: [0, 1],
                              }
                            ),

                            overflow: "hidden",
                          }}
                        >
                          {menuItems[key]}
                        </Animated.View>
                      </View>
                    )}

                    {Object.keys(menuItems).length - 1 !==
                    Object.keys(menuItems).indexOf(key) ? (
                      <LineSeperator extraStyle="my-3" />
                    ) : null}
                  </View>
                );
              })}
            </View>
            <LineSeperator extraStyle="mt-4 mb-3" />
            <View className={`${globalStyles.containers.flex_center} my-3`}>
              <TouchableOpacity
                className={`
            p-3 rounded-md
             ${
               (truplan_upload_status?.toLowerCase() === "pending" ||
                 truplan_upload_status?.toLowerCase() === "completed" ||
                 truplan_upload_status?.toLowerCase() === "failed") &&
               (cta_status || study?.length > 0)
                 ? `bg-primaryPurple`
                 : `bg-secondaryGray`
             }
           `}
                disabled={
                  (truplan_upload_status?.toLowerCase() === "pending" ||
                    truplan_upload_status?.toLowerCase() === "completed" ||
                    truplan_upload_status?.toLowerCase() === "failed") &&
                  (cta_status || study?.length > 0)
                    ? false
                    : true
                }
                onPress={uploader}
              >
                <View className="flex-row justify-center items-center">
                  <CustomText
                    value={
                      truplan_upload_status?.toLowerCase() === "pending" ||
                      truplan_upload_status?.toLowerCase() === "completed" ||
                      truplan_upload_status?.toLowerCase() === "failed"
                        ? "Upload TruPlan"
                        : "Processing TruPlan..."
                    }
                    className={`text-primaryWhite font-semibold`}
                  />
                </View>
              </TouchableOpacity>
            </View>
            {truplan_upload_status?.toLowerCase() === "completed" && (
              <CustomText
                value={
                  <Text className="text-green-3">
                    TruPlan is already uploaded
                  </Text>
                }
                className={`text-center`}
              />
            )}
          </CustomCard>
        )}
        <View className="mt-9"></View>
      </ScreenWrapper>
      <BottomSheetComponent
        ref={bottomRef}
        snapPoints={["75%", "85%"]}
        backgroundColor="white"
        onClose={() => handleClose()}
      >
        {
          <View className="flex-1">
            <MedicationBottomSheetComponent
              ref={medicationBottomSheetRef}
              medicineId={medicationSelectedId || ""}
              bottomSheetClose={handleMedClose}
              medKey={medicationKey}
              isPreOp={true}
            />
          </View>
        }
      </BottomSheetComponent>

      <BottomSheetComponent
        ref={chadScoreRef}
        snapPoints={["75%", "85%"]}
        backgroundColor="white"
        onClose={handleBottomSheetClose(chadScoreRef)}
      >
        {chad && <CHADCard chad={chad} onSave={saveChadScoreDetails} />}
      </BottomSheetComponent>
      <BottomSheetComponent
        ref={hasBledRef}
        snapPoints={["75%", "85%"]}
        backgroundColor="white"
        onClose={handleBottomSheetClose(hasBledRef)}
      >
        {hasbled && (
          <HASBLEDCard hasbled={hasbled} onSave={saveHasbledDetails} />
        )}
      </BottomSheetComponent>

      {/* Add Referring Provider Bottom Sheet */}
      <BottomSheetComponent
        ref={addReferringProviderRef}
        snapPoints={["75%", "85%"]}
        backgroundColor="white"
        onClose={() => addReferringProviderRef.current?.close()}
      >
        <AddReferringProvider
          onSuccess={handleAddReferringProviderSuccess}
          onCancel={() => addReferringProviderRef.current?.close()}
          bottomSheetRef={addReferringProviderRef}
        />
      </BottomSheetComponent>

      {/* Add PCP Provider Bottom Sheet */}
      <BottomSheetComponent
        ref={addPcpProviderRef}
        snapPoints={["75%", "85%"]}
        backgroundColor="white"
        onClose={() => addPcpProviderRef.current?.close()}
      >
        <AddPcpProvider
          onSuccess={handleAddPcpProviderSuccess}
          onCancel={() => addPcpProviderRef.current?.close()}
          bottomSheetRef={addPcpProviderRef}
        />
      </BottomSheetComponent>
    </>
  );
};

export default PreOpScreen;
