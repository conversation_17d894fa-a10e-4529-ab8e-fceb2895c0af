import { createAsyncThunk } from "@reduxjs/toolkit";
import api from "../../../../api/api";

const fetchAbstractorSchedules = createAsyncThunk(
  "abstractor/schedule/fetchAbstractorSchedules",
  async (
    {
      procedure_date,
    }: {
      procedure_date: string;
    },
    thunkAPI
  ) => {
    try {
      const response = await api.private.get(
        api.routes.privateRoutes.abstractor.schedule,
        {
          params: {
            procedure_date: procedure_date,
          },
        }
      );

      if (response.data && response.data.result) {
        return response.data.result;
      } else {
        return thunkAPI.rejectWithValue(
          'Invalid response format: Missing "result" field'
        );
      }
    } catch (error: any) {
      console.error("Error caught during API call:", error.message || error);
      return thunkAPI.rejectWithValue(
        error.response?.data?.message || "Failed to fetch abstractor schedules"
      );
    }
  }
);

export { fetchAbstractorSchedules };
