import * as React from "react";
import ScreenWrapper from "../../../components/ScreenWrapper";
import SearchablePicker from "../../../components/SearchablePicker";
import { Text, View } from "react-native";
import ToggleButton from "../../../components/ToggleButton";
import Heading from "../../../components/Heading";
import { useNavigation } from "@react-navigation/native";
import CustomCard from "../../../components/CustomCard";
import CustomInput from "../../../components/CustomTextInput";
import LineSeperator from "../../../components/LineSeperator";
import globalStyles from "../../../styles/GlobalStyles";
import {
  fetchTransseptalDetails,
  putTransseptalDetails,
} from "../../../store/clinician/ScheduleStack/transseptalpuncture/thunk";
import {
  useTransseptalUserDetails,
  useLoaderAndError,
  useTransseptalDetails,
} from "../hooks/transseptalPunctureHooks";
import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons";
import { useClinicianSelectedPatient } from "../hooks/schedulesHooks";
import Loader from "../../../components/Loader";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch } from "../../../store";
import { useFocusEffect } from "@react-navigation/native";
import { setTransseptalUserDetails } from "../../../store/rep/ScheduleStack/laaoProcedures/transseptalpuncture";
import {
  formatAnesthesiaOptions,
  formatTransseptalPunctureOptions,
} from "../../../utils";
import { findTransseptalDiff } from "../hooks/transseptalPunctureHooks";
import SaveActionButton from "../../../components/SaveActionButton";
import Heart from "../../../../assests/Heart";
import { parseIntInput } from "../../../utils";
import PopupModal from "../../../components/Popup";
import CustomCheckbox from "../../../components/CustomCheckboxComponent";
import {
  findAnesthesiaDiff,
  useAnesthesiaDetails,
  useAnesthesiaUserDetails,
  useLoaderAndError as useAnesthesiaLoaderAndError,
} from "../hooks/anesthesiaHooks";
import {
  fetchAnesthesiaDetails,
  putAnesthesiaDetails,
} from "../../../store/clinician/ScheduleStack/anesthesia/thunk";
import { setAnesthesiaUserDetails } from "../../../store/clinician/ScheduleStack/anesthesia";
import { setLastSavedData, setSaveStatus } from "../../../store/services";
import { simpleAutoSave } from "../../../services/simpleAutoSave";

const TransseptalPunctureScreen: React.FunctionComponent = () => {
  const dispatch = useDispatch<AppDispatch>();

  const [modalVisible, setModalVisible] = React.useState(false);

  const [popupMsg, setPopupMsg] = React.useState<string[]>([]);

  const [popupMsgType, setPopupMsgType] = React.useState("");
  const navigation = useNavigation();
  const selectedPatient = useClinicianSelectedPatient();
  const { loader, error } = useLoaderAndError();
  const { loader: anesthesiaLoader, error: anesthesiaError } =
    useAnesthesiaLoaderAndError();

  const userDetails = useTransseptalUserDetails();
  const transseptaDetails = useTransseptalDetails();
  const anesthesiaUserDetails = useAnesthesiaUserDetails();
  const anesthesiaDetails = useAnesthesiaDetails();
  const diff = findTransseptalDiff();
  const anesthesiaDiff = findAnesthesiaDiff();

  const {
    accessSheathType,
    accessSheathTypeSelected,
    act,
    transseptalAccessSystem,
    transseptalAccessSystemSelected,
    tspRecross,
    atriaSeptostompPerformed,
    tspImagingType,
    tspImagingTypeSelected,
    tspLocationType,
    tspLocationTypeSelected,
    heparinAdministred,
    heparinAdministredUnits,
  } = formatTransseptalPunctureOptions(userDetails);

  const { fluidBolusType, fluidBolusTypeSelected, laaPressure } =
    formatAnesthesiaOptions(anesthesiaUserDetails);

  const validator = () => {
    setPopupMsg([]);

    var temp = false;

    // Only validate ACT if it has a value (not null, undefined, or empty)
    if (
      act !== null &&
      act !== undefined &&
      act !== "" &&
      (act < 0 || act > 500)
    ) {
      setPopupMsg((prev) => [...prev, "ACT Value Must be Between 0 and 500"]);
      temp = true;
    }

    // Access Sheath Type is not required for auto-save, only for manual save
    // Commenting out for auto-save compatibility
    // if (
    //   accessSheathTypeSelected === null ||
    //   accessSheathTypeSelected === undefined ||
    //   accessSheathTypeSelected === ""
    // ) {
    //   setPopupMsg((prev) => [...prev, "Please Select Access Sheath Type"]);
    //   temp = true;
    // }

    // Only validate heparin units if heparin is administered and units have a value
    if (
      heparinAdministred === "Yes" &&
      heparinAdministredUnits !== null &&
      heparinAdministredUnits !== undefined &&
      heparinAdministredUnits !== "" &&
      heparinAdministredUnits > 10000
    ) {
      setPopupMsg((prev) => [
        ...prev,
        "Heparin Administered Value Must be Between 0 and 10000",
      ]);
      temp = true;
    }

    if (temp) {
      setModalVisible(true);
      return false;
    } else {
      return true;
    }
  };

  const handleHeartInput = (value: string) => {
    const id = tspLocationType?.filter((item: any) => item.label === value);

    dispatch(
      setTransseptalUserDetails({
        tsp_location: {
          ...userDetails.tsp_location,
          selected: {
            id: id[0].value,
            name: id[0].label,
          },
        },
      })
    );
  };

  const saveDetails = async (dataToSave: any): Promise<void> => {
    try {
      const isValid = validator();

      if (!isValid) {
        dispatch(
          setSaveStatus({
            screenId: "transseptalPuncture",
            status: "validation_failed",
          })
        );
        setLocalTransseptalSaveStatus("validation_failed");
        throw new Error("Validation failed");
      }

      if (!userDetails?.case_detail_id) {
        throw new Error("Missing case_detail_id");
      }

      // Make a copy of the data to avoid mutation issues
      const dataCopy = { ...dataToSave };

      // Handle conditional fields
      if (!dataCopy.heparin_administred) {
        delete dataCopy.heparin_administred_units;
      }

      // Save transseptal data only
      const res = await dispatch(
        putTransseptalDetails({
          case_details_id: userDetails.case_detail_id,
          payload: dataCopy,
        })
      );

      // Refetch transseptal data
      if (res) {
        const refetchRes = await dispatch(
          fetchTransseptalDetails({ case_id: selectedPatient?.case_id })
        );

        if (refetchRes.payload) {
          dispatch(setTransseptalUserDetails(refetchRes.payload));
        }

        const refetchAnesthesiaRes = await dispatch(
          fetchAnesthesiaDetails({ case_id: selectedPatient?.case_id })
        );

        if (refetchAnesthesiaRes.payload) {
          dispatch(setAnesthesiaUserDetails(refetchAnesthesiaRes.payload));
        }
      }

      // No longer saving anesthesia data here - let the Anesthesia screen handle that
    } catch (err) {
      console.error("Save error in TransseptalPunctureScreen:", err);
      // Re-throw the error so the autosave hook can handle it
      throw err;
    }
  };

  const handleCancel = () => {
    dispatch(
      setTransseptalUserDetails({
        ...userDetails,
        ...transseptaDetails,
      })
    );
    dispatch(
      setAnesthesiaUserDetails({
        ...anesthesiaUserDetails,
        ...anesthesiaDetails,
      })
    );
  };

  useFocusEffect(
    React.useCallback(() => {
      const fetchDetails = async () => {
        if (!selectedPatient?.case_id) return;

        // first fetch TSP details
        const tspRes = await dispatch(
          fetchTransseptalDetails({ case_id: selectedPatient?.case_id })
        );
        if (tspRes.payload) {
          dispatch(setTransseptalUserDetails(tspRes.payload));
        }

        // then fetch anesthesia details
        const anesthesiaRes = await dispatch(
          fetchAnesthesiaDetails({ case_id: selectedPatient.case_id })
        );
        if (anesthesiaRes.payload) {
          dispatch(setAnesthesiaUserDetails(anesthesiaRes.payload));
        }
      };

      fetchDetails();
    }, [dispatch, selectedPatient?.case_id])
  );

  // Get save status for transseptal puncture
  const transseptalSaveStatus = useSelector(
    (state: any) => state.common?.autoSave?.["transseptalPuncture"]?.status
  );

  // Get save status for anesthesia
  const anesthesiaSaveStatus = useSelector(
    (state: any) => state.common?.autoSave?.["anesthesia"]?.status
  );

  // Local state for fallback
  const [localTransseptalSaveStatus, setLocalTransseptalSaveStatus] =
    React.useState<string>("");
  const [localAnesthesiaSaveStatus, setLocalAnesthesiaSaveStatus] =
    React.useState<string>("");

  // Determine effective save status for each component
  const effectiveTransseptalSaveStatus =
    transseptalSaveStatus || localTransseptalSaveStatus;
  const effectiveAnesthesiaSaveStatus =
    anesthesiaSaveStatus || localAnesthesiaSaveStatus;

  // Track if user has made changes to prevent auto-navigation on initial load
  const [hasUserMadeChanges, setHasUserMadeChanges] = React.useState(false);

  // Track when user makes changes to any field
  React.useEffect(() => {
    if ((diff.hasDiff || anesthesiaDiff.hasDiff) && !hasUserMadeChanges) {
      setHasUserMadeChanges(true);
    }
  }, [diff.hasDiff, anesthesiaDiff.hasDiff, hasUserMadeChanges]);

  // Combine the save statuses to determine the overall status
  const combinedSaveStatus = React.useMemo(() => {
    // If either component is saving, show saving
    if (
      effectiveTransseptalSaveStatus === "saving" ||
      effectiveAnesthesiaSaveStatus === "saving"
    ) {
      return "saving";
    }

    // If either component has validation failed, show validation failed
    if (
      effectiveTransseptalSaveStatus === "validation_failed" ||
      effectiveAnesthesiaSaveStatus === "validation_failed"
    ) {
      return "validation_failed";
    }

    // If either component has an error, show error
    if (
      effectiveTransseptalSaveStatus === "error" ||
      effectiveAnesthesiaSaveStatus === "error"
    ) {
      return "error";
    }

    // If either component has changes detected, show changes detected
    if (
      effectiveTransseptalSaveStatus === "changes_detected" ||
      effectiveAnesthesiaSaveStatus === "changes_detected"
    ) {
      return "changes_detected";
    }

    // Only show saved if at least one component has explicitly been saved
    // This prevents showing "All changes saved" when no changes have been made yet
    if (
      // If transseptal has been explicitly saved
      effectiveTransseptalSaveStatus === "saved" ||
      // Or if anesthesia has been explicitly saved
      effectiveAnesthesiaSaveStatus === "saved"
    ) {
      return "saved";
    }

    // Default to empty string
    return "";
  }, [
    effectiveTransseptalSaveStatus,
    effectiveAnesthesiaSaveStatus,
    diff.hasDiff,
    anesthesiaDiff.hasDiff,
  ]);

  // Add effect to navigate to LAAO Implant screen when save is successful
  // React.useEffect(() => {
  //   if (effectiveTransseptalSaveStatus === "saved" && hasUserMadeChanges) {
  //     // Add a small delay to ensure the user sees the "saved" message briefly
  //     const timer = setTimeout(() => {
  //       (navigation as any).navigate("LAAO Implant");
  //     }, 1000);

  //     return () => clearTimeout(timer);
  //   }
  // }, [
  //   effectiveTransseptalSaveStatus,
  //   combinedSaveStatus,
  //   navigation,
  //   hasUserMadeChanges,
  // ]);

  /**
   * Derive the save message and styling based on the combined save status
   */
  const { saveMessage, messageStyle, iconName, iconColor } =
    React.useMemo(() => {
      let message = "";
      let style = "text-primaryPurple italic";
      let icon = "alert-circle-outline";
      let color = "#8143d9";

      switch (combinedSaveStatus) {
        case "changes_detected":
          message = "Changes detected, saving soon...";
          style = "text-primaryBlack italic";
          icon = "alert-circle-outline";
          color = "#f5c542"; // yellow
          break;
        case "saving":
          message = "Saving... Please don't close the app";
          style = "text-primaryPurple font-bold italic";
          icon = "alert-circle-outline";
          color = "#8143d9"; // purple
          break;
        case "saved":
          message = "All changes saved";
          style = "text-green-3 italic";
          icon = "check-circle-outline";
          color = "#28a745"; // green
          break;
        case "validation_failed":
          message = "Validation failed. Please check your inputs.";
          style = "text-red-3 font-bold italic";
          icon = "alert-circle-outline";
          color = "#d93025"; // red
          break;
        case "error":
          message = "Error saving changes. Please try again.";
          style = "text-red-3 font-bold italic";
          icon = "alert-circle-outline";
          color = "#d93025"; // red
          break;
      }

      return {
        saveMessage: message,
        messageStyle: style,
        iconName: icon,
        iconColor: color,
      };
    }, [
      combinedSaveStatus,
      effectiveTransseptalSaveStatus,
      effectiveAnesthesiaSaveStatus,
    ]);

  /**
   * Refs to track previous diff values to prevent unnecessary saves
   */
  const prevTransseptalDiffRef = React.useRef<string>("");
  const prevAnesthesiaDiffRef = React.useRef<string>("");

  /**
   * Ref to track if initial data has been loaded
   * This prevents autosave from triggering before data is fetched
   */
  const initialDataLoadedRef = React.useRef<boolean>(false);

  /**
   * Effect to set initialDataLoaded when data is fetched
   */
  React.useEffect(() => {
    // Check if we have valid data
    const hasValidData = userDetails?.case_detail_id !== undefined;

    if (hasValidData && !initialDataLoadedRef.current) {
      initialDataLoadedRef.current = true;
    }
  }, [userDetails]);

  /**
   * Effect to trigger autosave when transseptal data changes
   * This uses our new simpleAutoSave service with local state fallback
   */
  React.useEffect(() => {
    if (!initialDataLoadedRef.current) return;

    if (!userDetails?.case_detail_id) return;

    if (diff.hasDiff) {
      // Check if all values are undefined/null or if the object is empty
      const hasUndefinedValues = Object.values(diff.currentValues).every(
        (val) => val === undefined || val === null
      );

      // Also check if the currentValues object is empty
      const hasNoValues = Object.keys(diff.currentValues).length === 0;

      if (hasUndefinedValues || hasNoValues) return;

      const currentDiffString = JSON.stringify(diff.currentValues);

      if (currentDiffString !== prevTransseptalDiffRef.current) {
        prevTransseptalDiffRef.current = currentDiffString;

        const setStatusWithFallback = (payload: {
          screenId: string;
          status: string;
        }) => {
          if (payload.screenId === "transseptalPuncture") {
            setLocalTransseptalSaveStatus(payload.status);
          } else if (payload.screenId === "anesthesia") {
            setLocalAnesthesiaSaveStatus(payload.status);
          }
          return dispatch(setSaveStatus(payload));
        };

        simpleAutoSave(
          "transseptalPuncture",
          diff.currentValues,
          saveDetails,
          4000,
          dispatch,
          setStatusWithFallback,
          setLastSavedData
        );
      }
    }
  }, [
    diff.hasDiff,
    diff.currentValues,
    dispatch,
    setLocalTransseptalSaveStatus,
    setLocalAnesthesiaSaveStatus,
    userDetails,
  ]);

  /**
   * Effect to trigger autosave when anesthesia data changes
   * This is a separate effect to handle anesthesia changes independently
   */
  React.useEffect(() => {
    if (!initialDataLoadedRef.current) return;

    if (!userDetails?.case_detail_id) return;

    if (anesthesiaDiff && anesthesiaDiff.hasDiff) {
      // Check if all values are undefined/null or if the object is empty
      const hasUndefinedValues = Object.values(
        anesthesiaDiff.currentValues
      ).every((val) => val === undefined || val === null);

      // Also check if the currentValues object is empty
      const hasNoValues =
        Object.keys(anesthesiaDiff.currentValues).length === 0;

      if (hasUndefinedValues || hasNoValues) return;

      const currentDiffString = JSON.stringify(anesthesiaDiff.currentValues);

      if (currentDiffString !== prevAnesthesiaDiffRef.current) {
        prevAnesthesiaDiffRef.current = currentDiffString;

        const setStatusWithFallback = (payload: {
          screenId: string;
          status: string;
        }) => {
          if (payload.screenId === "transseptalPuncture") {
            setLocalTransseptalSaveStatus(payload.status);
          } else if (payload.screenId === "anesthesia") {
            setLocalAnesthesiaSaveStatus(payload.status);
          }
          return dispatch(setSaveStatus(payload));
        };

        const saveAnesthesiaWrapper = async (data: any): Promise<void> => {
          try {
            if (!userDetails?.case_detail_id) {
              console.error("Missing case_detail_id");
              throw new Error("Missing case_detail_id");
            }

            await dispatch(
              putAnesthesiaDetails({
                case_details_id: userDetails.case_detail_id,
                payload: data,
              })
            );
          } catch (err) {
            console.error("Error saving anesthesia data:", err);
            throw err;
          }
        };

        simpleAutoSave(
          "anesthesia",
          anesthesiaDiff.currentValues,
          saveAnesthesiaWrapper,
          4000,
          dispatch,
          setStatusWithFallback,
          setLastSavedData
        );
      }
    }
  }, [
    anesthesiaDiff?.hasDiff,
    anesthesiaDiff?.currentValues,
    dispatch,
    setLocalTransseptalSaveStatus,
    setLocalAnesthesiaSaveStatus,
    userDetails,
  ]);

  const handleRefresh = async () => {
    if (!selectedPatient?.case_id) return;

    try {
      // Dispatch both fetches in parallel
      const [tRes, aRes] = await Promise.all([
        dispatch(
          fetchTransseptalDetails({ case_id: selectedPatient?.case_id })
        ),
        dispatch(fetchAnesthesiaDetails({ case_id: selectedPatient?.case_id })),
      ]);

      // If Transseptal payload exists, update store
      if (tRes.payload) {
        dispatch(setTransseptalUserDetails(tRes.payload));
      }

      // If Anesthesia payload exists, update store
      if (aRes.payload) {
        dispatch(setAnesthesiaUserDetails(aRes.payload));
      }
    } catch (err) {
      console.error("Error refreshing details:", err);
    }
  };

  if (loader || anesthesiaLoader) {
    return <Loader />;
  }

  return (
    <ScreenWrapper direction="column" onRefresh={handleRefresh}>
      <CustomCard>
        {saveMessage && (
          <View
            style={{
              flexDirection: "row",
              justifyContent: "flex-end",
              alignItems: "center",
              marginVertical: 4,
            }}
          >
            <MaterialCommunityIcons
              name={iconName as any}
              color={iconColor}
              size={20}
            />
            <Text
              className={`text-sm ${messageStyle}`}
              style={{ marginLeft: 6 }}
            >
              {saveMessage}
            </Text>
          </View>
        )}
        <View className="mt-2">
          <Heading
            text="Access Sheath"
            size="sub-heading"
            showSeperator={false}
            extraStyle="pb-3"
          />

          <SearchablePicker
            items={accessSheathType}
            placeholder="Select"
            value={accessSheathTypeSelected}
            onValueChange={(option) =>
              dispatch(
                setTransseptalUserDetails({
                  tsp_access_sheath: {
                    ...(userDetails?.tsp_access_sheath || {}),
                    selected: {
                      id: option.value,
                      name: option.label,
                    },
                  },
                })
              )
            }
          />
        </View>

        <LineSeperator extraStyle="mt-4 mb-3" />

        {/* <View>
          <Heading
            text="Transseptal Access System"
            size="sub-heading"
            color="black"
            showSeperator={false}
            extraStyle="pb-3"
          />

          <SearchablePicker
            items={transseptalAccessSystem}
            placeholder="Select an option"
            value={transseptalAccessSystemSelected}
            onValueChange={(option) =>
              dispatch(
                setTransseptalUserDetails({
                  tsp_access_system: {
                    ...userDetails.tsp_access_system,
                    selected: {
                      id: option.value,
                      name: option.label,
                    },
                  },
                })
              )
            }
          />
        </View> */}

        <View className="flex-row justify-between items-center">
          <Heading
            text="TSP Recross"
            size="sub-heading"
            color="black"
            showSeperator={false}
          />
          <ToggleButton
            messages={["Yes", "No"]}
            selected={tspRecross}
            setSelected={(value) =>
              dispatch(
                setTransseptalUserDetails({
                  tsp_recross: value === "Yes" ? true : false,
                })
              )
            }
            customToggler
            width="w-[55px]"
            customColors={["#fde047", "#16a34a"]}
          />

          {/* <CustomCheckbox
            value={tspRecross}
            yesColor="yellow"
            noColor="green"
            onChange={(val) => {
              dispatch(
                setTransseptalUserDetails({
                  tsp_recross: val,
                })
              );
            }}
          /> */}
        </View>

        <LineSeperator extraStyle="mt-4 mb-3" />

        <View className="flex-row justify-between items-center">
          <Heading
            text="Atrial Septostomy Performed"
            size="sub-heading"
            color="black"
            showSeperator={false}
          />
          <ToggleButton
            messages={["Yes", "No"]}
            selected={atriaSeptostompPerformed}
            setSelected={(value) =>
              dispatch(
                setTransseptalUserDetails({
                  atrial_septostomy: value === "Yes" ? true : false,
                })
              )
            }
            customToggler
            width="w-[55px]"
            customColors={["#fde047", "#16a34a"]}
          />

          {/* <CustomCheckbox
            value={atriaSeptostompPerformed}
            yesColor="yellow"
            noColor="green"
            onChange={(val) => {
              dispatch(
                setTransseptalUserDetails({
                  atrial_septostomy: val,
                })
              );
            }}
          /> */}
        </View>

        <LineSeperator extraStyle="mt-4 mb-3" />

        {/* <View className="flex-column">
          <Heading
            text="TSP Imaging"
            size="sub-heading"
            color="black"
            showSeperator={false}
            extraStyle="pb-3"
          />
          <SearchablePicker
            items={tspImagingType}
            placeholder="Select an option"
            value={tspImagingTypeSelected}
            onValueChange={(option) =>
              dispatch(
                setTransseptalUserDetails({
                  tsp_imaging: {
                    ...userDetails.tsp_imaging,
                    selected: {
                      id: option.value,
                      name: option.label,
                    },
                  },
                })
              )
            }
          />
        </View> */}

        {/* <LineSeperator extraStyle={globalStyles.lineseperator} /> */}

        <View className="flex-column">
          <View className="">
            <Heading
              text="Final TSP Location"
              size="sub-heading"
              color="black"
              showSeperator={false}
              extraStyle="pb-3"
            />
          </View>
          {/* <SearchablePicker
            items={tspLocationType}
            placeholder="Select an option"
            value={tspLocationTypeSelected}
            onValueChange={(option) =>
              dispatch(
                setTransseptalUserDetails({
                  tsp_location: {
                    ...userDetails.tsp_location,
                    selected: {
                      id: option.value,
                      name: option.label,
                    },
                  },
                })
              )
            }
          /> */}
          {/* <View className="w-full h-[350px] pt-4">
            <Image
              source={
                tspLocationTypeSelected === 19
                  ? FinalTspIA
                  : tspLocationTypeSelected === 18
                  ? FinalTspIP
                  : tspLocationTypeSelected === 17
                  ? FinalTspSA
                  : FinalTspSP
              }
              style={{ width: "100%", height: "100%" }}
              resizeMode="fit"
            />
          </View> */}
          <Heart
            heartPlacement={tspLocationTypeSelected}
            setHeartPlacement={(value) => handleHeartInput(value)}
          />
        </View>

        <LineSeperator extraStyle="mt-4 mb-3" />
        <View className="">
          <View
            className={`
          ${globalStyles.containers.flex_between_row}

          `}
          >
            <Heading
              text="Heparin Administered (units/mL)"
              size="sub-heading"
              color="black"
              showSeperator={false}
            />
            <ToggleButton
              messages={["Yes", "No"]}
              selected={heparinAdministred}
              setSelected={(value) =>
                dispatch(
                  setTransseptalUserDetails({
                    heparin_administred: value === "Yes" ? true : false,
                  })
                )
              }
              width="w-[55px]"
            />
            {/*
            <CustomCheckbox
              value={heparinAdministred}
              yesColor="green"
              noColor="red"
              onChange={(val) => {
                dispatch(
                  setTransseptalUserDetails({
                    heparin_administred: val,
                  })
                );
              }}
            /> */}
          </View>
          {heparinAdministred === "Yes" && (
            <View className="mt-3">
              <CustomInput
                inputValue={heparinAdministredUnits?.toString()}
                placeholder="Heparin"
                error={
                  heparinAdministredUnits === undefined ||
                  heparinAdministredUnits > 10000
                }
                onInputChange={(value) =>
                  parseIntInput(
                    value,
                    (updatedValue) => {
                      dispatch(
                        setTransseptalUserDetails({
                          heparin_administred_units: updatedValue,
                        })
                      );
                    },
                    0,
                    10000
                  )
                }
                keyboardType="numeric"
              />
            </View>
          )}
        </View>

        <LineSeperator extraStyle="mt-5 mb-3" />

        <View>
          <Heading
            text="ACT (sec)"
            size="sub-heading"
            showSeperator={false}
            extraStyle="pb-3"
          />
          <CustomInput
            inputValue={act?.toString()}
            error={act < 0 || act > 500}
            onInputChange={(value) =>
              parseIntInput(
                value,
                (updatedValue) => {
                  dispatch(
                    setTransseptalUserDetails({
                      activated_clotting_time: updatedValue,
                    })
                  );
                },
                0,
                500
              )
            }
            keyboardType="numeric"
            placeholder="ACT"
          />
        </View>

        <LineSeperator extraStyle="mt-4 mb-3" />

        <View>
          <Heading
            text="Left Atrial Pressure (mmHg)"
            size="sub-heading"
            showSeperator={false}
            extraStyle="mb-3"
          />
          <CustomInput
            inputValue={laaPressure?.toString()}
            placeholder="Left Atrial Pressure"
            keyboardType="numeric"
            // error={
            //   laaPressure === 0 ||
            //   laaPressure === null ||
            //   laaPressure === undefined
            // }
            onInputChange={(value) =>
              parseIntInput(
                value,
                (updatedValue) => {
                  <View className="">
                    <View
                      className={`
          ${globalStyles.containers.flex_between_row}

          `}
                    >
                      <Heading
                        text="Heparin Administered (units/mL)"
                        size="sub-heading"
                        color="black"
                        showSeperator={false}
                      />
                      <ToggleButton
                        messages={["Yes", "No"]}
                        selected={heparinAdministred}
                        setSelected={(value) =>
                          dispatch(
                            setTransseptalUserDetails({
                              heparin_administred:
                                value === "Yes" ? true : false,
                            })
                          )
                        }
                        width="w-[55px]"
                      />
                      {/*
            <CustomCheckbox
              value={heparinAdministred}
              yesColor="green"
              noColor="red"
              onChange={(val) => {
                dispatch(
                  setTransseptalUserDetails({
                    heparin_administred: val,
                  })
                );
              }}
            /> */}
                    </View>
                    {heparinAdministred === "Yes" && (
                      <View className="mt-3">
                        <CustomInput
                          inputValue={heparinAdministredUnits?.toString()}
                          placeholder="Heparin"
                          error={
                            heparinAdministredUnits === undefined ||
                            heparinAdministredUnits > 10000
                          }
                          onInputChange={(value) =>
                            parseIntInput(
                              value,
                              (updatedValue) => {
                                dispatch(
                                  setTransseptalUserDetails({
                                    heparin_administred_units: updatedValue,
                                  })
                                );
                              },
                              0,
                              10000
                            )
                          }
                          keyboardType="numeric"
                        />
                      </View>
                    )}
                  </View>;
                  dispatch(
                    setAnesthesiaUserDetails({
                      la_pressure: updatedValue,
                    })
                  );
                },
                0,
                100
              )
            }
          />
        </View>

        <LineSeperator extraStyle="mt-4 mb-3" />

        <View>
          <Heading
            text="Fluid Bolus (mL)"
            size="sub-heading"
            showSeperator={false}
            extraStyle="mb-2"
          />

          <SearchablePicker
            items={fluidBolusType?.sort(
              (a: { label: number }, b: { label: number }) => a.label - b.label
            )}
            placeholder="Select"
            value={fluidBolusTypeSelected}
            onValueChange={(option) =>
              dispatch(
                setAnesthesiaUserDetails({
                  fluid_bolus: {
                    ...(anesthesiaUserDetails?.fluid_bolus || {}),
                    selected: {
                      id: option.value,
                      name: option.label,
                    },
                  },
                })
              )
            }
            disableError
          />
        </View>

        <LineSeperator extraStyle="my-5" />

        {/* <View className="flex-row items-center justify-center">
          <SaveActionButton
            disabled={!diff.hasDiff}
            onPress={() => saveDetails(diff.currentValues)}
            onCancel={handleCancel}
          />
        </View> */}

        <View className="mb-3 "></View>
      </CustomCard>

      <View className="mb-9 "></View>
      <PopupModal
        show={modalVisible}
        msg={popupMsg}
        status="warning"
        onClose={() => setModalVisible(false)}
      />
    </ScreenWrapper>
  );
};

export default TransseptalPunctureScreen;
