import React, { useState, useEffect } from "react";
import { TouchableOpacity, Text, ScrollView } from "react-native";
import { genUUID } from "../utils";
import CustomText from "./CustomText";

interface IFilterProps {
  options: string[];
  selectedOption: string | null;
  onSelect: (option: string) => void;
}

const Filter: React.FunctionComponent<IFilterProps> = ({
  options,
  selectedOption,
  onSelect,
}) => {
  // Initialize with the first option if no selectedOption is provided
  const [localSelection, setLocalSelection] = useState<string | null>(
    selectedOption || (options.length > 0 ? options[0] : null)
  );

  useEffect(() => {
    if (selectedOption) {
      setLocalSelection(selectedOption);
    }
  }, [selectedOption]);

  const handleSelect = (option: string) => {
    setLocalSelection(option); // Update immediately for smooth feedback
    onSelect(option); // Notify parent asynchronously
  };

  return (
    <ScrollView
      horizontal
      showsHorizontalScrollIndicator={false}
      className="h-12 flex-row w-full"
    >
      {options.map((option) => (
        <TouchableOpacity
          key={genUUID()}
          className={`border border-primaryGray min-w-[33%] flex-row justify-center items-center h-full ${
            localSelection === option ? "bg-primaryPurple" : "bg-primaryWhite"
          }`}
          onPress={() => handleSelect(option)}
        >
          <CustomText
            className={`text-center font-semibold ${
              localSelection === option ? `text-primaryWhite` : `text-greyText`
            } `}
            value={option}
          />
        </TouchableOpacity>
      ))}
    </ScrollView>
  );
};

export default Filter;
