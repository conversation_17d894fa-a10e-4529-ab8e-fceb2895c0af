import { useSelector } from "react-redux";
import { RootState } from "../../../store";

const usePatientDemographicDetails = () => {
  const demographics = useSelector(
    (state: RootState) => state.patientDetails.demographicDetails
  );
  return demographics;
};

const useGroinAccessDetails = () => {
  const groinAccess = useSelector(
    (state: RootState) => state.patientDetails.groinAccess
  );
  return groinAccess;
};

const useLoaderAndError = () => {
  const loaders = useSelector(
    (state: RootState) => state.patientDetails.loaders
  );
  const errors = useSelector((state: RootState) => state.patientDetails.errors);
  return { loaders, errors };
};

export {
  usePatientDemographicDetails,
  useLoaderAndError,
  useGroinAccessDetails,
};
