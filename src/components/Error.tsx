import * as React from "react";
import ScreenWrapper from "./ScreenWrapper";
import { View } from "react-native";
import CustomText from "./CustomText";

interface ErrorProps {
  message?: string;
}

const Error: React.FunctionComponent<ErrorProps> = ({ message }) => {
  return (
    <ScreenWrapper>
      <View className="flex-1 justify-center items-center">
        <CustomText value={message ? message : "Error occured"} />
      </View>
    </ScreenWrapper>
  );
};

export default Error;
