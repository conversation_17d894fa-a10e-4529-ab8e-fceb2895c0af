import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { PcpProvider } from "./types";
import {
  fetchPcpProviders,
  fetchPcpProviderById,
  createPcpProvider,
  updatePcpProvider,
  fetchCredentialOptions,
} from "./thunk";
import initialState from "./initialstate";

const pcpProvidersSlice = createSlice({
  name: "pcpProviders",
  initialState,
  reducers: {
    setProviderDetails: (state, action) => {
      state.userDetails = {
        ...state.userDetails,
        ...action.payload,
      };
    },
    resetProviderDetails: (state) => {
      state.userDetails = null;
    },
    setActiveTab: (
      state,
      action: PayloadAction<"referring-providers" | "pcp-providers">
    ) => {
      state.activeTab = action.payload;
    },
    showAddPcpProviderModal: (state, action: PayloadAction<boolean>) => {
      state.showAddPcpProviderModal = action.payload;
    },
    setSelectedProvider: (state, action: PayloadAction<string>) => {
      state.selectedProvider = action.payload;
    },
  },
  extraReducers: (builder) => {
    // Fetch all PCP providers
    builder.addCase(fetchPcpProviders.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(
      fetchPcpProviders.fulfilled,
      (state, action: PayloadAction<PcpProvider[]>) => {
        state.providerListDetails = action.payload;
        state.loading = false;
      }
    );
    builder.addCase(fetchPcpProviders.rejected, (state, action) => {
      state.loading = false;
      state.error = action.error.message || "Failed to fetch PCP providers";
    });

    // Fetch specific PCP provider
    builder.addCase(fetchPcpProviderById.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(
      fetchPcpProviderById.fulfilled,
      (state, action: PayloadAction<PcpProvider>) => {
        state.providerDetails = action.payload;
        state.userDetails = action.payload;
        state.loading = false;
      }
    );
    builder.addCase(fetchPcpProviderById.rejected, (state, action) => {
      state.loading = false;
      state.error = action.error.message || "Failed to fetch PCP provider";
    });

    // Update PCP provider
    builder.addCase(updatePcpProvider.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(
      updatePcpProvider.fulfilled,
      (state, action: PayloadAction<PcpProvider>) => {
        state.providerDetails = action.payload;
        state.userDetails = action.payload;

        // const index = state.providerListDetails.findIndex(
        //   (provider) => provider.id === action.payload.id
        // );
        // if (index !== -1) {
        //   state.providerListDetails[index] = action.payload;
        // }

        state.loading = false;
      }
    );
    builder.addCase(updatePcpProvider.rejected, (state, action) => {
      state.loading = false;
      state.error = action.error.message || "Failed to update PCP provider";
    });

    // Add PCP provider
    builder.addCase(createPcpProvider.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(
      createPcpProvider.fulfilled,
      (state, action: PayloadAction<PcpProvider>) => {
        // state.providerListDetails.push(action.payload);
        state.providerDetails = action.payload;
        state.userDetails = action.payload;
        // state.selectedProvider = action.payload.id;
        state.loading = false;
      }
    );
    builder.addCase(createPcpProvider.rejected, (state, action) => {
      state.loading = false;
      state.error = action.error.message || "Failed to add PCP provider";
    });

    // Fetch credential options
    builder.addCase(fetchCredentialOptions.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(
      fetchCredentialOptions.fulfilled,
      (state, action: PayloadAction<string[]>) => {
        state.credentialOptions = action.payload;
        state.loading = false;
      }
    );
    builder.addCase(fetchCredentialOptions.rejected, (state, action) => {
      state.loading = false;
      state.error =
        action.error.message || "Failed to fetch credential options";
    });
  },
});

export const {
  setProviderDetails,
  resetProviderDetails,
  setActiveTab,
  showAddPcpProviderModal,
  setSelectedProvider,
} = pcpProvidersSlice.actions;

export default pcpProvidersSlice.reducer;
