import { createAsyncThunk } from "@reduxjs/toolkit";
import api from "../../../api/api";
import { ReferringProvider } from "./types";

// Fetch all referring providers
export const fetchReferringProviders = createAsyncThunk(
  "addreferringprovider/fetchReferringProviders",
  async (_, thunkAPI) => {
    try {
      const response = await api.private.get(
        api.routes.privateRoutes.common.referring_providers_get
      );

      if (response.data?.result) {
        return response.data.result;
      }

      throw new Error('Missing "result" in API response');
    } catch (error: any) {
      console.error(
        "Error fetching referring providers:",
        error.response || error
      );
      return thunkAPI.rejectWithValue(
        error.response?.data?.message || "Failed to fetch referring providers"
      );
    }
  }
);

// Fetch specific referring provider by ID
export const fetchReferringProviderById = createAsyncThunk(
  "addreferringprovider/fetchReferringProviderById",
  async (id: string, thunkAPI) => {
    try {
      const response = await api.private.get(
        api.routes.privateRoutes.common.referring_providers_id_get(id)
      );

      if (response.data?.result) return response.data.result;

      throw new Error('Missing "result" in API response');
    } catch (error: any) {
      console.error(
        "Error fetching referring provider by ID:",
        error.response || error
      );
      return thunkAPI.rejectWithValue(
        error.response?.data?.message || "Failed to fetch referring provider"
      );
    }
  }
);

// Update (PUT) referring provider by ID
export const updateReferringProvider = createAsyncThunk(
  "addreferringprovider/updateReferringProvider",
  async (
    { id, payload }: { id: string; payload: ReferringProvider },
    thunkAPI
  ) => {
    try {
      const response = await api.private.put(
        api.routes.privateRoutes.common.referring_providers_put(id),
        payload
      );

      if (response.data?.result) return response.data.result;

      throw new Error('Missing "result" in API response');
    } catch (error: any) {
      console.error(
        "Error updating referring provider:",
        error.response || error
      );
      return thunkAPI.rejectWithValue(
        error.response?.data?.message || "Failed to update referring provider"
      );
    }
  }
);

// Create (POST) a new referring provider
export const createReferringProvider = createAsyncThunk(
  "addreferringprovider/putPostReferringProvider",
  async (payload: Omit<ReferringProvider, "id">, thunkAPI) => {
    try {
      const response = await api.private.post(
        api.routes.privateRoutes.common.referring_providers_post,
        payload
      );

      if (response.data?.result) return response.data.result;

      throw new Error('Missing "result" in API response');
    } catch (error: any) {
      console.error(
        "Error creating referring provider:",
        error.response || error
      );
      return thunkAPI.rejectWithValue(
        error.response?.data?.message || "Failed to create referring provider"
      );
    }
  }
);

// export const fetchCredentialOptions = createAsyncThunk(
//   "addreferringprovider/fetchCredentialOptions",
//   async (_, thunkAPI) => {
//     try {
//       const response = await api.private.get(
//         api.routes.privateRoutes.common.referring_providers_credentials_get
//       );

//       if (response.data) {
//         console.log(response.data, "fetchCredentialOptions");
//         return response.data;
//       }

//       throw new Error('Missing "result" in API response');
//     } catch (error: any) {
//       console.error(
//         "Error fetching credential options:",
//         error.response || error
//       );
//       return thunkAPI.rejectWithValue(
//         error.response?.data?.message || "Failed to fetch credential options"
//       );
//     }
//   }
// );

export const fetchCredentialOptions = createAsyncThunk(
  "addreferringprovider/fetchCredentialOptions",
  async (_, thunkAPI) => {
    try {
      const response = await api.private.get(
        "/referring-providers/credential-options"
      );
      if (response.data?.result) {
        return response.data.result;
      }
      throw new Error('Missing "result" in API response');
    } catch (error: any) {
      console.error(
        "Error fetching credential options:",
        error.response || error
      );
      return thunkAPI.rejectWithValue(
        error.response?.data?.message || "Failed to fetch credential options"
      );
    }
  }
);

// NPI provider lookup
export const lookupProviderByNPI = createAsyncThunk(
  "addreferringprovider/lookupProviderByNPI",
  async (npi_number: string, thunkAPI) => {
    try {
      const response = await api.private.get(
        api.routes.privateRoutes.common.npi_provider_lookup(npi_number)
      );

      if (response.data?.result) {
        return response.data.result;
      }

      throw new Error('Missing "result" in API response');
    } catch (error: any) {
      console.error(
        "Error looking up provider by NPI:",
        error.response || error
      );
      return thunkAPI.rejectWithValue(
        error.response?.data?.message || "Failed to lookup provider by NPI"
      );
    }
  }
);
