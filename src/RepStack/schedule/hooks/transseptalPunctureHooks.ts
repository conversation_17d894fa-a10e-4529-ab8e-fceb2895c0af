import { useSelector } from "react-redux";
import { RootState } from "../../../store";

const useTransseptalDetails = () => {
  const { transseptaDetails } = useSelector(
    (state: RootState) => state.rep.transseptalpuncture
  );
  return transseptaDetails;
};

const useTransseptalUserDetails = () => {
  const { userDetails } = useSelector(
    (state: RootState) => state.rep.transseptalpuncture
  );
  return userDetails;
};

const useLoaderAndError = () => {
  const loader = useSelector(
    (state: RootState) => state.rep.transseptalpuncture?.loading
  );
  const error = useSelector(
    (state: RootState) => state.rep.transseptalpuncture?.error
  );
  return { loader, error };
};

const findTransseptalDiff = () => {
  const transseptalDetails = useTransseptalDetails();
  const userDetails = useTransseptalUserDetails();

  // If either transseptalDetails or userDetails is null/undefined, no diff
  if (!transseptalDetails || !userDetails) {
    return {
      hasDiff: false,
      currentValues: {},
    };
  }

  // Helper function to safely compare values, treating undefined/null as equal
  const safeCompare = (val1: any, val2: any) => {
    // If both are null/undefined, they're equal
    if (val1 == null && val2 == null) return true;
    // If one is null/undefined and the other isn't, they're different
    if ((val1 == null) !== (val2 == null)) return false;
    // Otherwise, compare normally
    return val1 === val2;
  };

  const diff = {
    access_sheath: safeCompare(
      transseptalDetails?.tsp_access_sheath?.selected.id,
      userDetails?.tsp_access_sheath?.selected.id
    ),

    act: safeCompare(
      transseptalDetails?.activated_clotting_time,
      userDetails?.activated_clotting_time
    ),

    transseptal_access_system: safeCompare(
      transseptalDetails?.tsp_access_system?.selected.id,
      userDetails?.tsp_access_system?.selected.id
    ),

    tsp_recross: safeCompare(
      transseptalDetails?.tsp_recross,
      userDetails?.tsp_recross
    ),

    atrial_septostomy: safeCompare(
      transseptalDetails?.atrial_septostomy,
      userDetails?.atrial_septostomy
    ),

    tsp_imaging: safeCompare(
      transseptalDetails?.tsp_imaging?.selected.id,
      userDetails?.tsp_imaging?.selected.id
    ),

    final_tsp_location: safeCompare(
      transseptalDetails?.tsp_location?.selected.id,
      userDetails?.tsp_location?.selected.id
    ),

    heparin_administred: safeCompare(
      transseptalDetails?.heparin_administred,
      userDetails?.heparin_administred
    ),

    heparin_administred_units: safeCompare(
      transseptalDetails?.heparin_administred_units,
      userDetails?.heparin_administred_units
    ),
  };

  const currentValues = {
    tsp_access_sheath: userDetails?.tsp_access_sheath?.selected.id,
    activated_clotting_time: userDetails?.activated_clotting_time,
    tsp_access_system: userDetails?.tsp_access_system?.selected.id,
    tsp_recross: userDetails?.tsp_recross,
    atrial_septostomy: userDetails?.atrial_septostomy,
    tsp_imaging_type: userDetails?.tsp_imaging?.selected.id,
    tsp_location: userDetails?.tsp_location?.selected.id,
    heparin_administred: userDetails?.heparin_administred,
    heparin_administred_units: userDetails?.heparin_administred_units,
  };

  return {
    hasDiff: Object.values(diff).includes(false),
    currentValues: currentValues,
  };
};

export {
  useTransseptalDetails,
  useTransseptalUserDetails,
  useLoaderAndError,
  findTransseptalDiff,
};
