import { createAsyncThunk } from "@reduxjs/toolkit";
import api from "../../../../api/api";

const fetchFluoroscopyDetails = createAsyncThunk(
  "schedule/fetchFluoroscopyDetails",
  async ({ case_id }: { case_id: string }, thunkAPI) => {
    try {
      const response = await api.private.get(
        api.routes.privateRoutes.clinician.fluoroscopy_get(case_id)
      );

      if (response.data && response.data.result) {
        return response.data.result;
      } else {
        return thunkAPI.rejectWithValue(
          'Invalid response format: Missing "result" field'
        );
      }
    } catch (error: any) {
      console.error("Error caught during API call:", error.message || error);
      return thunkAPI.rejectWithValue(
        error.response?.data?.message || "Failed to fetch fluoroscopy details"
      );
    }
  }
);

const putFluoroscopyDetails = createAsyncThunk(
  "schedule/putFluoroscopyDetails",
  async (
    { case_details_id, payload }: { case_details_id: string; payload: any },
    thunkAPI
  ) => {
    try {
      const response = await api.private.put(
        api.routes.privateRoutes.clinician.fluoroscopy_put(case_details_id),
        payload
      );
      if (response.data && response.data.result) {
        return response.data.result;
      } else {
        return thunkAPI.rejectWithValue(
          'Invalid response format: Missing "result" field'
        );
      }
    } catch (error: any) {
      console.error("Error caught during API call:", error.message || error);
      return thunkAPI.rejectWithValue(
        error.response?.data?.message || "Failed to fetch fluoroscopy details"
      );
    }
  }
);
export { fetchFluoroscopyDetails, putFluoroscopyDetails };
