import { createSlice } from "@reduxjs/toolkit";
import initialState from "./initialstate";
import { fetchDashboardDetails } from "./thunk";

const preopSlice = createSlice({
  name: "dashboard",
  initialState,
  reducers: {
    setToken: (state, action) => {
      state.token = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchDashboardDetails.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchDashboardDetails.fulfilled, (state, action) => {
        state.loading = false;
        state.dashboardDetails = action.payload;
      })
      .addCase(fetchDashboardDetails.rejected, (state, action) => {
        state.loading = false;
        state.dashboardDetails = null;
        state.error = action.payload as string;
      });
  },
});

export const { setToken } = preopSlice.actions;
export default preopSlice.reducer;
