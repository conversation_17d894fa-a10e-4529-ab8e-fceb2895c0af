import { createAsyncThunk } from "@reduxjs/toolkit";
import api from "../../../../api/api";

const fetchProfile = createAsyncThunk(
  "profile/fetchProfile",
  async (_, thunkAPI) => {
    try {
      const response = await api.private.get(
        api.routes.privateRoutes.coordinator.profile_get
      );

      if (response.data && response.data.result) {
        return response.data.result;
      } else {
        return thunkAPI.rejectWithValue(
          'Invalid response format: Missing "result" field'
        );
      }
    } catch (error: any) {
      if (error.response) {
        console.error("Error response 3 received:", error.response);
        return thunkAPI.rejectWithValue("Failed to fetch profile");
      } else if (error.request) {
        console.error("No response received:", error.request);
        return thunkAPI.rejectWithValue("No response received from the server");
      } else {
        console.error("Error occurred:", error.message);
        return thunkAPI.rejectWithValue("An unknown error occurred");
      }
    }
  }
);

export { fetchProfile };
