interface IReferringProvider {
  value: any;
  label: any;
  id: string;
  name: string;
}

interface IImplantingPhysician {
  id: string;
  name: string;
}

interface IPostPatient {
  referring_provider_id: string;
  site_selected: string;
  first_name: string;
  last_name: string;
  middle_name?: string;
  dob: string;
  sex: string;
  mrn: string;
  ssn: string;
  referring_providers: string[];
  pcp_providers: string[];
  rationale: string;
  rationale_selected_id: string | null;
  rationale_other: string | null;
  procedure_date: string;
  procedure_time: string;
  implanting_physician_id: string;
}

export type { IReferringProvider, IImplantingPhysician, IPostPatient };
