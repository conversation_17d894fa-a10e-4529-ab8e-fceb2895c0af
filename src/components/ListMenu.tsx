import * as React from "react";
import { View, Text, ScrollView, TouchableOpacity, Image } from "react-native";
import { useNavigation } from "@react-navigation/native";
import caretRight from "../../assests/caretRight.png";
import { genUUID } from "../utils";

interface itemList {
  iconAsset?: any;
  menuName: string;
  navigateTo: string;
  params?: any;
}

interface IListMenuProps {
  listProp: itemList[];
}

const ListMenu: React.FunctionComponent<IListMenuProps> = ({ listProp }) => {
  const navigation = useNavigation();

  return (
    <ScrollView>
      {listProp.map((item, index) => (
        <TouchableOpacity
          onPress={() => {
            if (item.params) {
              navigation.navigate(item.navigateTo, item.params);
            } else {
              navigation.navigate(item.navigateTo);
            }
          }}
          key={genUUID()}
          className="flex-row justify-between items-center p-5 bg-primaryBg shadow-md"
        >
          <View className="flex-row justify-center items-center gap-5">
            {item.iconAsset && <Image source={item.iconAsset} />}
            <Text>{item.menuName}</Text>
          </View>
          <Image source={caretRight} />
        </TouchableOpacity>
      ))}
    </ScrollView>
  );
};

export default ListMenu;
