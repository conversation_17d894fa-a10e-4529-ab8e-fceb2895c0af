import { IJiraIssue, IIssueType } from "./types";

const initialState: {
  issue: IJiraIssue | null;
  loading: boolean;
  error: string | null;

  issueTypes: IIssueType[] | null;
  loadingIssueTypes: boolean;
  issueTypesError: string | null;
} = {
  issue: null,
  loading: false,
  error: null,

  issueTypes: null,
  loadingIssueTypes: false,
  issueTypesError: null,
};

export default initialState;
