import React, { useState } from "react";
import { View, Text, TouchableOpacity, FlatList } from "react-native";
import CustomCard from "../../../components/CustomCard";
import { useDispatch } from "react-redux";
import { AppDispatch } from "../../../store";
import { useClinicianSelectedPatient } from "../hooks/schedulesHooks";
import CustomText from "../../../components/CustomText";
import CustomButton from "../../../components/CustomButton";
import SearchablePicker from "../../../components/SearchablePicker";
import DatePicker from "react-native-date-picker";
import moment from "moment";

import {
  useConsultVisit,
  usePreopDetails,
  useUserDetails,
  useConsultVisitDiff,
} from "../hooks/preopHooks";
import {
  useImplantingPhysicianDetails,
  formatImplantingPhysician,
} from "../../../utils.tsx";
import LineSeperator from "../../../components/LineSeperator";
import Heading from "../../../components/Heading.tsx";

import {
  fetchPreopDetails,
  putConsultVisit,
} from "../../../store/coordinator/ScheduleStack/preop/thunk.ts";
import { setPreopUserDetails } from "../../../store/clinician/ScheduleStack/preop/index.ts";
import PopupModal from "../../../components/Popup";

interface IConsultVisitCardProps {}

const ConsultVisitCard: React.FunctionComponent<
  IConsultVisitCardProps
> = () => {
  const dispatch = useDispatch<AppDispatch>();
  const userDetails = useUserDetails();
  const preopDetails = usePreopDetails();
  const selectedPatient = useClinicianSelectedPatient();
  const useImplantingPhysicianDetail = useImplantingPhysicianDetails();
  const { implantingPhysicians } = formatImplantingPhysician(
    useImplantingPhysicianDetail
  );

  const [modalVisible, setModalVisible] = React.useState(false);

  const [popupMsg, setPopupMsg] = React.useState<string[]>([]);

  const { pre_op, post_op, schdeuled, visit_date, implantingPhysician } =
    useConsultVisit(userDetails);

  const diff = useConsultVisitDiff();

  const [procedureDate, setProcedureDate] = useState(new Date());
  const [selectedProvider, setSelectedProvider] = useState<string | null>(null);
  const [openConsultDatePicker, setOpenConsultDatePicker] = useState(false);
  const [openProcedureDatePicker, setOpenProcedureDatePicker] = useState(false);

  const validator = () => {
    setPopupMsg([]);

    var temp = false;
    if (
      implantingPhysician === "" ||
      implantingPhysician === null ||
      implantingPhysician === undefined
    ) {
      setPopupMsg((prev) => [...prev, "Please Select Implanting Physician"]);
      temp = true;
    }

    if (visit_date === null || visit_date === undefined || visit_date === "") {
      setPopupMsg((prev) => [...prev, "Please Enter Date"]);
      temp = true;
    }

    if (temp) {
      setModalVisible(true);
      return false;
    } else {
      return true;
    }
  };

  const handleSave = async () => {
    try {
      if (validator()) {
        const res = await dispatch(
          putConsultVisit({
            case_id: selectedPatient?.case_id,
            payload: diff.currentValues,
          })
        );
        if (res.payload) {
          const res = await dispatch(
            fetchPreopDetails({
              case_id: selectedPatient?.case_id,
            })
          );
        }
      }
    } catch (err) {
      console.error(err);
    }
  };

  const handleCancel = () => {
    dispatch(
      setPreopUserDetails({
        ...userDetails,
        ...preopDetails,
      })
    );
  };

  const renderVisitSection = (
    title: string | null,
    visits?: { visit_date: string | null; physician_name: string | null }[]
  ) => (
    <View>
      <CustomText
        value={title}
        color="text-primaryPurple"
        className="text-lg font-bold mb-3"
      />
      {visits && visits.length > 0 ? (
        <View className="space-y-3">
          {visits.map((visit, index) => (
            <View
              key={index}
              className="mt-2 flex-row justify-between items-center bg-primaryWhite p-3 rounded-lg shadow-sm"
            >
              <Text className="text-md font-medium text-left text-primaryBlack">
                {moment(visit.visit_date).format("MM/DD/YYYY")}
              </Text>
              <Text className="text-md font-medium text-right text-primaryPurple italic">
                {`with ${visit.physician_name || "N/A"}`}
              </Text>
            </View>
          ))}
        </View>
      ) : (
        <CustomText value="No visits found" color="text-gray-500" />
      )}
    </View>
  );

  return (
    <>
      <View className="p-3">
        <View className="rounded-md p-3 pb-6 bg-primaryBg">
          {renderVisitSection("Pre-op Testing", pre_op)}
          <LineSeperator extraStyle="my-5" color="primaryWhite" />

          {renderVisitSection("Post-op Testing", post_op)}
          <LineSeperator extraStyle="my-5" color="primaryWhite" />
          {renderVisitSection("Upcoming Visits", schdeuled)}
          {/* <LineSeperator extraStyle="my-5" color="primaryWhite" />

          <CustomText
            value="Schedule Consult Visit"
            color="text-primaryPurple"
            className="text-lg font-bold mb-3"
          />
          <View className="flex-row justify-between items-center ">
            <Text className="text-primaryBlack font-medium w-[40%]">Date</Text>
            <View className="flex-1">
              <TouchableOpacity
                onPress={() => {
                  setOpenConsultDatePicker(true);
                }}
                className="bg-primaryWhite border  border-primaryPurple h-[38px] rounded justify-center px-3"
              >
                <CustomText
                  value={
                    visit_date
                      ? moment(visit_date).format("MM/DD/YYYY")
                      : "Select Date"
                  }
                  className="text-primaryGray text-md"
                />
              </TouchableOpacity>
            </View>
          </View> */}

          {/* <View className="mt-2 flex-row justify-between items-center ">
            <Text className="text-primaryBlack font-medium w-[40%]">
              Implanting Physician
            </Text>

            <View className="flex-1">
              <SearchablePicker
                placeholder="Enter Implanting Physician"
                items={implantingPhysicians.map((physician) => ({
                  label: physician.name,
                  value: physician.id,
                }))}
                value={implantingPhysician?.toString()}
                onValueChange={(value) =>
                  dispatch(
                    setPreopUserDetails({
                      consult_visit: {
                        ...userDetails?.consult_visit,
                        physician_id: value.value,
                      },
                    })
                  )
                }
                height={38}
              />
            </View>
          </View> */}

          {/* <Text className="text-primaryPurple text-lg font-medium mb-3">
          Followup Imaging
        </Text> */}

          {/* <View className="flex-row items-center justify-between mb-3">
          <Text className="pr-12 text-primaryBlack font-medium w-[40%]">
            Procedure Schedule Date
          </Text>
          <View className="flex-1">
            <TouchableOpacity
              onPress={() => setOpenProcedureDatePicker(true)}
              className="bg-primaryWhite border border-primaryPurple h-[45px] rounded-md justify-center px-3"
            >
              <Text className="text-primaryGray">
                {moment(procedureDate).format("MM-DD-YYYY")}
              </Text>
            </TouchableOpacity>
            <DatePicker
              modal
              open={openProcedureDatePicker}
              date={procedureDate}
              mode="date"
              onConfirm={(selectedDate) => {
                setOpenProcedureDatePicker(false);
                setProcedureDate(selectedDate);
              }}
              onCancel={() => setOpenProcedureDatePicker(false)}
            />
          </View>
        </View> */}
          {/* <LineSeperator extraStyle="my-5" color="primaryWhite" />

          <View className="flex-row justify-center gap-4 ">
            <TouchableOpacity
              onPress={handleSave}
              className={`border 
                      ${
                        !diff?.hasDiff
                          ? `bg-primaryGray border-primaryGray`
                          : `bg-primaryPurple border-primaryPurple`
                      }
                       px-6 py-3 rounded-full`}
              disabled={!diff?.hasDiff}
            >
              <Text className="text-primaryWhite font-semibold">Save</Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={handleCancel}
              className="border border-primaryPurple px-6 py-3 rounded-full bg-primaryBg"
            >
              <Text className="text-primaryPurple">Cancel</Text>
            </TouchableOpacity>
          </View> */}
        </View>
      </View>
      <DatePicker
        modal
        open={openConsultDatePicker}
        date={
          visit_date ? moment(visit_date, "YYYY-MM-DD").toDate() : new Date()
        }
        onConfirm={(date) => {
          setOpenConsultDatePicker(false);
          dispatch(
            setPreopUserDetails({
              consult_visit: {
                ...userDetails?.consult_visit,
                visit_date: moment(date).format("YYYY-MM-DD"),
              },
            })
          );
        }}
        onCancel={() => setOpenConsultDatePicker(false)}
        mode={"date"}
      />

      <PopupModal
        show={modalVisible}
        msg={popupMsg}
        status="warning"
        onClose={() => setModalVisible(false)}
      />
    </>
  );
};

export default ConsultVisitCard;
