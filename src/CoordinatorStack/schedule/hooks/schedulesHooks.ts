import { useSelector } from "react-redux";
import { RootState } from "../../../store";

const useCoordinatorSchedules = () => {
  const { schedules } = useSelector(
    (state: RootState) => state.coordinator.schedule.schedules
  );
  return schedules;
};

const useCoordinatorSchedulesUserDetails = () => {
  const { userDetails } = useSelector(
    (state: RootState) => state.coordinator.schedule.schedules
  );
  return userDetails;
};

const useLoadersState = () => {
  const scheduleLoader = useSelector(
    (state: RootState) =>
      state.coordinator.schedule.schedules.loaders.scheduleLoader
  );
  return { scheduleLoader };
};

const useCoordinatorSelectedPatient = () => {
  const { selectedPatient } = useSelector(
    (state: RootState) => state.coordinator.schedule.schedules
  );
  return selectedPatient;
};

export {
  useCoordinatorSchedules,
  useCoordinatorSchedulesUserDetails,
  useLoadersState,
  useCoordinatorSelectedPatient,
};
