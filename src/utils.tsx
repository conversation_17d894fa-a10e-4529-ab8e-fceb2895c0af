import { View, Text } from "react-native";
import uuid from "react-native-uuid";
import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons";
import { IAfibBasic } from "./store/rep/ScheduleStack/afibAblation/basic/types";
import { IProcedureDetails } from "./store/rep/ScheduleStack/afibAblation/procedureDetails/types";
import {
  IImplantingPhysician,
  IRationale,
} from "./store/coordinator/ScheduleStack/addpatient/types";
import React from "react";
import { useSelector } from "react-redux";
import { RootState } from "./store";
import moment from "moment";

export interface IgenUUIDProps {}

function genUUID() {
  return uuid.v4().toString();
}
function MatIcon(name: string, color: string, size: number) {
  return (
    <View className="">
      <Text>
        {" "}
        <MaterialCommunityIcons name={name} color={color} size={size} />
      </Text>
    </View>
  );
}

const getDeviceSizeOptions = (deviceType: string) => {
  switch (deviceType) {
    case "Watchman":
      return [21, 24, 27, 30, 33];

    case "Watchman FLX":
      return [20, 24, 27, 31, 35];

    case "Watchman FLX Pro":
      return [20, 24, 27, 31, 35, 40];

    default:
      return [];
  }
};

const formatAnesthesiaOptions = (data: any) => {
  const anesthesiaType = data?.anesthesia?.options.map((option: any) => ({
    label: option.name,
    value: option.id,
  }));

  const anesthesiaTypeSelected = data?.anesthesia?.selected.id;

  const procedureImageType = data?.imaging?.options.map((option: any) => ({
    label: option.name,
    value: option.id,
  }));

  const procedureImageTypeSelected = data?.imaging?.selected.id;

  const procedureImageTypeSelectedName = data?.imaging?.selected.name;

  const catheterType = data?.catheter?.options.map((option: any) => ({
    label: option.name,
    value: option.id,
  }));

  const catheterTypeSelected = data?.catheter?.selected.id;

  const fluidBolusType = data?.fluid_bolus?.options?.map((option: any) => ({
    label: option.name?.toString(),
    value: option.id,
  }));

  const fluidBolusTypeSelected = data?.fluid_bolus?.selected.id;

  const laaPressure = data?.la_pressure;

  return {
    anesthesiaType,
    anesthesiaTypeSelected,
    procedureImageType,
    procedureImageTypeSelected,
    catheterType,
    catheterTypeSelected,
    fluidBolusType,
    fluidBolusTypeSelected,
    laaPressure,
    procedureImageTypeSelectedName,
  };
};

const formatTransseptalPunctureOptions = (data: any) => {
  const accessSheathType = data?.tsp_access_sheath?.options.map(
    (option: any) => ({
      label: option.name,
      value: option.id,
    })
  );

  const accessSheathTypeSelected = data?.tsp_access_sheath?.selected.id;

  const act = data?.activated_clotting_time;

  const transseptalAccessSystem = data?.tsp_access_system?.options.map(
    (option: any) => ({
      label: option.name,
      value: option.id,
    })
  );

  const transseptalAccessSystemSelected = data?.tsp_access_system?.selected.id;

  const tspRecross = data?.tsp_recross === true ? "Yes" : "No";
  // const tspRecross = data?.tsp_recross;

  const atriaSeptostompPerformed =
    data?.atrial_septostomy === true ? "Yes" : "No";

  // const atriaSeptostompPerformed = data?.atrial_septostomy;

  const tspImagingType = data?.tsp_imaging?.options.map((option: any) => ({
    label: option.name,
    value: option.id,
  }));

  const tspImagingTypeSelected = data?.tsp_imaging?.selected.id;

  const tspLocationType = data?.tsp_location?.options.map((option: any) => ({
    label: option.name,
    value: option.id,
  }));

  const tspLocationTypeSelected = data?.tsp_location?.selected.name;

  const heparinAdministred = data?.heparin_administred === true ? "Yes" : "No";
  // const heparinAdministred = data?.heparin_administred;

  const heparinAdministredUnits = data?.heparin_administred_units;

  return {
    accessSheathType,
    accessSheathTypeSelected,
    act,
    transseptalAccessSystem,
    transseptalAccessSystemSelected,
    tspRecross,
    atriaSeptostompPerformed,
    tspImagingType,
    tspImagingTypeSelected,
    tspLocationType,
    tspLocationTypeSelected,
    heparinAdministred,
    heparinAdministredUnits,
  };
};

const formatLaaoImplantOptions = (data: any) => {
  const accessSheathType = data?.implant_access_sheath?.options.map(
    (option: any) => ({
      label: option.name,
      value: option.id,
    })
  );

  const accessSheathTypeSelected = data?.implant_access_sheath?.selected.id;

  const selectedDevice = data?.device?.selected.id;
  const selectDeviceValue = data?.device?.selected.device_size;

  const deviceType = data?.device?.options.map((option: any) => ({
    label: option.name,
    value: option.id,
    value2: option.device_size?.map((size: any) => ({
      label: size?.toString(),
      value: size,
    })),
  }));

  const suitabilityTug = data?.suitability_tug === true ? "Passed" : "Failed";
  // const suitabilityTug = data?.suitability_tug;

  const partialRecaptures = data?.partial_recaptures === true ? "Yes" : "No";
  // const partialRecaptures = data?.partial_recaptures;

  const number_partial_recaptures = data?.no_partial_recaptures;

  const manipulationType = data?.partial_recaptures_manipulation;

  const deviceDeployed = data?.device_deployed === true ? "Yes" : "No";
  // const deviceDeployed = data?.device_deployed;

  const deviceNotDeployedInput = data?.device_not_deployed_rationale;

  const caseAborted = data?.case_aborted === true ? "Yes" : "No";
  // const caseAborted = data?.case_aborted;

  const caseAbortedInput = data?.case_aborted_rationale;

  const productChargable = data?.product_chargeable === true ? "Yes" : "No";
  // const productChargable = data?.product_chargeable;

  const productChargableInput = data?.product_not_chargeable_rationale;

  const complications_toggler =
    data?.complication?.selected?.complication_present === true ? "Yes" : "No";
  // const complications_toggler =
  //   data?.complication?.selected?.complication_present;

  const complications_type = data?.complication?.options?.map(
    (option: any) => ({
      label: option.name,
      value: option.id,
    })
  );

  const complication_selected_id = data?.complication?.selected?.id;

  const complication_selected_name = data?.complication?.selected?.name;

  const complication_other = data?.complication?.selected?.complication_other;

  return {
    accessSheathType,
    accessSheathTypeSelected,
    selectedDevice,
    deviceType,
    selectDeviceValue,
    suitabilityTug,
    partialRecaptures,
    number_partial_recaptures,
    manipulationType,
    deviceDeployed,
    deviceNotDeployedInput,
    caseAborted,
    caseAbortedInput,
    productChargable,
    productChargableInput,
    complications_type,
    complication_other,
    complication_selected_id,
    complications_toggler,
    complication_selected_name,
  };
};

const formatFluoroscopyOptions = (data: any) => {
  const creatinine_value = data?.creatinine_value;

  const fluoro_time = data?.fluoro_time;

  const fluoro_total = data?.fluoro_total;

  const total_contrast = data?.total_contrast;

  return {
    creatinine_value,
    fluoro_time,
    fluoro_total,
    total_contrast,
  };
};

const formatPassCriteriaOptions = (data: any) => {
  const position = data?.position === true ? "Passed" : "Failed";

  const anchor = data?.anchor === true ? "Passed" : "Failed";

  const leak = data?.leak;

  const leak_value = data?.leak_value;

  const compression_ratio = data?.compression_ratio.map((option: any) => ({
    label: option.angle,
    value: option.value,
  }));

  const device_sizes = data?.device?.options;

  const size_selected = data?.device?.selected;

  const width0 =
    data?.compression_ratio.find((option: any) => option.angle === 0)?.value ||
    0;

  const width45 =
    data?.compression_ratio.find((option: any) => option.angle === 45)?.value ||
    0;

  const width90 =
    data?.compression_ratio.find((option: any) => option.angle === 90)?.value ||
    0;

  const width135 =
    data?.compression_ratio.find((option: any) => option.angle === 135)
      ?.value || 0;

  const jetValue = data?.jet_value ? "Yes" : "No";

  return {
    device_sizes,
    size_selected,
    compression_ratio,
    width0,
    width45,
    width90,
    width135,
    position,
    anchor,
    leak,
    leak_value,
    jetValue,
  };
};

const formatLaaAnatomyOptions = (data: any) => {
  const morphology_selected = data?.morphology?.selected?.id;

  const morphologyOptions = data?.morphology?.options.map((option: any) => ({
    label: option.name,
    value: option.id,
  }));

  const study = data?.study;

  const complex_features = data?.complex_features;

  return {
    morphology_selected,
    morphologyOptions,
    study,
    complex_features,
  };
};

const formatReportData = (data: any) => {
  const ptName = data?.patient?.name;
  const dob = data?.patient?.dob;
  //
  const date = data?.case_details?.procedure_date;
  //
  const hospital = data?.site?.name;

  const implantingMD = data?.implanting_physician?.name;

  const caseSpecialist = data?.rep?.name;

  const laaType = data?.case_details?.morphology;

  const finalTspLocation = data?.case_details?.tsp_location;

  const device = `${data?.case_details?.device_size}`;

  const deviceName = data?.case_details?.device_name;

  const leak = data?.case_details?.leak;

  const complications = data?.case_details?.complication;

  const postDrugRx = data?.case_details?.post_drug_rx;

  const lap = data?.case_details?.la_pressure;

  const act = data?.case_details?.activated_clotting_time;

  const creatinine = data?.case_details?.creatinine_value;

  const fluoroTime = data?.case_details?.fluoro_time;

  const fluoro = data?.case_details?.fluoro_total;

  const ptRationale = data?.patient?.rationale;

  const CHAD = data?.patient?.cha2ds2_vasc;

  const referringProvider = data?.patient?.referring_provider?.name;

  const pcp = data?.patient?.pcp?.name;

  const baselineTable = data?.case_details?.baseline_tee_measurement?.reduce(
    (acc, item) => {
      acc[item.angle] = {
        value1: item.width,
        value2: item.depth,
      };
      return acc;
    },
    { tableHeading: "Baseline TEE Measurements" }
  );

  const finalMeasurements = data?.case_details?.final_measurement?.reduce(
    (acc, item) => {
      acc[item.angle] = {
        w: item.width,
        per: item.compression,
      };
      return acc;
    },
    { heading: "Final Measurements" }
  );

  return {
    ptName,
    dob,
    date,
    hospital,
    implantingMD,
    caseSpecialist,
    laaType,
    finalTspLocation,
    device,
    deviceName,
    leak,
    complications,
    postDrugRx,
    lap,
    act,
    creatinine,
    fluoroTime,
    fluoro,
    ptRationale,
    CHAD,
    referringProvider,
    pcp,
    baselineTable,
    finalMeasurements,
  };
};

const formatAfibBasicData = (afibBasicDetails: IAfibBasic | null) => {
  const groin_access_start_time = afibBasicDetails?.groin_access_start_time;

  const physicianSelected = afibBasicDetails?.physician_details?.selected?.id;
  const physicianOptions = afibBasicDetails?.physician_details?.options?.map(
    (option: any) => ({
      label: option.name,
      value: option.id,
    })
  );
  const procedure_date = afibBasicDetails?.procedure_date;
  const sheath_removal_end_time = afibBasicDetails?.sheath_removal_end_time;
  const site_details = afibBasicDetails?.site_details;
  const hospitalSelected = afibBasicDetails?.site_details?.selected.id;
  const hospitalOptions = afibBasicDetails?.site_details?.options?.map(
    (option: any) => ({
      label: option.name,
      value: option.id,
    })
  );
  const study = afibBasicDetails?.study;

  return {
    groin_access_start_time,
    physicianOptions,
    procedure_date,
    sheath_removal_end_time,
    hospitalSelected,
    hospitalOptions,
    study,
    physicianSelected,
  };
};

const formatAfibProcedureData = (data: IProcedureDetails | null) => {
  const sheath_utilized_selected = data?.sheath_utilized?.selected.id;
  const sheat_utilized_options = data?.sheath_utilized?.options.map(
    (option: any) => ({
      label: option.name,
      value: option.id,
    })
  );

  const act = data?.act_sec;

  const pvi_selected_id = data?.pvi_lesioin_sets?.selected.id;
  const pvi_options = data?.pvi_lesioin_sets?.options?.map((option: any) => ({
    label: option.name,
    value: option.id,
  }));

  const pvi_data = data?.pvi_lesioin_sets?.options?.map((option: any) => ({
    option,
  }));

  const cathered_selected_id =
    data?.pvi_lesioin_sets?.selected?.catheter_used[0]?.id || null;

  const cathered_selected_name =
    data?.pvi_lesioin_sets?.selected?.catheter_used[0]?.name || null;

  const fluoroscopy = data?.fluroscopy;

  const add_lesion_sets_selected = data?.add_lesioin_sets?.selected?.id;

  const add_lesion_sets_options = data?.add_lesioin_sets?.options.map(
    (option: any) => ({
      label: option.name,
      value: option.id,
    })
  );

  const other_selected_value =
    data?.pvi_lesioin_sets?.selected?.catheter_used[0]
      ?.csu_pvi_other_complications || "";

  const complications_toggler =
    data?.complication?.selected?.complication_present === true ? "Yes" : "No";

  const complications_type = data?.complication?.options?.map(
    (option: any) => ({
      label: option.name,
      value: option.id,
    })
  );
  const complication_selected_id = data?.complication?.selected?.id;
  const complication_selected_name = data?.complication?.selected?.name;

  const complication_other = data?.complication?.selected?.complication_other;

  return {
    sheath_utilized_selected,
    sheat_utilized_options,
    act,
    pvi_selected_id,
    pvi_options,
    pvi_data,
    cathered_selected_id,
    fluoroscopy,
    add_lesion_sets_selected,
    add_lesion_sets_options,
    complications_toggler,
    complications_type,
    complication_selected_id,
    complication_selected_name,
    complication_other,
    cathered_selected_name,
    other_selected_value,
  };
};

// const parseIntInput = (
//   value: string,
//   callback: (parsed: number) => void,
//   min: number = 0,
//   max: number = 10
// ): void => {
//   // Remove any non-numeric characters and limit to max digits
//   const filteredValue = value
//     .replace(/[^0-9]/g, "")
//     .slice(0, max.toString().length); // Limit by max digits length

//   // Parse the filtered value as an integer
//   let parsedValue = filteredValue ? parseInt(filteredValue, 10) : 0;

//   // Restrict the value within the min and max range
//   if (parsedValue < min) {
//     parsedValue = min;
//   } else if (parsedValue > max) {
//     parsedValue = max;
//   }

//   callback(parsedValue);
// };

const parseIntInput = (
  value: string,
  callback: (parsed: number) => void,
  min: number = 0,
  max?: number
): void => {
  // Remove any non-numeric characters
  const filteredValue = value.replace(/[^0-9]/g, "");

  // Parse the filtered value as an integer, defaulting to 0 if empty
  let parsedValue = filteredValue ? parseInt(filteredValue, 10) : 0;

  // Enforce that the result is no less than min
  if (parsedValue < min) {
    parsedValue = min;
  }

  // If max is provided and parsedValue exceeds it, restrict to max
  if (max !== undefined && parsedValue > max) {
    parsedValue = max;
  }

  callback(parsedValue);
};

// const parseFloatInput = (
//   value: string,
//   callback: (result: number | string) => void,
//   max: number = 10
// ): void => {
//   // Regular expression to allow numbers and at most one decimal point
//   const validInput = /^[0-9]*\.?[0-9]*$/;

//   if (validInput.test(value)) {
//     // Truncate the input to the maximum allowed characters
//     const truncatedValue = value.slice(0, max);

//     if (truncatedValue === "") {
//       // If the truncated input is empty, default to 0
//       callback(0);
//     } else if (truncatedValue.endsWith(".")) {
//       // If the truncated input ends with a decimal point, return it as a string
//       callback(truncatedValue);
//     } else {
//       // Otherwise, parse the truncated input into a float and return it
//       callback(parseFloat(truncatedValue));
//     }
//   }
// };

const parseFloatInput = (
  value: string,
  callback: (result: number | string) => void,
  min: number = 0,
  max?: number,
  decimal?: number
): void => {
  // Remove any non-numeric characters except for a single decimal point
  let filteredValue = value.replace(/[^0-9.]/g, "");

  // Ensure only one decimal point is present
  const decimalCount = (filteredValue.match(/\./g) || []).length;
  if (decimalCount > 1) {
    filteredValue = filteredValue.replace(/\.+$/, ""); // Remove extra decimals at the end
  }

  if (filteredValue === "") {
    callback(min); // Default to min if empty
    return;
  }

  if (filteredValue.endsWith(".")) {
    callback(filteredValue); // Allow trailing decimal point for user input
    return;
  }

  let parsedValue = parseFloat(filteredValue);

  // Enforce minimum value
  if (parsedValue < min) {
    parsedValue = min;
  }

  // Enforce maximum value
  if (max !== undefined && parsedValue > max) {
    parsedValue = max;
  }

  // Enforce decimal precision if provided
  if (decimal !== undefined) {
    parsedValue = parseFloat(parsedValue.toFixed(decimal));
  }

  callback(parsedValue);
};

const parseSSNInput = (
  value: string,
  callback: (formatted: string) => void
): void => {
  const filteredValue = value.replace(/[^0-9]/g, "");
  if (filteredValue.length > 9) return;

  const formattedSSN = filteredValue.replace(
    /^(\d{3})(\d{0,2})(\d{0,4})$/,
    (match, p1, p2, p3) => {
      if (p3) return `${p1}-${p2}-${p3}`;
      if (p2) return `${p1}-${p2}`;
      return p1;
    }
  );
  callback(formattedSSN);
};
const parseMRNInput = (
  value: string,
  callback: (formatted: string) => void
): void => {
  const filteredValue = value.replace(/[^a-zA-Z0-9-]/g, "");
  callback(filteredValue);
};

const parseCharInput = (value: string, callback: (value: string) => void) => {
  // Allow alphabetic characters and spaces
  const validInput = /^[a-zA-Z\s]*$/;

  // Remove any characters that aren't letters or spaces
  const filteredValue = value.replace(/[^a-zA-Z\s]/g, "");

  // Always call the callback with the filtered value
  callback(filteredValue);
};
const parseAlphaNumericInput = (value, callback) => {
  // Allow input to contain only numbers and alphabetic characters
  const validInput = /^[a-zA-Z0-9]*$/; // Regex for alphanumeric input only

  if (validInput.test(value)) {
    callback(value); // Pass the valid input to the callback
  }
};

const parseEmailInput = (value: string, callback: (value: string) => void) => {
  // Allow alphanumeric characters, @, dots, and hyphens for email addresses
  const validInput = /^[a-zA-Z0-9@._-]*$/;

  if (validInput.test(value)) {
    callback(value);
  }
};

/**
 * Parses and validates NPI number input
 * @param {string} value - The input value
 * @param {function} callback - Callback function to handle the parsed value
 * @param {function} onValidNPI - Optional callback when a valid 10-digit NPI is entered
 */
const parseNPIInput = (
  value: string,
  callback: (parsed: string) => void,
  onValidNPI?: (npi: string) => void
): void => {
  // Remove any non-numeric characters and limit to 10 digits
  const filteredValue = value.replace(/[^0-9]/g, "").slice(0, 10);

  callback(filteredValue);

  // If we have exactly 10 digits, trigger the NPI lookup
  if (filteredValue.length === 10 && onValidNPI) {
    onValidNPI(filteredValue);
  }
};

/**
 * Validates if a string is a valid 10-digit NPI number
 * @param {string} npi - The NPI number to validate
 * @returns {boolean} - Returns true if valid 10-digit NPI, otherwise false
 */
const isValidNPI = (npi: string): boolean => {
  return /^\d{10}$/.test(npi);
};

/**
 * Validates if the selected date is a weekday.
 * @param {Date} date - The selected date.
 * @returns {boolean} - Returns true if the date is a valid weekday, otherwise false.
 */

const isValidWeekday = (date) => {
  const dayOfWeek = moment(date).day(); // 0 = Sunday, 6 = Saturday
  return dayOfWeek !== 0 && dayOfWeek !== 6; // Return true for weekdays
};

function replaceUndefinedWithNull(obj: any): any {
  if (obj === undefined) {
    return null;
  } else if (Array.isArray(obj)) {
    return obj.map(replaceUndefinedWithNull);
  } else if (typeof obj === "object" && obj !== null) {
    return Object.fromEntries(
      Object.entries(obj).map(([key, value]) => [
        key,
        replaceUndefinedWithNull(value),
      ])
    );
  } else {
    return obj;
  }
}

const useImplantingPhysicianDetails = () => {
  const { implantingPhysicians } = useSelector(
    (state: RootState) => state.coordinator.schedule.implantingPhysicians
  );
  return implantingPhysicians;
};

const formatImplantingPhysician = (data: IImplantingPhysician[] | null) => {
  return {
    implantingPhysicians: data || [],
  };
};

const useRationaleDetails = () => {
  const { rationale } = useSelector(
    (state: RootState) => state.coordinator.schedule.rationaleOptions
  );
  return rationale;
};

const formatRationale = (data: IRationale[] | null) => {
  return {
    rationaleOptions: data || [],
  };
};

const useSiteList = () => {
  const { siteList } = useSelector((state: RootState) => state.schedules);
  return siteList;
};

const capitalizeName = (name: string) => {
  return name
    .split(" ") // Split the name into an array of words
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()) // Capitalize the first letter and make the rest lowercase
    .join(" "); // Join the words back into a string
};

export {
  genUUID,
  MatIcon,
  getDeviceSizeOptions,
  formatAnesthesiaOptions,
  formatTransseptalPunctureOptions,
  formatLaaoImplantOptions,
  formatFluoroscopyOptions,
  parseFloatInput,
  parseIntInput,
  parseSSNInput,
  parseMRNInput,
  parseEmailInput,
  parseNPIInput,
  isValidNPI,
  formatPassCriteriaOptions,
  formatLaaAnatomyOptions,
  formatReportData,
  formatAfibBasicData,
  formatAfibProcedureData,
  replaceUndefinedWithNull,
  useImplantingPhysicianDetails,
  formatImplantingPhysician,
  useRationaleDetails,
  formatRationale,
  parseCharInput,
  parseAlphaNumericInput,
  isValidWeekday,
  capitalizeName,
};
