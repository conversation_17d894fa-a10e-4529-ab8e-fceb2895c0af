export type Environment = "DEV" | "STAGING" | "PROD";

interface ApiConfig {
  DEV: string;
  STAGING: string;
  PROD: string;
}

interface DashboardUrlConfig {
  (token: string, dashboardId: string): string;
}

interface DashboardConfig {
  DEV: DashboardUrlConfig;
  STAGING: DashboardUrlConfig;
  PROD: DashboardUrlConfig;
}

const apiVersion = "v2";

const apiUrl: ApiConfig = {
  DEV: `https://dev-api.cormetrix.com/api/${apiVersion}`,
  STAGING: `https://staging-api.cormetrix.com/api/${apiVersion}`,
  PROD: `https://api.cormetrix.com/api/${apiVersion}`,
};

const SchedulerUrl: ApiConfig = {
  DEV: "https://dev-scheduler.cormetrix.com",
  STAGING: "https://staging-scheduler.cormetrix.com",
  PROD: "https://scheduler.cormetrix.com",
};

const dashboardUrl: DashboardConfig = {
  DEV: (token: string, dashboardId: string) =>
    `https://dev-scheduler.cormetrix.com/dashboard/dashboard.html?access_token=${token}&dashboard_id=${dashboardId}`,
  STAGING: (token: string, dashboardId: string) =>
    `https://staging-scheduler.cormetrix.com/dashboard/dashboard.html?access_token=${token}&dashboard_id=${dashboardId}`,
  PROD: (token: string, dashboardId: string) =>
    `https://scheduler.cormetrix.com/dashboard/dashboard.html?access_token=${token}&dashboard_id=${dashboardId}`,
};

const authUrl: ApiConfig = {
  DEV: "https://dev-auth.cormetrix.com/realms/Cormetrix-AtriAI",
  STAGING: "https://staging-auth.cormetrix.com/realms/Cormetrix-AtriAI",
  PROD: "https://auth.cormetrix.com/realms/Cormetrix-AtriAI",
};

export default apiUrl;

export const Enviromnent: Environment = "DEV";

export { SchedulerUrl, dashboardUrl, authUrl };
