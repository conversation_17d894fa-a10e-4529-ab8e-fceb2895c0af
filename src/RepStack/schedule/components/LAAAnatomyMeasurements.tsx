import React, { useState, useRef, useEffect } from "react";
import {
  View,
  Text,
  Platform,
  TextInput,
  Keyboard,
  InteractionManager,
} from "react-native";
import CustomInput from "../../../components/CustomTextInput";

interface LAAAnatomyMeasurement {
  label: string;
  w: number;
  d: number;
  onWChange: (value: string) => void;
  onDChange: (value: string) => void;
}

interface ILAAAnatomyMeasurementsProps {
  data: {
    [key: string]: LAAAnatomyMeasurement;
  };
}

const LAAAnatomyMeasurements: React.FunctionComponent<
  ILAAAnatomyMeasurementsProps
> = ({ data }) => {
  const measurementEntries = Object.entries(data);
  const totalFields = measurementEntries.length * 2;

  // Find the largest measurements for highlighting
  // const largestMeasurements = React.useMemo(() => {
  //   const measurements = measurementEntries.map(([key, { w, d }]) => ({
  //     key,
  //     w,
  //     d,
  //   }));

  //   // Find largest width (W) measurement
  //   const maxW = Math.max(...measurements.map((m) => m.w).filter((w) => w > 0));
  //   const largestW = measurements.find((m) => m.w === maxW && m.w > 0);

  //   // Find largest depth (D) measurement
  //   const maxD = Math.max(...measurements.map((m) => m.d).filter((d) => d > 0));
  //   const largestD = measurements.find((m) => m.d === maxD && m.d > 0);

  //   return {
  //     largestW: largestW?.key || null,
  //     largestD: largestD?.key || null,
  //     maxWValue: maxW > 0 ? maxW : null,
  //     maxDValue: maxD > 0 ? maxD : null,
  //   };
  // }, [measurementEntries]);
  const fieldRefs = useRef<Array<TextInput | null>>(
    Array(totalFields).fill(null)
  );
  const viewRefs = useRef<Array<View | null>>(
    Array(measurementEntries.length).fill(null)
  );

  const isTransitioning = useRef<boolean>(false);
  useEffect(() => {
    if (Platform.OS === "ios") {
      const keyboardDidShowListener = Keyboard.addListener(
        "keyboardDidShow",
        () => {}
      );

      const keyboardDidHideListener = Keyboard.addListener(
        "keyboardDidHide",
        () => {
          if (!isTransitioning.current) {
            setContentPosition(0);
          }

          if (isTransitioning.current && focusedField !== null) {
            const currentField = fieldRefs.current[focusedField];
            if (currentField) {
              InteractionManager.runAfterInteractions(() => {
                currentField.focus();
              });
            }
          }
        }
      );

      return () => {
        keyboardDidShowListener.remove();
        keyboardDidHideListener.remove();
      };
    }

    return undefined;
  }, [focusedField]);

  const [focusedField, setFocusedField] = useState<number | null>(null);
  const [contentPosition, setContentPosition] = useState(0);

  useEffect(() => {
    if (focusedField !== null) {
      const currentField = fieldRefs.current[focusedField];
      const rowIndex = Math.floor(focusedField / 2);

      if (Platform.OS === "ios") {
        const positionValues = [0, 80, 160, 240];
        const newPosition = positionValues[rowIndex] || rowIndex * 200;
        setContentPosition(newPosition);

        if (currentField) {
          setTimeout(() => {
            currentField.focus();
          }, 50);
        }
      } else if (currentField) {
        currentField.focus();
      }
    }
  }, [focusedField]);

  const handleInputChange = (
    fieldIndex: number,
    text: string,
    onChange: (value: string) => void
  ) => {
    const rawLength = text.length;
    let newText = text.replace(/^0+/, "");

    if (newText === "") {
      newText = "0";
    }

    onChange(newText);

    if (
      rawLength === 2 &&
      newText.length === 2 &&
      fieldIndex < totalFields - 1
    ) {
      if (Platform.OS === "ios") {
        isTransitioning.current = true;

        const currentRow = Math.floor(fieldIndex / 2);
        const nextRow = Math.floor((fieldIndex + 1) / 2);
        const isNewRow = currentRow !== nextRow;

        if (isNewRow) {
          const positionValues = [0, 60, 115, 130];
          const newPosition = positionValues[nextRow] || nextRow * 200;

          setContentPosition(newPosition);
        }

        const currentField = fieldRefs.current[fieldIndex];
        const nextField = fieldRefs.current[fieldIndex + 1];

        if (currentField && nextField) {
          if (isNewRow) {
            setFocusedField(fieldIndex + 1);

            setTimeout(() => {
              nextField.focus();

              // Reset the transition flag after a delay
              setTimeout(() => {
                isTransitioning.current = false;
              }, 300);
            }, 250);
          } else {
            InteractionManager.runAfterInteractions(() => {
              nextField.focus();
              setFocusedField(fieldIndex + 1);

              // Reset the transition flag after a delay
              setTimeout(() => {
                isTransitioning.current = false;
              }, 300);
            });
          }
        }
      } else {
        if (fieldRefs.current[fieldIndex + 1]) {
          fieldRefs.current[fieldIndex + 1]?.focus();
          setFocusedField(fieldIndex + 1);
        }
      }
    }
  };

  const handleFocus = (fieldIndex: number) => {
    // Reset the transition flag when a field is manually focused
    isTransitioning.current = false;

    // Calculate which row this field is in and adjust position
    const rowIndex = Math.floor(fieldIndex / 2);
    if (Platform.OS === "ios") {
      // Use much larger values to ensure the content moves up enough
      const positionValues = [0, 60, 115, 130];
      const newPosition = positionValues[rowIndex] || rowIndex * 200;

      // Set the new position directly
      setContentPosition(newPosition);
    }

    setFocusedField(fieldIndex);
  };

  return (
    <View style={{ flex: 1 }}>
      <View
        style={{
          transform: [{ translateY: -contentPosition }],
          // paddingBottom: 300, // Add extra padding at the bottom
          backgroundColor: "#ffffff",
        }}
      >
        <View className="flex-row justify-evenly items-center py-2 bg-lightGray rounded-md mb-2">
          <Text className="text-primaryBlack font-bold text-lg w-[20%] text-center">
            Angle
          </Text>
          <Text className="text-primaryBlack font-bold text-lg w-[30%] text-center">
            W (mm)
          </Text>
          <Text className="text-primaryBlack font-bold text-lg w-[30%] text-center">
            D (mm)
          </Text>
        </View>

        {measurementEntries.map(
          ([key, { label, w, d, onWChange, onDChange }], index) => {
            const widthFieldIndex = index * 2;
            const diameterFieldIndex = index * 2 + 1;

            return (
              <View
                key={key}
                className="bg-primaryBg rounded-md mb-2"
                ref={(ref) => (viewRefs.current[index] = ref)}
              >
                <View className="flex-row justify-evenly items-center my-2 gap-3">
                  <View className="w-[20%]">
                    <Text className="text-primaryBlack text-md text-center">
                      {label}
                    </Text>
                  </View>

                  <View className="w-[30%] items-center">
                    <CustomInput
                      ref={(ref) => {
                        if (ref && ref.focus) {
                          fieldRefs.current[widthFieldIndex] = ref;
                        }
                      }}
                      inputValue={w < 0 ? "" : w.toString()}
                      error={w === null}
                      onInputChange={(text) =>
                        handleInputChange(widthFieldIndex, text, onWChange)
                      }
                      placeholder="W"
                      keyboardType="numeric"
                      width={60}
                      height={38}
                      maxLength={2}
                      blurOnSubmit={false}
                      returnKeyType="next"
                      onFocus={() => handleFocus(widthFieldIndex)}
                      // customBorderColor={
                      //   largestMeasurements.largestW === key
                      //     ? "#16a34a"
                      //     : undefined
                      // }
                      // customBorderWidth={
                      //   largestMeasurements.largestW === key ? 4 : undefined
                      // }
                    />
                  </View>

                  <View className="w-[30%] items-center">
                    <CustomInput
                      ref={(ref) => {
                        if (ref && ref.focus) {
                          fieldRefs.current[diameterFieldIndex] = ref;
                        }
                      }}
                      inputValue={d < 0 ? "" : d.toString()}
                      error={d === null}
                      onInputChange={(text) =>
                        handleInputChange(diameterFieldIndex, text, onDChange)
                      }
                      placeholder="D"
                      keyboardType="numeric"
                      width={60}
                      height={38}
                      maxLength={2}
                      blurOnSubmit={false}
                      returnKeyType="next"
                      onFocus={() => handleFocus(diameterFieldIndex)}
                    />
                  </View>
                </View>
              </View>
            );
          }
        )}
      </View>
    </View>
  );
};

export default LAAAnatomyMeasurements;
