import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import initialState from "./initialstate";

/**
 * AutoSave slice for managing save status across different screens
 * This is a simplified version that should work more reliably
 */
const autoSaveSlice = createSlice({
  name: "autoSave",
  initialState,
  reducers: {
    setSaveStatus: (
      state,
      action: PayloadAction<{ screenId: string; status: string }>
    ) => {
      const { screenId, status } = action.payload;
      // Ensure the screenId exists in state
      if (!state[screenId]) {
        state[screenId] = { status: "", lastSavedData: null };
      }
      // Update the status
      state[screenId].status = status;
    },
    setLastSavedData: (
      state,
      action: PayloadAction<{ screenId: string; data: any }>
    ) => {
      const { screenId, data } = action.payload;
      // Ensure the screenId exists in state
      if (!state[screenId]) {
        state[screenId] = { status: "", lastSavedData: null };
      }
      // Update the last saved data
      state[screenId].lastSavedData = data;
    },
  },
});

export const { setSaveStatus, setLastSavedData } = autoSaveSlice.actions;
export default autoSaveSlice.reducer;
