import { PreopApiResponse, Medication } from "./types";

const initialState = {
  preopDetails: null as PreopApiResponse | null,
  userDetails: null as PreopApiResponse | null,
  medication: [] as Medication[],
  apiMedication: [] as Medication[],
  groinAccess: {
    case_detail_id: "",
    groin_access_end_time: "",
    groin_access_start_time: "",
    total_groin_time: "",
  },
  loading: false,
  error: null as string | null,
};

export default initialState;
