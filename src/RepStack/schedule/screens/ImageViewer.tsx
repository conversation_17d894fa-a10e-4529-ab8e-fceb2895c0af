import * as React from 'react';
import WebView from 'react-native-webview';

interface IImageViewerScreenProps {
  route: {
    params: {
      webUrl: string;
    };
  };
}

const ImageViewerScreen: React.FunctionComponent<IImageViewerScreenProps> = ({
  route,
}) => {
  const {webUrl} = route.params;
  return (
    <WebView
      source={{
        uri: webUrl,
      }}
      style={{
        flex: 1,
        backgroundColor: 'black',
        justifyContent: 'center',
        alignItems: 'center',
      }}
    />
  );
};

export default ImageViewerScreen;
