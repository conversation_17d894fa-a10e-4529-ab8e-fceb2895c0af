import { useSelector } from "react-redux";
import { RootState } from "../../../store";

const useLoaderAndError = () => {
  const loader = useSelector(
    (state: RootState) => state.clinician.laaoProcedure.fluoroscopy.loading
  );
  const error = useSelector(
    (state: RootState) => state.clinician.laaoProcedure.fluoroscopy.error
  );
  return { loader, error };
};

const useFluoroscopyDetails = () => {
  const fluoroscopyDetails = useSelector(
    (state: RootState) =>
      state.clinician.laaoProcedure.fluoroscopy.fluoroscopyDetails
  );
  return fluoroscopyDetails;
};

const useFluoroscopyUserDetails = () => {
  const userDetails = useSelector(
    (state: RootState) => state.clinician.laaoProcedure.fluoroscopy.userDetails
  );
  return userDetails;
};

const findFluoroscopyDiff = () => {
  const fluoroscopyDetails = useFluoroscopyDetails();
  const fluoroscopyUserDetails = useFluoroscopyUserDetails();

  const diff = {
    creatinine_value:
      fluoroscopyDetails?.creatinine_value ===
      fluoroscopyUserDetails?.creatinine_value
        ? null
        : fluoroscopyUserDetails?.creatinine_value,

    fluoro_time:
      fluoroscopyDetails?.fluoro_time === fluoroscopyUserDetails?.fluoro_time
        ? null
        : fluoroscopyUserDetails?.fluoro_time,

    fluoro_total:
      fluoroscopyDetails?.fluoro_total === fluoroscopyUserDetails?.fluoro_total
        ? null
        : fluoroscopyUserDetails?.fluoro_total,

    total_contrast:
      fluoroscopyDetails?.total_contrast ===
      fluoroscopyUserDetails?.total_contrast
        ? null
        : fluoroscopyUserDetails?.total_contrast,
  };

  const nonNullDiff = Object.entries(diff).reduce((acc, [key, value]) => {
    if (value !== null && value !== undefined) {
      acc[key] = value;
    }
    return acc;
  }, {});

  return {
    hasDiff: Object.values(nonNullDiff).some((value) => value !== ""),
    currentValues: nonNullDiff,
  };
};

export {
  useLoaderAndError,
  useFluoroscopyDetails,
  useFluoroscopyUserDetails,
  findFluoroscopyDiff,
};
