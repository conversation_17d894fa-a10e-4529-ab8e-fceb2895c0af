import * as React from "react";
import { Text, View } from "react-native";
import LineSeperator from "./LineSeperator";

export interface IHeadingProps {
  size: "heading" | "sub-heading" | "label";
  text: string;
  color?: "white" | "black" | any;
  extraStyle?: string;
  showSeperator?: boolean;
}

const Heading: React.FunctionComponent<IHeadingProps> = ({
  size,
  color = "black",
  text,
  extraStyle,
  showSeperator = true,
}) => {
  var headingSize =
    size === "heading"
      ? `text-xl`
      : size === "sub-heading"
      ? `text-[16px]`
      : `text-md`;

  var headingWeight =
    size === "heading"
      ? `font-bold`
      : size === "sub-heading"
      ? `font-semibold`
      : "font-medium";

  var headingColor =
    color === "white" ? "text-primaryWhite" : "text-primaryBlack";
  return (
    <View className={`${extraStyle}`}>
      <Text
        className={`${headingColor} ${headingWeight} ${headingSize}`}
        numberOfLines={2}
      >
        {text}
      </Text>
      {showSeperator ? <LineSeperator extraStyle="my-2" /> : null}
    </View>
  );
};

export default Heading;
