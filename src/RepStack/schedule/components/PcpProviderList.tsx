import React, { useEffect } from "react";
import { Text, View } from "react-native";
import { useFocusEffect } from "@react-navigation/native";
import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons";
import SearchablePicker from "../../../components/SearchablePicker";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "../../../store";
import {
  findPostPcpProviderDiff,
  usePcpLoaderAndError,
  useUserPcpProviderDetails,
  usePcpProviderDetails,
  useSelectedPcpProvider,
  usePcpCredentialOptions,
  usePcpProvidersListData,
} from "../hooks/addPcpProvider";
import {
  fetchPcpProviderById,
  fetchPcpProviders,
  updatePcpProvider,
} from "../../../store/common/addPcpProvider/thunk";
import CustomCard from "../../../components/CustomCard";
import Heading from "../../../components/Heading";
import LineSeperator from "../../../components/LineSeperator";
import SaveActionButton from "../../../components/SaveActionButton";
import Loader from "../../../components/Loader";
import CustomInput from "../../../components/CustomTextInput";
import ScreenWrapper from "../../../components/ScreenWrapper";
import { parseCharInput, parseIntInput } from "../../../utils";
import {
  setSelectedProvider,
  setProviderDetails,
  showAddPcpProviderModal,
} from "../../../store/common/addPcpProvider";
import PopupModal from "../../../components/Popup";
import { simpleAutoSave } from "../../../services/simpleAutoSave";
import { setSaveStatus, setLastSavedData } from "../../../store/services";
import { fetchCredentialOptions } from "../../../store/common/addPcpProvider/thunk";

interface PcpProviderListProps {
  onAddPress: () => void;
  onProviderSelect?: (providerId: string) => void;
}

const PcpProviderList: React.FC<PcpProviderListProps> = ({
  onAddPress,
  onProviderSelect,
}) => {
  const dispatch = useDispatch<AppDispatch>();
  const [popupMsg, setPopupMsg] = React.useState<string[]>([]);
  const [modalVisible, setModalVisible] = React.useState(false);

  // const providersListData = useSelector(
  //   (state: RootState) => state.common.pcpProviders.providerListDetails
  // );

  const providersListData = usePcpProvidersListData();

  const userDetails = useUserPcpProviderDetails();
  const providerDetails = usePcpProviderDetails();
  const selectedProvider = useSelectedPcpProvider();
  const { loader, error } = usePcpLoaderAndError();
  const credentialOptionsData = usePcpCredentialOptions();

  const {
    first_name,
    last_name,
    middle_name,
    credential,
    npi_number,
    email_id,
    phone_number,
    fax_number,
    address,
    city,
    state,
    zip_code,
    hospital_system,
  } = userDetails || {};

  const diff = findPostPcpProviderDiff();

  // Fetch credential options when component mounts
  useEffect(() => {
    dispatch(fetchCredentialOptions());
  }, [dispatch]);

  // Create a ref to track if a save is in progress
  const saveInProgressRef = React.useRef(false);

  // Create a ref to track the last saved data hash
  const lastSavedDataHashRef = React.useRef("");

  useFocusEffect(
    React.useCallback(() => {
      const fetchDetails = async () => {
        if (selectedProvider) {
          const res = await dispatch(fetchPcpProviderById(selectedProvider));
          if (res.payload) {
            dispatch(setProviderDetails(res.payload));
          }
        }
      };

      fetchDetails();
    }, [dispatch, selectedProvider])
  );

  const validator = () => {
    setPopupMsg([]);
    var temp = false;

    if (!first_name) {
      setPopupMsg((prev) => [...prev, "Please Enter First Name"]);
      temp = true;
    }

    if (!last_name) {
      setPopupMsg((prev) => [...prev, "Please Enter Last Name"]);
      temp = true;
    }

    if (!npi_number) {
      setPopupMsg((prev) => [...prev, "Please Enter NPI Number"]);
      temp = true;
    }

    if (temp) {
      setModalVisible(true);
      return false;
    } else {
      return true;
    }
  };

  const handlePcpContextAdd = (context: string) => {
    switch (context) {
      case "pcp_provider":
        dispatch(showAddPcpProviderModal(true));
        break;
      case "credential":
        // Handle credential add if needed
        break;
      default:
        console.log("Unknown context:", context);
    }
  };
  const credentialOptions = React.useMemo(() => {
    if (
      Array.isArray(credentialOptionsData) &&
      credentialOptionsData.length > 0
    ) {
      return credentialOptionsData.map((provider) => ({
        label: provider.name,
        value: provider.id,
      }));
    }
    return [];
  }, [credentialOptionsData]);

  // Transform providers data for SearchablePicker
  const providerOptions = React.useMemo(() => {
    if (Array.isArray(providersListData) && providersListData.length > 0) {
      return providersListData.map((provider) => ({
        label: provider.name,
        value: provider.id,
      }));
    }
    return [];
  }, [providersListData]);

  const handleProviderChange = async (value: {
    value: string;
    label: string;
  }) => {
    dispatch(showAddPcpProviderModal(false));
    dispatch(setSelectedProvider(value.value));
    onProviderSelect?.(value.value);

    if (value.value) {
      const res = await dispatch(fetchPcpProviderById(value.value));
      if (res.payload) {
        dispatch(setProviderDetails(res.payload));
      }
    }
  };

  // Enhanced validation for autosave - only save if data is meaningful
  const hasValidDataForSave = React.useCallback(() => {
    // Check if we have at least the required fields filled
    return first_name && last_name && npi_number;
  }, [first_name, last_name, npi_number]);

  const saveDetails = async (): Promise<void> => {
    try {
      // For autosave, check if we have meaningful data first
      if (!hasValidDataForSave()) {
        return;
      }

      const isValid = validator();

      if (!isValid) {
        dispatch(
          setSaveStatus({
            screenId: "providerDetails",
            status: "validation_failed",
          })
        );
        setLocalSaveStatus("validation_failed");
        throw new Error("Validation failed");
      }

      if (saveInProgressRef.current) {
        return;
      }

      const dataCopy = { ...diff.currentValues };
      const currentDataHash = JSON.stringify(dataCopy);

      if (currentDataHash === lastSavedDataHashRef.current) {
        return;
      }

      saveInProgressRef.current = true;

      const res = await dispatch(
        updatePcpProvider({
          id: selectedProvider,
          payload: dataCopy,
        })
      );

      if (res?.payload) {
        await dispatch(fetchPcpProviderById(selectedProvider));
        await dispatch(fetchPcpProviders());

        lastSavedDataHashRef.current = currentDataHash;
      }

      saveInProgressRef.current = false;
    } catch (err) {
      console.error("Save error:", err);
      saveInProgressRef.current = false;
      throw err;
    }
  };

  const handleCancel = () => {
    dispatch(
      setProviderDetails({
        ...userDetails,
        ...providerDetails,
      })
    );
  };

  const saveStatus = useSelector(
    (state: any) => state.common?.autoSave?.["providerDetails"]?.status
  );

  const [localSaveStatus, setLocalSaveStatus] = React.useState<string>("");
  const effectiveSaveStatus = saveStatus || localSaveStatus;

  // Derive the save message and styling based on the save status
  const { saveMessage, messageStyle, iconName, iconColor } =
    React.useMemo(() => {
      let message = "";
      let style = "text-primaryPurple italic";
      let icon = "alert-circle-outline";
      let color = "#8143d9";

      switch (effectiveSaveStatus) {
        case "changes_detected":
          message = "Changes detected, saving soon...";
          style = "text-primaryBlack italic";
          icon = "alert-circle-outline";
          color = "#f5c542"; // yellow
          break;
        case "saving":
          message = "Saving... Please don't close the app";
          style = "text-primaryPurple font-bold italic";
          icon = "alert-circle-outline";
          color = "#8143d9"; // purple
          break;
        case "saved":
          message = "All changes saved";
          style = "text-green-3 italic";
          icon = "check-circle-outline";
          color = "#28a745"; // green
          break;
        case "validation_failed":
          message = "Validation failed. Please check your inputs.";
          style = "text-red-3 font-bold italic";
          icon = "alert-circle-outline";
          color = "#d93025"; // red
          break;
        case "error":
          message = "Error saving changes. Please try again.";
          style = "text-red-3 font-bold italic";
          icon = "alert-circle-outline";
          color = "#d93025"; // red
          break;
      }

      return {
        saveMessage: message,
        messageStyle: style,
        iconName: icon,
        iconColor: color,
      };
    }, [effectiveSaveStatus]);

  // Ref to track previous diff values to prevent unnecessary saves
  const prevDiffRef = React.useRef<string>("");

  // Ref to track if initial data has been loaded
  const initialDataLoadedRef = React.useRef<boolean>(false);

  // Effect to set initialDataLoaded when data is fetched
  React.useEffect(() => {
    const hasValidData =
      selectedProvider !== undefined && userDetails !== undefined;

    if (hasValidData && !initialDataLoadedRef.current) {
      initialDataLoadedRef.current = true;
      // Store the initial state to prevent immediate autosave triggers
      if (diff.currentValues) {
        prevDiffRef.current = JSON.stringify(diff.currentValues);
      }
    }
  }, [userDetails, selectedProvider, diff.currentValues]);

  // Enhanced autosave effect with better change detection
  React.useEffect(() => {
    // Don't trigger autosave until initial data is loaded
    if (!initialDataLoadedRef.current) {
      return;
    }

    // Must have a selected provider
    if (!selectedProvider) {
      return;
    }

    // Must have actual differences
    if (!diff.hasDiff) {
      return;
    }

    // Check if all current values are undefined/empty (indicates no real changes)
    const hasUndefinedValues = Object.values(diff.currentValues).every(
      (val) => val === undefined || val === null || val === ""
    );

    if (hasUndefinedValues) {
      return;
    }

    // Check if we have meaningful data worth saving
    if (!hasValidDataForSave()) {
      return;
    }

    const currentDiffString = JSON.stringify(diff.currentValues);

    // Only proceed if the diff has actually changed
    if (currentDiffString !== prevDiffRef.current) {
      prevDiffRef.current = currentDiffString;

      const setStatusWithFallback = (payload: {
        screenId: string;
        status: string;
      }) => {
        setLocalSaveStatus(payload.status);
        return dispatch(setSaveStatus(payload));
      };

      simpleAutoSave(
        "providerDetails",
        diff.currentValues,
        saveDetails,
        4000,
        dispatch,
        setStatusWithFallback,
        setLastSavedData
      );
    }
  }, [
    diff.hasDiff,
    diff.currentValues,
    dispatch,
    selectedProvider,
    hasValidDataForSave,
  ]);

  const handleRefresh = async () => {
    try {
      // Refresh will be handled by parent component
      if (selectedProvider) {
        const providerRes = await dispatch(
          fetchPcpProviderById(selectedProvider)
        );
        if (providerRes.payload) {
          dispatch(setProviderDetails(providerRes.payload));
        }
      }
    } catch (error) {
      console.error("Error refreshing provider list:", error);
    }
  };

  return (
    <>
      {loader ? (
        <View className="mt-64">
          <Loader />
        </View>
      ) : (
        <ScreenWrapper direction="column" onRefresh={handleRefresh}>
          <CustomCard>
            {saveMessage && (
              <View
                style={{
                  flexDirection: "row",
                  justifyContent: "flex-end",
                  alignItems: "center",
                  marginVertical: 4,
                }}
              >
                <MaterialCommunityIcons
                  name={iconName}
                  color={iconColor}
                  size={20}
                />
                <Text
                  className={`text-sm ${messageStyle}`}
                  style={{ marginLeft: 6 }}
                >
                  {saveMessage}
                </Text>
              </View>
            )}
            <View className="mt-2 gap-3">
              <View className="flex-row justify-between items-center">
                <Heading
                  text="Pcp Provider"
                  size="sub-heading"
                  showSeperator={false}
                />
                <MaterialCommunityIcons
                  name="plus-circle-outline"
                  color="#8143d9"
                  size={25}
                  onPress={onAddPress}
                />
              </View>

              <View className="">
                <SearchablePicker
                  placeholder="Select Provider"
                  items={providerOptions}
                  value={selectedProvider}
                  onValueChange={handleProviderChange}
                  autoFocus={true}
                  screenContext="pcp_provider"
                  onAddPress={handlePcpContextAdd}
                />
              </View>
            </View>

            {selectedProvider && userDetails && (
              <>
                <LineSeperator extraStyle="mt-5 mb-3" />

                <View className="rounded-lg">
                  <View className="mb-3">
                    <Heading
                      text="First Name"
                      size="sub-heading"
                      showSeperator={false}
                    />
                    <View className="mt-2">
                      <CustomInput
                        inputValue={first_name}
                        placeholder="First Name"
                        onInputChange={(value) => {
                          parseCharInput(value, (parsedValue) => {
                            dispatch(
                              setProviderDetails({
                                ...userDetails,
                                first_name: parsedValue,
                              })
                            );
                          });
                        }}
                      />
                    </View>
                  </View>

                  <LineSeperator extraStyle="mt-2 mb-3" />

                  <View className="mb-3">
                    <Heading
                      text="Middle Name"
                      size="sub-heading"
                      showSeperator={false}
                    />
                    <View className="mt-2">
                      <CustomInput
                        inputValue={middle_name}
                        placeholder="Middle Name"
                        onInputChange={(value) => {
                          parseCharInput(value, (parsedValue) => {
                            dispatch(
                              setProviderDetails({
                                ...userDetails,
                                middle_name: parsedValue,
                              })
                            );
                          });
                        }}
                      />
                    </View>
                  </View>

                  <LineSeperator extraStyle="mt-2 mb-3" />

                  <View className="mb-3">
                    <Heading
                      text="Last Name"
                      size="sub-heading"
                      showSeperator={false}
                    />
                    <View className="mt-2">
                      <CustomInput
                        inputValue={last_name}
                        placeholder="Last Name"
                        onInputChange={(value) => {
                          parseCharInput(value, (parsedValue) => {
                            dispatch(
                              setProviderDetails({
                                ...userDetails,
                                last_name: parsedValue,
                              })
                            );
                          });
                        }}
                      />
                    </View>
                  </View>

                  <LineSeperator extraStyle="mt-2 mb-3" />

                  <View className="mb-3">
                    <Heading
                      text="Credential"
                      size="sub-heading"
                      showSeperator={false}
                    />
                    <View className="mt-2">
                      <SearchablePicker
                        value={credential}
                        placeholder="Select Credential"
                        items={credentialOptions.map((option) => {
                          return {
                            label: option.label,
                            value: option.value,
                          };
                        })}
                        onValueChange={(selectedItem) => {
                          dispatch(
                            setProviderDetails({
                              credential: selectedItem.value,
                            })
                          );
                        }}
                        screenContext="credential"
                        onAddPress={handlePcpContextAdd}
                        floatingLabel={false}
                        disable={true}
                        error={false}
                        disableError={false}
                      />
                    </View>
                  </View>

                  <LineSeperator extraStyle="mt-2 mb-3" />

                  <View className="mb-3">
                    <Heading
                      text="NPI Number"
                      size="sub-heading"
                      showSeperator={false}
                    />
                    <View className="mt-2">
                      <CustomInput
                        inputValue={npi_number}
                        placeholder="NPI Number"
                        onInputChange={(value) => {
                          parseIntInput(value, (parsedValue) => {
                            dispatch(
                              setProviderDetails({
                                ...userDetails,
                                npi_number: parsedValue.toString(),
                              })
                            );
                          });
                        }}
                        keyboardType="numeric"
                      />
                    </View>
                  </View>

                  <LineSeperator extraStyle="mt-2 mb-3" />

                  <View className="mb-3">
                    <Heading
                      text="Email"
                      size="sub-heading"
                      showSeperator={false}
                    />
                    <View className="mt-2">
                      <CustomInput
                        inputValue={email_id}
                        placeholder="Email"
                        onInputChange={(value) => {
                          dispatch(
                            setProviderDetails({
                              ...userDetails,
                              email_id: value,
                            })
                          );
                        }}
                        keyboardType="email-address"
                      />
                    </View>
                  </View>

                  <LineSeperator extraStyle="mt-2 mb-3" />

                  <View className="mb-3">
                    <Heading
                      text="Phone Number"
                      size="sub-heading"
                      showSeperator={false}
                    />
                    <View className="mt-2">
                      <CustomInput
                        inputValue={phone_number}
                        placeholder="Phone Number"
                        onInputChange={(value) => {
                          parseIntInput(value, (parsedValue) => {
                            dispatch(
                              setProviderDetails({
                                ...userDetails,
                                phone_number: parsedValue.toString(),
                              })
                            );
                          });
                        }}
                        keyboardType="phone-pad"
                      />
                    </View>
                  </View>

                  <LineSeperator extraStyle="mt-2 mb-3" />

                  <View className="mb-3">
                    <Heading
                      text="Fax Number"
                      size="sub-heading"
                      showSeperator={false}
                    />
                    <View className="mt-2">
                      <CustomInput
                        inputValue={fax_number}
                        placeholder="Fax Number"
                        onInputChange={(value) => {
                          parseIntInput(value, (parsedValue) => {
                            dispatch(
                              setProviderDetails({
                                ...userDetails,
                                fax_number: parsedValue.toString(),
                              })
                            );
                          });
                        }}
                        keyboardType="phone-pad"
                      />
                    </View>
                  </View>

                  <LineSeperator extraStyle="mt-2 mb-3" />

                  <View className="mb-3">
                    <Heading
                      text="Address"
                      size="sub-heading"
                      showSeperator={false}
                    />
                    <View className="mt-2">
                      <CustomInput
                        inputValue={address}
                        placeholder="Address"
                        onInputChange={(value) => {
                          dispatch(
                            setProviderDetails({
                              ...userDetails,
                              address: value,
                            })
                          );
                        }}
                      />
                    </View>
                  </View>

                  <LineSeperator extraStyle="mt-2 mb-3" />

                  <View className="mb-3">
                    <Heading
                      text="City"
                      size="sub-heading"
                      showSeperator={false}
                    />
                    <View className="mt-2">
                      <CustomInput
                        inputValue={city}
                        placeholder="City"
                        onInputChange={(value) => {
                          parseCharInput(value, (parsedValue) => {
                            dispatch(
                              setProviderDetails({
                                ...userDetails,
                                city: parsedValue,
                              })
                            );
                          });
                        }}
                      />
                    </View>
                  </View>

                  <LineSeperator extraStyle="mt-2 mb-3" />

                  <View className="mb-3">
                    <Heading
                      text="State"
                      size="sub-heading"
                      showSeperator={false}
                    />
                    <View className="mt-2">
                      <CustomInput
                        inputValue={state}
                        placeholder="State"
                        onInputChange={(value) => {
                          parseCharInput(value, (parsedValue) => {
                            dispatch(
                              setProviderDetails({
                                ...userDetails,
                                state: parsedValue,
                              })
                            );
                          });
                        }}
                      />
                    </View>
                  </View>

                  <LineSeperator extraStyle="mt-2 mb-3" />

                  <View className="mb-3">
                    <Heading
                      text="Zip Code"
                      size="sub-heading"
                      showSeperator={false}
                    />
                    <View className="mt-2">
                      <CustomInput
                        inputValue={zip_code}
                        placeholder="Zip Code"
                        onInputChange={(value) => {
                          parseIntInput(value, (parsedValue) => {
                            dispatch(
                              setProviderDetails({
                                ...userDetails,
                                zip_code: parsedValue.toString(),
                              })
                            );
                          });
                        }}
                        keyboardType="numeric"
                      />
                    </View>
                  </View>

                  <LineSeperator extraStyle="mt-2 mb-3" />

                  <View className="mb-3">
                    <Heading
                      text="Hospital System"
                      size="sub-heading"
                      showSeperator={false}
                    />
                    <View className="mt-2">
                      <CustomInput
                        inputValue={hospital_system}
                        placeholder="Hospital System"
                        onInputChange={(value) => {
                          dispatch(
                            setProviderDetails({
                              ...userDetails,
                              hospital_system: value,
                            })
                          );
                        }}
                      />
                    </View>
                  </View>
                </View>

                <LineSeperator extraStyle="my-5" />
              </>
            )}
            <View className="mb-3"></View>
          </CustomCard>
        </ScreenWrapper>
      )}
      <View className="mt-9"></View>

      <PopupModal
        show={modalVisible}
        msg={popupMsg}
        status="warning"
        onClose={() => setModalVisible(false)}
      />
    </>
  );
};

export default PcpProviderList;

// import React, { useEffect } from "react";
// import { Text, View } from "react-native";
// import { useFocusEffect } from "@react-navigation/native";
// import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons";
// import SearchablePicker from "../../../components/SearchablePicker";
// import { useDispatch, useSelector } from "react-redux";
// import { AppDispatch, RootState } from "../../../store";
// import {
//   findPostPcpProviderDiff,
//   usePcpLoaderAndError,
//   useUserPcpProviderDetails,
//   usePcpProviderDetails,
//   useSelectedPcpProvider,
//   usePcpCredentialOptions,
// } from "../hooks/addPcpProvider";
// import {
//   fetchPcpProviderById,
//   fetchPcpProviders,
//   updatePcpProvider,
// } from "../../../store/common/addPcpProvider/thunk";
// import CustomCard from "../../../components/CustomCard";
// import Heading from "../../../components/Heading";
// import LineSeperator from "../../../components/LineSeperator";
// import SaveActionButton from "../../../components/SaveActionButton";
// import Loader from "../../../components/Loader";
// import CustomInput from "../../../components/CustomTextInput";
// import ScreenWrapper from "../../../components/ScreenWrapper";
// import { parseCharInput, parseIntInput } from "../../../utils";
// import {
//   setSelectedProvider,
//   setProviderDetails,
//   showAddPcpProviderModal,
// } from "../../../store/common/addPcpProvider";
// import PopupModal from "../../../components/Popup";
// import { simpleAutoSave } from "../../../services/simpleAutoSave";
// import { setSaveStatus, setLastSavedData } from "../../../store/services";
// import { fetchCredentialOptions } from "../../../store/common/addPcpProvider/thunk";

// interface PcpProviderListProps {
//   onAddPress: () => void;
//   onProviderSelect?: (providerId: string) => void;
// }

// const PcpProviderList: React.FC<PcpProviderListProps> = ({
//   onAddPress,
//   onProviderSelect,
// }) => {
//   const dispatch = useDispatch<AppDispatch>();
//   const [popupMsg, setPopupMsg] = React.useState<string[]>([]);
//   const [modalVisible, setModalVisible] = React.useState(false);

//   const providersListData = useSelector(
//     (state: RootState) => state.common.pcpProviders.providerListDetails
//   );

//   const userDetails = useUserPcpProviderDetails();
//   const providerDetails = usePcpProviderDetails();
//   const selectedProvider = useSelectedPcpProvider();
//   const { loader, error } = usePcpLoaderAndError();
//   const credentialOptionsData = usePcpCredentialOptions(); // Added this hook

//   const {
//     first_name,
//     last_name,
//     middle_name,
//     credential,
//     npi_number,
//     email_id,
//     phone_number,
//     fax_number,
//     address,
//     city,
//     state,
//     zip_code,
//     hospital_system,
//   } = userDetails || {};

//   const diff = findPostPcpProviderDiff();

//   // Fetch credential options when component mounts
//   useEffect(() => {
//     dispatch(fetchCredentialOptions());
//   }, [dispatch]);

//   // Create a ref to track if a save is in progress
//   const saveInProgressRef = React.useRef(false);

//   // Create a ref to track the last saved data hash
//   const lastSavedDataHashRef = React.useRef("");

//   useFocusEffect(
//     React.useCallback(() => {
//       const fetchDetails = async () => {
//         if (selectedProvider) {
//           const res = await dispatch(fetchPcpProviderById(selectedProvider));
//           if (res.payload) {
//             dispatch(setProviderDetails(res.payload));
//           }
//         }
//       };

//       fetchDetails();
//     }, [dispatch, selectedProvider])
//   );

//   const validator = () => {
//     setPopupMsg([]);
//     var temp = false;

//     if (!first_name) {
//       setPopupMsg((prev) => [...prev, "Please Enter First Name"]);
//       temp = true;
//     }

//     if (!last_name) {
//       setPopupMsg((prev) => [...prev, "Please Enter Last Name"]);
//       temp = true;
//     }

//     if (!npi_number) {
//       setPopupMsg((prev) => [...prev, "Please Enter NPI Number"]);
//       temp = true;
//     }

//     if (temp) {
//       setModalVisible(true);
//       return false;
//     } else {
//       return true;
//     }
//   };

//   const credentialOptions = React.useMemo(() => {
//     if (
//       Array.isArray(credentialOptionsData) &&
//       credentialOptionsData.length > 0
//     ) {
//       return credentialOptionsData.map((provider) => ({
//         label: provider.name,
//         value: provider.id,
//       }));
//     }
//     return [];
//   }, [credentialOptionsData]);

//   console.log("credentialOptions:", credentialOptionsData);

//   // FIXED: Transform providers data for SearchablePicker using the correct field
//   const providerOptions = React.useMemo(() => {
//     if (Array.isArray(providersListData) && providersListData.length > 0) {
//       return providersListData.map((provider) => ({
//         label: provider.name,
//         value: provider.id,
//       }));
//     }
//     return [];
//   }, [providersListData]);

//   // console.log("providerOptions", providerOptions);
//   // console.log("providersListData", providersListData);

//   const handleProviderChange = async (value: {
//     value: string;
//     label: string;
//   }) => {
//     dispatch(showAddPcpProviderModal(false));
//     dispatch(setSelectedProvider(value.value));
//     onProviderSelect?.(value.value);

//     if (value.value) {
//       const res = await dispatch(fetchPcpProviderById(value.value));
//       if (res.payload) {
//         dispatch(setProviderDetails(res.payload));
//       }
//     }
//   };

//   console.log("selectedProvider", selectedProvider);

//   const saveDetails = async (): Promise<void> => {
//     try {
//       const isValid = validator();

//       if (!isValid) {
//         dispatch(
//           setSaveStatus({
//             screenId: "providerDetails",
//             status: "validation_failed",
//           })
//         );
//         setLocalSaveStatus("validation_failed");
//         throw new Error("Validation failed");
//       }

//       if (saveInProgressRef.current) {
//         return;
//       }

//       const dataCopy = { ...diff.currentValues };
//       const currentDataHash = JSON.stringify(dataCopy);

//       if (currentDataHash === lastSavedDataHashRef.current) {
//         return;
//       }

//       saveInProgressRef.current = true;

//       const res = await dispatch(
//         updatePcpProvider({
//           id: selectedProvider,
//           payload: dataCopy,
//         })
//       );

//       if (res?.payload) {
//         await dispatch(fetchPcpProviderById(selectedProvider));
//         await dispatch(fetchPcpProviders());

//         lastSavedDataHashRef.current = currentDataHash;
//       }

//       saveInProgressRef.current = false;
//     } catch (err) {
//       console.error("Save error:", err);
//       saveInProgressRef.current = false;
//       throw err;
//     }
//   };

//   const handleCancel = () => {
//     dispatch(
//       setProviderDetails({
//         ...userDetails,
//         ...providerDetails,
//       })
//     );
//   };

//   const saveStatus = useSelector(
//     (state: any) => state.common?.autoSave?.["providerDetails"]?.status
//   );

//   const [localSaveStatus, setLocalSaveStatus] = React.useState<string>("");
//   const effectiveSaveStatus = saveStatus || localSaveStatus;

//   // Derive the save message and styling based on the save status
//   const { saveMessage, messageStyle } = React.useMemo(() => {
//     let message = "";
//     let style = "text-primaryPurple italic";

//     switch (effectiveSaveStatus) {
//       case "changes_detected":
//         message = "Changes detected, saving soon...";
//         style = "text-amber-500 italic";
//         break;
//       case "saving":
//         message = "⚠️ Saving... Please don't close the app";
//         style = "text-amber-600 font-bold italic";
//         break;
//       case "saved":
//         message = "✅ All changes saved";
//         style = "text-green-600 italic";
//         break;
//       case "validation_failed":
//         message = "⚠️ Validation failed. Please check your inputs.";
//         style = "text-red-600 font-bold italic";
//         break;
//       case "error":
//         message = "❌ Error saving changes. Please try again.";
//         style = "text-red-600 font-bold italic";
//         break;
//     }

//     return { saveMessage: message, messageStyle: style };
//   }, [effectiveSaveStatus, saveStatus, localSaveStatus]);

//   // Ref to track previous diff values to prevent unnecessary saves
//   const prevDiffRef = React.useRef<string>("");

//   // Ref to track if initial data has been loaded
//   const initialDataLoadedRef = React.useRef<boolean>(false);

//   // Effect to set initialDataLoaded when data is fetched
//   React.useEffect(() => {
//     const hasValidData = selectedProvider !== undefined;

//     if (hasValidData && !initialDataLoadedRef.current) {
//       initialDataLoadedRef.current = true;
//     }
//   }, [userDetails]);

//   // Effect to trigger autosave when data changes
//   React.useEffect(() => {
//     if (!initialDataLoadedRef.current) {
//       return;
//     }

//     if (!selectedProvider) {
//       return;
//     }

//     if (diff.hasDiff) {
//       const hasUndefinedValues = Object.values(diff.currentValues).every(
//         (val) => val === undefined
//       );
//       if (hasUndefinedValues) {
//         return;
//       }

//       const currentDiffString = JSON.stringify(diff.currentValues);

//       if (currentDiffString !== prevDiffRef.current) {
//         prevDiffRef.current = currentDiffString;

//         const setStatusWithFallback = (payload: {
//           screenId: string;
//           status: string;
//         }) => {
//           setLocalSaveStatus(payload.status);
//           return dispatch(setSaveStatus(payload));
//         };

//         simpleAutoSave(
//           "providerDetails",
//           diff.currentValues,
//           saveDetails,
//           4000,
//           dispatch,
//           setStatusWithFallback,
//           setLastSavedData
//         );
//       }
//     }
//   }, [
//     diff.hasDiff,
//     diff.currentValues,
//     dispatch,
//     setLocalSaveStatus,
//     userDetails,
//   ]);

//   const handleRefresh = async () => {
//     try {
//       // Refresh will be handled by parent component
//       if (selectedProvider) {
//         const providerRes = await dispatch(
//           fetchPcpProviderById(selectedProvider)
//         );
//         if (providerRes.payload) {
//           dispatch(setProviderDetails(providerRes.payload));
//         }
//       }
//     } catch (error) {
//       console.error("Error refreshing provider list:", error);
//     }
//   };

//   return (
//     <>
//       {loader ? (
//         <View className="mt-64">
//           <Loader />
//         </View>
//       ) : (
//         <ScreenWrapper direction="column" onRefresh={handleRefresh}>
//           <CustomCard>
//             {saveMessage && (
//               <Text className={`text-sm text-right my-1 ${messageStyle}`}>
//                 {saveMessage}
//               </Text>
//             )}
//             <View className="mt-2 gap-3">
//               <View className="flex-row justify-between items-center">
//                 <Heading
//                   text="Pcp Provider"
//                   size="sub-heading"
//                   showSeperator={false}
//                 />
//                 <MaterialCommunityIcons
//                   name="plus-circle-outline"
//                   color="#8143d9"
//                   size={25}
//                   onPress={onAddPress}
//                 />
//               </View>

//               <View className="">
//                 <SearchablePicker
//                   placeholder="Select Provider"
//                   items={providerOptions}
//                   value={selectedProvider}
//                   onValueChange={handleProviderChange}
//                   autoFocus={true}
//                 />
//               </View>
//             </View>

//             {selectedProvider && userDetails && (
//               <>
//                 <LineSeperator extraStyle="mt-5 mb-3" />

//                 <View className="rounded-lg">
//                   <View className="mb-3">
//                     <Heading
//                       text="First Name"
//                       size="sub-heading"
//                       showSeperator={false}
//                     />
//                     <View className="mt-2">
//                       <CustomInput
//                         inputValue={first_name}
//                         placeholder="First Name"
//                         onInputChange={(value) => {
//                           parseCharInput(value, (parsedValue) => {
//                             dispatch(
//                               setProviderDetails({
//                                 ...userDetails,
//                                 first_name: parsedValue,
//                               })
//                             );
//                           });
//                         }}
//                       />
//                     </View>
//                   </View>

//                   <LineSeperator extraStyle="mt-2 mb-3" />

//                   <View className="mb-3">
//                     <Heading
//                       text="Middle Name"
//                       size="sub-heading"
//                       showSeperator={false}
//                     />
//                     <View className="mt-2">
//                       <CustomInput
//                         inputValue={middle_name}
//                         placeholder="Middle Name"
//                         onInputChange={(value) => {
//                           parseCharInput(value, (parsedValue) => {
//                             dispatch(
//                               setProviderDetails({
//                                 ...userDetails,
//                                 middle_name: parsedValue,
//                               })
//                             );
//                           });
//                         }}
//                       />
//                     </View>
//                   </View>

//                   <LineSeperator extraStyle="mt-2 mb-3" />

//                   <View className="mb-3">
//                     <Heading
//                       text="Last Name"
//                       size="sub-heading"
//                       showSeperator={false}
//                     />
//                     <View className="mt-2">
//                       <CustomInput
//                         inputValue={last_name}
//                         placeholder="Last Name"
//                         onInputChange={(value) => {
//                           parseCharInput(value, (parsedValue) => {
//                             dispatch(
//                               setProviderDetails({
//                                 ...userDetails,
//                                 last_name: parsedValue,
//                               })
//                             );
//                           });
//                         }}
//                       />
//                     </View>
//                   </View>

//                   <LineSeperator extraStyle="mt-2 mb-3" />

//                   <View className="mb-3">
//                     <Heading
//                       text="Credential"
//                       size="sub-heading"
//                       showSeperator={false}
//                     />
//                     <View className="mt-2">
//                       <SearchablePicker
//                         value={credential}
//                         placeholder="Select Credential"
//                         items={credentialOptions.map((option) => {
//                           return {
//                             label: option.label,
//                             value: option.value,
//                           };
//                         })}
//                         onValueChange={(selectedItem) => {
//                           console.log("Selected Credential:", selectedItem);
//                           dispatch(
//                             setProviderDetails({
//                               credential: selectedItem.value,
//                             })
//                           );
//                         }}
//                         floatingLabel={false}
//                         disable={true}
//                         error={false}
//                         disableError={false}
//                       />
//                     </View>
//                   </View>

//                   <LineSeperator extraStyle="mt-2 mb-3" />

//                   <View className="mb-3">
//                     <Heading
//                       text="NPI Number"
//                       size="sub-heading"
//                       showSeperator={false}
//                     />
//                     <View className="mt-2">
//                       <CustomInput
//                         inputValue={npi_number}
//                         placeholder="NPI Number"
//                         onInputChange={(value) => {
//                           parseIntInput(value, (parsedValue) => {
//                             dispatch(
//                               setProviderDetails({
//                                 ...userDetails,
//                                 npi_number: parsedValue.toString(),
//                               })
//                             );
//                           });
//                         }}
//                         keyboardType="numeric"
//                       />
//                     </View>
//                   </View>

//                   <LineSeperator extraStyle="mt-2 mb-3" />

//                   <View className="mb-3">
//                     <Heading
//                       text="Email"
//                       size="sub-heading"
//                       showSeperator={false}
//                     />
//                     <View className="mt-2">
//                       <CustomInput
//                         inputValue={email_id}
//                         placeholder="Email"
//                         onInputChange={(value) => {
//                           dispatch(
//                             setProviderDetails({
//                               ...userDetails,
//                               email_id: value,
//                             })
//                           );
//                         }}
//                         keyboardType="email-address"
//                       />
//                     </View>
//                   </View>

//                   <LineSeperator extraStyle="mt-2 mb-3" />

//                   <View className="mb-3">
//                     <Heading
//                       text="Phone Number"
//                       size="sub-heading"
//                       showSeperator={false}
//                     />
//                     <View className="mt-2">
//                       <CustomInput
//                         inputValue={phone_number}
//                         placeholder="Phone Number"
//                         onInputChange={(value) => {
//                           parseIntInput(value, (parsedValue) => {
//                             dispatch(
//                               setProviderDetails({
//                                 ...userDetails,
//                                 phone_number: parsedValue.toString(),
//                               })
//                             );
//                           });
//                         }}
//                         keyboardType="phone-pad"
//                       />
//                     </View>
//                   </View>

//                   <LineSeperator extraStyle="mt-2 mb-3" />

//                   <View className="mb-3">
//                     <Heading
//                       text="Fax Number"
//                       size="sub-heading"
//                       showSeperator={false}
//                     />
//                     <View className="mt-2">
//                       <CustomInput
//                         inputValue={fax_number}
//                         placeholder="Fax Number"
//                         onInputChange={(value) => {
//                           parseIntInput(value, (parsedValue) => {
//                             dispatch(
//                               setProviderDetails({
//                                 ...userDetails,
//                                 fax_number: parsedValue.toString(),
//                               })
//                             );
//                           });
//                         }}
//                         keyboardType="phone-pad"
//                       />
//                     </View>
//                   </View>

//                   <LineSeperator extraStyle="mt-2 mb-3" />

//                   <View className="mb-3">
//                     <Heading
//                       text="Address"
//                       size="sub-heading"
//                       showSeperator={false}
//                     />
//                     <View className="mt-2">
//                       <CustomInput
//                         inputValue={address}
//                         placeholder="Address"
//                         onInputChange={(value) => {
//                           dispatch(
//                             setProviderDetails({
//                               ...userDetails,
//                               address: value,
//                             })
//                           );
//                         }}
//                       />
//                     </View>
//                   </View>

//                   <LineSeperator extraStyle="mt-2 mb-3" />

//                   <View className="mb-3">
//                     <Heading
//                       text="City"
//                       size="sub-heading"
//                       showSeperator={false}
//                     />
//                     <View className="mt-2">
//                       <CustomInput
//                         inputValue={city}
//                         placeholder="City"
//                         onInputChange={(value) => {
//                           parseCharInput(value, (parsedValue) => {
//                             dispatch(
//                               setProviderDetails({
//                                 ...userDetails,
//                                 city: parsedValue,
//                               })
//                             );
//                           });
//                         }}
//                       />
//                     </View>
//                   </View>

//                   <LineSeperator extraStyle="mt-2 mb-3" />

//                   <View className="mb-3">
//                     <Heading
//                       text="State"
//                       size="sub-heading"
//                       showSeperator={false}
//                     />
//                     <View className="mt-2">
//                       <CustomInput
//                         inputValue={state}
//                         placeholder="State"
//                         onInputChange={(value) => {
//                           parseCharInput(value, (parsedValue) => {
//                             dispatch(
//                               setProviderDetails({
//                                 ...userDetails,
//                                 state: parsedValue,
//                               })
//                             );
//                           });
//                         }}
//                       />
//                     </View>
//                   </View>

//                   <LineSeperator extraStyle="mt-2 mb-3" />

//                   <View className="mb-3">
//                     <Heading
//                       text="Zip Code"
//                       size="sub-heading"
//                       showSeperator={false}
//                     />
//                     <View className="mt-2">
//                       <CustomInput
//                         inputValue={zip_code}
//                         placeholder="Zip Code"
//                         onInputChange={(value) => {
//                           parseIntInput(value, (parsedValue) => {
//                             dispatch(
//                               setProviderDetails({
//                                 ...userDetails,
//                                 zip_code: parsedValue.toString(),
//                               })
//                             );
//                           });
//                         }}
//                         keyboardType="numeric"
//                       />
//                     </View>
//                   </View>

//                   <LineSeperator extraStyle="mt-2 mb-3" />

//                   <View className="mb-3">
//                     <Heading
//                       text="Hospital System"
//                       size="sub-heading"
//                       showSeperator={false}
//                     />
//                     <View className="mt-2">
//                       <CustomInput
//                         inputValue={hospital_system}
//                         placeholder="Hospital System"
//                         onInputChange={(value) => {
//                           dispatch(
//                             setProviderDetails({
//                               ...userDetails,
//                               hospital_system: value,
//                             })
//                           );
//                         }}
//                       />
//                     </View>
//                   </View>
//                 </View>

//                 <LineSeperator extraStyle="my-5" />
//               </>
//             )}
//             <View className="mb-3"></View>
//           </CustomCard>
//         </ScreenWrapper>
//       )}
//       <View className="mt-9"></View>

//       <PopupModal
//         show={modalVisible}
//         msg={popupMsg}
//         status="warning"
//         onClose={() => setModalVisible(false)}
//       />
//     </>
//   );
// };

// export default PcpProviderList;
