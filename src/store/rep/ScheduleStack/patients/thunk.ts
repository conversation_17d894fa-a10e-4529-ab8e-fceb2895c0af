import { createAsyncThunk } from "@reduxjs/toolkit";
import api from "../../../../api/api";

const fetchCases = createAsyncThunk(
  "schedule/fetchCases",
  async ({ siteId, date }: { siteId: string; date: string }, thunkAPI) => {
    try {
      const response = await api.private.get(
        api.routes.privateRoutes.rep.patientsList(siteId, date)
      );

      if (response.data && response.data.result) {
        return response.data.result;
      } else {
        return thunkAPI.rejectWithValue(
          'Invalid response format: Missing "result" field'
        );
      }
    } catch (error: any) {
      if (error.response) {
        console.error(
          "Error response 5received:",
          error.response,
          error.response.message
        );
        if (error.response.status === 404) {
          return thunkAPI.rejectWithValue("Cases not found");
        }
        return thunkAPI.rejectWithValue(
          error.response.data.error_description ||
            "Failed to fetch patient cases"
        );
      } else if (error.request) {
        console.error("No response received:", error.request);
        return thunkAPI.rejectWithValue("No response received from the server");
      } else {
        console.error("Error occurred:", error.message);
        return thunkAPI.rejectWithValue(
          error.message || "An unknown error occurred"
        );
      }
    }
  }
);

export { fetchCases };
