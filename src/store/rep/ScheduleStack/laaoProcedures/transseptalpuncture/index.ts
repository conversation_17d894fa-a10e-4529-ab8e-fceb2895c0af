import { createSlice } from "@reduxjs/toolkit";
import initialState from "./initialstate";
import { fetchTransseptalDetails, putTransseptalDetails } from "./thunk";

const transsceptalpunctureSlice = createSlice({
  name: "transsceptalpuncture",
  initialState,
  reducers: {
    setTransseptalUserDetails: (state, action) => {
      state.userDetails = {
        ...state.userDetails,
        ...action.payload,
      };
    },

    resetTransseptalState: (state) => {
      state.transseptaDetails = null;
      state.userDetails = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchTransseptalDetails.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchTransseptalDetails.fulfilled, (state, action) => {
        state.loading = false;
        state.transseptaDetails = action.payload;
      })
      .addCase(fetchTransseptalDetails.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });

    // builder
    //   .addCase(putTransseptalDetails.pending, (state) => {
    //     state.loading = true;
    //   })
    //   .addCase(putTransseptalDetails.fulfilled, (state, action) => {
    //     state.loading = false;
    //     state.error = null;
    //   })
    //   .addCase(putTransseptalDetails.rejected, (state, action) => {
    //     state.loading = false;
    //     state.error = action.payload as string;
    //   });
  },
});

export const { setTransseptalUserDetails, resetTransseptalState } =
  transsceptalpunctureSlice.actions;

export default transsceptalpunctureSlice.reducer;
