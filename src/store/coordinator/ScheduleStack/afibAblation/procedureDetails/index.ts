import { createSlice } from "@reduxjs/toolkit";
import initialState from "./initialstate";
import { fetchAfibAblationProedure } from "./thunk";

const afibAblationProcedureDetails = createSlice({
  name: "afibAblationProcedureDetails",
  initialState,
  reducers: {
    setAfibProcedureDetails: (state, action) => {
      state.userDetails = {
        ...state.userDetails,
        ...action.payload,
      };
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchAfibAblationProedure.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchAfibAblationProedure.fulfilled, (state, action) => {
        state.loading = false;
        state.procedureDetails = action.payload;
        state.userDetails = action.payload;
      })
      .addCase(fetchAfibAblationProedure.rejected, (state, action) => {
        state.loading = false;
        state.procedureDetails = null;
        state.error = action.payload as string;
      });
  },
});

export const { setAfibProcedureDetails } = afibAblationProcedureDetails.actions;

export default afibAblationProcedureDetails.reducer;
