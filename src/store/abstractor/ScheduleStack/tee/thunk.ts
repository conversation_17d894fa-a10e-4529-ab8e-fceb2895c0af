import { createAsyncThunk } from "@reduxjs/toolkit";
import api from "../../../../api/api";

const fetchTEEDetails = createAsyncThunk(
  "schedule/fetchTEEDetails",
  async ({ case_id }: { case_id: string }, thunkAPI) => {
    try {
      const response = await api.private.get(
        api.routes.privateRoutes.rep.laaanatomy_get(case_id)
      );

      if (response.data && response.data.result) {
        return response.data.result;
      } else {
        return thunkAPI.rejectWithValue(
          'Invalid response format: Missing "result" field'
        );
      }
    } catch (error: any) {
      console.error("Error caught during API call:", error.message || error);
      return thunkAPI.rejectWithValue(
        error.response?.data?.message || "Failed to fetch laaAnatomy details"
      );
    }
  }
);

const putTEEDetails = createAsyncThunk(
  "schedule/putTEEDetails",
  async (
    { case_details_id, payload }: { case_details_id: number; payload: any },
    thunkAPI
  ) => {
    try {
      const response = await api.private.put(
        api.routes.privateRoutes.rep.laaanatomy_put(case_details_id),
        payload
      );
      if (response.data && response.data.result) {
        return response.data.result;
      } else {
        return thunkAPI.rejectWithValue(
          'Invalid response format: Missing "result" field'
        );
      }
    } catch (error: any) {
      console.error("Error caught during API call:", error.message || error);
      return thunkAPI.rejectWithValue(
        error.response?.data?.message || "Failed to fetch laaAnatomy details"
      );
    }
  }
);
export { fetchTEEDetails, putTEEDetails };
