import { Middleware } from "@reduxjs/toolkit";

const asyncActionPerformanceLogger: Middleware = (storeAPI) => {
  const startTimes: { [actionType: string]: number } = {};

  const colorMap = {
    key: "\x1b[94m", // Light Blue for keys
    value: "\x1b[92m", // Light Green for values
    fulfilled: "\x1b[92m", // Subtle Light Green
    rejected: "\x1b[91m", // Subtle Light Red
    pending: "\x1b[36m", // Subtle Cyan
    reset: "\x1b[0m", // Reset color
    durationHigh: "\x1b[91m", // Red for duration above 5s
    durationMedium: "\x1b[93m", // Yellow for duration between 3s and 5s
    durationLow: "\x1b[92m", // Green for duration below 3s
  };

  const getDurationColor = (duration: number) => {
    if (duration > 5000) {
      return colorMap.durationHigh; // Above 5s -> Red
    } else if (duration > 3000) {
      return colorMap.durationMedium; // Between 3s and 5s -> Yellow
    } else {
      return colorMap.durationLow; // Below 3s -> Green
    }
  };

  return (next) => (action) => {
    if (action.type.endsWith("/pending")) {
      const startTime = Date.now();
      startTimes[action.type] = startTime;
      console.log(
        `${colorMap.pending}[${action.type}] Start: ${new Date(
          startTime
        ).toLocaleString()}${colorMap.reset}`
      );
    }

    if (
      action.type.endsWith("/fulfilled") ||
      action.type.endsWith("/rejected")
    ) {
      const status = action.type.endsWith("/fulfilled")
        ? "fulfilled"
        : "rejected";

      const startTimeKey = action.type
        .replace("/fulfilled", "/pending")
        .replace("/rejected", "/pending");
      const startTime = startTimes[startTimeKey] || Date.now();
      const endTime = Date.now();
      const duration = endTime - startTime;

      const startTimeFormatted = new Date(startTime).toLocaleString();
      const endTimeFormatted = new Date(endTime).toLocaleString();
      const durationColor = getDurationColor(duration);

      console.log(
        `${colorMap[status]}==============================\n` +
          `[${action.type}]\n` +
          `${colorMap.key}Status: ${colorMap.value}${status}${colorMap.reset}\n` +
          `${colorMap.key}Start Time: ${colorMap.value}${startTimeFormatted}${colorMap.reset}\n` +
          `${colorMap.key}End Time: ${colorMap.value}${endTimeFormatted}${colorMap.reset}\n` +
          `${colorMap.key}Duration: ${durationColor}${duration.toFixed(2)}ms${
            colorMap.reset
          }\n` +
          `${colorMap[status]}====================================${colorMap.reset}`
      );

      delete startTimes[startTimeKey];
    }

    return next(action);
  };
};

export default asyncActionPerformanceLogger;
