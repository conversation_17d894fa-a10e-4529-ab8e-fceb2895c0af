interface CaseData {
  case_id: string;
  anticoagulation: {
    selected: {
      id: string;
      name: string;
      quantity: number;
      dosing_frequency: {
        id: string;
        name: string;
      };
      period: {
        count: number | null;
        duration: {
          id: string;
          name: string;
        };
      };
    }[];
    options: {
      id: string;
      name: string;
      quantity: number[];
    }[];
    frequency_options: {
      id: string;
      name: string;
    }[];
    duration_options: {
      id: string;
      name: string;
    }[];
  };
  discharge_plan: {
    selected: {
      id: string;
      name: string;
    };
    options: {
      id: string;
      name: string;
    }[];
  };
  anticipated_45_days_follow_up_date: string;
  follow_ups_45_days: {
    completed: boolean;
    date: string;
    peri_device_leak: boolean;
    width: number;
    thrombus: boolean;
    cta_link: {
      viewer_link: string;
      uuid: string;
      modality: string;
    } | null;
    tee_link: {
      viewer_link: string;
      uuid: string;
      modality: string;
    } | null;
    physician: {
      id: string;
      name: string;
    };
  };
  follow_ups_6_months: {
    completed: boolean;
    date: string;
    peri_device_leak: boolean;
    width: number;
    thrombus: boolean;
    cta_link: {
      viewer_link: string;
      uuid: string;
      modality: string;
    } | null;
    tee_link: {
      viewer_link: string;
      uuid: string;
      modality: string;
    } | null;
    physician: {
      id: string;
      name: string;
    };
  };
}

interface Medication {
  id: string;
  med_id: string;
  med_name: string;
  med_dose: string | number;
  dosing_frequency: string;
  dosing_frequency_id: string;
  period: string;
  period_id: string;
  period_count: string | number;
}

export type { CaseData, Medication };
