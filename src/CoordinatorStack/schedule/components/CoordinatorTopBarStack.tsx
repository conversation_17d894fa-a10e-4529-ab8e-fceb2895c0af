import * as React from "react";
import { createMaterialTopTabNavigator } from "@react-navigation/material-top-tabs";
import ProcedureScreen from "../screens/ProcedureScreen";
import PreOpScreen from "../screens/PreOpScreen";
import PostOpScreen from "../screens/PostOpScreen";
import AfibProcedureScreen from "../screens/AfibProcedureScreen";
import ReportScreen from "../screens/ReportScreen";
import { useCoordinatorSelectedPatient } from "../hooks/schedulesHooks";

interface ICoordinatorTopBarStackProps {}

const Tab = createMaterialTopTabNavigator();

const CoordinatorTopBarStack: React.FunctionComponent<
  ICoordinatorTopBarStackProps
> = (props) => {
  const patient = useCoordinatorSelectedPatient();

  return (
    <Tab.Navigator
      screenOptions={{
        tabBarScrollEnabled: true,
        tabBarIndicatorStyle: {
          backgroundColor: "#8143d9",
          height: 100,
        },
        tabBarActiveTintColor: "white",
        tabBarInactiveTintColor: "black",
        tabBarLabelStyle: {
          fontSize: 12,
          fontWeight: "bold",
        },
      }}
      sceneContainerStyle={{ backgroundColor: "white" }}
    >
      <Tab.Screen name="Pre Op" component={PreOpScreen} />
      <Tab.Screen name="LAAO Procedure" component={ProcedureScreen} />
      {/* {patient?.patient?.afib_ablation && (
        <Tab.Screen name="Afib Ablation" component={AfibProcedureScreen} />
      )} */}

      <Tab.Screen name="Post Op" component={PostOpScreen} />

      <Tab.Screen name="Case Synopsis" component={ReportScreen} />
    </Tab.Navigator>
  );
};

export default CoordinatorTopBarStack;
