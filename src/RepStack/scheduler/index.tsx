import * as React from "react";
import { createStackNavigator } from "@react-navigation/stack";
import SchedulerHome from "./screens/SchedulerHome";
import { useChatBotState } from "../components/hooks";

interface IRepHomeStackProps {}

const SchedulerStack: React.FunctionComponent<IRepHomeStackProps> = (props) => {
  const SchedulerStack = createStackNavigator();

  return (
    <>
      <SchedulerStack.Navigator>
        <SchedulerStack.Screen
          name="Scheduler"
          options={{
            headerShown: false,
          }}
          component={SchedulerHome}
        />
      </SchedulerStack.Navigator>
    </>
  );
};

export default SchedulerStack;
