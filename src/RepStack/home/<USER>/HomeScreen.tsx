import React, { useState, useCallback } from "react";
import { View } from "react-native";
import { WebView } from "react-native-webview";
import { dashboardUrl } from "../../../api/config";
import { getToken, getDashboardId } from "../../../Hooks/dashboard";
import { Enviromnent } from "../../../api/config";
import Loader from "../../../components/Loader";
import { useFocusEffect } from "@react-navigation/native";

const HomeScreen: React.FC = () => {
  const token = getToken();
  const dashboardId = getDashboardId();
  const url = dashboardUrl[Enviromnent](token, dashboardId);
  const [showLoader, setShowLoader] = useState(true);
  const [loaded, setLoaded] = useState(false);

  useFocusEffect(
    useCallback(() => {
      if (!loaded) {
        setShowLoader(true);
      }
    }, [loaded])
  );

  const handleLoadEnd = () => {
    setTimeout(() => {
      setShowLoader(false);
      setLoaded(true);
    }, 3500);
  };

  return (
    <View className="flex-1">
      <WebView
        className="flex-1"
        source={{ uri: url }}
        onLoadEnd={handleLoadEnd}
      />
      {showLoader && <Loader />}
    </View>
  );
};

export default HomeScreen;
