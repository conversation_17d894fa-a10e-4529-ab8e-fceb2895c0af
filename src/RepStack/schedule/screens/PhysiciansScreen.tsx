import React, { useCallback, useEffect, useState } from "react";
import { ScrollView, RefreshControl } from "react-native";
import { useDispatch } from "react-redux";
import { useFocusEffect } from "@react-navigation/native";
import { AppDispatch } from "../../../store";

import PhysicianList from "../components/PhysicianList";
import {
  resetImplantingPhysicianDetails,
  setSelectedPhysician,
  showAddPhysicianModal,
  setSelectedSite as physicianSetSelectedSite,
} from "../../../store/rep/ScheduleStack/addPhysician";

const PhysiciansTabScreen: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const [refreshing, setRefreshing] = useState(false);

  const fetchData = async () => {};

  useEffect(() => {
    fetchData();
  }, []);

  useFocusEffect(
    useCallback(() => {
      dispatch(resetImplantingPhysicianDetails());
      dispatch(setSelectedPhysician(""));
      dispatch(physicianSetSelectedSite(""));
      dispatch(showAddPhysicianModal(false));
      fetchData();

      return () => {
        // Optional cleanup
      };
    }, [dispatch])
  );

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchData();
    setRefreshing(false);
  };

  const handleAdd = () => {
    dispatch(setSelectedPhysician(""));
    dispatch(physicianSetSelectedSite(""));
    dispatch(resetImplantingPhysicianDetails());
    dispatch(showAddPhysicianModal(true));
  };

  return (
    <ScrollView
      className="flex-1"
      refreshControl={
        <RefreshControl
          refreshing={refreshing}
          onRefresh={handleRefresh}
          colors={["#8143d9"]}
          tintColor={"#8143d9"}
        />
      }
    >
      <PhysicianList onAddPress={handleAdd} />
    </ScrollView>
  );
};

export default PhysiciansTabScreen;
