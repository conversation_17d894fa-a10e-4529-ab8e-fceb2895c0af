import * as React from "react";
import { View, Text, TouchableOpacity, StyleSheet } from "react-native";
import { useNavigation } from "@react-navigation/native";
import AsyncStorage from "@react-native-async-storage/async-storage";
import LineSeperator from "../../../components/LineSeperator";
import { useDispatch } from "react-redux";
import { AppDispatch } from "../../../store";
import { setPatientDetails } from "../../../store/chatbot";
import { capitalizeName } from "../../../utils";

interface PatientCardProps {
  patient: {
    dob: string;
    patient_id: string;
    patient_name: string;
    procedure_date: string;
    referring_provider: string;
  };
}

const PatientCard: React.FunctionComponent<PatientCardProps> = ({
  patient,
}) => {
  const dispatch = useDispatch<AppDispatch>();
  const navigation = useNavigation();

  const handlePress = async () => {
    try {
      const accessToken = await AsyncStorage.getItem("accessToken");
      dispatch(setPatientDetails(patient?.patient_name));

      if (accessToken) {
        // const url = `https://2ca1-2405-201-e00a-b062-50af-58ed-157e-9fc.ngrok-free.app/index.html?access_token=${accessToken}&patient_id=${patient.patient_id}`;
        const url = `https://dev-scheduler.cormetrix.com/abstractor/index.html?access_token=${accessToken}&patient_id=${patient.patient_id}`;
        navigation.navigate("Details", {
          link: url,
          title: patient?.patient_name,
        });
      } else {
        console.error("Access token not found");
      }
    } catch (error) {
      console.error("Error retrieving access token:", error);
    }
  };

  return (
    <View style={styles.cardContainer}>
      <TouchableOpacity
        onPress={handlePress}
        className="flex-row items-center justify-between w-full"
      >
        <View className="flex-column flex-1 p-1">
          <View className="flex-row justify-between p-1">
            <View className="flex-row items-center">
              <Text className="font-semibold text-lg text-primaryPurple mr-2">
                {/* {patient?.patient_name} */}
                {capitalizeName(patient?.patient_name)}
              </Text>
              <View className="h-6 border-l-[2px] rounded-full border-primaryPurple mx-2" />
              <Text className="font-bold text-lg text-primaryPurple">
                {patient?.dob}
              </Text>
            </View>
          </View>

          <View className="flex-row gap-2 p-1">
            <Text className="font-md text-primaryBlack font-semibold">
              Procedure Date:
            </Text>
            <Text className="text-primaryBlack">{patient?.procedure_date}</Text>
          </View>

          <View className="flex-row gap-2 p-1">
            <Text className="font-md text-primaryBlack font-semibold">
              Referring Provider:
            </Text>
            <Text className="text-primaryBlack">
              {patient?.referring_provider}
            </Text>
          </View>
        </View>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  cardContainer: {
    position: "relative",
    flexDirection: "column",
    width: "100%",
    backgroundColor: "#ffffff",
    borderRadius: 10,
    padding: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
  },
});

export default PatientCard;
