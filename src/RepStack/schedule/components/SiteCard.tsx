import * as React from "react";
import { View, Text, TouchableOpacity, Image } from "react-native";
import { MatIcon } from "../../../utils";
import { useDispatch } from "react-redux";
import { setSiteDetails } from "../../../store/rep/ScheduleStack/patients";
import { useNavigation } from "@react-navigation/native";
import { AppDispatch } from "../../../store";
import moment from "moment";

interface ISiteCardProps {
  details: {
    date: string;
    hospitalName: string;
    siteId: number;
    startTime?: string;
    noOfCases: number;
    implantingMD: string | null;
    implantingPhysicianImage?: string;
    hospitalImage: string;
  };
  showDate: boolean;
  isPatients?: boolean; // New Prop (optional)
}

const SiteCard: React.FunctionComponent<ISiteCardProps> = ({
  details,
  showDate,
  isPatients = false, // Default to false
}) => {
  const dispatch = useDispatch<AppDispatch>();
  const navigation = useNavigation();

  const CardContent = (
    <View className="p-4 bg-primaryWhite flex-row items-center justify-between">
      <View className="w-16 h-16 rounded-full overflow-hidden">
        <Image
          source={{ uri: details.hospitalImage }}
          className="w-full h-full"
          resizeMode="cover"
        />
      </View>

      <View className="flex-1 justify-between ml-4">
        <View className="flex-row justify-between items-center">
          <Text
            className="text-md font-semibold text-primaryPurple py-1"
            numberOfLines={3}
          >
            {details.hospitalName}
          </Text>
        </View>

        <View className="flex-row items-center justify-between">
          <Text className="text-md text-primaryBlack mb-1">
            {details.implantingMD}
          </Text>

          <View className="flex-row justify-center items-center">
            {showDate ? (
              <>
                <View className="mr-1">
                  {MatIcon("calendar-outline", "#8143d9", 20)}
                </View>
                <Text className="text-md text-primaryBlack">
                  {moment(details.date).format("MM/DD/YYYY")}
                </Text>
              </>
            ) : (
              <>
                <View className="mr-1">
                  {MatIcon("clock-time-four-outline", "#8143d9", 20)}
                </View>
                <Text className="text-md text-primaryPurple">
                  {moment(details.startTime, "HH:mm:ss").format("HH:mm")}
                </Text>
              </>
            )}
          </View>
        </View>

        <Text className="text-md text-primaryGray">
          Cases: {details.noOfCases}
        </Text>
      </View>
    </View>
  );

  return isPatients ? (
    <View>{CardContent}</View>
  ) : (
    <TouchableOpacity
      onPress={() => {
        if (!showDate) {
          dispatch(
            setSiteDetails({
              selectedDate: details.date,
              selectedHospitalName: details.hospitalName,
              selectedHospitalId: details.siteId,
              implantingPhysicianName: details.implantingMD,
              implantingPhysicianImage: details.implantingPhysicianImage,
              hospitalImage: details.hospitalImage,
            })
          );

          navigation.navigate("ScheduleTab", {
            screen: "Patients",
          });
        }
      }}
    >
      {CardContent}
    </TouchableOpacity>
  );
};

export default SiteCard;
