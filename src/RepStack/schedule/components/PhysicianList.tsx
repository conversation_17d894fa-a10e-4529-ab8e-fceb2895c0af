import React, { useEffect } from "react";
import { View, Text } from "react-native";
import { useDispatch, useSelector } from "react-redux";
import { useFocusEffect, useNavigation } from "@react-navigation/native";

import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons";
import { AppDispatch } from "../../../store";
import { useSiteList } from "../hooks/schedulesHooks";
import { fetchSiteList } from "../../../store/rep/ScheduleStack/schedules/thunk";
import { fetchImplantingPhysicians } from "../../../store/rep/ScheduleStack/addpatient/thunk";
import {
  fetchProviderCredentials,
  fetchProviderExperience,
  fetchSpecificImplantingPhysician,
  putImplantingPhysician,
} from "../../../store/rep/ScheduleStack/addPhysician/thunk.ts";
import {
  setSelectedSite,
  setSelectedPhysician,
  setImplantingPhysicianList,
  setImplantingPhysicianDetails,
  showAddPhysicianModal,
} from "../../../store/rep/ScheduleStack/addPhysician";
import { parseCharInput, parseIntInput } from "../../../utils";
import Loader from "../../../components/Loader";
import SearchablePicker from "../../../components/SearchablePicker";
import ScreenWrapper from "../../../components/ScreenWrapper";
import LineSeperator from "../../../components/LineSeperator";
import {
  findPostImplantingPhysicianDiff,
  formatPostImplantingPhysician,
  useSelectedSite,
  useSelectedPhysician,
  useImplantingPhysiciansDetails,
  useLoaderAndError,
  usePhysicianList,
  useUserImplantingPhysiciansDetails,
} from "../hooks/addImplantingPhysicianHooks.ts";
import CustomCard from "../../../components/CustomCard.tsx";
import Heading from "../../../components/Heading.tsx";
import PopupModal from "../../../components/Popup.tsx";
import SaveActionButton from "../../../components/SaveActionButton.tsx";
import CustomInput from "../../../components/CustomTextInput.tsx";
import { simpleAutoSave } from "../../../services/simpleAutoSave.ts";
import {
  setSaveStatus,
  setLastSavedData,
} from "../../../store/services/index.ts";

interface PhysicianListProps {
  onAddPress: () => void;
}

const PhysicianList: React.FC<PhysicianListProps> = ({ onAddPress }) => {
  const dispatch = useDispatch<AppDispatch>();
  const implantingPhysicians = usePhysicianList();
  const [popupMsg, setPopupMsg] = React.useState<string[]>([]);
  const [modalVisible, setModalVisible] = React.useState(false);
  const siteListData = useSiteList();
  const navigation = useNavigation();
  // Create a ref to track if a save is in progress
  const saveInProgressRef = React.useRef(false);

  // Create a ref to track the last saved data hash
  const lastSavedDataHashRef = React.useRef("");
  const userDetails = useUserImplantingPhysiciansDetails();
  const physicianDetails = useImplantingPhysiciansDetails();
  const selectedSite = useSelectedSite();
  const selectedPhysician = useSelectedPhysician();
  const {
    first_name,
    last_name,
    middle_name,
    credential,
    credential_options,
    npi_number,
    email_id,
    phone_number,
    fax_number,
    address,
    city,
    state,
    zip_code,
    experience,
    experience_options,
  } = formatPostImplantingPhysician(userDetails);

  const { loader, error } = useLoaderAndError();
  const diff = findPostImplantingPhysicianDiff();

  // Convert site list data to picker format
  const siteOptions = React.useMemo(() => {
    if (siteListData && Array.isArray(siteListData)) {
      return siteListData.map((site) => ({
        label: site.name,
        value: site.id,
      }));
    }
    return [];
  }, [siteListData]);

  const handlePhysicianChange = async (value: {
    value: string;
    label: string;
  }) => {
    dispatch(showAddPhysicianModal(false));
    dispatch(setSelectedPhysician(value.value));

    if (value.value) {
      const physicianRes = await dispatch(
        fetchSpecificImplantingPhysician(value.value)
      );
      if (physicianRes.payload) {
        dispatch(setImplantingPhysicianDetails(physicianRes.payload));
      }
    }
  };

  const validator = () => {
    setPopupMsg([]);
    var temp = false;

    // Don't validate if initial data hasn't been loaded yet
    if (!initialDataLoadedRef.current) {
      return false;
    }

    if (!selectedSite) {
      setPopupMsg((prev) => [...prev, "Please Select Site"]);
      temp = true;
    }

    if (!first_name) {
      setPopupMsg((prev) => [...prev, "Please Enter First Name"]);
      temp = true;
    }
    if (!last_name) {
      setPopupMsg((prev) => [...prev, "Please Enter Last Name"]);
      temp = true;
    }

    if (email_id) {
      const emailRegex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
      if (!emailRegex.test(email_id)) {
        setPopupMsg((prev) => [...prev, "Please Enter Valid Email Format"]);
        temp = true;
      }
    }

    if (!experience) {
      setPopupMsg((prev) => [...prev, "Please Enter Experience"]);
      temp = true;
    }

    if (temp) {
      setModalVisible(true);
      return false;
    }
    return true;
  };
  // const validator = () => {
  //   setPopupMsg([]);
  //   var temp = false;

  //   if (!selectedSite) {
  //     setPopupMsg((prev) => [...prev, "Please Select Site"]);
  //     temp = true;
  //   }

  //   if (!first_name) {
  //     setPopupMsg((prev) => [...prev, "Please Enter First Name"]);
  //     temp = true;
  //   }
  //   if (!last_name) {
  //     setPopupMsg((prev) => [...prev, "Please Enter Last Name"]);
  //     temp = true;
  //   }

  //   if (email_id) {
  //     const emailRegex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
  //     if (!emailRegex.test(email_id)) {
  //       setPopupMsg((prev) => [...prev, "Please Enter Valid Email Format"]);
  //       temp = true;
  //     }
  //   }

  //   if (!experience) {
  //     setPopupMsg((prev) => [...prev, "Please Enter Experience"]);
  //     temp = true;
  //   }

  //   if (temp) {
  //     setModalVisible(true);
  //     return false;
  //   }
  //   return true;
  // };

  // Fetch site list on component mount and screen focus
  React.useEffect(() => {
    dispatch(fetchSiteList());
    dispatch(fetchProviderCredentials());
    dispatch(fetchProviderExperience());
  }, [dispatch]);

  // Fetch implanting physicians when site is selected
  const handleSiteChange = async (value: { value: string; label: string }) => {
    dispatch(showAddPhysicianModal(false));
    dispatch(setSelectedSite(value.value));
    dispatch(setSelectedPhysician(""));

    if (value.value) {
      const response = await dispatch(fetchImplantingPhysicians(value.value));
      if (response.payload) {
        // FIX: Use dispatch to call the Redux action
        dispatch(
          setImplantingPhysicianList(
            response.payload.map((physician: any) => ({
              label: physician.name,
              value: physician.id,
            }))
          )
        );
      }
    }
  };

  useFocusEffect(
    React.useCallback(() => {
      const fetchDetails = async () => {
        if (selectedPhysician && selectedSite) {
          const physicianRes = await dispatch(
            fetchSpecificImplantingPhysician(selectedPhysician)
          );
          if (physicianRes.payload) {
            dispatch(setImplantingPhysicianDetails(physicianRes.payload));
          }
        }
      };

      fetchDetails();
    }, [dispatch, selectedPhysician])
  );

  const saveDetails = async (): Promise<void> => {
    try {
      const isValid = validator();

      if (!isValid) {
        dispatch(
          setSaveStatus({
            screenId: "implantingPhysician",
            status: "validation_failed",
          })
        );
        setLocalSaveStatus("validation_failed");
        throw new Error("Validation failed");
      }

      if (saveInProgressRef.current) {
        return;
      }

      const dataCopy = { ...diff.currentValues };
      const currentDataHash = JSON.stringify(dataCopy);

      if (currentDataHash === lastSavedDataHashRef.current) {
        return;
      }

      saveInProgressRef.current = true;

      const res = await dispatch(
        putImplantingPhysician({
          physician_id: selectedPhysician,
          payload: dataCopy,
        })
      );

      if (res?.payload) {
        const physicianRes = await dispatch(
          fetchSpecificImplantingPhysician(selectedPhysician)
        );
        if (physicianRes?.payload) {
          dispatch(setImplantingPhysicianDetails(physicianRes.payload));
          lastSavedDataHashRef.current = currentDataHash;
        }
      }

      saveInProgressRef.current = false;
    } catch (err) {
      console.error("Save error:", err);
      saveInProgressRef.current = false;
      throw err;
    }
  };

  const saveStatus = useSelector(
    (state: any) => state.common?.autoSave?.["implantingPhysician"]?.status
  );

  const [localSaveStatus, setLocalSaveStatus] = React.useState<string>("");

  const effectiveSaveStatus = saveStatus || localSaveStatus;

  const handlePhysicianContextAdd = (context: string) => {
    switch (context) {
      case "implanting_physician":
        dispatch(showAddPhysicianModal(true));
        break;
      case "credential":
        // Handle credential add if needed
        break;
      default:
        console.log("Unknown context:", context);
    }
  };

  /**
   * Derive the save message and styling based on the save status
   */
  const { saveMessage, messageStyle, iconName, iconColor } =
    React.useMemo(() => {
      let message = "";
      let style = "text-primaryPurple italic";
      let icon = "alert-circle-outline";
      let color = "#8143d9";

      switch (effectiveSaveStatus) {
        case "changes_detected":
          message = "Changes detected, saving soon...";
          style = "text-primaryBlack italic";
          icon = "alert-circle-outline";
          color = "#f5c542"; // yellow
          break;
        case "saving":
          message = "Saving... Please don't close the app";
          style = "text-primaryPurple font-bold italic";
          icon = "alert-circle-outline";
          color = "#8143d9"; // purple
          break;
        case "saved":
          message = "All changes saved";
          style = "text-green-3 italic";
          icon = "check-circle-outline";
          color = "#28a745"; // green
          break;
        case "validation_failed":
          message = "Validation failed. Please check your inputs.";
          style = "text-red-3 font-bold italic";
          icon = "alert-circle-outline";
          color = "#d93025"; // red
          break;
        case "error":
          message = "Error saving changes. Please try again.";
          style = "text-red-3 font-bold italic";
          icon = "alert-circle-outline";
          color = "#d93025"; // red
          break;
      }

      return {
        saveMessage: message,
        messageStyle: style,
        iconName: icon,
        iconColor: color,
      };
    }, [effectiveSaveStatus]);

  /**
   * Ref to track previous diff values to prevent unnecessary saves
   */
  const prevDiffRef = React.useRef<string>("");

  /**
   * Ref to track if initial data has been loaded
   * This prevents autosave from triggering before data is fetched
   */
  const initialDataLoadedRef = React.useRef<boolean>(false);

  /**
   * Effect to set initialDataLoaded when data is fetched
   */
  // React.useEffect(() => {
  //   // Check if we have valid data
  //   const hasValidData = selectedSite !== undefined;

  //   if (hasValidData && !initialDataLoadedRef.current) {
  //     initialDataLoadedRef.current = true;
  //   }
  // }, [userDetails]);

  React.useEffect(() => {
    // Check if we have valid data - ensure both selectedSite and userDetails are loaded
    const hasValidData =
      selectedSite && userDetails && Object.keys(userDetails).length > 0;

    if (hasValidData && !initialDataLoadedRef.current) {
      initialDataLoadedRef.current = true;
    }
  }, [selectedSite, userDetails]);

  /**
   * Effect to trigger autosave when data changes
   * This uses our new simpleAutoSave service with local state fallback
   */
  // React.useEffect(() => {
  //   // Only proceed if initial data has been loaded and there are actual differences
  //   if (!initialDataLoadedRef.current) {
  //     return;
  //   }

  //   // Check if selectedSite exists to prevent API calls with undefined ID
  //   if (!selectedSite) {
  //     return;
  //   }

  //   // Only proceed if there are actual differences
  //   if (diff.hasDiff) {
  //     // Check if we have any undefined values in critical fields
  //     const hasUndefinedValues = Object.values(diff.currentValues).every(
  //       (val) => val === undefined
  //     );
  //     if (hasUndefinedValues) {
  //       return;
  //     }

  //     // Convert current values to string for comparison
  //     const currentDiffString = JSON.stringify(diff.currentValues);

  //     // Only trigger save if the values have actually changed
  //     if (currentDiffString !== prevDiffRef.current) {
  //       // Update the ref with current values
  //       prevDiffRef.current = currentDiffString;

  //       // Create a custom wrapper for setSaveStatus that updates both Redux and local state
  //       const setStatusWithFallback = (payload: {
  //         screenId: string;
  //         status: string;
  //       }) => {
  //         // Update local state as a fallback
  //         setLocalSaveStatus(payload.status);
  //         // Also dispatch to Redux
  //         return dispatch(setSaveStatus(payload));
  //       };

  //       // Trigger the simple autosave with the current values
  //       simpleAutoSave(
  //         "implantingPhysician", // Screen ID
  //         diff.currentValues, // Data to save
  //         saveDetails, // Save function
  //         4000, // 4 second delay before saving
  //         dispatch, // Redux dispatch
  //         setStatusWithFallback, // Custom action to set save status with fallback
  //         setLastSavedData // Action to set last saved data
  //       );
  //     } else {
  //       console.log("No actual changes in diff values, skipping autosave");
  //     }
  //   }
  // }, [
  //   diff.hasDiff,
  //   diff.currentValues,
  //   dispatch,
  //   setLocalSaveStatus,
  //   userDetails,
  // ]);

  React.useEffect(() => {
    // Only proceed if initial data has been loaded and there are actual differences
    if (!initialDataLoadedRef.current) {
      return;
    }

    // Check if selectedSite exists to prevent API calls with undefined ID
    if (!selectedSite) {
      return;
    }

    // Check if selectedPhysician exists - we need this for the save operation
    if (!selectedPhysician) {
      return;
    }

    // Check if userDetails is properly loaded
    if (!userDetails || Object.keys(userDetails).length === 0) {
      return;
    }

    // Only proceed if there are actual differences
    if (diff.hasDiff) {
      // Check if we have any undefined values in critical fields
      const hasUndefinedValues = Object.values(diff.currentValues).every(
        (val) => val === undefined
      );
      if (hasUndefinedValues) {
        return;
      }

      // Additional check: ensure we have meaningful data in required fields
      const hasRequiredData = first_name && last_name && experience;
      if (!hasRequiredData) {
        return;
      }

      // Convert current values to string for comparison
      const currentDiffString = JSON.stringify(diff.currentValues);

      // Only trigger save if the values have actually changed
      if (currentDiffString !== prevDiffRef.current) {
        // Update the ref with current values
        prevDiffRef.current = currentDiffString;

        // Create a custom wrapper for setSaveStatus that updates both Redux and local state
        const setStatusWithFallback = (payload: {
          screenId: string;
          status: string;
        }) => {
          // Update local state as a fallback
          setLocalSaveStatus(payload.status);
          // Also dispatch to Redux
          return dispatch(setSaveStatus(payload));
        };

        // Trigger the simple autosave with the current values
        simpleAutoSave(
          "implantingPhysician", // Screen ID
          diff.currentValues, // Data to save
          saveDetails, // Save function
          4000, // 4 second delay before saving
          dispatch, // Redux dispatch
          setStatusWithFallback, // Custom action to set save status with fallback
          setLastSavedData // Action to set last saved data
        );
      } else {
        console.log("No actual changes in diff values, skipping autosave");
      }
    }
  }, [
    diff.hasDiff,
    diff.currentValues,
    dispatch,
    setLocalSaveStatus,
    userDetails,
    selectedSite,
    selectedPhysician, // Add selectedPhysician to dependencies
    first_name,
    last_name,
    experience, // Add required fields to dependencies
  ]);
  const handleCancel = () => {
    dispatch(
      setImplantingPhysicianDetails({
        ...userDetails,
        ...physicianDetails,
      })
    );
  };

  const handleRefresh = async () => {
    try {
      await dispatch(fetchSiteList());

      if (selectedSite) {
        const response = await dispatch(
          fetchImplantingPhysicians(selectedSite)
        );
        if (response.payload) {
          // FIX: Use dispatch here too
          dispatch(
            setImplantingPhysicianList(
              response.payload.map((physician: any) => ({
                label: physician.name,
                value: physician.id,
              }))
            )
          );
        }

        if (selectedPhysician) {
          const physicianRes = await dispatch(
            fetchSpecificImplantingPhysician(selectedPhysician)
          );
          if (physicianRes.payload) {
            dispatch(setImplantingPhysicianDetails(physicianRes.payload));
          }
        }
      }
    } catch (error) {
      console.error("Error refreshing physician list:", error);
    }
  };

  return (
    <>
      {loader ? (
        <View className="mt-64">
          <Loader />
        </View>
      ) : (
        <ScreenWrapper direction="column" onRefresh={handleRefresh}>
          <CustomCard>
            {saveMessage && (
              <View
                style={{
                  flexDirection: "row",
                  justifyContent: "flex-end",
                  alignItems: "center",
                  marginVertical: 4,
                }}
              >
                <MaterialCommunityIcons
                  name={iconName}
                  color={iconColor}
                  size={20}
                />
                <Text
                  className={`text-sm ${messageStyle}`}
                  style={{ marginLeft: 6 }}
                >
                  {saveMessage}
                </Text>
              </View>
            )}
            <View className="mt-2 gap-3">
              <View className="flex-row justify-between items-center">
                <Heading
                  text="Hospital Site"
                  size="sub-heading"
                  showSeperator={false}
                />
                <MaterialCommunityIcons
                  name="plus-circle-outline"
                  color="#8143d9"
                  size={25}
                  onPress={onAddPress}
                />
              </View>

              <View className="">
                <SearchablePicker
                  placeholder="Select"
                  items={siteOptions}
                  value={selectedSite}
                  onValueChange={handleSiteChange}
                />
              </View>
            </View>

            {selectedSite && (
              <>
                <LineSeperator extraStyle="mt-5 mb-3" />
                <View className="mt-2 gap-3">
                  <View className="flex-row justify-between items-center">
                    <Heading
                      text="Physician"
                      size="sub-heading"
                      showSeperator={false}
                    />
                  </View>

                  <View className="">
                    <SearchablePicker
                      placeholder="Select Physician"
                      items={implantingPhysicians}
                      value={selectedPhysician}
                      onValueChange={handlePhysicianChange}
                      screenContext="implanting_physician"
                      onAddPress={handlePhysicianContextAdd}
                      showAddInSearch={true}
                    />
                  </View>
                </View>
              </>
            )}

            {selectedPhysician && userDetails && (
              <>
                <LineSeperator extraStyle="mt-5 mb-3" />

                <View className="mt-3 rounded-lg">
                  <View className="mb-3">
                    <Heading
                      text="First Name"
                      size="sub-heading"
                      showSeperator={false}
                    />
                    <View className="mt-2">
                      <CustomInput
                        inputValue={first_name}
                        placeholder="First Name"
                        onInputChange={(value) => {
                          parseCharInput(value, (parsedValue) => {
                            dispatch(
                              setImplantingPhysicianDetails({
                                ...userDetails,
                                first_name: parsedValue,
                              })
                            );
                          });
                        }}
                      />
                    </View>
                  </View>

                  <LineSeperator extraStyle="mt-2 mb-3" />

                  <View className="mb-3">
                    <Heading
                      text="Last Name"
                      size="sub-heading"
                      showSeperator={false}
                    />
                    <View className="mt-2">
                      <CustomInput
                        inputValue={last_name}
                        placeholder="Last Name"
                        onInputChange={(value) => {
                          parseCharInput(value, (parsedValue) => {
                            dispatch(
                              setImplantingPhysicianDetails({
                                ...userDetails,
                                last_name: parsedValue,
                              })
                            );
                          });
                        }}
                      />
                    </View>
                  </View>

                  <LineSeperator extraStyle="mt-2 mb-3" />

                  <View className="mb-3">
                    <Heading
                      text="Middle Name"
                      size="sub-heading"
                      showSeperator={false}
                    />
                    <View className="mt-2">
                      <CustomInput
                        inputValue={middle_name}
                        placeholder="Middle Name"
                        onInputChange={(value) => {
                          parseCharInput(value, (parsedValue) => {
                            dispatch(
                              setImplantingPhysicianDetails({
                                ...userDetails,
                                middle_name: parsedValue,
                              })
                            );
                          });
                        }}
                      />
                    </View>
                  </View>

                  <LineSeperator extraStyle="mt-2 mb-3" />

                  <View className="mb-3">
                    <Heading
                      text="Provider Credentials"
                      size="sub-heading"
                      showSeperator={false}
                    />
                    <View className="mt-2">
                      <SearchablePicker
                        placeholder="Select"
                        items={credential_options}
                        value={credential.toString()}
                        onValueChange={(value) => {
                          dispatch(
                            setImplantingPhysicianDetails({
                              credential: value.value,
                            })
                          );
                        }}
                      />
                    </View>
                  </View>

                  <LineSeperator extraStyle="mt-2 mb-3" />

                  <View className="mb-3">
                    <Heading
                      text="NPI Number"
                      size="sub-heading"
                      showSeperator={false}
                    />
                    <View className="mt-2">
                      <CustomInput
                        inputValue={npi_number?.toString()}
                        placeholder="NPI Number"
                        onInputChange={(value) => {
                          parseIntInput(value, (parsedValue) => {
                            dispatch(
                              setImplantingPhysicianDetails({
                                ...userDetails,
                                npi_number: parsedValue.toString(),
                              })
                            );
                          });
                        }}
                        keyboardType="numeric"
                      />
                    </View>
                  </View>

                  <LineSeperator extraStyle="mt-2 mb-3" />

                  <View className="mb-3">
                    <Heading
                      text="Email"
                      size="sub-heading"
                      showSeperator={false}
                    />
                    <View className="mt-2">
                      <CustomInput
                        inputValue={email_id}
                        placeholder="Email"
                        onInputChange={(value) => {
                          dispatch(
                            setImplantingPhysicianDetails({
                              ...userDetails,
                              email_id: value,
                            })
                          );
                        }}
                      />
                    </View>
                  </View>

                  <LineSeperator extraStyle="mt-2 mb-3" />

                  <View className="mb-3">
                    <Heading
                      text="Phone Number"
                      size="sub-heading"
                      showSeperator={false}
                    />
                    <View className="mt-2">
                      <CustomInput
                        inputValue={phone_number?.toString()}
                        placeholder="Phone Number"
                        onInputChange={(value) => {
                          parseIntInput(value, (parsedValue) => {
                            dispatch(
                              setImplantingPhysicianDetails({
                                ...userDetails,
                                phone_number: parsedValue.toString(),
                              })
                            );
                          });
                        }}
                        keyboardType="numeric"
                      />
                    </View>
                  </View>
                  <LineSeperator extraStyle="mt-2 mb-3" />

                  {/* <View className="mb-3">
                    <Heading
                      text="Fax Number"
                      size="sub-heading"
                      showSeperator={false}
                    />
                    <View className="mt-2">
                      <CustomInput
                        inputValue={fax_number?.toString()}
                        placeholder="Fax Number"
                        onInputChange={(value) => {
                          parseIntInput(value, (parsedValue) => {
                            dispatch(
                              setImplantingPhysicianDetails({
                                ...userDetails,
                                fax_number: parsedValue.toString(),
                              })
                            );
                          });
                        }}
                        keyboardType="numeric"
                      />
                    </View>
                  </View>

                  <LineSeperator extraStyle="mt-2 mb-3" /> */}

                  <View className="mb-3">
                    <Heading
                      text="Address"
                      size="sub-heading"
                      showSeperator={false}
                    />
                    <View className="mt-2">
                      <CustomInput
                        inputValue={address}
                        placeholder="Address"
                        onInputChange={(value) => {
                          parseCharInput(value, (parsedValue) => {
                            dispatch(
                              setImplantingPhysicianDetails({
                                ...userDetails,
                                address: parsedValue,
                              })
                            );
                          });
                        }}
                      />
                    </View>
                  </View>

                  <LineSeperator extraStyle="mt-2 mb-3" />

                  <View className="mb-3">
                    <Heading
                      text="LAAO Caseload Experience"
                      size="sub-heading"
                      showSeperator={false}
                    />
                    <View className="mt-2">
                      <SearchablePicker
                        placeholder="Select"
                        items={experience_options}
                        value={experience}
                        onValueChange={(value) => {
                          dispatch(
                            setImplantingPhysicianDetails({
                              ...userDetails,
                              experience: value.value,
                            })
                          );
                        }}
                      />
                    </View>
                  </View>
                </View>

                <LineSeperator extraStyle="my-5" />

                {/* <View className="flex-row justify-center gap-4">
                  <SaveActionButton
                    disabled={!diff.hasDiff}
                    onPress={saveDetails}
                    onCancel={handleCancel}
                  />
                </View> */}
              </>
            )}
            <View className="mb-3"></View>
          </CustomCard>
        </ScreenWrapper>
      )}
      <View className="mt-9"></View>
      <PopupModal
        show={modalVisible}
        msg={popupMsg}
        status="warning"
        onClose={() => setModalVisible(false)}
      />
    </>
  );
};

export default PhysicianList;
