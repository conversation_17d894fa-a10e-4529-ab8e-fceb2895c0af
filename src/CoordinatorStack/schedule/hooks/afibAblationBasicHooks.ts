import { useSelector } from "react-redux";
import { RootState } from "../../../store";
import { IAfibBasic } from "../../../store/rep/ScheduleStack/afibAblation/basic/types";

const useLoaderAndError = () => {
  const loader = useSelector(
    (state: RootState) => state.coordinator.schedule.afib.basic.loading
  );
  const error = useSelector(
    (state: RootState) => state.coordinator.schedule.afib.basic.error
  );
  return { loader, error };
};

const useAfibBasicUserDetails = (): IAfibBasic | null => {
  const { userDetails } = useSelector(
    (state: RootState) => state.coordinator.schedule.afib.basic
  );
  return userDetails;
};

const useAfibBAsicDetails = () => {
  const { basic } = useSelector(
    (state: RootState) => state.coordinator.schedule.afib.basic
  );
  return basic;
};

const findAfibBasicDiff = () => {
  const userDetails = useAfibBasicUserDetails();
  const basic = useAfibBAsicDetails();

  const diff = {
    procedure_date: userDetails?.procedure_date === basic?.procedure_date,
    physician_id:
      userDetails?.physician_details?.selected?.id ===
      basic?.physician_details?.selected?.id,
    site_id:
      userDetails?.site_details?.selected?.id ===
      basic?.site_details?.selected?.id,
    groin_access_start_time:
      userDetails?.groin_access_start_time === basic?.groin_access_start_time,
    sheath_removal_end_time:
      userDetails?.sheath_removal_end_time === basic?.sheath_removal_end_time,
  };

  const currentValues = {
    procedure_date: userDetails?.procedure_date,
    physician_id: userDetails?.physician_details?.selected?.id,
    site_id: userDetails?.site_details?.selected?.id,
    groin_access_start_time: userDetails?.groin_access_start_time,
    sheath_removal_end_time: userDetails?.sheath_removal_end_time,
  };

  return {
    hasDiff: Object.values(diff).includes(false),
    currentValues: currentValues,
  };
};

export {
  useLoaderAndError,
  useAfibBasicUserDetails,
  findAfibBasicDiff,
  useAfibBAsicDetails,
};
