import React, {
  forwardRef,
  useImperativeHandle,
  useState,
  useRef,
  useEffect,
} from "react";
import {
  Modal,
  View,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  ScrollView,
  PanResponder,
  Animated,
  Keyboard,
  Platform,
  KeyboardAvoidingView,
} from "react-native";

const { height: SCREEN_HEIGHT } = Dimensions.get("window");

export interface CustomBottomModalProps {
  children: React.ReactNode;
  backgroundColor?: string;
  onClose?: () => void;
  height?: string; // e.g., "90%"
}

export interface CustomBottomModalRefProps {
  open: () => void;
  close: () => void;
}

const CustomBottomModal = forwardRef<
  CustomBottomModalRefProps,
  CustomBottomModalProps
>(({ children, backgroundColor = "#f8f9fa", onClose, height = "90%" }, ref) => {
  const [visible, setVisible] = useState(false);
  const [keyboardHeight, setKeyboardHeight] = useState(0);
  const translateY = useRef(new Animated.Value(SCREEN_HEIGHT)).current;
  const modalHeight = SCREEN_HEIGHT * (parseInt(height) / 100);

  const openModal = () => {
    setVisible(true);
    // Start from bottom and animate up
    translateY.setValue(SCREEN_HEIGHT);
    Animated.spring(translateY, {
      toValue: SCREEN_HEIGHT - modalHeight,
      useNativeDriver: true,
      tension: 100,
      friction: 8,
    }).start(() => {
      console.log("Simple animation completed");
    });
  };

  const closeModal = () => {
    // Hide the modal immediately to remove overlay
    setVisible(false);
    onClose?.();

    // Still animate the modal out for smooth transition
    Animated.spring(translateY, {
      toValue: SCREEN_HEIGHT,
      useNativeDriver: true,
      tension: 100,
      friction: 8,
    }).start();
  };

  useImperativeHandle(ref, () => ({
    open: openModal,
    close: closeModal,
  }));

  // Keyboard event listeners
  useEffect(() => {
    let keyboardShowListener: any;
    let keyboardHideListener: any;

    if (Platform.OS === "ios") {
      // iOS - Use keyboardWillShow/Hide for smooth animations
      keyboardShowListener = Keyboard.addListener(
        "keyboardWillShow",
        (event) => {
          setKeyboardHeight(event.endCoordinates.height);

          // Animate to new position when keyboard shows
          const targetPosition =
            SCREEN_HEIGHT - modalHeight - event.endCoordinates.height + 20;
          const minPosition = 50;
          const newPosition = Math.max(targetPosition, minPosition);

          Animated.timing(translateY, {
            toValue: newPosition,
            duration: event.duration || 250,
            useNativeDriver: true,
          }).start();
        }
      );

      keyboardHideListener = Keyboard.addListener(
        "keyboardWillHide",
        (event) => {
          setKeyboardHeight(0);

          // Animate back to original position when keyboard hides
          Animated.timing(translateY, {
            toValue: SCREEN_HEIGHT - modalHeight,
            duration: event.duration || 250,
            useNativeDriver: true,
          }).start();
        }
      );
    } else {
      // Android - Use keyboardDidShow/Hide
      keyboardShowListener = Keyboard.addListener(
        "keyboardDidShow",
        (event) => {
          setKeyboardHeight(event.endCoordinates.height);

          // Animate to new position when keyboard shows
          const targetPosition =
            SCREEN_HEIGHT - modalHeight - event.endCoordinates.height + 20;
          const minPosition = 50;
          const newPosition = Math.max(targetPosition, minPosition);

          Animated.timing(translateY, {
            toValue: newPosition,
            duration: 250,
            useNativeDriver: true,
          }).start();
        }
      );

      keyboardHideListener = Keyboard.addListener("keyboardDidHide", () => {
        setKeyboardHeight(0);

        // Animate back to original position when keyboard hides
        Animated.timing(translateY, {
          toValue: SCREEN_HEIGHT - modalHeight,
          duration: 250,
          useNativeDriver: true,
        }).start();
      });
    }

    return () => {
      keyboardShowListener?.remove();
      keyboardHideListener?.remove();
    };
  }, [modalHeight, translateY]);

  const handleBackdropPress = () => {
    Keyboard.dismiss();
    closeModal();
  };

  const panResponder = PanResponder.create({
    onStartShouldSetPanResponder: (evt, gestureState) => {
      // Only respond to gestures that start near the top of the modal (handle area)
      const modalTop = SCREEN_HEIGHT - modalHeight;
      return evt.nativeEvent.pageY < modalTop + 60; // 60px from top of modal
    },
    onMoveShouldSetPanResponder: (evt, gestureState) => {
      // Respond to vertical gestures more than 5px
      return (
        Math.abs(gestureState.dy) > 5 &&
        Math.abs(gestureState.dy) > Math.abs(gestureState.dx)
      );
    },
    onPanResponderGrant: () => {
      // Disable keyboard avoidance during drag
      Keyboard.dismiss();
    },
    onPanResponderMove: (evt, gestureState) => {
      // Only allow downward movement
      const basePosition = SCREEN_HEIGHT - modalHeight;
      const newTranslateY = basePosition + gestureState.dy;

      if (newTranslateY >= basePosition) {
        translateY.setValue(newTranslateY);
      }
    },
    onPanResponderRelease: (evt, gestureState) => {
      // Close if dragged down significantly or fast swipe down
      if (gestureState.dy > 80 || gestureState.vy > 0.8) {
        closeModal();
      } else {
        // Snap back to original position
        Animated.spring(translateY, {
          toValue: SCREEN_HEIGHT - modalHeight,
          useNativeDriver: true,
          tension: 100,
          friction: 8,
        }).start();
      }
    },
    onPanResponderTerminate: () => {
      // If gesture is terminated, snap back
      Animated.spring(translateY, {
        toValue: SCREEN_HEIGHT - modalHeight,
        useNativeDriver: true,
        tension: 100,
        friction: 8,
      }).start();
    },
  });

  return (
    <Modal
      visible={visible}
      transparent
      animationType="none"
      statusBarTranslucent
      onRequestClose={handleBackdropPress}
    >
      <KeyboardAvoidingView
        style={styles.overlay}
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        keyboardVerticalOffset={Platform.OS === "ios" ? 0 : 20}
      >
        <TouchableOpacity
          style={styles.backdrop}
          activeOpacity={1}
          onPress={handleBackdropPress}
        />
        <Animated.View
          style={[
            styles.modalContainer,
            {
              backgroundColor,
              height: modalHeight,
              transform: [{ translateY }],
            },
          ]}
          {...panResponder.panHandlers}
        >
          {/* Handle bar */}
          <View style={styles.handleContainer}>
            <View style={styles.handle} />
          </View>

          {/* Scrollable content */}
          <ScrollView
            style={styles.scrollView}
            contentContainerStyle={styles.scrollContent}
            showsVerticalScrollIndicator={true}
            bounces={false}
            keyboardShouldPersistTaps="handled"
            keyboardDismissMode="interactive"
          >
            {children}
          </ScrollView>
        </Animated.View>
      </KeyboardAvoidingView>
    </Modal>
  );
});

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
  },
  backdrop: {
    flex: 1,
  },
  modalContainer: {
    position: "absolute",
    left: 0,
    right: 0,
    bottom: 0,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: -2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  handleContainer: {
    alignItems: "center",
    paddingVertical: 12,
  },
  handle: {
    width: 50,
    height: 5,
    backgroundColor: "#999",
    borderRadius: 3,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: 20,
  },
});

export default CustomBottomModal;
