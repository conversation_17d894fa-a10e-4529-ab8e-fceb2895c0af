import { createSlice } from "@reduxjs/toolkit";
import initialState from "./initialstate";
import {
  fetchCoordinatorAfibAblationBasic,
  putAfibAblationBasic,
} from "./thunk";

const afibAblationBasicSlice = createSlice({
  name: "afibAblationBasic",
  initialState,
  reducers: {
    setAfibBAsicUserDetails: (state, action) => {
      state.userDetails = {
        ...state.userDetails,
        ...action.payload,
      };
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchCoordinatorAfibAblationBasic.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchCoordinatorAfibAblationBasic.fulfilled, (state, action) => {
        state.loading = false;
        state.basic = action.payload;
        state.userDetails = action.payload;
      })
      .addCase(fetchCoordinatorAfibAblationBasic.rejected, (state, action) => {
        state.loading = false;
        state.basic = null;
        state.error = action.payload as string;
      });

    builder
      .addCase(putAfibAblationBasic.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(putAfibAblationBasic.fulfilled, (state, action) => {
        state.loading = false;
        state.basic = action.payload;
        state.userDetails = action.payload;
      })
      .addCase(putAfibAblationBasic.rejected, (state, action) => {
        state.loading = false;
        state.basic = null;
        state.error = action.payload as string;
      });
  },
});

export const { setAfibBAsicUserDetails } = afibAblationBasicSlice.actions;
export default afibAblationBasicSlice.reducer;
