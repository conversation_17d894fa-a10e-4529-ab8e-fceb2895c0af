import * as React from "react";
import { View, Text } from "react-native";
import CustomText from "./CustomText";
import CustomCheckBox from "./CustomCheckBox";
import globalStyles from "../styles/GlobalStyles";
import SaveActionButton from "./SaveActionButton";

interface ICHADCardProps {
  chad: any;
  diff: any;
  onChadChange: (updatedChad: any) => void;
  onSave: (updatedChad: any) => void;
}

const CHADCard: React.FunctionComponent<ICHADCardProps> = ({
  chad,
  onSave,
}) => {
  // Initialize with an empty object if chad is undefined
  const defaultChad = chad || {};
  const [updatedChad, setUpdatedChad] = React.useState(defaultChad);
  const [diff, setDiff] = React.useState(false);

  React.useEffect(() => {
    // Update state when chad changes, ensuring we never set undefined
    setUpdatedChad(chad || {});
    setDiff(false);
  }, [chad]);

  const handleCheckBoxChange = (key: string, value: boolean) => {
    setUpdatedChad((prev: any) => {
      // Simply update the key with the new isChecked value without special handling.
      const newChad = { ...prev, [key]: { ...prev[key], isChecked: value } };

      // Update `diff` flag if any change is detected, only if chad is defined
      if (chad) {
        setDiff(
          Object.keys(newChad).some(
            (k) => newChad[k]?.isChecked !== chad[k]?.isChecked
          )
        );
      }

      return newChad;
    });
  };

  const saveDetails = () => {
    // If chad is undefined, use updatedChad as the source of keys
    const sourceObject = chad || updatedChad;

    const formattedDiff = Object.keys(sourceObject).reduce((acc, key) => {
      if (key !== "age" && key !== "sex") {
        // Exclude age and sex. For object fields, update the boolean flag.
        if (typeof sourceObject[key] === "object") {
          // Use optional chaining to safely access properties
          acc[key] =
            updatedChad[key]?.isChecked ?? sourceObject[key]?.isChecked;
        } else {
          acc[key] = updatedChad[key];
        }
      }
      return acc;
    }, {} as Record<string, boolean>);

    onSave(formattedDiff);
  };

  const handleCancel = () => {
    setUpdatedChad(chad || {});
    setDiff(false);
  };
  return (
    <View className="px-4">
      <View className={`${globalStyles.containers.flex_center} mb-6`}>
        <Text style={{ fontSize: 18, color: "#6A1B9A", fontWeight: "bold" }}>
          CHA
          <Text style={{ fontSize: 12, bottom: 2, position: "relative" }}>
            2
          </Text>
          DS
          <Text style={{ fontSize: 12, bottom: 2, position: "relative" }}>
            2
          </Text>
          -Vasc Score
        </Text>
      </View>
      <View className="flex-row justify-between items-center border-b p-2 my-1">
        <Text className="text-primaryBlack font-medium">CHF</Text>
        <View className="w-[40%] flex-row items-center justify-between">
          <Text className="text-primaryBlack">
            {updatedChad?.congestive_heart_failure?.value}
          </Text>
          <CustomCheckBox
            checked={updatedChad?.congestive_heart_failure?.isChecked}
            onChange={(value) =>
              handleCheckBoxChange("congestive_heart_failure", value)
            }
          />
        </View>
      </View>

      <View className="flex-row justify-between items-center border-b p-2 my-1">
        <Text className="text-primaryBlack font-medium">HTN</Text>
        <View className="w-[40%] flex-row items-center justify-between">
          <Text className="text-primaryBlack">
            {updatedChad?.hypertension?.value}
          </Text>
          <CustomCheckBox
            checked={updatedChad?.hypertension?.isChecked}
            onChange={(value) => handleCheckBoxChange("hypertension", value)}
          />
        </View>
      </View>

      <View
        className={`${globalStyles.containers.flex_between_row} border-b p-2 my-1`}
      >
        <Text className="text-primaryBlack font-medium">Age</Text>
        <View className="w-[40%] flex-row items-center justify-between">
          <Text className="text-primaryBlack">{updatedChad?.age}</Text>
        </View>
      </View>

      <View className="flex-row justify-between items-center border-b p-2 my-1">
        <Text className="text-primaryBlack font-medium">Diabetes Mellitus</Text>
        <View className="w-[40%] flex-row items-center justify-between">
          <Text className="text-primaryBlack">
            {updatedChad?.diabetes_mellitus?.value}
          </Text>
          <CustomCheckBox
            checked={updatedChad?.diabetes_mellitus?.isChecked}
            onChange={(value) =>
              handleCheckBoxChange("diabetes_mellitus", value)
            }
          />
        </View>
      </View>

      <View className="flex-row justify-between items-center border-b p-2 my-1">
        <Text className="text-primaryBlack font-medium">Stroke/TIA/TE</Text>
        <View className="w-[40%] flex-row items-center justify-between">
          <Text className="text-primaryBlack">
            {updatedChad?.stroke_thromboembic_event_tia?.value}
          </Text>
          <CustomCheckBox
            checked={updatedChad?.stroke_thromboembic_event_tia?.isChecked}
            onChange={(value) =>
              handleCheckBoxChange("stroke_thromboembic_event_tia", value)
            }
          />
        </View>
      </View>
      <View className="flex-row justify-between items-center border-b p-2 my-1">
        <Text className="text-primaryBlack font-medium">Vascular Disease</Text>
        <View className="w-[40%] flex-row items-center justify-between">
          <Text className="text-primaryBlack">
            {updatedChad?.vascular_disease?.value}
          </Text>
          <CustomCheckBox
            checked={updatedChad?.vascular_disease?.isChecked}
            onChange={(value) =>
              handleCheckBoxChange("vascular_disease", value)
            }
          />
        </View>
      </View>

      <View
        className={`${globalStyles.containers.flex_between_row} border-b p-2 my-1`}
      >
        <CustomText value="Sex" />
        <View className="w-[40%] flex-row items-center justify-between">
          <CustomText value={updatedChad?.sex} />
        </View>
      </View>

      <View className="mt-2 mb-5 border-t border-primaryWhite" />

      <View className="flex-row justify-center gap-4">
        <SaveActionButton
          disabled={!diff}
          onPress={saveDetails}
          onCancel={handleCancel}
        />
      </View>
    </View>
  );
};

export default CHADCard;
