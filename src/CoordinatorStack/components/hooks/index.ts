import { RootState } from "../../../store";
import { useSelector } from "react-redux";

const useChatBotState = () => {
  const chatBotIsOpened = useSelector(
    (state: RootState) => state.chatbot.isOpened
  );
  const chatBotScreenFocused = useSelector(
    (state: RootState) => state.chatbot.currentScreen
  );
  const screenPath = useSelector(
    (state: RootState) => state.chatbot.screenPath
  );
  const patientId = useSelector((state: RootState) => state.chatbot.patientId);
  const patientName = useSelector(
    (state: RootState) => state.chatbot.patientName
  );
  return {
    patientId,
    patientName,
    chatBotIsOpened,
    chatBotScreenFocused,
    screenPath,
  };
};

export { useChatBotState };
