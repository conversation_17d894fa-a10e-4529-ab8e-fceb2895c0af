import { createSlice } from "@reduxjs/toolkit";
import { fetchCTADeails, fetchTruplanUrl } from "./thunk";
import initialState from "./initialstate";

const ctaSlice = createSlice({
  name: "cta",
  initialState,
  reducers: {
    setCtaNotes: (state, action) => {
      state.ctaDetails.notes = action.payload;
    },
    resetCtaNotes: (state) => {
      state.ctaDetails.notes = state.apiCtaDetails.notes;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchCTADeails.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchCTADeails.fulfilled, (state, action) => {
        state.loading = false;
        state.ctaDetails = action.payload;
      })
      .addCase(fetchCTADeails.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
        state.ctaDetails = initialState.ctaDetails;
      });

    builder
      .addCase(fetchTruplanUrl.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchTruplanUrl.fulfilled, (state, action) => {
        state.loading = false;
        state.truplanPdfLink = action.payload;
      })
      .addCase(fetchTruplanUrl.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
        state.truplanPdfLink = initialState.truplanPdfLink;
      });
  },
});

export const { setCtaNotes, resetCtaNotes } = ctaSlice.actions;
export default ctaSlice.reducer;
