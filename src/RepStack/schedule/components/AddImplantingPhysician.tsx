import React from "react";
import { View, Text } from "react-native";
import { useDispatch } from "react-redux";
import { AppDispatch } from "../../../store";
import { useFocusEffect } from "@react-navigation/native";
import { useSiteList } from "../hooks/schedulesHooks";
import {
  fetchProcedureTypes,
  fetchProviderCredentials,
  fetchProviderExperience,
  postImplantingPhysician,
} from "../../../store/rep/ScheduleStack/addPhysician/thunk";
import {
  useLoaderAndError,
  formatPostImplantingPhysician,
  useImplantingPhysiciansDetails,
  useUserImplantingPhysiciansDetails,
  findPostImplantingPhysicianDiff,
  useProcedureTypes,
  useSelectedSite,
} from "../hooks/addImplantingPhysicianHooks.ts";
import {
  setImplantingPhysicianDetails,
  resetImplantingPhysicianDetails,
  showAddPhysicianModal,
  setSelectedSite,
  setSelectedPhysician,
  setImplantingPhysicianList,
} from "../../../store/rep/ScheduleStack/addPhysician";
import CustomBottomSheetTextInput from "../../../components/CustomBottomSheetTextInput";
import LineSeperator from "../../../components/LineSeperator";
import SaveActionButton from "../../../components/SaveActionButton";
import Loader from "../../../components/Loader";
import { parseCharInput, parseEmailInput, parseIntInput } from "../../../utils";
import PopupModal from "../../../components/Popup";
import SearchablePicker from "../../../components/SearchablePicker.tsx";
import { fetchSiteList } from "../../../store/rep/ScheduleStack/schedules/thunk.ts";
import { fetchImplantingPhysicians } from "../../../store/rep/ScheduleStack/addpatient/thunk.ts";
import { setSelectedProvider } from "../../../store/common/addReferringProvider/index.ts";
import PhysicianList from "./PhysicianList.tsx";

interface AddImplantingPhysicianProps {
  onSuccess?: (newPhysicianId: string) => void;
  onCancel?: () => void;
  bottomSheetRef?: React.RefObject<any>;
  currentSiteId?: string; // Current site from PatientDemographicsCard
}

const AddImplantingPhysician: React.FC<AddImplantingPhysicianProps> = ({
  onSuccess,
  onCancel,
  bottomSheetRef,
  currentSiteId,
}) => {
  const dispatch = useDispatch<AppDispatch>();

  const [popupMsg, setPopupMsg] = React.useState<string[]>([]);
  const [modalVisible, setModalVisible] = React.useState(false);
  const [siteList, setSiteList] = React.useState<
    { label: string; value: string }[]
  >([]);
  const [implantingPhysicianId, setImplantingPhysicianId] =
    React.useState<string>();
  const [siteSelected, setSiteSelected] = React.useState(currentSiteId || "");
  const userDetails = useUserImplantingPhysiciansDetails();
  const siteListData = useSiteList();
  const selectedSite = useSelectedSite();
  const {
    first_name,
    last_name,
    middle_name,
    credential,
    credential_options,
    npi_number,
    email_id,
    phone_number,
    fax_number,
    address,
    city,
    state,
    zip_code,
    experience,
    experience_options,
  } = formatPostImplantingPhysician(userDetails);

  const { loader, error } = useLoaderAndError();
  const diff = findPostImplantingPhysicianDiff();

  const validator = () => {
    setPopupMsg([]);
    var temp = false;

    if (!siteSelected) {
      setPopupMsg((prev) => [...prev, "Please Select Site"]);
      temp = true;
    }

    if (!first_name) {
      setPopupMsg((prev) => [...prev, "Please Enter First Name"]);
      temp = true;
    }
    if (!last_name) {
      setPopupMsg((prev) => [...prev, "Please Enter Last Name"]);
      temp = true;
    }

    if (email_id) {
      const emailRegex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
      if (!emailRegex.test(email_id)) {
        setPopupMsg((prev) => [...prev, "Please Enter Valid Email Format"]);
        temp = true;
      }
    }

    if (!experience) {
      setPopupMsg((prev) => [...prev, "Please Enter Experience"]);
      temp = true;
    }

    if (temp) {
      setModalVisible(true);
      return false;
    }
    return true;
  };

  useFocusEffect(
    React.useCallback(() => {
      dispatch(fetchSiteList());
      // Fetch provider credentials and experience options for dropdowns
      dispatch(fetchProviderCredentials());
      dispatch(fetchProviderExperience());
    }, [dispatch])
  );

  React.useEffect(() => {
    if (siteListData && Array.isArray(siteListData)) {
      setSiteList(
        siteListData.map((site) => ({
          label: site.name,
          value: site.id,
        }))
      );
    }
  }, [siteListData]);

  const saveDetails = async () => {
    try {
      if (validator()) {
        const res = await dispatch(
          postImplantingPhysician({
            site_id: siteSelected,
            payload: diff.currentValues,
          })
        );

        if (res) {
          const newPhysicianId = res.payload?.id || res.payload?.physician_id;

          if (newPhysicianId) {
            onSuccess?.(newPhysicianId);
          } else {
            console.error("No physician ID found in response");
          }

          // NOTE: Removed Redux updates here - PatientDemographicsCard handles them
          // This follows the same pattern as referring provider and PCP provider
        }
      }
    } catch (err) {
      console.error(err);
    }
  };

  const handleCancel = () => {
    dispatch(resetImplantingPhysicianDetails());
    dispatch(showAddPhysicianModal(false));
    bottomSheetRef?.current?.close();
  };
  return (
    <>
      {loader ? (
        <View className="mt-64">
          <Loader />
        </View>
      ) : (
        <View className="p-3 bg-primaryWhite">
          <View className="rounded-md p-3">
            <Text className="text-primaryPurple text-center font-semibold text-lg mb-4">
              Add Implanting Physician
            </Text>

            <View className="flex-row justify-center gap-4">
              <SaveActionButton
                disabled={!diff}
                onPress={saveDetails}
                onCancel={handleCancel}
              />
            </View>

            <LineSeperator extraStyle="my-5" />

            <View className="flex-row justify-between items-center">
              <Text className="text-primaryBlack font-medium w-[40%]">
                Hospital Site <Text className="text-red-3 text-lg">*</Text>
              </Text>
              <View className="flex-1">
                <SearchablePicker
                  placeholder="Select"
                  items={siteList}
                  value={siteSelected}
                  onValueChange={(value) => {
                    // Only allow changes if no currentSiteId is provided
                    if (!currentSiteId) {
                      setSiteSelected(value.value);
                    }
                  }}
                  error={!siteSelected}
                />
              </View>
            </View>

            <LineSeperator extraStyle="my-5" />

            <View className="flex-row justify-between items-center">
              <Text className="text-primaryBlack font-medium w-[40%]">
                First Name <Text className="text-red-3 text-lg">*</Text>
              </Text>
              <View className="flex-1">
                <CustomBottomSheetTextInput
                  inputValue={first_name}
                  placeholder="First Name"
                  onInputChange={(value) => {
                    parseCharInput(value, (parsedValue) => {
                      dispatch(
                        setImplantingPhysicianDetails({
                          first_name: parsedValue,
                        })
                      );
                    });
                  }}
                />
              </View>
            </View>

            <LineSeperator extraStyle="my-5" />

            <View className="flex-row justify-between items-center">
              <Text className="text-primaryBlack font-medium w-[40%]">
                Last Name <Text className="text-red-3 text-lg">*</Text>
              </Text>
              <View className="flex-1">
                <CustomBottomSheetTextInput
                  inputValue={last_name}
                  placeholder="Last Name"
                  onInputChange={(value) => {
                    parseCharInput(value, (parsedValue) => {
                      dispatch(
                        setImplantingPhysicianDetails({
                          last_name: parsedValue,
                        })
                      );
                    });
                  }}
                />
              </View>
            </View>

            <LineSeperator extraStyle="my-5" />

            <View className="flex-row justify-between items-center">
              <Text className="text-primaryBlack font-medium w-[40%]">
                Middle Name
              </Text>
              <View className="flex-1">
                <CustomBottomSheetTextInput
                  inputValue={middle_name}
                  placeholder="Middle Name"
                  onInputChange={(value) => {
                    parseCharInput(value, (parsedValue) => {
                      dispatch(
                        setImplantingPhysicianDetails({
                          middle_name: parsedValue,
                        })
                      );
                    });
                  }}
                />
              </View>
            </View>

            <LineSeperator extraStyle="my-5" />

            <View className="flex-row justify-between items-center">
              <Text className="text-primaryBlack font-medium w-[40%]">
                Provider Credentials
              </Text>
              <View className="flex-1">
                <SearchablePicker
                  placeholder="Select"
                  items={credential_options}
                  value={credential}
                  onValueChange={(value) => {
                    dispatch(
                      setImplantingPhysicianDetails({
                        credential: value.value,
                      })
                    );
                  }}
                  disableError
                />
              </View>
            </View>

            <LineSeperator extraStyle="my-5" />

            <View className="flex-row justify-between items-center">
              <Text className="text-primaryBlack font-medium w-[40%]">
                NPI Number
              </Text>
              <View className="flex-1">
                <CustomBottomSheetTextInput
                  inputValue={npi_number?.toString()}
                  placeholder="NPI Number"
                  onInputChange={(value) => {
                    parseIntInput(value, (parsedValue) => {
                      dispatch(
                        setImplantingPhysicianDetails({
                          npi_number: parsedValue.toString(),
                        })
                      );
                    });
                  }}
                  keyboardType="numeric"
                />
              </View>
            </View>

            <LineSeperator extraStyle="my-5" />

            <View className="flex-row justify-between items-center">
              <Text className="text-primaryBlack font-medium w-[40%]">
                Email
              </Text>
              <View className="flex-1">
                <CustomBottomSheetTextInput
                  inputValue={email_id}
                  placeholder="Email"
                  onInputChange={(value) => {
                    parseEmailInput(value, (parsedValue) => {
                      dispatch(
                        setImplantingPhysicianDetails({
                          email_id: parsedValue,
                        })
                      );
                    });
                  }}
                />
              </View>
            </View>

            <LineSeperator extraStyle="my-5" />

            <View className="flex-row justify-between items-center">
              <Text className="text-primaryBlack font-medium w-[40%]">
                Phone Number
              </Text>
              <View className="flex-1">
                <CustomBottomSheetTextInput
                  inputValue={phone_number?.toString()}
                  placeholder="Phone Number"
                  onInputChange={(value) => {
                    parseIntInput(value, (parsedValue) => {
                      dispatch(
                        setImplantingPhysicianDetails({
                          phone_number: parsedValue.toString(),
                        })
                      );
                    });
                  }}
                  keyboardType="numeric"
                />
              </View>
            </View>

            <LineSeperator extraStyle="my-5" />

            {/* <View className="flex-row justify-between items-center">
              <Text className="text-primaryBlack font-medium w-[40%]">
                Fax Number
              </Text>
              <View className="flex-1">
                <CustomBottomSheetTextInput
                  inputValue={fax_number}
                  placeholder="Fax Number"
                  onInputChange={(value) => {
                    parseIntInput(value, (parsedValue) => {
                      dispatch(
                        setImplantingPhysicianDetails({
                          fax_number: parsedValue.toString(),
                        })
                      );
                    });
                  }}
                  keyboardType="numeric"
                />
              </View>
            </View>

            <LineSeperator extraStyle="my-5" /> */}

            {/* <View className="flex-row justify-between items-center">
              <Text className="text-primaryBlack font-medium w-[40%]">
                Address
              </Text>
              <View className="flex-1">
                <CustomBottomSheetTextInput
                  inputValue={address}
                  placeholder="Address"
                  onInputChange={(value) => {
                    dispatch(
                      setImplantingPhysicianDetails({
                        address: value,
                      })
                    );
                  }}
                />
              </View>
            </View>

            <LineSeperator extraStyle="my-5" />

            <View className="flex-row justify-between items-center">
              <Text className="text-primaryBlack font-medium w-[40%]">
                City
              </Text>
              <View className="flex-1">
                <CustomBottomSheetTextInput
                  inputValue={city}
                  placeholder="City"
                  onInputChange={(value) => {
                    parseCharInput(value, (parsedValue) => {
                      dispatch(
                        setImplantingPhysicianDetails({
                          city: parsedValue,
                        })
                      );
                    });
                  }}
                />
              </View>
            </View>

            <LineSeperator extraStyle="my-5" />

            <View className="flex-row justify-between items-center">
              <Text className="text-primaryBlack font-medium w-[40%]">
                State
              </Text>
              <View className="flex-1">
                <CustomBottomSheetTextInput
                  inputValue={state}
                  placeholder="State"
                  onInputChange={(value) => {
                    parseCharInput(value, (parsedValue) => {
                      dispatch(
                        setImplantingPhysicianDetails({
                          state: parsedValue,
                        })
                      );
                    });
                  }}
                />
              </View>
            </View>

            <LineSeperator extraStyle="my-5" />

            <View className="flex-row justify-between items-center">
              <Text className="text-primaryBlack font-medium w-[40%]">
                Zip Code
              </Text>
              <View className="flex-1">
                <CustomBottomSheetTextInput
                  inputValue={zip_code?.toString()}
                  placeholder="Zip Code"
                  onInputChange={(value) => {
                    parseIntInput(value, (parsedValue) => {
                      dispatch(
                        setImplantingPhysicianDetails({
                          zip_code: parsedValue.toString(),
                        })
                      );
                    });
                  }}
                  keyboardType="numeric"
                />
              </View>
            </View>

            <LineSeperator extraStyle="my-5" /> */}

            <View className="flex-row justify-between items-center">
              <Text className="text-primaryBlack font-medium w-[40%]">
                LAAO Caseload Experience{" "}
                <Text className="text-red-3 text-lg">*</Text>
              </Text>
              <View className="flex-1">
                <SearchablePicker
                  placeholder="Select"
                  items={experience_options}
                  value={experience}
                  onValueChange={(value) => {
                    dispatch(
                      setImplantingPhysicianDetails({
                        experience: value.value,
                      })
                    );
                  }}
                  error={!experience}
                />
              </View>
            </View>

            <LineSeperator extraStyle="my-5" />
          </View>
        </View>
      )}
      <PopupModal
        show={modalVisible}
        msg={popupMsg}
        status="warning"
        onClose={() => setModalVisible(false)}
      />
    </>
  );
};

export default AddImplantingPhysician;

// import React from "react";
// import { View, Text } from "react-native";
// import { useDispatch } from "react-redux";
// import { AppDispatch } from "../../../store";
// import { useFocusEffect } from "@react-navigation/native";
// import { useSiteList } from "../hooks/schedulesHooks";
// import {
//   fetchProcedureTypes,
//   fetchSpecificImplantingPhysician,
//   postImplantingPhysician,
// } from "../../../store/rep/ScheduleStack/addPhysician/thunk";
// import {
//   useLoaderAndError,
//   formatPostImplantingPhysician,
//   useImplantingPhysiciansDetails,
//   useUserImplantingPhysiciansDetails,
//   findPostImplantingPhysicianDiff,
//   useProcedureTypes,
//   useSelectedSite,
//   useSiteListGlobal,
//   useSiteSelectedGlobal,
//   usePopupMsgGlobal,
//   useModalVisibleGlobal,
// } from "../hooks/addImplantingPhysicianHooks.ts";
// import {
//   setImplantingPhysicianDetails,
//   resetImplantingPhysicianDetails,
//   showAddPhysicianModal,
//   setSelectedSite,
//   setSelectedPhysician,
//   setSiteList,
//   setSiteSelected,
//   setPopupMsg,
//   addPopupMsg,
//   clearPopupMsg,
//   setModalVisible,
//   resetAddPhysicianForm,
// } from "../../../store/rep/ScheduleStack/addPhysician";
// import CustomBottomSheetTextInput from "../../../components/CustomBottomSheetTextInput";
// import LineSeperator from "../../../components/LineSeperator";
// import SaveActionButton from "../../../components/SaveActionButton";
// import Loader from "../../../components/Loader";
// import { parseCharInput, parseEmailInput, parseIntInput } from "../../../utils";
// import PopupModal from "../../../components/Popup";
// import SearchablePicker from "../../../components/SearchablePicker.tsx";
// import { fetchSiteList } from "../../../store/rep/ScheduleStack/schedules/thunk.ts";
// import { fetchImplantingPhysicians } from "../../../store/rep/ScheduleStack/addpatient/thunk.ts";
// // import { setSelectedProvider } from "../../../store/common/addReferringProvider/index.ts";

// interface AddImplantingPhysicianProps {
//   onSuccess?: () => void;
//   bottomSheetRef?: React.RefObject<any>;
// }

// const AddImplantingPhysician: React.FC<AddImplantingPhysicianProps> = ({
//   onSuccess,
//   bottomSheetRef,
// }) => {
//   const dispatch = useDispatch<AppDispatch>();
//   const siteList = useSiteListGlobal();
//   const siteSelected = useSiteSelectedGlobal();
//   const popupMsg = usePopupMsgGlobal();
//   const modalVisible = useModalVisibleGlobal();
//   const userDetails = useUserImplantingPhysiciansDetails();
//   const siteListData = useSiteList();
//   const selectedSite = useSelectedSite();
//   const {
//     first_name,
//     last_name,
//     middle_name,
//     credential,
//     credential_options,
//     npi_number,
//     email_id,
//     phone_number,
//     fax_number,
//     address,
//     city,
//     state,
//     zip_code,
//     experience,
//     experience_options,
//   } = formatPostImplantingPhysician(userDetails);

//   const { loader, error } = useLoaderAndError();
//   const diff = findPostImplantingPhysicianDiff();

//   const validator = () => {
//     setPopupMsg([]);
//     var temp = false;

//     if (!siteSelected) {
//       setPopupMsg((prev) => [...prev, "Please Select Site"]);
//       temp = true;
//     }

//     if (!first_name) {
//       setPopupMsg((prev) => [...prev, "Please Enter First Name"]);
//       temp = true;
//     }
//     if (!last_name) {
//       setPopupMsg((prev) => [...prev, "Please Enter Last Name"]);
//       temp = true;
//     }

//     if (email_id) {
//       const emailRegex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
//       if (!emailRegex.test(email_id)) {
//         setPopupMsg((prev) => [...prev, "Please Enter Valid Email Format"]);
//         temp = true;
//       }
//     }

//     if (!experience) {
//       setPopupMsg((prev) => [...prev, "Please Enter Experience"]);
//       temp = true;
//     }

//     if (temp) {
//       setModalVisible(true);
//       return false;
//     }
//     return true;
//   };

//   useFocusEffect(
//     React.useCallback(() => {
//       dispatch(fetchSiteList());
//     }, [dispatch])
//   );

//   React.useEffect(() => {
//     if (siteListData && Array.isArray(siteListData)) {
//       setSiteList(
//         siteListData.map((site) => ({
//           label: site.name,
//           value: site.id,
//         }))
//       );
//     }
//   }, [siteListData]);

//   const saveDetails = async () => {
//     try {
//       if (validator()) {
//         const res = await dispatch(
//           postImplantingPhysician({
//             site_id: siteSelected,
//             payload: diff.currentValues,
//           })
//         );

//         if (res?.payload) {
//           // Get the newly created physician ID from the response
//           const newPhysicianId = res.payload.id || res.payload.physician_id;

//           console.log("New physician created with ID:", newPhysicianId);

//           // Set the selected site to the site where physician was added
//           await dispatch(setSelectedSite(siteSelected));

//           // Set the newly created physician as selected
//           if (newPhysicianId) {
//             await dispatch(setSelectedPhysician(newPhysicianId));

//             // Fetch the complete physician details for the newly created physician
//             await dispatch(fetchSpecificImplantingPhysician(newPhysicianId));
//           }

//           // Refresh the physicians list for the selected site
//           await dispatch(fetchImplantingPhysicians(siteSelected));

//           // Clear the selected provider
//           // await dispatch(setSelectedProvider(""));

//           // Call success callback
//           onSuccess?.();

//           // Close the modal
//           dispatch(showAddPhysicianModal(false));
//         }
//       }
//     } catch (err) {
//       console.error("Error saving physician:", err);
//     }
//   };

//   const handleCancel = () => {
//     dispatch(resetImplantingPhysicianDetails());
//     dispatch(showAddPhysicianModal(false));
//   };

//   return (
//     <>
//       {loader ? (
//         <View className="mt-64">
//           <Loader />
//         </View>
//       ) : (
//         <View className="p-3 bg-primaryWhite">
//           <View className="rounded-md p-3">
//             <Text className="text-primaryPurple text-center font-semibold text-lg mb-4">
//               Add Implanting Physician
//             </Text>

//             <View className="flex-row justify-center gap-4">
//               <SaveActionButton
//                 disabled={!diff}
//                 onPress={saveDetails}
//                 onCancel={handleCancel}
//               />
//             </View>

//             <LineSeperator extraStyle="my-5" />

//             <View className="flex-row justify-between items-center">
//               <Text className="text-primaryBlack font-medium w-[40%]">
//                 Hospital Site <Text className="text-red-3 text-lg">*</Text>
//               </Text>
//               <View className="flex-1">
//                 <SearchablePicker
//                   placeholder="Select"
//                   items={siteList}
//                   value={siteSelected}
//                   onValueChange={(value) => setSiteSelected(value.value)}
//                   error={!siteSelected}
//                 />
//               </View>
//             </View>

//             <LineSeperator extraStyle="my-5" />

//             <View className="flex-row justify-between items-center">
//               <Text className="text-primaryBlack font-medium w-[40%]">
//                 First Name <Text className="text-red-3 text-lg">*</Text>
//               </Text>
//               <View className="flex-1">
//                 <CustomBottomSheetTextInput
//                   inputValue={first_name}
//                   placeholder="First Name"
//                   onInputChange={(value) => {
//                     parseCharInput(value, (parsedValue) => {
//                       dispatch(
//                         setImplantingPhysicianDetails({
//                           first_name: parsedValue,
//                         })
//                       );
//                     });
//                   }}
//                 />
//               </View>
//             </View>

//             <LineSeperator extraStyle="my-5" />

//             <View className="flex-row justify-between items-center">
//               <Text className="text-primaryBlack font-medium w-[40%]">
//                 Last Name <Text className="text-red-3 text-lg">*</Text>
//               </Text>
//               <View className="flex-1">
//                 <CustomBottomSheetTextInput
//                   inputValue={last_name}
//                   placeholder="Last Name"
//                   onInputChange={(value) => {
//                     parseCharInput(value, (parsedValue) => {
//                       dispatch(
//                         setImplantingPhysicianDetails({
//                           last_name: parsedValue,
//                         })
//                       );
//                     });
//                   }}
//                 />
//               </View>
//             </View>

//             <LineSeperator extraStyle="my-5" />

//             <View className="flex-row justify-between items-center">
//               <Text className="text-primaryBlack font-medium w-[40%]">
//                 Middle Name
//               </Text>
//               <View className="flex-1">
//                 <CustomBottomSheetTextInput
//                   inputValue={middle_name}
//                   placeholder="Middle Name"
//                   onInputChange={(value) => {
//                     parseCharInput(value, (parsedValue) => {
//                       dispatch(
//                         setImplantingPhysicianDetails({
//                           middle_name: parsedValue,
//                         })
//                       );
//                     });
//                   }}
//                 />
//               </View>
//             </View>

//             <LineSeperator extraStyle="my-5" />

//             <View className="flex-row justify-between items-center">
//               <Text className="text-primaryBlack font-medium w-[40%]">
//                 Provider Credentials
//               </Text>
//               <View className="flex-1">
//                 <SearchablePicker
//                   placeholder="Select"
//                   items={credential_options}
//                   value={credential}
//                   onValueChange={(value) => {
//                     dispatch(
//                       setImplantingPhysicianDetails({
//                         credential: value.value,
//                       })
//                     );
//                   }}
//                   disableError
//                 />
//               </View>
//             </View>

//             <LineSeperator extraStyle="my-5" />

//             <View className="flex-row justify-between items-center">
//               <Text className="text-primaryBlack font-medium w-[40%]">
//                 NPI Number
//               </Text>
//               <View className="flex-1">
//                 <CustomBottomSheetTextInput
//                   inputValue={npi_number?.toString()}
//                   placeholder="NPI Number"
//                   onInputChange={(value) => {
//                     parseIntInput(value, (parsedValue) => {
//                       dispatch(
//                         setImplantingPhysicianDetails({
//                           npi_number: parsedValue.toString(),
//                         })
//                       );
//                     });
//                   }}
//                   keyboardType="numeric"
//                 />
//               </View>
//             </View>

//             <LineSeperator extraStyle="my-5" />

//             <View className="flex-row justify-between items-center">
//               <Text className="text-primaryBlack font-medium w-[40%]">
//                 Email
//               </Text>
//               <View className="flex-1">
//                 <CustomBottomSheetTextInput
//                   inputValue={email_id}
//                   placeholder="Email"
//                   onInputChange={(value) => {
//                     parseEmailInput(value, (parsedValue) => {
//                       dispatch(
//                         setImplantingPhysicianDetails({
//                           email_id: parsedValue,
//                         })
//                       );
//                     });
//                   }}
//                 />
//               </View>
//             </View>

//             <LineSeperator extraStyle="my-5" />

//             <View className="flex-row justify-between items-center">
//               <Text className="text-primaryBlack font-medium w-[40%]">
//                 Phone Number
//               </Text>
//               <View className="flex-1">
//                 <CustomBottomSheetTextInput
//                   inputValue={phone_number?.toString()}
//                   placeholder="Phone Number"
//                   onInputChange={(value) => {
//                     parseIntInput(value, (parsedValue) => {
//                       dispatch(
//                         setImplantingPhysicianDetails({
//                           phone_number: parsedValue.toString(),
//                         })
//                       );
//                     });
//                   }}
//                   keyboardType="numeric"
//                 />
//               </View>
//             </View>

//             <LineSeperator extraStyle="my-5" />

//             <View className="flex-row justify-between items-center">
//               <Text className="text-primaryBlack font-medium w-[40%]">
//                 LAAO Caseload Experience{" "}
//                 <Text className="text-red-3 text-lg">*</Text>
//               </Text>
//               <View className="flex-1">
//                 <SearchablePicker
//                   placeholder="Select"
//                   items={experience_options}
//                   value={experience}
//                   onValueChange={(value) => {
//                     dispatch(
//                       setImplantingPhysicianDetails({
//                         experience: value.value,
//                       })
//                     );
//                   }}
//                   error={!experience}
//                 />
//               </View>
//             </View>

//             <LineSeperator extraStyle="my-5" />
//           </View>
//         </View>
//       )}
//       <PopupModal
//         show={modalVisible}
//         msg={popupMsg}
//         status="warning"
//         onClose={() => setModalVisible(false)}
//       />
//     </>
//   );
// };

// export default AddImplantingPhysician;
