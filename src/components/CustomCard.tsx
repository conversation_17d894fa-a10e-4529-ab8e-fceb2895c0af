import * as React from "react";
import { View } from "react-native";

interface ICustomCardProps {
  children: React.ReactNode;
  paddingTop?: boolean;
  extraStyle?: string;
}

const CustomCard: React.FunctionComponent<ICustomCardProps> = (props) => {
  return (
    <View
      className={`w-full rounded-lg p-3 bg-primaryWhite shadow-sm  ${
        props.paddingTop ? "mt-3" : ""
      } ${props.extraStyle ? props.extraStyle : ""}`}
    >
      {props.children}
    </View>
  );
};

export default CustomCard;
