import { useSelector } from "react-redux";
import { createSelector } from "reselect";
import { RootState } from "../../store";

const selectAuthState = createSelector(
  (state: RootState) => state.auth,
  (auth) => ({
    isAuthenticated: auth.isAuthenticated,
    loading: auth.loading,
    error: auth.error,
    errorStatus: auth.errorStatus,
  })
);

const useAuthStates = () => {
  return useSelector(selectAuthState);
};

export { useAuthStates };
