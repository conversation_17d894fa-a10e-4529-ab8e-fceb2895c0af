import React, { useState } from "react";
import { View, Text, TouchableOpacity } from "react-native";
import CustomCard from "../../../components/CustomCard";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch } from "../../../store";
import CustomText from "../../../components/CustomText";
import LineSeperator from "../../../components/LineSeperator";
import SearchablePicker from "../../../components/SearchablePicker";
import DatePicker from "react-native-date-picker";
import moment from "moment";
import {
  useImplantingPhysicianDetails,
  formatImplantingPhysician,
} from "../../../utils.tsx";
import { setProcedureDetailsUserDetails } from "../../../store/coordinator/ScheduleStack/procedure/index.ts";
import {
  findProcedureDetailsDiff,
  formatProcedureDetails,
  useProcedureDetails,
  useProcedureDetailsUserDetails,
} from "../hooks/procedureDetailsHooks.ts";
import Loader from "../../../components/Loader.tsx";
import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons";

import {
  putProcedureDetails,
  fetchProcedureDetails,
} from "../../../store/coordinator/ScheduleStack/procedure/thunk.ts";
import { useCoordinatorSelectedPatient } from "../hooks/schedulesHooks.ts";
import { useFocusEffect, useNavigation } from "@react-navigation/native";
import { fetchImplantingPhysicians } from "../../../store/coordinator/ScheduleStack/addpatient/thunk.ts";
import { useUserDetails } from "../hooks/preopHooks.ts";
import PopupModal from "../../../components/Popup";
import SaveActionButton from "../../../components/SaveActionButton.tsx";
import { simpleAutoSave } from "../../../services/simpleAutoSave.ts";
import {
  setSaveStatus,
  setLastSavedData,
} from "../../../store/services/index.ts";

const ProcedureDateCard: React.FunctionComponent = () => {
  const dispatch = useDispatch<AppDispatch>();
  const navigation = useNavigation();
  const implantingPhysicianDetails = useImplantingPhysicianDetails();
  const { implantingPhysicians } = formatImplantingPhysician(
    implantingPhysicianDetails
  );

  const [modalVisible, setModalVisible] = React.useState(false);

  const [popupMsg, setPopupMsg] = React.useState<string[]>([]);

  const userDetails = useProcedureDetailsUserDetails();
  const procedureDetails = useProcedureDetails();
  // Create a ref to track if a save is in progress
  const saveInProgressRef = React.useRef(false);

  // Create a ref to track the last saved data hash
  const lastSavedDataHashRef = React.useRef("");
  const preopDetails = useUserDetails();

  const { procedure_date, implanting_physician, procedure_time } =
    formatProcedureDetails(userDetails);

  const diff = findProcedureDetailsDiff();

  const selectedPatient = useCoordinatorSelectedPatient();

  const [openProcedureDatePicker, setOpenProcedureDatePicker] = useState(false);
  const [openProcedureTimePicker, setOpenProcedureTimePicker] = useState(false);

  const validator = () => {
    setPopupMsg([]);

    var temp = false;
    if (
      procedure_date === "" ||
      procedure_date === null ||
      procedure_date === undefined
    ) {
      setPopupMsg((prev) => [...prev, "Please Enter Procedure Date"]);
      temp = true;
    }

    if (
      procedure_time === null ||
      procedure_time === undefined ||
      procedure_time === ""
    ) {
      setPopupMsg((prev) => [...prev, "Please Enter Procedure Time"]);
      temp = true;
    }

    if (
      implanting_physician === null ||
      implanting_physician === undefined ||
      implanting_physician === ""
    ) {
      setPopupMsg((prev) => [...prev, "Please Select Implanting Physician"]);
      temp = true;
    }

    if (temp) {
      setModalVisible(true);
      return false;
    } else {
      return true;
    }
  };

  const saveDetails = async (): Promise<void> => {
    try {
      // Run validation
      const isValid = validator();

      if (!isValid) {
        dispatch(
          setSaveStatus({
            screenId: "procedureDetails", // change if needed
            status: "validation_failed",
          })
        );
        setLocalSaveStatus("validation_failed");
        throw new Error("Validation failed");
      }

      // Avoid saving if already in progress
      if (saveInProgressRef.current) {
        return;
      }

      const dataCopy = { ...diff.currentValues };
      const currentDataHash = JSON.stringify(dataCopy);

      // Skip saving if data hasn't changed
      if (currentDataHash === lastSavedDataHashRef.current) {
        return;
      }

      // Mark save as in progress
      saveInProgressRef.current = true;

      const res = await dispatch(
        putProcedureDetails({
          case_id: selectedPatient?.case_id,
          payload: dataCopy,
        })
      );

      if (res.payload && selectedPatient?.case_id) {
        const refetchRes = await dispatch(
          fetchProcedureDetails({
            case_id: selectedPatient.case_id,
          })
        );

        if (refetchRes.payload) {
          dispatch(setProcedureDetailsUserDetails(refetchRes.payload)); // if this action exists
          lastSavedDataHashRef.current = currentDataHash;
        }
      }

      saveInProgressRef.current = false;
    } catch (err) {
      console.error("Save error:", err);
      saveInProgressRef.current = false;
      throw err;
    }
  };

  const saveStatus = useSelector(
    (state: any) => state.common?.autoSave?.["procedureDetails"]?.status
  );

  const [localSaveStatus, setLocalSaveStatus] = React.useState<string>("");

  const effectiveSaveStatus = saveStatus || localSaveStatus;

  // Track if user has made changes to prevent auto-navigation on initial load
  const [hasUserMadeChanges, setHasUserMadeChanges] = React.useState(false);

  // Track when user makes changes to any field
  React.useEffect(() => {
    if (diff.hasDiff && !hasUserMadeChanges) {
      setHasUserMadeChanges(true);
    }
  }, [diff.hasDiff, hasUserMadeChanges]);

  // Add effect to navigate to LAAO Procedure screen when all required fields are filled and saved
  React.useEffect(() => {
    if (effectiveSaveStatus === "saved" && hasUserMadeChanges) {
      // Add a small delay to ensure the user sees the "saved" message briefly
      const timer = setTimeout(() => {
        navigation.navigate("LAAO Procedure");
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [effectiveSaveStatus, navigation, hasUserMadeChanges]);

  /**
   * Derive the save message and styling based on the save status
   */
  const { saveMessage, messageStyle, iconName, iconColor } =
    React.useMemo(() => {
      let message = "";
      let style = "text-primaryPurple italic";
      let icon = "alert-circle-outline";
      let color = "#8143d9";

      switch (effectiveSaveStatus) {
        case "changes_detected":
          message = "Changes detected, saving soon...";
          style = "text-primaryBlack italic";
          icon = "alert-circle-outline";
          color = "#f5c542"; // yellow
          break;
        case "saving":
          message = "Saving... Please don't close the app";
          style = "text-primaryPurple font-bold italic";
          icon = "alert-circle-outline";
          color = "#8143d9"; // purple
          break;
        case "saved":
          message = "All changes saved";
          style = "text-green-3 italic";
          icon = "check-circle-outline";
          color = "#28a745"; // green
          break;
        case "validation_failed":
          message = "Validation failed. Please check your inputs.";
          style = "text-red-3 font-bold italic";
          icon = "alert-circle-outline";
          color = "#d93025"; // red
          break;
        case "error":
          message = "Error saving changes. Please try again.";
          style = "text-red-3 font-bold italic";
          icon = "alert-circle-outline";
          color = "#d93025"; // red
          break;
      }

      return {
        saveMessage: message,
        messageStyle: style,
        iconName: icon,
        iconColor: color,
      };
    }, [effectiveSaveStatus]);
  /**
   * Ref to track previous diff values to prevent unnecessary saves
   */
  const prevDiffRef = React.useRef<string>("");

  /**
   * Ref to track if initial data has been loaded
   * This prevents autosave from triggering before data is fetched
   */
  const initialDataLoadedRef = React.useRef<boolean>(false);

  /**
   * Effect to set initialDataLoaded when data is fetched
   */
  React.useEffect(() => {
    // Check if we have valid data
    const hasValidData = userDetails?.case_id !== undefined;

    if (hasValidData && !initialDataLoadedRef.current) {
      initialDataLoadedRef.current = true;
    }
  }, [userDetails]);

  /**
   * Effect to trigger autosave when data changes
   * This uses our new simpleAutoSave service with local state fallback
   */
  React.useEffect(() => {
    // Only proceed if initial data has been loaded and there are actual differences
    if (!initialDataLoadedRef.current) {
      return;
    }

    // Check if case_id exists to prevent API calls with undefined ID
    if (!userDetails?.case_id) {
      return;
    }

    // Only proceed if there are actual differences
    if (diff.hasDiff) {
      // Check if we have any undefined values in critical fields
      const hasUndefinedValues = Object.values(diff.currentValues).every(
        (val) => val === undefined
      );
      if (hasUndefinedValues) {
        return;
      }

      // Convert current values to string for comparison
      const currentDiffString = JSON.stringify(diff.currentValues);

      // Only trigger save if the values have actually changed
      if (currentDiffString !== prevDiffRef.current) {
        // Update the ref with current values
        prevDiffRef.current = currentDiffString;

        // Create a custom wrapper for setSaveStatus that updates both Redux and local state
        const setStatusWithFallback = (payload: {
          screenId: string;
          status: string;
        }) => {
          // Update local state as a fallback
          setLocalSaveStatus(payload.status);
          // Also dispatch to Redux
          return dispatch(setSaveStatus(payload));
        };

        // Trigger the simple autosave with the current values
        simpleAutoSave(
          "procedureDetails", // Screen ID
          diff.currentValues, // Data to save
          saveDetails, // Save function
          4000, // 4 second delay before saving
          dispatch, // Redux dispatch
          setStatusWithFallback, // Custom action to set save status with fallback
          setLastSavedData // Action to set last saved data
        );
      } else {
        console.log("No actual changes in diff values, skipping autosave");
      }
    }
  }, [
    diff.hasDiff,
    diff.currentValues,
    dispatch,
    setLocalSaveStatus,
    userDetails,
  ]);

  const handleCancel = async () => {
    dispatch(
      setProcedureDetailsUserDetails({
        ...userDetails,
        ...procedureDetails,
      })
    );
  };

  return (
    <>
      <View className="p-3">
        {saveMessage && (
          <View
            style={{
              flexDirection: "row",
              justifyContent: "flex-end",
              alignItems: "center",
              marginVertical: 4,
            }}
          >
            <MaterialCommunityIcons
              name={iconName}
              color={iconColor}
              size={20}
            />
            <Text
              className={`text-sm ${messageStyle}`}
              style={{ marginLeft: 6 }}
            >
              {saveMessage}
            </Text>
          </View>
        )}

        <View className="mt-2 flex-row justify-between items-center">
          <Text className="text-primaryBlack font-medium w-[40%]">Date</Text>
          <TouchableOpacity
            onPress={() => setOpenProcedureDatePicker(true)}
            className={`bg-primaryWhite border ${
              procedure_date ? "border-primaryPurple" : "border-red-3"
            } h-[38px] rounded justify-center px-3 flex-1`}
          >
            <CustomText
              value={
                procedure_date
                  ? moment(procedure_date).format("MM/DD/YYYY")
                  : "Select Date"
              }
              className={`
                  ${procedure_date ? "" : "text-primaryGray"}
                  text-md
                  `}
            />
          </TouchableOpacity>
        </View>

        <View className="flex-row justify-between items-center mt-3">
          <Text className="text-primaryBlack font-medium w-[40%]">Time</Text>
          <TouchableOpacity
            onPress={() => setOpenProcedureTimePicker(true)}
            className={`bg-primaryWhite border ${
              procedure_time ? "border-primaryPurple" : "border-red-3"
            } h-[38px] rounded justify-center px-3 flex-1`}
          >
            <CustomText
              value={
                procedure_time
                  ? moment(procedure_time, "HH:mm:ss").format("HH:mm")
                  : "Select Time"
              }
              className={`
                  ${procedure_time ? "" : "text-primaryGray"}
                  text-md
                  `}
            />
          </TouchableOpacity>
        </View>

        {/* Implanting MD */}
        {/* <LineSeperator extraStyle="my-5" color="primaryWhite" /> */}

        <View className="mt-2 flex-row justify-between items-center">
          <Text className="text-primaryBlack font-medium w-[40%]">
            Implanting Physician
          </Text>

          <View className="flex-1">
            <SearchablePicker
              placeholder="Select"
              items={implantingPhysicians.map((physician) => ({
                label: physician.name,
                value: physician.id,
              }))}
              value={implanting_physician}
              onValueChange={(value) =>
                dispatch(
                  setProcedureDetailsUserDetails({
                    ...userDetails,

                    implanting_physician: {
                      ...userDetails?.implanting_physician,
                      id: value.value,
                      name: value.label,
                    },
                  })
                )
              }
              height={38}
            />
          </View>
        </View>

        <LineSeperator extraStyle="my-5" />

        <View className="flex-row justify-center gap-4 ">
          {/* <TouchableOpacity
            onPress={saveDetails}
            className={`border 
              ${
                !diff?.hasDiff
                  ? `bg-primaryGray border-primaryGray`
                  : `bg-primaryPurple border-primaryPurple`
              }
               px-6 py-3 rounded-full`}
            disabled={!diff?.hasDiff}
          >
            <Text className="text-primaryWhite font-semibold">Save</Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={handleCancel}
            className="border border-primaryPurple px-6 py-3 rounded-full bg-primaryBg"
          >
            <Text className="text-primaryPurple">Cancel</Text>
          </TouchableOpacity> */}
          {/* <SaveActionButton
            disabled={!diff.hasDiff}
            onPress={saveDetails}
            onCancel={handleCancel}
          /> */}
        </View>
      </View>
      <DatePicker
        modal
        open={openProcedureDatePicker}
        date={
          procedure_date
            ? moment(procedure_date, "YYYY-MM-DD").toDate()
            : new Date()
        }
        onConfirm={(date) => {
          setOpenProcedureDatePicker(false);
          dispatch(
            setProcedureDetailsUserDetails({
              ...userDetails,
              procedure_date: moment(date).format("YYYY-MM-DD"),
            })
          );
        }}
        onCancel={() => setOpenProcedureDatePicker(false)}
        mode="date"
        minimumDate={new Date()}
      />

      <DatePicker
        modal
        open={openProcedureTimePicker}
        date={
          procedure_time
            ? moment(procedure_time, "HH:mm:ss").toDate()
            : new Date()
        }
        onConfirm={(date) => {
          setOpenProcedureTimePicker(false);
          dispatch(
            setProcedureDetailsUserDetails({
              ...userDetails,
              procedure_time: moment(date).format("HH:mm:ss"),
            })
          );
        }}
        onCancel={() => {
          setOpenProcedureTimePicker(false);
        }}
        mode={"time"}
      />

      <PopupModal
        show={modalVisible}
        msg={popupMsg}
        status="warning"
        onClose={() => setModalVisible(false)}
      />
    </>
  );
};

export default ProcedureDateCard;
