// types.ts

interface ReferringProvider {

  id: string;
  first_name: string;
  last_name: string;
  middle_name?: string;
  credential?: string;
  npi_number: string;
  email_id?: string;
  phone_number?: string;
  fax_number?: string;
  address?: string;
  city?: string;
  state?: string;
  zip_code?: string;
  hospital_system?: string;
}

interface RepReferringProvider {
  id: string;
  name: string;
}

export type { ReferringProvider, RepReferringProvider };
