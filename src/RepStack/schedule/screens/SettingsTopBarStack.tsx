import React from "react";
import { createMaterialTopTabNavigator } from "@react-navigation/material-top-tabs";
import SitesTabScreen from "./SitesScreen";
import PhysiciansTabScreen from "./PhysiciansScreen";
import ReferringProviderTabScreen from "./ReferringProviderScreen";
import PcpProvidersScreen from "./PcpProvidersScreen";
import { useDispatch } from "react-redux";
import { showAddSiteModal } from "../../../store/rep/ScheduleStack/addSite";
import { showAddPhysicianModal } from "../../../store/rep/ScheduleStack/addPhysician";
import { showAddProviderModal } from "../../../store/common/addReferringProvider";
import { showAddPcpProviderModal } from "../../../store/common/addPcpProvider";

const Tab = createMaterialTopTabNavigator();

const TopTabs = () => {
  const dispatch = useDispatch();

  const handleTabChange = () => {
    // Close all modals when switching tabs
    dispatch(showAddSiteModal(false));
    dispatch(showAddPhysicianModal(false));
    dispatch(showAddProviderModal(false));
    dispatch(showAddPcpProviderModal(false));
  };

  return (
    <Tab.Navigator
      screenOptions={{
        tabBarScrollEnabled: true,
        tabBarIndicatorStyle: { backgroundColor: "#8143d9", height: "100%" },
        tabBarActiveTintColor: "white",
        tabBarInactiveTintColor: "black",
        tabBarLabelStyle: { fontSize: 12, fontWeight: "bold" },
      }}
      sceneContainerStyle={{ backgroundColor: "white" }}
      screenListeners={{
        tabPress: handleTabChange, // Close modals when tab is pressed
        state: handleTabChange, // Close modals when navigation state changes
      }}
    >
      <Tab.Screen name="Sites" component={SitesTabScreen} />
      <Tab.Screen
        name="Implanting Physicians"
        component={PhysiciansTabScreen}
      />
      <Tab.Screen
        name="Referring Provider"
        component={ReferringProviderTabScreen}
      />
      <Tab.Screen name="PCP Provider" component={PcpProvidersScreen} />
    </Tab.Navigator>
  );
};

export default TopTabs;

// import React from "react";
// import { createMaterialTopTabNavigator } from "@react-navigation/material-top-tabs";
// import SitesTabScreen from "./SitesScreen";
// import PhysiciansTabScreen from "./PhysiciansScreen";
// import ReferringProviderTabScreen from "./ReferringProviderScreen";
// import PcpProvidersScreen from "./PcpProvidersScreen";
// import { useDispatch } from "react-redux";

// const Tab = createMaterialTopTabNavigator();

// const TopTabs = () => {
//   const dispatch = useDispatch();

//   return (
//     <Tab.Navigator
//       screenOptions={{
//         tabBarScrollEnabled: true,
//         tabBarIndicatorStyle: { backgroundColor: "#8143d9", height: "100%" },
//         tabBarActiveTintColor: "white",
//         tabBarInactiveTintColor: "black",
//         tabBarLabelStyle: { fontSize: 12, fontWeight: "bold" },
//       }}
//       sceneContainerStyle={{ backgroundColor: "white" }}
//     >
//       <Tab.Screen name="Sites" component={SitesTabScreen} />
//       <Tab.Screen
//         name="Implanting Physicians"
//         component={PhysiciansTabScreen}
//       />
//       <Tab.Screen
//         name="Referring Provider"
//         component={ReferringProviderTabScreen}
//       />
//       <Tab.Screen name="PCP Provider" component={PcpProvidersScreen} />
//     </Tab.Navigator>
//   );
// };

// export default TopTabs;
