import { createAsyncThunk } from "@reduxjs/toolkit";
import api from "../../../../api/api";

const fetchCases = createAsyncThunk(
  "schedule/fetchCases",
  async ({ siteId, date }: { siteId: string; date: string }, thunkAPI) => {
    try {
      const response = await api.private.get(
        api.routes.privateRoutes.clinician.patientsList(siteId, date)
      );

      if (response.data && response.data.result) {
        return response.data.result;
      } else {
        return thunkAPI.rejectWithValue(
          'Invalid response format: Missing "result" field'
        );
      }
    } catch (error) {
      console.error("Error caught during API call:", error);
      return thunkAPI.rejectWithValue("Failed to fetch patient cases");
    }
  }
);

export { fetchCases };
