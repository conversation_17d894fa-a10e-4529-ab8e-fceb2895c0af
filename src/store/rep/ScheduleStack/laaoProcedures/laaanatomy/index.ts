import { createSlice } from "@reduxjs/toolkit";
import initialState from "./initialstate";
import { fetchLaaAnatomyDetails } from "./thunk";

const laaanatomySlice = createSlice({
  name: "laaanatomy",
  initialState,
  reducers: {
    setLaaAnatomyUserDetails: (state, action) => {
      state.userDetails = {
        ...state.userDetails,
        ...action.payload,
      };
    },

    resetLaaAnatomyState: (state) => {
      state.laaAnatomyDetails = null;
      state.userDetails = null;
    },

    // Reducer to update width for a specific angle
    setWidthForAngle: (state, action) => {
      const { angle, width } = action.payload;
      state.userDetails.tee_measurement = state.userDetails.tee_measurement.map(
        (item) => (item.angle === angle ? { ...item, width } : item)
      );
    },

    // Reducer to update depth for a specific angle
    setDepthForAngle: (state, action) => {
      const { angle, depth } = action.payload;
      state.userDetails.tee_measurement = state.userDetails.tee_measurement.map(
        (item) => (item.angle === angle ? { ...item, depth } : item)
      );
    },

    // Reducer to update both width and depth for a specific angle
    setMeasurementForAngle: (state, action) => {
      const { angle, width, depth } = action.payload;
      state.userDetails.tee_measurement = state.userDetails.tee_measurement.map(
        (item) =>
          item.angle === angle
            ? {
                ...item,
                width: width || item.width,
                depth: depth || item.depth,
              }
            : item
      );
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchLaaAnatomyDetails.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchLaaAnatomyDetails.fulfilled, (state, action) => {
        state.loading = false;
        state.laaAnatomyDetails = action.payload;
      })
      .addCase(fetchLaaAnatomyDetails.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

export const {
  setLaaAnatomyUserDetails,
  resetLaaAnatomyState,
  setWidthForAngle,
  setDepthForAngle,
  setMeasurementForAngle,
} = laaanatomySlice.actions;

export default laaanatomySlice.reducer;
