import React, { useRef } from "react";
import { Button, Text, View, StyleSheet } from "react-native";
import BottomSheetComponent, {
  BottomSheetRefProps,
} from "./BottomSheetComponent";

const TestBot: React.FunctionComponent = () => {
  const bottomRef = useRef<BottomSheetRefProps>(null);

  return (
    <View>
      <Button
        onPress={() => bottomRef.current?.open()}
        title="Open Bottom Sheet"
      />

      <BottomSheetComponent
        ref={bottomRef}
        snapPoints={["50%", "100%"]} // Allows the bottom sheet to snap to 50% or 100%
        backgroundColor="white"
      >
        <Text>Here is the content of the bottom sheet!</Text>
      </BottomSheetComponent>
    </View>
  );
};

const styles = StyleSheet.create({
  contentContainer: {
    flex: 1,
    padding: 16,
  },
  bottomSheetStyle: {
    position: "absolute", // Position the BottomSheet above the tab bar
    bottom: 0,
    left: 0,
    right: 0,
    zIndex: 10, // Ensure it's above the tab bar
  },
});

export default TestBot;
