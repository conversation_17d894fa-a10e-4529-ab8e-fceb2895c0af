import { createAsyncThunk } from "@reduxjs/toolkit";
import api from "../../../../api/api";

const fetchTransseptalDetails = createAsyncThunk(
  "schedule/fetchTransseptalDetails",
  async ({ case_id }: { case_id: string }, thunkAPI) => {
    try {
      const response = await api.private.get(
        api.routes.privateRoutes.clinician.transseptal_get(case_id)
      );

      if (response.data && response.data.result) {
        return response.data.result;
      } else {
        return thunkAPI.rejectWithValue(
          'Invalid response format: Missing "result" field'
        );
      }
    } catch (error: any) {
      console.error("Error caught during API call:", error.message || error);
      return thunkAPI.rejectWithValue(
        error.response?.data?.message || "Failed to fetch Transseptal details"
      );
    }
  }
);

const putTransseptalDetails = createAsyncThunk(
  "schedule/putTransseptalDetails",
  async (
    { case_details_id, payload }: { case_details_id: string; payload: any },
    thunkAPI
  ) => {
    try {
      const response = await api.private.put(
        api.routes.privateRoutes.clinician.transseptal_put(case_details_id),
        payload
      );
      if (response.data && response.data.result) {
        return response.data.result;
      } else {
        return thunkAPI.rejectWithValue(
          'Invalid response format: Missing "result" field'
        );
      }
    } catch (error: any) {
      console.error("Error caught during API call:", error.response.data);
      return thunkAPI.rejectWithValue(
        error.response?.data?.message || "Failed to fetch Transseptal details"
      );
    }
  }
);
export { fetchTransseptalDetails, putTransseptalDetails };
