import { createAsyncThunk } from "@reduxjs/toolkit";
import { ImplantingPhysician } from "./types";
import api from "../../../../api/api";

export const fetchProcedureTypes = createAsyncThunk(
  "addImplantingPhysician/fetchProcedureTypes",
  async (_, thunkAPI) => {
    try {
      const response = await api.private.get(
        api.routes.privateRoutes.rep.procedure_types
      );

      if (response.data && response.data.result) {
        return response.data.result;
      } else {
        throw new Error('Invalid response format: Missing "result" field');
      }
    } catch (error: any) {
      console.error("Error caught during API call:", error.response?.message);
      if (error.response) {
        return thunkAPI.rejectWithValue(
          error.response.data.message || "Failed to fetch procedure types"
        );
      } else if (error.request) {
        console.error("No response received:", error.request);
        return thunkAPI.rejectWithValue("No response received from the server");
      } else {
        console.error("Error occurred:", error.message);
        return thunkAPI.rejectWithValue("An unknown error occurred");
      }
    }
  }
);

export const fetchProviderCredentials = createAsyncThunk(
  "addImplantingPhysician/fetchProviderCredentials",
  async (_, thunkAPI) => {
    try {
      const response = await api.private.get(
        api.routes.privateRoutes.rep.provider_credentials_get
      );

      if (response.data && response.data.result) {
        return response.data.result;
      } else {
        throw new Error('Invalid response format: Missing "result" field');
      }
    } catch (error: any) {
      console.error("Error caught during API call:", error.response?.message);
      if (error.response) {
        return thunkAPI.rejectWithValue(
          error.response.data.message || "Failed to fetch provider credentials"
        );
      } else if (error.request) {
        console.error("No response received:", error.request);
        return thunkAPI.rejectWithValue("No response received from the server");
      } else {
        console.error("Error occurred:", error.message);
        return thunkAPI.rejectWithValue("An unknown error occurred");
      }
    }
  }
);

export const fetchProviderExperience = createAsyncThunk(
  "addImplantingPhysician/fetchProviderExperience",
  async (_, thunkAPI) => {
    try {
      const response = await api.private.get(
        api.routes.privateRoutes.rep.provider_experience_get
      );

      if (response.data && response.data.result) {
        return response.data.result;
      } else {
        throw new Error('Invalid response format: Missing "result" field');
      }
    } catch (error: any) {
      console.error("Error caught during API call:", error.response?.message);
      if (error.response) {
        return thunkAPI.rejectWithValue(
          error.response.data.message || "Failed to fetch provider experience"
        );
      } else if (error.request) {
        console.error("No response received:", error.request);
        return thunkAPI.rejectWithValue("No response received from the server");
      } else {
        console.error("Error occurred:", error.message);
        return thunkAPI.rejectWithValue("An unknown error occurred");
      }
    }
  }
);

export const fetchSpecificImplantingPhysician = createAsyncThunk(
  "addImplantingPhysician/fetchSpecificImplantingPhysician",
  async (physician_id: string, thunkAPI) => {
    try {
      const response = await api.private.get(
        api.routes.privateRoutes.rep.implanting_physician_get(physician_id)
      );

      if (response.data && response.data.result) {
        return response.data.result;
      } else {
        throw new Error('Invalid response format: Missing "result" field');
      }
    } catch (error: any) {
      console.error("Error caught during API call:", error.response?.message);
      if (error.response) {
        return thunkAPI.rejectWithValue(
          error.response.data.message || "Failed to fetch physician details"
        );
      } else if (error.request) {
        console.error("No response received:", error.request);
        return thunkAPI.rejectWithValue("No response received from the server");
      } else {
        console.error("Error occurred:", error.message);
        return thunkAPI.rejectWithValue("An unknown error occurred");
      }
    }
  }
);

export const putImplantingPhysician = createAsyncThunk(
  "addImplantingPhysician/putImplantingPhysician",
  async (
    {
      physician_id,
      payload,
    }: {
      physician_id: string;
      payload: ImplantingPhysician;
    },
    thunkAPI
  ) => {
    try {
      const response = await api.private.put(
        api.routes.privateRoutes.rep.implanting_physician_put(physician_id),
        payload
      );

      if (response.data && response.data.result) {
        return response.data.result;
      } else {
        throw new Error('Invalid response format: Missing "result" field');
      }
    } catch (error: any) {
      console.error("Error caught during API call:", error.response?.message);
      if (error.response) {
        if (error.response.status === 400) {
          return thunkAPI.rejectWithValue(error.response.data.message);
        } else {
          return thunkAPI.rejectWithValue("Failed to update physician");
        }
      } else if (error.request) {
        console.error("No response received:", error.request);
        return thunkAPI.rejectWithValue("No response received from the server");
      } else {
        console.error("Error occurred:", error.message);
        return thunkAPI.rejectWithValue("An unknown error occurred");
      }
    }
  }
);

export const postImplantingPhysician = createAsyncThunk(
  "addImplantingPhysician/putpostImplantingPhysician",
  async (
    { site_id, payload }: { site_id: string; payload: ImplantingPhysician },
    thunkAPI
  ) => {
    try {
      const response = await api.private.post(
        api.routes.privateRoutes.rep.post_implanting_physician(site_id),
        payload
      );

      if (response.data && response.data.result) {
        return response.data.result;
      } else {
        throw new Error('Invalid response format: Missing "result" field');
      }
    } catch (error: any) {
      console.error("Error caught during API call:", error.response.message);
      if (error.response) {
        if (error.response.status === 400) {
          return thunkAPI.rejectWithValue(error.response.data.message);
        } else {
          return thunkAPI.rejectWithValue("Failed to add site");
        }
      } else if (error.request) {
        console.error("No response received:", error.request);
        return thunkAPI.rejectWithValue("No response received from the server");
      } else {
        console.error("Error occurred:", error.message);
        return thunkAPI.rejectWithValue("An unknown error occurred");
      }
    }
  }
);
