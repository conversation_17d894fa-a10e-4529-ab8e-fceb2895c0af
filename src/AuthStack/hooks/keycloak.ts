import { useState } from "react";
import { authorize, AuthorizeResult } from "react-native-app-auth";

const baseUrl = "https://dev-auth.cormetrix.com/realms/Cormetrix-AtriAI";
const config = {
  issuer: baseUrl,
  clientId: "cormetrix-api",
  redirectUrl: "atriai://login",
  scopes: ["openid", "profile", "email", "offline_access"],
  additionalParameters: {
    prompt: "login",
  },
};

export const useKeyCloak = () => {
  const [state, setState] = useState<AuthorizeResult>({} as AuthorizeResult);
  const onLogin = async () => {
    try {
      const auth = await authorize(config);
      setState(auth);
    } catch (err) {
      console.error(err);
    }
  };

  const handler = {
    onLogin,
  };
  return {
    state,
    handler,
  };
};

