import { createSlice } from "@reduxjs/toolkit";
import initialState from "./initialstate";
import { fetchClinicianSchedules, putHasbledScoreDetails } from "./thunk";
import { putChadScoreDetails } from "./thunk";

const scheduleSlice = createSlice({
  name: "schedules",
  initialState,
  reducers: {
    setSelectedPatient: (state, action) => {
      if (state.selectedPatient) {
        state.selectedPatient = {
          ...state.selectedPatient,
          ...action.payload,
        };
      } else {
        state.selectedPatient = action.payload;
      }
    },
    setChadScore: (state, action) => {
      state.chadScore = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchClinicianSchedules.pending, (state) => {
        state.loaders.scheduleLoader = true;
        state.errors.scheduleError = null;
      })
      .addCase(fetchClinicianSchedules.fulfilled, (state, action) => {
        state.loaders.scheduleLoader = false;
        state.schedules = action.payload;
      })
      .addCase(fetchClinicianSchedules.rejected, (state, action) => {
        state.loaders.scheduleLoader = false;
        state.errors.scheduleError = action.payload as string;
        state.schedules = initialState?.schedules;
      })
      .addCase(putChadScoreDetails.pending, (state) => {
        state.loaders.scheduleLoader = true;
        state.errors.scheduleError = null;
      })
      .addCase(putChadScoreDetails.fulfilled, (state, action) => {
        state.loaders.scheduleLoader = false;
        state.chadScore = action.payload;
      })
      .addCase(putChadScoreDetails.rejected, (state, action) => {
        state.loaders.scheduleLoader = false;
        state.errors.scheduleError = action.payload as string;
        state.chadScore = initialState?.chadScore;
      })
      .addCase(putHasbledScoreDetails.pending, (state) => {
        state.loaders.scheduleLoader = true;
        state.errors.scheduleError = null;
      })
      .addCase(putHasbledScoreDetails.fulfilled, (state, action) => {
        state.loaders.scheduleLoader = false;
        state.chadScore = action.payload;
      })
      .addCase(putHasbledScoreDetails.rejected, (state, action) => {
        state.loaders.scheduleLoader = false;
        state.errors.scheduleError = action.payload as string;
        state.chadScore = initialState?.chadScore;
      });
  },
});

export const { setSelectedPatient, setChadScore } = scheduleSlice.actions;

export {};

export default scheduleSlice.reducer;
