import { useSelector } from "react-redux";
import { RootState } from "../../../store";
import {
  ReferringProvider,
  CredentialOption,
} from "../../../store/common/addReferringProvider/types";

// Loader and Error
const useLoaderAndError = () => {
  const loader = useSelector(
    (state: RootState) => state.common.referringProviders.loading
  );

  const error = useSelector(
    (state: RootState) => state.common.referringProviders.error
  );
  return { loader, error };
};

// Provider details from API
const useProviderDetails = (): ReferringProvider | null => {
  return useSelector(
    (state: RootState) => state.common.referringProviders.providerDetails
  );
};

// Modified/Editable form values
const useUserProviderDetails = (): ReferringProvider | null => {
  return useSelector(
    (state: RootState) => state.common.referringProviders.userDetails
  );
};

// referring provider list
const useReferringProvidersListData = () => {
  return useSelector(
    (state: RootState) => state.common.referringProviders.providerListDetails
  );
};

// Active tab
const useActiveTab = ():
  | "referring-providers"
  | "pcp-providers"
  | "sites"
  | "implanting-physicians" => {
  return useSelector(
    (state: RootState) => state.common.referringProviders.activeTab
  );
};

// Modal state
const useAddProviderModalState = (): boolean => {
  return useSelector(
    (state: RootState) => state.common.referringProviders.showAddProviderModal
  );
};

// Selected provider
const useSelectedProvider = (): string => {
  return useSelector(
    (state: RootState) => state.common.referringProviders.selectedProvider
  );
};

// Format data for POST API
const formatPostProvider = (data: ReferringProvider) => {
  return {
    provider_name: `${data.first_name} ${data.middle_name || ""} ${
      data.last_name
    }`.trim(),
    provider_credential: data.credential || "",
    provider_email: data.email_id || "",
    provider_phone: data.phone_number || "",
    provider_npi: data.npi_number || "",
    provider_fax: data.fax_number || "",
    provider_address: data.address || "",
    provider_city: data.city || "",
    provider_state: data.state || "",
    provider_zip: data.zip_code || "",
    hospital_system: data.hospital_system || "",
  };
};

// Diff check between form & original
const findPostProviderDiff = () => {
  const userDetails = useUserProviderDetails();
  const providerDetails = useProviderDetails();

  const hasDiff =
    JSON.stringify(userDetails) !== JSON.stringify(providerDetails);

  const currentValues = {
    first_name: userDetails?.first_name || "",
    middle_name: userDetails?.middle_name || "",
    last_name: userDetails?.last_name || "",
    credential: userDetails?.credential || "",
    email_id: userDetails?.email_id || "",
    phone_number: userDetails?.phone_number || "",
    fax_number: userDetails?.fax_number || "",
    npi_number: userDetails?.npi_number || "",
    address: userDetails?.address || "",
    city: userDetails?.city || "",
    state: userDetails?.state || "",
    zip_code: userDetails?.zip_code || "",
    hospital_system: userDetails?.hospital_system || "",
  };

  return {
    hasDiff,
    currentValues,
  };
};

const useReferringProviderCredentialOptions = (): CredentialOption[] => {
  return useSelector(
    (state: RootState) => state.common.referringProviders.credentialOptions
  );
};

export {
  useActiveTab,
  useLoaderAndError,
  useAddProviderModalState,
  useProviderDetails,
  useUserProviderDetails,
  useSelectedProvider,
  formatPostProvider,
  findPostProviderDiff,
  useReferringProviderCredentialOptions,
  useReferringProvidersListData,
};
