import React from "react";
import { WebView } from "react-native-webview";
import { dashboardUrl } from "../../../api/config";
import { getToken, getDashboardId } from "../../../Hooks/dashboard";
import { Enviromnent } from "../../../api/config";

const HomeScreen: React.FC = () => {
  const token = getToken();
  const dashboardId = getDashboardId();

  const url = dashboardUrl[Enviromnent](token, dashboardId);

  return (
    <WebView
      className="flex-1"
      source={{
        uri: url,
      }}
    />
  );
};

export default HomeScreen;
