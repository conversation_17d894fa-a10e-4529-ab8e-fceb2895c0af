import React from "react";
import { TouchableOpacity, View, StyleSheet } from "react-native";
import Icon from "react-native-vector-icons/MaterialCommunityIcons";
import SearchablePicker from "./SearchablePicker";

interface RadioWithDropdownProps {
  onChange: (value: boolean) => void;
  items: { label: string; value: boolean }[];
  selected: boolean;
  yesColor?: string;
  noColor?: string;
  size?: number;
}

const getColor = (color?: string, defaultColor?: string) =>
  color || defaultColor || "gray";

const RadioWithDropdown: React.FC<RadioWithDropdownProps> = ({
  items,
  selected,
  onChange,
  yesColor,
  noColor,
  size = 24,
}) => {
  return (
    <View style={styles.container}>
      <View style={styles.radioContainer}>
        {/* Yes (True) Radio Button */}
        <TouchableOpacity
          style={[
            styles.radioButton,
            selected
              ? { backgroundColor: getColor(yesColor, "green") }
              : {
                  borderWidth: 2,
                  borderColor: getColor(yesColor, "green"),
                  backgroundColor: "transparent",
                },
            { width: size, height: size },
          ]}
          onPress={() => onChange(true)}
          activeOpacity={0.7}
        >
          <View
            style={[
              styles.innerCircle,
              { width: size * 0.6, height: size * 0.6 },
              selected && { backgroundColor: "white" },
            ]}
          >
            {selected && (
              <Icon
                name="check"
                size={size * 0.5}
                color={getColor(yesColor, "green")}
              />
            )}
          </View>
        </TouchableOpacity>

        {/* No (False) Radio Button */}
        <TouchableOpacity
          style={[
            styles.radioButton,
            !selected
              ? { backgroundColor: getColor(noColor, "red") }
              : {
                  borderWidth: 2,
                  borderColor: getColor(noColor, "red"),
                  backgroundColor: "transparent",
                },
            { width: size, height: size },
          ]}
          onPress={() => onChange(false)}
          activeOpacity={0.7}
        >
          <View
            style={[
              styles.innerCircle,
              { width: size * 0.6, height: size * 0.6 },
              !selected && { backgroundColor: "white" },
            ]}
          >
            {!selected && (
              <Icon
                name="close"
                size={size * 0.5}
                color={getColor(noColor, "red")}
              />
            )}
          </View>
        </TouchableOpacity>
      </View>

      {/* Dropdown */}
      <View style={{ width: 50 }}>
        <SearchablePicker
          items={items}
          onValueChange={(val) => onChange(val.value)}
          placeholder="Select"
          value={selected}
          disableError
          width="100%"
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  radioContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  radioButton: {
    borderRadius: 50,
    alignItems: "center",
    justifyContent: "center",
  },
  innerCircle: {
    borderRadius: 50,
    alignItems: "center",
    justifyContent: "center",
  },
});

export default RadioWithDropdown;
