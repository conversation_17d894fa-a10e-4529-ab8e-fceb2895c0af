import * as React from "react";
import { View, Text, TouchableOpacity, StyleSheet } from "react-native";
import { useNavigation } from "@react-navigation/native";
import moment from "moment";
import LineSeperator from "../../../components/LineSeperator";
import { useDispatch } from "react-redux";
import { AppDispatch } from "../../../store";
import { setSelectedPatient } from "../../../store/clinician/ScheduleStack/schedule";
// NEW START
import { useClinicianSchedules } from "../hooks/schedulesHooks";
import { setPatientDetails } from "../../../store/chatbot";
import { capitalizeName } from "../../../utils";

// NEW END

interface PatientCase {
  case_id: string;
  patient: {
    afib_ablation: boolean;
    age: number;
    anticoagulation: [] | null;
    cha2ds2_vasc: {
      score: number;
      calculation: {
        congestive_heart_failure: number;
        hypertension: number;
        age: number;
        diabetes_mellitus: number;
        stroke_tia_thromboembolic_event: number;
        vascular_disease: number;
        sex: number;
      };
    };
    cta: boolean;
    tee: boolean;
    id: string;
    name: string;
    rationale: string;
    referring_provider: any;
    sex: string;
    study: any[];
  };
  procedure_date: string;
  procedure_time: string;
  preop: boolean;
}

interface IPatientCardProps {
  patientCase: PatientCase;
  chadPress: (calculation: any) => void;
  showAnticipatedAndDate: boolean;
}

const PatientCard: React.FunctionComponent<IPatientCardProps> = ({
  patientCase,
  chadPress,
  showAnticipatedAndDate,
}) => {
  const dispatch = useDispatch<AppDispatch>();
  const navigation = useNavigation();

  const patientCases = useClinicianSchedules();

  const selectPatient = (caseId: string) => {
    const selectedPatient = patientCases?.find(
      (patientCase: PatientCase) => patientCase.case_id === caseId
    );

    if (selectedPatient) {
      dispatch(setSelectedPatient(selectedPatient));
      dispatch(
        setPatientDetails({
          caseId: selectedPatient?.case_id,
          patientName: selectedPatient?.patient?.name,
        })
      );
    } else {
      console.error("Patient case not found");
    }
  };

  const { patient, procedure_date, procedure_time, preop } = patientCase;

  return (
    <View style={styles.cardContainer}>
      {patient.afib_ablation && (
        <View style={styles.ribbonContainer}>
          <Text style={styles.ribbonText}>+ Afib Ablation</Text>
        </View>
      )}
      <TouchableOpacity
        onPress={() => {
          selectPatient(patientCase.case_id);
          navigation.navigate("Patient Details", {
            name: patient.name,
          });
        }}
        className="flex-row items-center justify-between w-full"
      >
        <View className="flex-column flex-1 p-1">
          <View className="flex-row justify-between p-1">
            <View className="flex-row items-center justify-between">
              <Text className="font-semibold text-center text-lg text-primaryPurple mr-2">
                {/* {patient?.name} */}
                {capitalizeName(patient?.name)}
              </Text>
              <View className="h-6 border-l-[2px] rounded-full border-primaryPurple" />
              <View className="flex-row items-center ml-2">
                <Text className="font-semibold text-lg text-primaryPurple mr-1">
                  {patient?.age}
                </Text>
                <Text className="font-semibold text-lg text-primaryPurple mr-1">
                  Y/O
                </Text>
                <Text className="font-semibold text-lg text-primaryPurple">
                  {patient?.sex}
                </Text>
              </View>
            </View>
            <View className="px-3 pt-1">
              {showAnticipatedAndDate && (
                <Text className="text-center text-primaryPurple text-lg font-semibold">
                  {procedure_time
                    ? moment(procedure_time, "HH:mm:ss").format("hh:mm A")
                    : "N/A"}
                </Text>
              )}
            </View>
          </View>

          {/* {!preop && (
            <View className="flex-row gap-2 p-1">
              <Text className="font-md text-primaryBlack font-semibold ">
                Hometown:
              </Text>
              <Text className="text-primaryBlack">{patient?.address}</Text>
            </View>
          )} */}

          {/* <View className="flex-row gap-2 p-1">
            <Text className="font-md text-primaryBlack font-semibold ">
              Patient Rationale:
            </Text>
            <Text className="text-primaryBlack">{patient?.rationale}</Text>
          </View> */}

          <View className="flex-row gap-2 p-1">
            <Text className="font-md text-primaryBlack font-semibold ">
              Referring Provider:
            </Text>
            <Text className="text-primaryBlack">
              {patient?.referring_provider.name
                ? patient?.referring_provider.name
                : "N/A"}
            </Text>
          </View>

          <View className="flex-row gap-2 p-1">
            <Text>
              <Text className="font-md text-primaryBlack font-semibold">
                Current Medication Regimen:{" "}
              </Text>

              <Text className="text-primaryBlack " numberOfLines={3}>
                {patient?.anticoagulation?.length
                  ? (() => {
                      const meds = patient.anticoagulation.map(
                        (med: any) => med.name
                      );
                      const hasAspirin = meds.includes("Aspirin");
                      const hasClopidogrel = meds.includes("Clopidogrel");

                      // If both Aspirin and Clopidogrel are present, replace them with "DAPT"
                      if (hasAspirin && hasClopidogrel) {
                        return [
                          ...meds.filter(
                            (med) => med !== "Aspirin" && med !== "Clopidogrel"
                          ),
                          "DAPT",
                        ].join(", ");
                      }

                      return meds.join(", ");
                    })()
                  : "N/A"}
              </Text>
            </Text>
          </View>

          {showAnticipatedAndDate && (
            <View className="flex-row gap-2 p-1">
              <Text className="font-md text-primaryBlack font-semibold ">
                Anticipated D/C Plan:
              </Text>
              <Text className="text-primaryBlack">
                {patient?.anticipated ? patient.anticipated : "N/A"}
              </Text>
            </View>
          )}
        </View>
      </TouchableOpacity>

      <LineSeperator extraStyle="my-2" />

      <View className="flex-row justify-between px-2 py-2">
        <View className="justify-end w-[50%]">
          <TouchableOpacity
            className={`flex-row shadow-sm ${
              patient?.cha2ds2_vasc?.score < 3 ? "bg-red-1" : "bg-primaryBg"
            } rounded justify-between p-2 mb-1`}
            onPress={() => chadPress(patient.cha2ds2_vasc.calculation)}
          >
            <Text
              className={`${
                patient?.cha2ds2_vasc?.score < 3
                  ? "text-red-3"
                  : "text-primaryPurple"
              }`}
            >
              CHA<Text style={{ fontSize: 9 }}>2</Text>DS
              <Text style={{ fontSize: 9 }}>2</Text>-VASc
            </Text>
            <Text
              className={`
                         ${
                           patient?.cha2ds2_vasc?.score < 3
                             ? "text-red-3"
                             : "text-primaryPurple"
                         }
                         `}
            >
              {patient?.cha2ds2_vasc?.score}
            </Text>
          </TouchableOpacity>
        </View>

        <View className="items-end gap-2 w-[50%]">
          <TouchableOpacity
            className={`${
              patient?.cta || patient?.study?.length > 0
                ? `bg-green-2`
                : `bg-secondaryGray`
            } rounded items-center w-[60%] p-2 shadow-sm`}
            onPress={() => {
              if (!patient?.cta && patient?.study?.length > 0) {
                navigation.navigate("WebViewer", {
                  link: patient?.study[0]?.viewer_link,
                });
              } else if (patient?.cta) {
                selectPatient(patientCase.case_id);
                navigation.navigate("CTA");
              }
            }}
            disabled={patient?.cta || patient?.study?.length > 0 ? false : true}
          >
            <Text
              className={`
                                  ${
                                    patient?.cta || patient?.study?.length > 0
                                      ? `text-green-3`
                                      : `text-primaryWhite`
                                  }
                                  `}
            >
              {"Pre-Op CTA"}
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            className={`${
              patient?.tee ? `bg-green-2` : `bg-secondaryGray`
            } rounded items-center w-[60%] p-2 shadow-sm`}
            onPress={() => {
              navigation.navigate("TEE");
            }}
            disabled={patient?.tee ? false : true}
          >
            <Text
              className={`
                                  ${
                                    patient?.tee
                                      ? `text-green-3`
                                      : `text-primaryWhite`
                                  }
                                  `}
            >
              {"Pre-Op TEE"}
            </Text>
          </TouchableOpacity>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  cardContainer: {
    position: "relative",
    flexDirection: "column",
    width: "100%",
    backgroundColor: "#ffffff",
    borderRadius: 10,
    padding: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
  },
  ribbonContainer: {
    position: "absolute",
    top: 0,
    right: 12,
    backgroundColor: "#FF4D4F", // Red color for the ribbon
    paddingVertical: 2,
    width: 100,
    alignItems: "center",
    justifyContent: "center",
    zIndex: 10,

    // borderBottomRightRadius: 5,
    // borderTopLeftRadius: 5,
    // borderBottomLeftRadius: 5,
  },
  ribbonText: {
    color: "#FFFFFF",
    fontWeight: "bold",
    fontSize: 10,
  },
});

export default PatientCard;
