import React from "react";
import { createStackNavigator } from "@react-navigation/stack";
import ScheduleScreen from "./screens/ScheduleScreen";
import PatientDetailsScreen from "./screens/PatientDetailsScreen";
import CTAScreen from "./screens/CTAScreen";
import TEEScreen from "../../RepStack/schedule/screens/TEEScreen";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import PDFViewer from "./screens/PdfViewer";

const ScheduleStack = createStackNavigator();

export default function ScheduleScreenStack(): React.JSX.Element {
  const insets = useSafeAreaInsets();

  return (
    <ScheduleStack.Navigator>
      <ScheduleStack.Screen
        name="Schedule"
        component={ScheduleScreen}
        options={{
          headerShown: true,

          headerStyle: {
            height: insets.top,
          },
        }}
      />
      <ScheduleStack.Screen
        name="Patient Details"
        component={PatientDetailsScreen}
        // options={{headerBackTitle: 'Homepage'}}
        options={{ title: "Patient Details" }}
      />
      <ScheduleStack.Screen
        name="CTA"
        component={CTAScreen}
        options={{ title: "PRE-OP CTA" }}
      />
      <ScheduleStack.Screen
        name="TEE"
        component={TEEScreen}
        options={{ title: "PRE-OP TEE" }}
      />
      <ScheduleStack.Screen
        name="PdfViewer"
        component={PDFViewer}
        options={{ title: "" }}
      />

      <ScheduleStack.Screen
        name="WebViewer"
        component={PDFViewer}
        options={{ title: "" }}
      />
    </ScheduleStack.Navigator>
  );
}
