// src/services/debounceSaveService.ts

import { Dispatch } from "redux";
import { setSaveStatus, setLastSavedData } from "../store/services";

// Track debounce timers for each screen
const debounceTimers: { [screenId: string]: NodeJS.Timeout | null } = {};

// Track save operations in progress for each screen
const saveInProgress: { [screenId: string]: boolean } = {};

// Queue for pending save operations for each screen
const saveQueue: { [screenId: string]: { data: any; timestamp: number }[] } =
  {};

// Cache of last saved data for each screen to avoid unnecessary saves
const lastSavedDataCache: { [screenId: string]: any } = {};

/**
 * Process the save queue for a specific screen
 * This function will process all queued save operations in order
 */
const processSaveQueue = async (
  screenId: string,
  saveFunction: (data: any) => Promise<void>,
  dispatch: any,
  actions: {
    setSaveStatus: (payload: { screenId: string; status: string }) => void;
    setLastSavedData: (payload: { screenId: string; data: any }) => void;
  }
) => {
  // If no queue exists or queue is empty, exit
  if (!saveQueue[screenId] || saveQueue[screenId].length === 0) {
    saveInProgress[screenId] = false;
    return;
  }

  // Mark as in progress
  saveInProgress[screenId] = true;

  // Get the next item from the queue (the most recent one)
  const queueItem = saveQueue[screenId].pop();

  if (!queueItem) {
    saveInProgress[screenId] = false;
    return;
  }

  try {
    // Set status to saving
    dispatch(actions.setSaveStatus({ screenId, status: "saving" }));

    // Call save function with the data
    await saveFunction(queueItem.data);

    // Update cache with successfully saved data
    lastSavedDataCache[screenId] = JSON.parse(JSON.stringify(queueItem.data));

    // Set status to saved
    dispatch(actions.setSaveStatus({ screenId, status: "saved" }));

    // Update Redux with last saved data
    dispatch(actions.setLastSavedData({ screenId, data: queueItem.data }));
  } catch (error) {
    console.error(`Error saving data for ${screenId}:`, error);
    dispatch(actions.setSaveStatus({ screenId, status: "error" }));
  } finally {
    // Process the next item in the queue if any
    if (saveQueue[screenId] && saveQueue[screenId].length > 0) {
      processSaveQueue(screenId, saveFunction, dispatch, actions);
    } else {
      // No more items in the queue
      saveInProgress[screenId] = false;
    }
  }
};

/**
 * Debounced auto-save function that supports queuing
 * Each screen has its own independent debounce and queue
 */
export const debounceAutoSave = ({
  screenId,
  data,
  saveFunction,
  delay,
  dispatch,
  actions,
}: {
  screenId: string;
  data: any;
  saveFunction: (data: any) => Promise<void>;
  delay: number;
  dispatch: any;
  actions: {
    setSaveStatus: (payload: { screenId: string; status: string }) => void;
    setLastSavedData: (payload: { screenId: string; data: any }) => void;
  };
}) => {
  // Convert data to string for comparison
  const dataString = JSON.stringify(data);
  const lastSavedDataString = JSON.stringify(lastSavedDataCache[screenId]);

  // Only proceed if there are actual changes
  if (dataString === lastSavedDataString) {
    return; // No changes detected, exit early
  }

  // Initialize queue for this screen if it doesn't exist
  if (!saveQueue[screenId]) {
    saveQueue[screenId] = [];
  }

  // Clear existing timer if any
  if (debounceTimers[screenId]) {
    clearTimeout(debounceTimers[screenId]);
  }

  // Set status to changes_detected
  dispatch(actions.setSaveStatus({ screenId, status: "changes_detected" }));

  // Set up new timer
  debounceTimers[screenId] = setTimeout(() => {
    // Add the data to the queue with a timestamp
    saveQueue[screenId] = [{ data, timestamp: Date.now() }];

    // If no save is in progress, start processing the queue
    if (!saveInProgress[screenId]) {
      processSaveQueue(screenId, saveFunction, dispatch, actions);
    }

    // Clean up timer
    delete debounceTimers[screenId];
  }, delay);
};
