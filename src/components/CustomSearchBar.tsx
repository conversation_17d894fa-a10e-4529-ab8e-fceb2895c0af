import React, { useState } from "react";
import { View } from "react-native";
import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons";
import CustomInput from "./CustomTextInput";

interface ICustomSearchBar {
  placeholder: string;
  value: string;
  onChangeText: (text: string) => void;
}

const CustomSearchBar: React.FC<ICustomSearchBar> = ({
  onChangeText,
  placeholder,
  value,
}) => {
  const handleInputChange = (text: string) => {
    onChangeText(text);
  };

  return (
    <View className="flex-row items-center">
      <View className="flex-1">
        <CustomInput
          inputValue={value}
          onInputChange={handleInputChange}
          placeholder={"  " + placeholder}
          searchIcon={{ icon: true, iconPress: () => {} }}
          height={35}
        />
        {/* <MaterialCommunityIcons
          name="card-search"
          size={40}
          color="#8143d9"
          className="absolute -top-1"
        /> */}
      </View>
    </View>
  );
};

export default CustomSearchBar;
