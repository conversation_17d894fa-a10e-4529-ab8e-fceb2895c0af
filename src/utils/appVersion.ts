import { version as packageVersion } from "../../package.json";
import { Enviromnent } from "../api/config";

interface VersionConfig {
  DEV: string;
  STAGING: string;
  PROD: string;
}

const environmentVersions: VersionConfig = {
  DEV: packageVersion, // Using package.json version for DEV
  STAGING: packageVersion, // Using package.json version for STAGING
  PROD: "0.5", // Custom version for PROD
};

export const getAppVersion = (): string => {
  const currentVersion = environmentVersions[Enviromnent];
  if (Enviromnent !== "PROD") {
    return `${Enviromnent}-${currentVersion}`;
  }
  return currentVersion;
};

export const getRawVersion = (): string => {
  return environmentVersions[Enviromnent];
};

export const updateEnvironmentVersion = (
  env: keyof VersionConfig,
  newVersion: string
): void => {
  environmentVersions[env] = newVersion;
};
