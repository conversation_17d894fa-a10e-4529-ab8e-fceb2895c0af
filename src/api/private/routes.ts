const routes = {
  rep: {
    schedule: `/reps/schedule/cases`,

    sites: "/sites",

    site_details_get: (site_id: string) => `/sites/${site_id}`,
    site_details_put: (site_id: string) => `/sites/${site_id}`,

    implanting_physician_get: (physician_id: string) =>
      `/sites/physicians/${physician_id}`,
    implanting_physician_put: (physician_id: string) =>
      `/sites/physicians/${physician_id}`,

    provider_credentials_get: `/sites/provider/credentials`,

    provider_experience_get: `/sites/provider/experience`,

    procedure_types: "/procedure-type",
    patientsList: (site_id: string, case_date: string) =>
      `/reps/schedule/sites/${site_id}/cases/${case_date}`,
    post_implanting_physician: (site_id: string) =>
      `/sites/${site_id}/physicians`,
    pre_op: (case_id: string) => `/reps/schedule/cases/${case_id}/patient`,

    patientsListUnassigned: (site_id: string, case_date: string) =>
      `/reps/schedule/sites/${site_id}/cases/${case_date}/rep-unassigned`,

    anesthesia_get: (case_id: string) =>
      `/reps/case-details/${case_id}/anesthesia`,

    anesthesia_put: (case_details_id: string) =>
      `/reps/case-details/${case_details_id}/anesthesia`,

    transseptal_get: (case_id: string) =>
      `/reps/case-details/${case_id}/transseptal-puncture`,

    transseptal_put: (case_details_id: string) =>
      `/reps/case-details/${case_details_id}/transseptal-puncture`,

    laao_implant_get: (case_id: string) =>
      `/reps/case-details/${case_id}/laao-implant`,

    laao_implant_put: (case_details_id: string) =>
      `/reps/case-details/${case_details_id}/laao-implant`,

    fluoroscopy_get: (case_id: string) =>
      `/reps/case-details/${case_id}/fluoroscopy`,

    fluoroscopy_put: (case_details_id: string) =>
      `/reps/case-details/${case_details_id}/fluoroscopy`,

    passcriteria_get: (case_id: string) =>
      `/reps/case-details/${case_id}/pass-criteria`,

    passcriteria_put: (case_details_id: string) =>
      `/reps/case-details/${case_details_id}/pass-criteria`,

    laaanatomy_get: (case_id: string) =>
      `/reps/case-details/${case_id}/anatomy`,

    laaanatomy_put: (case_details_id: string) =>
      `/reps/case-details/${case_details_id}/anatomy`,

    preop_cta_get: (case_id: string) =>
      `/reps/case-details/${case_id}/preop-cta`,

    preop_cta_put: (case_id: string) =>
      `/reps/case-details/${case_id}/preop-cta`,

    post_op_get: (case_id: string) => `/case-details/post-op/${case_id}`,

    post_op_put: (case_id: string) => `/case-details/post-op/${case_id}`,

    truplan_pdf_get: (case_id: string) => `/truplan/case-details/${case_id}`,

    truplan_pdf_post: (case_id: string) => `/truplan/case-details/${case_id}`,

    caseSynopsis_get: (case_id: string) => `/cases/${case_id}`,

    case_put: (case_id: string) => `/reps/schedule/cases/${case_id}`,

    task_get: `user/tasks`,

    afib_basic_get: (case_id: string) => `/reps/${case_id}/basic`,

    afib_basic_put: (case_id: string) => `/reps/${case_id}/basic`,

    afib_procedure_get: (case_id: string) =>
      `/reps/${case_id}/procedure-details`,

    afib_procedure_put: (case_id: string) =>
      `/reps/${case_id}/procedure-details`,

    groin_access_get: (case_id: string) =>
      `/reps/case-details/${case_id}/groin-access`,

    groin_access_put: (case_detail_id: string) =>
      `/reps/case-details/${case_detail_id}/groin-access`,
    patient_post: (site_id: string) => `/sites/${site_id}/patients`,

    // referring_providers_get: `/referring-providers`,
    // referring_providers_put: (id: string) => `/referring-providers/${id}`,
    // referring_providers_post: `/referring-providers`,
    // referring_providers_credentials_get: `/referring-providers/credential-options`,
    get_implanting_physicians: (site_id: string) =>
      `/sites/${site_id}/implanting-physicians`,
  },
  coordinator: {
    schedule: `/clinical/schedules`,

    task_get: `/user/tasks`,

    profile_get: `/users/profile`,

    patient_post: () => `/clinical/patients`,

    referring_providers_get: `/referring-providers`,

    implanting_physicians_get: `/clinical/implanting-physician`,

    procedure_get: (case_id: string) => `/cases/${case_id}/laao-procedure`,

    procedure_put: (case_id: string) => `/cases/${case_id}/laao-procedure`,

    post_op_get: (case_id: string) => `/case-details/post-op/${case_id}`,

    post_op_put: (case_id: string) => `/case-details/post-op/${case_id}`,

    preop_get: (case_id: string) => `/reps/schedule/cases/${case_id}/patient`,

    preop_put: (case_id: string) => `/patient/${case_id}`,

    preop_history_put: (case_id: string) => `/cases/${case_id}/history`,

    pre_op_consult_visit_post: (case_id: string) => `/${case_id}/consult-visit`,
    preop_testing_put: (case_id: string) => `/${case_id}/pre-op-lab`,

    preop_cta_put: (case_id: string) =>
      `/reps/case-details/${case_id}/preop-cta`,
    truplan_pdf_get: (case_id: string) => `/truplan/case-details/${case_id}`,

    truplan_pdf_post: (case_id: string) => `/truplan/case-details/${case_id}`,

    chad_score_put: (case_id: string) => `/patient/${case_id}/chadscore`,
    hasbled_score_put: (case_id: string) => `/patient/${case_id}/hasbled`,
  },
  clinician: {
    anesthesia_get: (case_id: string) =>
      `/reps/case-details/${case_id}/anesthesia`,

    anesthesia_put: (case_details_id: string) =>
      `/reps/case-details/${case_details_id}/anesthesia`,

    fluoroscopy_get: (case_id: string) =>
      `/reps/case-details/${case_id}/fluoroscopy`,

    fluoroscopy_put: (case_details_id: string) =>
      `/reps/case-details/${case_details_id}/fluoroscopy`,

    laaanatomy_get: (case_id: string) =>
      `/reps/case-details/${case_id}/anatomy`,

    laaanatomy_put: (case_details_id: string) =>
      `/reps/case-details/${case_details_id}/anatomy`,

    laao_implant_get: (case_id: string) =>
      `/reps/case-details/${case_id}/laao-implant`,

    laao_implant_put: (case_details_id: string) =>
      `/reps/case-details/${case_details_id}/laao-implant`,

    passcriteria_get: (case_id: string) =>
      `/reps/case-details/${case_id}/pass-criteria`,

    passcriteria_put: (case_details_id: string) =>
      `/reps/case-details/${case_details_id}/pass-criteria`,

    patientsList: (site_id: string, case_date: string) =>
      `/reps/schedule/sites/${site_id}/cases/${case_date}`,

    caseSynopsis_get: (case_id: string) => `/cases/${case_id}`,

    transseptal_get: (case_id: string) =>
      `/reps/case-details/${case_id}/transseptal-puncture`,

    transseptal_put: (case_details_id: string) =>
      `/reps/case-details/${case_details_id}/transseptal-puncture`,

    schedule: `/clinical/schedules`,

    task_get: `/user/tasks`,

    profile_get: `/users/profile`,

    patient_post: () => `/clinical/patients`,

    referring_providers_get: `/referring-providers`,

    implanting_physicians_get: `/clinical/implanting-physician`,

    procedure_get: (case_id: string) => `/cases/${case_id}/laao-procedure`,

    procedure_put: (case_id: string) => `/cases/${case_id}/laao-procedure`,

    post_op_get: (case_id: string) => `/case-details/post-op/${case_id}`,

    post_op_put: (case_id: string) => `/case-details/post-op/${case_id}`,

    preop_get: (case_id: string) => `/reps/schedule/cases/${case_id}/patient`,

    preop_put: (case_id: string) => `/patient/${case_id}`,

    preop_history_put: (case_id: string) => `/cases/${case_id}/history`,

    pre_op_consult_visit_post: (case_id: string) => `/${case_id}/consult-visit`,

    preop_testing_put: (case_id: string) => `/${case_id}/pre-op-lab`,
  },
  abstractor: {
    schedule: `/abstractor/patients`,
  },
  common: {
    dashboard_get: `/dashboards`,
    rationale_get: `/rationale`,

    //referring providers
    referring_providers_get: `/referring-providers`,
    referring_providers_put: (id: string) => `/referring-providers/${id}`,
    referring_providers_id_get: (id: string) => `/referring-providers/${id}`,
    referring_providers_post: `/referring-providers`,
    referring_providers_credentials_get: `/referring-providers/credential-options`,

    pcp_providers_post: `/pcp-providers`,
    pcp_providers_get: `/pcp-providers`,
    pcp_providers_put: (id: string) => `/pcp-providers/${id}`,

    pcp_providers_id_get: (id: string) => `/pcp-providers/${id}`,

    // NPI provider lookup
    npi_provider_lookup: (npi_number: string) =>
      `/npi-provider/lookup/${npi_number}`,

    application_version_get: (id: string) => `/version/${id}`,
  },
  jira: {
    create_issue: `/jira-issue`,
    get_issue_types: `/jira-issue-types`,
  },
};

export default routes;
