interface IProcedureDetails {
  case_id: string;
  case_detail_id: string;
  procedure_date: string;
  procedure_time: string;
  implanting_physician: {
    id: string;
    name: string;
    image_url: string;
  };
  device: {
    selected: {
      id: string;
      name: string;
      device_size: number;
    };
    options: {
      id: string;
      name: string;
      device_size: number;
    }[];
  };
  anesthesia: {
    selected: {
      id: string;
      name: string;
    };
    options: {
      id: string;
      name: string;
    }[];
  };

  leak: boolean;
  leak_value: number;
  complication: {
    selected: {
      id: string;
      name: string;
      complication_other: string;
      complication_present: boolean;
    };
    options: {
      id: string;
      name: string;
    }[];
  };
  afib_ablation: boolean;
}

export type { IProcedureDetails };
