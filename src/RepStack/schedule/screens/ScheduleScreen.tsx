import React from "react";
import { FlatList, View, Text, StyleSheet, RefreshControl } from "react-native";
import { CalendarProvider, ExpandableCalendar } from "react-native-calendars";
import { useDispatch } from "react-redux";
import { AppDispatch } from "../../../store";
import { fetchSchedules } from "../../../store/rep/ScheduleStack/schedules/thunk";
import {
  useSchedules,
  useLoadersState,
  useAddCaseModal,
  useAddPatientModal,
} from "../hooks/schedulesHooks";
import SiteCard from "../components/SiteCard";
import moment from "moment";
import Loader from "../../../components/Loader";
import BottomSheetComponent, {
  BottomSheetRefProps,
} from "../../../components/BottomSheetComponent";
import {
  showAddCaseModal,
  showAddPatientModal,
} from "../../../store/rep/ScheduleStack/schedules";
import AddCaseBottomSheet from "../components/AddCaseBottomSheet";
import AddPatient from "./AddPatient";
import CustomTabView from "../../../components/CustomTabView";
import AddCase from "../components/AddCase";
import {
  resetImplantingPhysicians,
  resetPatientDetails,
} from "../../../store/rep/ScheduleStack/addpatient";

interface IScheduleScreenProps {}

const ScheduleScreen: React.FunctionComponent<IScheduleScreenProps> = () => {
  const dispatch = useDispatch<AppDispatch>();
  const [selectedDate, setSelectedDate] = React.useState(new Date());
  const caseBottomRef = React.useRef<BottomSheetRefProps>(null);
  const patientBottomRef = React.useRef<BottomSheetRefProps>(null);
  const [refreshing, setRefreshing] = React.useState(false);
  const showModal = useAddCaseModal();
  const showAddModal = useAddPatientModal();
  const [addCase, setAddCase] = React.useState(false);
  const [addPatient, setAddpatient] = React.useState(true);
  const { agendaLoader } = useLoadersState();

  const fetchData = async () => {
    const startWeek = moment(selectedDate)
      .startOf("isoWeek")
      .format("YYYY-MM-DD");
    const endWeek = moment(selectedDate).endOf("isoWeek").format("YYYY-MM-DD");
    await dispatch(
      fetchSchedules({
        from_date: startWeek,
        to_date: endWeek,
      })
    );
  };

  React.useEffect(() => {
    if (showModal) {
      caseBottomRef?.current?.open();
    } else {
      caseBottomRef?.current?.close();
    }
  }, [showModal]);

  React.useEffect(() => {
    if (showAddModal) {
      patientBottomRef?.current?.open();
    } else {
      patientBottomRef?.current?.close();
    }
  }, [showAddModal]);

  React.useEffect(() => {
    fetchData();
  }, [selectedDate, addPatient, addCase]);

  const schedules = useSchedules();

  const startOfWeek = moment(selectedDate).startOf("isoWeek");
  const endOfWeek = moment(selectedDate).endOf("isoWeek");
  const allDates = [];
  const day = startOfWeek.clone();
  while (day.isSameOrBefore(endOfWeek, "day")) {
    allDates.push(day.format("YYYY-MM-DD"));
    day.add(1, "day");
  }

  const groupedSchedules = allDates.map((date) => ({
    title: date,
    data: schedules.filter((item: any) => item.procedure_date === date),
  }));

  const markedDates = schedules.reduce((acc: any, item: any) => {
    acc[item.procedure_date] = { marked: true, dotColor: "#8143d9" };
    return acc;
  }, {});

  const handleRefresh = async () => {
    setRefreshing(true); // Start refreshing

    try {
      await fetchData();
    } catch (error) {
      console.error("Error refreshing schedules:", error);
    } finally {
      setRefreshing(false);
    }
  };

  return (
    <>
      <CalendarProvider
        date={selectedDate.toString()}
        onDateChanged={(date) => setSelectedDate(date)}
        style={styles.calendarProvider}
      >
        <ExpandableCalendar
          firstDay={1}
          theme={styles.calendarTheme}
          markedDates={markedDates}
        />

        {agendaLoader ? (
          <View className="flex-1 justify-center items-center">
            <Loader />
          </View>
        ) : (
          <FlatList
            data={groupedSchedules}
            keyExtractor={(item) => item.title}
            renderItem={({ item }) => (
              <View className="mb-2 bg-primaryWhite">
                <Text style={styles.dateHeader}>
                  {moment(item.title).format("dddd, MMM D")}
                </Text>
                {item.data.length > 0 ? (
                  item.data.map((schedule: any, index) => (
                    <SiteCard
                      key={index}
                      details={{
                        date: schedule.procedure_date,
                        hospitalName: schedule.site.name,
                        siteId: schedule.site.id,
                        startTime: schedule.start_time,
                        noOfCases: schedule.cases,
                        implantingMD: schedule.implanting_physician.name,
                        implantingPhysicianImage:
                          schedule.implanting_physician.image_url,
                        hospitalImage: schedule.site.image_url,
                      }}
                      showDate={false}
                    />
                  ))
                ) : (
                  <View className="bg-primaryWhite h-16 flex items-center justify-center">
                    <Text style={styles.noDataText}>None</Text>
                  </View>
                )}
              </View>
            )}
            bounces={true}
            refreshControl={
              <RefreshControl
                refreshing={refreshing}
                onRefresh={handleRefresh}
                colors={["#8143d9"]}
                tintColor={"#8143d9"}
              />
            }
          />
        )}
      </CalendarProvider>
      <BottomSheetComponent
        ref={caseBottomRef}
        snapPoints={["50%", "90%"]}
        backgroundColor="white"
        onClose={() => {
          dispatch(showAddCaseModal(false));
        }}
      >
        {showModal && <AddCase onSuccess={handleRefresh} />}
      </BottomSheetComponent>

      <BottomSheetComponent
        ref={patientBottomRef}
        snapPoints={["50%", "90%"]}
        backgroundColor="white"
        onClose={() => {
          dispatch(showAddPatientModal(false));
          dispatch(resetPatientDetails());
          dispatch(resetImplantingPhysicians());
          setAddCase(false);
          setAddpatient(true);
        }}
      >
        {showAddModal && (
          <View className="flex-1">
            <View className="px-3">
              <CustomTabView
                options={[
                  { label: "Add Patient", value: "add-patient" },
                  { label: "Assign Case", value: "assign-case" },
                ]}
                onSelect={(option) => {
                  if (option === "add-patient") {
                    setAddCase(false);
                    setAddpatient(true);
                  } else {
                    setAddpatient(false);
                    setAddCase(true);
                  }
                }}
                defaultIndex={0}
              />
            </View>
            {addPatient && (
              <AddPatient
                onClose={() => {
                  dispatch(showAddPatientModal(false));
                }}
                onSave={handleRefresh}
                bottomSheetRef={patientBottomRef}
              />
            )}
            {addCase && (
              <AddCase
                onSuccess={() => {
                  dispatch(showAddPatientModal(false));
                  handleRefresh();
                }}
              />
            )}
          </View>
        )}
      </BottomSheetComponent>
    </>
  );
};

export default ScheduleScreen;

const styles = StyleSheet.create({
  calendarProvider: {
    backgroundColor: "#f8f6ff",
  },
  calendarTheme: {
    selectedDayBackgroundColor: "#8143d9",
    todayTextColor: "#8143d9",
    arrowColor: "#8143d9",
    monthTextColor: "#8143d9",
    textDayFontWeight: "500",
    textMonthFontWeight: "bold",
    textDayHeaderFontWeight: "500",
  },
  dateHeader: {
    fontSize: 14,
    fontWeight: "400",
    color: "#36454F",
    // backgroundColor: "#e6e6fa",
    paddingVertical: 8,
    paddingHorizontal: 12,
    marginTop: 10,
  },
  noDataText: {
    fontSize: 16,
    // fontStyle: "italic",
    textAlign: "center",
    paddingVertical: 10,
    color: "#999",
  },
});

// import React from "react";
// import { FlatList, View, Text, StyleSheet, RefreshControl } from "react-native";
// import { CalendarProvider, ExpandableCalendar } from "react-native-calendars";
// import { useDispatch } from "react-redux";
// import { AppDispatch } from "../../../store";
// import { fetchSchedules } from "../../../store/rep/ScheduleStack/schedules/thunk";
// import {
//   useSchedules,
//   useLoadersState,
//   useAddCaseModal,
//   useAddPatientModal,
// } from "../hooks/schedulesHooks";
// import SiteCard from "../components/SiteCard";
// import moment from "moment";
// import Loader from "../../../components/Loader";
// import BottomSheetComponent, {
//   BottomSheetRefProps,
// } from "../../../components/BottomSheetComponent";
// import {
//   showAddCaseModal,
//   showAddPatientModal,
// } from "../../../store/rep/ScheduleStack/schedules";
// import AddCaseBottomSheet from "../components/AddCaseBottomSheet";
// import AddPatient from "./AddPatient";
// import CustomTabView from "../../../components/CustomTabView";
// import AddCase from "../components/AddCase";
// import {
//   resetImplantingPhysicians,
//   resetPatientDetails,
// } from "../../../store/rep/ScheduleStack/addpatient";

// interface IScheduleScreenProps {}

// const ScheduleScreen: React.FunctionComponent<IScheduleScreenProps> = () => {
//   const dispatch = useDispatch<AppDispatch>();
//   const [selectedDate, setSelectedDate] = React.useState(new Date());
//   const caseBottomRef = React.useRef<BottomSheetRefProps>(null);
//   const patientBottomRef = React.useRef<BottomSheetRefProps>(null);
//   const [refreshing, setRefreshing] = React.useState(false);
//   const showModal = useAddCaseModal();
//   const showAddModal = useAddPatientModal();
//   const [addCase, setAddCase] = React.useState(false);
//   const [addPatient, setAddpatient] = React.useState(true);
//   const { agendaLoader } = useLoadersState();

//   const fetchData = async () => {
//     const startWeek = moment(selectedDate)
//       .startOf("isoWeek")
//       .format("YYYY-MM-DD");
//     const endWeek = moment(selectedDate).endOf("isoWeek").format("YYYY-MM-DD");
//     await dispatch(
//       fetchSchedules({
//         from_date: startWeek,
//         to_date: endWeek,
//       })
//     );
//   };

//   React.useEffect(() => {
//     if (showModal) {
//       caseBottomRef?.current?.open();
//     } else {
//       caseBottomRef?.current?.close();
//     }
//   }, [showModal]);

//   React.useEffect(() => {
//     if (showAddModal) {
//       patientBottomRef?.current?.open();
//     } else {
//       patientBottomRef?.current?.close();
//     }
//   }, [showAddModal]);

//   React.useEffect(() => {
//     fetchData();
//   }, [selectedDate, addPatient, addCase]);

//   const schedules = useSchedules();

//   const startOfWeek = moment(selectedDate).startOf("isoWeek");
//   const endOfWeek = moment(selectedDate).endOf("isoWeek");
//   const allDates = [];
//   const day = startOfWeek.clone();
//   while (day.isSameOrBefore(endOfWeek, "day")) {
//     allDates.push(day.format("YYYY-MM-DD"));
//     day.add(1, "day");
//   }

//   const groupedSchedules = allDates.map((date) => {
//     const daySchedules = schedules.filter(
//       (item: any) => item.procedure_date === date
//     );

//     // Group by site and sum up cases
//     const groupedBySite = daySchedules.reduce((acc: any, schedule: any) => {
//       const siteId = schedule.site.id;

//       if (acc[siteId]) {
//         // Site already exists, sum up the cases
//         acc[siteId].cases += schedule.cases;

//         // Handle multiple physicians
//         if (
//           acc[siteId].implanting_physician.name !==
//           schedule.implanting_physician.name
//         ) {
//           acc[siteId].implanting_physician.name = "Multiple Physicians";
//           acc[siteId].implanting_physician.image_url = null; // Clear image for multiple physicians
//         }

//         // Keep the earliest start time
//         if (schedule.start_time < acc[siteId].start_time) {
//           acc[siteId].start_time = schedule.start_time;
//         }
//       } else {
//         // New site, add it to the accumulator
//         acc[siteId] = { ...schedule };
//       }

//       return acc;
//     }, {});

//     return {
//       title: date,
//       data: Object.values(groupedBySite),
//     };
//   });

//   const markedDates = schedules.reduce((acc: any, item: any) => {
//     acc[item.procedure_date] = { marked: true, dotColor: "#8143d9" };
//     return acc;
//   }, {});

//   const handleRefresh = async () => {
//     setRefreshing(true); // Start refreshing

//     try {
//       await fetchData();
//     } catch (error) {
//       console.error("Error refreshing schedules:", error);
//     } finally {
//       setRefreshing(false);
//     }
//   };

//   console.log(JSON.stringify(groupedSchedules, null, 2), "groupedSchedules");

//   return (
//     <>
//       <CalendarProvider
//         date={selectedDate.toString()}
//         onDateChanged={(date) => setSelectedDate(date)}
//         style={styles.calendarProvider}
//       >
//         <ExpandableCalendar
//           firstDay={1}
//           theme={styles.calendarTheme}
//           markedDates={markedDates}
//         />

//         {agendaLoader ? (
//           <View className="flex-1 justify-center items-center">
//             <Loader />
//           </View>
//         ) : (
//           <FlatList
//             data={groupedSchedules}
//             keyExtractor={(item) => item.title}
//             renderItem={({ item }) => (
//               <View className="mb-2 bg-primaryWhite">
//                 <Text style={styles.dateHeader}>
//                   {moment(item.title).format("dddd, MMM D")}
//                 </Text>
//                 {item.data.length > 0 ? (
//                   item.data.map((schedule: any, index) => (
//                     <SiteCard
//                       key={index}
//                       details={{
//                         date: schedule.procedure_date,
//                         hospitalName: schedule.site.name,
//                         siteId: schedule.site.id,
//                         startTime: schedule.start_time,
//                         noOfCases: schedule.cases,
//                         implantingMD: schedule.implanting_physician.name,
//                         implantingPhysicianImage:
//                           schedule.implanting_physician.image_url,
//                         hospitalImage: schedule.site.image_url,
//                       }}
//                       showDate={false}
//                     />
//                   ))
//                 ) : (
//                   <View className="bg-primaryWhite h-16 flex items-center justify-center">
//                     <Text style={styles.noDataText}>None</Text>
//                   </View>
//                 )}
//               </View>
//             )}
//             bounces={true}
//             refreshControl={
//               <RefreshControl
//                 refreshing={refreshing}
//                 onRefresh={handleRefresh}
//                 colors={["#8143d9"]}
//                 tintColor={"#8143d9"}
//               />
//             }
//           />
//         )}
//       </CalendarProvider>
//       <BottomSheetComponent
//         ref={caseBottomRef}
//         snapPoints={["50%", "90%"]}
//         backgroundColor="white"
//         onClose={() => {
//           dispatch(showAddCaseModal(false));
//         }}
//       >
//         {showModal && <AddCase onSuccess={handleRefresh} />}
//       </BottomSheetComponent>

//       <BottomSheetComponent
//         ref={patientBottomRef}
//         snapPoints={["50%", "90%"]}
//         backgroundColor="white"
//         onClose={() => {
//           dispatch(showAddPatientModal(false));
//           dispatch(resetPatientDetails());
//           dispatch(resetImplantingPhysicians());
//           setAddCase(false);
//           setAddpatient(true);
//         }}
//       >
//         {showAddModal && (
//           <View className="flex-1">
//             <View className="px-3">
//               <CustomTabView
//                 options={[
//                   { label: "Add Patient", value: "add-patient" },
//                   { label: "Assign Case", value: "assign-case" },
//                 ]}
//                 onSelect={(option) => {
//                   if (option === "add-patient") {
//                     setAddCase(false);
//                     setAddpatient(true);
//                   } else {
//                     setAddpatient(false);
//                     setAddCase(true);
//                   }
//                 }}
//                 defaultIndex={0}
//               />
//             </View>
//             {addPatient && (
//               <AddPatient
//                 onClose={() => {
//                   dispatch(showAddPatientModal(false));
//                 }}
//                 onSave={handleRefresh}
//                 bottomSheetRef={patientBottomRef}
//               />
//             )}
//             {addCase && (
//               <AddCase
//                 onSuccess={() => {
//                   dispatch(showAddPatientModal(false));
//                   handleRefresh();
//                 }}
//               />
//             )}
//           </View>
//         )}
//       </BottomSheetComponent>
//     </>
//   );
// };

// export default ScheduleScreen;

// const styles = StyleSheet.create({
//   calendarProvider: {
//     backgroundColor: "#f8f6ff",
//   },
//   calendarTheme: {
//     selectedDayBackgroundColor: "#8143d9",
//     todayTextColor: "#8143d9",
//     arrowColor: "#8143d9",
//     monthTextColor: "#8143d9",
//     textDayFontWeight: "500",
//     textMonthFontWeight: "bold",
//     textDayHeaderFontWeight: "500",
//   },
//   dateHeader: {
//     fontSize: 14,
//     fontWeight: "400",
//     color: "#36454F",
//     // backgroundColor: "#e6e6fa",
//     paddingVertical: 8,
//     paddingHorizontal: 12,
//     marginTop: 10,
//   },
//   noDataText: {
//     fontSize: 16,
//     // fontStyle: "italic",
//     textAlign: "center",
//     paddingVertical: 10,
//     color: "#999",
//   },
// });
