import * as React from "react";
import { View, Text } from "react-native";
import CustomText from "./CustomText";
import CustomCheckBox from "./CustomCheckBox";
import globalStyles from "../styles/GlobalStyles";
import SaveActionButton from "./SaveActionButton";

interface IHASBLEDCardProps {
  hasbled: any;
  onSave: (updatedHasbled: any) => void;
}

// Updated mapping to match the response keys with desired display names
const keyDisplayMap: Record<string, string> = {
  age: "Age (> 65 y/o)",
  hyperten_uncontrol: "HTN",
  abnorm_liver_func: "Abnl Liver Fx",
  abnorm_renal_func: "Abnl Renal Fx",
  stroke: "Stroke",
  bleeding: "Bleeding Risk",
  labile_inr: "Labile INRs",
  medication_bleeding: "Drugs",
  alcohol: "Alcohol",
};

const HASBLEDCard: React.FunctionComponent<IHASBLEDCardProps> = ({
  hasbled,
  onSave,
}) => {
  // Initialize with an empty object if hasbled is undefined
  const defaultHasbled = hasbled || {};
  const [updatedHasbled, setUpdatedHasbled] = React.useState(defaultHasbled);
  const [diff, setDiff] = React.useState(false);

  React.useEffect(() => {
    // Update state when hasbled changes, ensuring we never set undefined
    setUpdatedHasbled(hasbled || {});
    setDiff(false);
  }, [hasbled]);

  const handleCheckBoxChange = (key: string, value: boolean) => {
    setUpdatedHasbled((prev: any) => {
      const newHasbled = { ...prev, [key]: { ...prev[key], isChecked: value } };

      // Only check for differences if hasbled is defined
      if (hasbled) {
        setDiff(
          Object.keys(newHasbled).some(
            (k) => newHasbled[k]?.isChecked !== hasbled[k]?.isChecked
          )
        );
      }

      return newHasbled;
    });
  };

  const saveDetails = () => {
    // If hasbled is undefined, use updatedHasbled as the source of keys
    const sourceObject = hasbled || updatedHasbled;

    const formattedDiff = Object.keys(sourceObject).reduce((acc, key) => {
      if (key !== "age") {
        if (typeof sourceObject[key] === "object") {
          // Use optional chaining to safely access properties
          acc[key] =
            updatedHasbled[key]?.isChecked ?? sourceObject[key]?.isChecked;
        } else {
          acc[key] = updatedHasbled[key];
        }
      }
      return acc;
    }, {} as Record<string, boolean>);

    onSave(formattedDiff);
  };

  const handleCancel = () => {
    setUpdatedHasbled(hasbled || {});
    setDiff(false);
  };

  return (
    <View className="px-4">
      <View className={`${globalStyles.containers.flex_center} mb-6`}>
        <Text style={{ fontSize: 18, color: "#6A1B9A", fontWeight: "bold" }}>
          HAS-BLED Risk Score
        </Text>
      </View>

      {Object.keys(updatedHasbled || {}).map((key) => {
        // Use the updated mapping, fallback to default format if key not provided
        const displayName = keyDisplayMap[key]
          ? keyDisplayMap[key]
          : key.charAt(0).toUpperCase() + key.slice(1).replace(/_/g, " ");

        if (key === "age") {
          // For "age", render a different layout since it's a numeric value only.
          return (
            <View
              key={key}
              className="flex-row justify-between items-center border-b p-2 my-1"
            >
              <Text className="text-primaryBlack font-medium">
                {displayName}
              </Text>
              <View className="w-[40%] flex-row items-center justify-between">
                <Text className="text-primaryBlack">{updatedHasbled?.age}</Text>
              </View>
            </View>
          );
        } else {
          return (
            <View
              key={key}
              className="flex-row justify-between items-center border-b p-2 my-1"
            >
              <Text className="text-primaryBlack font-medium">
                {displayName}
              </Text>
              <View className="w-[40%] flex-row items-center justify-between">
                <Text className="text-primaryBlack">
                  {updatedHasbled[key]?.value}
                </Text>
                <CustomCheckBox
                  checked={updatedHasbled[key]?.isChecked}
                  onChange={(value) => handleCheckBoxChange(key, value)}
                />
              </View>
            </View>
          );
        }
      })}

      <View className="mt-2 mb-5 border-t border-primaryWhite" />

      <View className="flex-row justify-center gap-4">
        <SaveActionButton
          disabled={!diff}
          onPress={saveDetails}
          onCancel={handleCancel}
        />
      </View>
    </View>
  );
};

export default HASBLEDCard;
