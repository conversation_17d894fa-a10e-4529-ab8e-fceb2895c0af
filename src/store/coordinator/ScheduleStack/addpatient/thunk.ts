import { createAsyncThunk } from "@reduxjs/toolkit";
import api from "../../../../api/api";
import { IPostPatient } from "./types";

const fetchReferringProviders = createAsyncThunk(
  "addpatient/fetchReferringProviders",
  async (_, thunkAPI) => {
    try {
      const response = await api.private.get(
        api.routes.privateRoutes.coordinator.referring_providers_get
      );

      if (response.data && response.data.result) {
        return response.data.result;
      } else {
        return thunkAPI.rejectWithValue(
          'Invalid response format: Missing "result" field'
        );
      }
    } catch (error: any) {
      if (error.response) {
        console.error("Error fetching referring providers:");
        return thunkAPI.rejectWithValue("Failed to fetch referring providers");
      } else if (error.request) {
        console.error("No response received:", error.request);
        return thunkAPI.rejectWithValue("No response received from the server");
      } else {
        console.error("Error occurred:", error.message);
        return thunkAPI.rejectWithValue("An unknown error occurred");
      }
    }
  }
);

const fetchImplantingPhysicians = createAsyncThunk(
  "addpatient/fetchImplantingPhysicians",
  async (_, thunkAPI) => {
    try {
      const response = await api.private.get(
        api.routes.privateRoutes.coordinator.implanting_physicians_get
      );

      if (response.data && response.data.result) {
        return response.data.result;
      } else {
        return thunkAPI.rejectWithValue(
          'Invalid response format: Missing "result" field'
        );
      }
    } catch (error: any) {
      if (error.response) {
        console.error(
          "Error fetching implanting physicians:",
          error.response.data
        );
        return thunkAPI.rejectWithValue(
          "Failed to fetch implanting physicians"
        );
      } else if (error.request) {
        console.error("No response received:", error.request);
        return thunkAPI.rejectWithValue("No response received from the server");
      } else {
        console.error("Error occurred:", error.message);
        return thunkAPI.rejectWithValue("An unknown error occurred");
      }
    }
  }
);

const fetchRationale = createAsyncThunk(
  "addpatient/fetchRationale",
  async (_, thunkAPI) => {
    try {
      const response = await api.private.get(
        api.routes.privateRoutes.common.rationale_get
      );

      if (response.data && response.data.result) {
        return response.data.result;
      } else {
        return thunkAPI.rejectWithValue(
          'Invalid response format: Missing "result" field'
        );
      }
    } catch (error: any) {
      if (error.response) {
        console.error("Error fetching rationale:");
        return thunkAPI.rejectWithValue("Failed to fetch rationale");
      } else if (error.request) {
        console.error("No response received:", error.request);
        return thunkAPI.rejectWithValue("No response received from the server");
      } else {
        console.error("Error occurred:", error.message);
        return thunkAPI.rejectWithValue("An unknown error occurred");
      }
    }
  }
);

const postPatientDetails = createAsyncThunk(
  "addpatient/putpostPatientDetails",
  async ({ payload }: { payload: IPostPatient }, thunkAPI) => {
    try {
      const response = await api.private.post(
        api.routes.privateRoutes.coordinator.patient_post(),
        payload
      );

      if (response.data && response.data.result) {
        return response.data.result;
      }
    } catch (error: any) {
      console.error("Error caught during API call:", error.response.data);
      if (error.response) {
        if (error.response.status === 400) {
        }
        return thunkAPI.rejectWithValue("Failed to post patient details");
      } else if (error.request) {
        console.error("No response received:", error.request);
        return thunkAPI.rejectWithValue("No response received from the server");
      } else {
        console.error("Error occurred:", error.message);
        return thunkAPI.rejectWithValue("An unknown error occurred");
      }
    }
  }
);

export {
  fetchReferringProviders,
  fetchImplantingPhysicians,
  fetchRationale,
  postPatientDetails,
};
