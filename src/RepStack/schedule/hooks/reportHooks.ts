import { useSelector } from "react-redux";
import { RootState } from "../../../store";

const usecaseSynopsisDetails = () => {
  const caseSynopsisDetails = useSelector((state: RootState) => state.report);

  return caseSynopsisDetails;
};

const useLoaderAndError = () => {
  const loader = useSelector((state: RootState) => state.report.loading);

  const error = useSelector((state: RootState) => state.report.error);
  return { loader, error };
};
export { usecaseSynopsisDetails, useLoaderAndError };