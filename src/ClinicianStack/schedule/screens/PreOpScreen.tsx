import React, { useState, useRef } from "react";
import { View, TouchableOpacity, Animated, Text } from "react-native";
import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons";
import PatientDemographicsCard from "../components/PatientDemographicsCard";
import SiteDetailsCard from "../components/SiteDetailsCard";
import HistoryCard from "../components/HistoryCard";
import ConsultVisitCard from "../components/ConsultVisitCard";
import ProcedureDateCard from "../components/ProcedureDateCard";
import PreOpTestingCard from "../components/PreOpTesingCard";
import PreOpImagingCard from "../components/PreOpImagingCard";
import ScreenWrapper from "../../../components/ScreenWrapper";
import LineSeperator from "../../../components/LineSeperator";
import CustomCard from "../../../components/CustomCard";
import { useLoaderAndError as useProcedureDetailsLoaderAndError } from "../hooks/procedureDetailsHooks";
// NEW START
import { fetchPreopDetails } from "../../../store/clinician/ScheduleStack/preop/thunk";
import { useFocusEffect } from "@react-navigation/native";
import { AppDispatch } from "../../../store";
import { useDispatch } from "react-redux";
import {
  useClinicianSelectedPatient,
  useLoadersState,
} from "../hooks/schedulesHooks";
import {
  useHistory,
  useLoaderAndError,
  useUserDetails,
} from "../hooks/preopHooks";
import Loader from "../../../components/Loader";
import Error from "../../../components/Error";
import { fetchImplantingPhysicians } from "../../../store/clinician/ScheduleStack/addpatient/thunk";
import { fetchProcedureDetails } from "../../../store/clinician/ScheduleStack/procedure/thunk";
import BottomSheetComponent, {
  BottomSheetRefProps,
} from "../../../components/BottomSheetComponent";
import MedicationBottomSheetComponent from "../components/MedicationBottomSheetComponent";
import CHADCard from "../../../components/CHADCard";
import {
  putChadScoreDetails,
  putHasbledScoreDetails,
} from "../../../store/clinician/ScheduleStack/schedule/thunk";
import HASBLEDCard from "../../../components/HASBLEDCard";

// NEW END

interface IPreOpScreenProps {}

type MenuOptions =
  | "PatientDemographics"
  // | "SiteDetails"
  | "History"
  // | "ConsultVisit"
  // | "PreOpTesting"
  | "PreOpImaging"
  | "ScheduleProcedure";

interface AnimatedState {
  open: boolean;
  animation: Animated.Value;
}

const PreOpScreen: React.FunctionComponent<IPreOpScreenProps> = (props) => {
  // NEW START

  const dispatch = useDispatch<AppDispatch>();

  const selectedPatient = useClinicianSelectedPatient();

  const bottomRef = useRef<BottomSheetRefProps>(null);
  const medicationBottomSheetRef = useRef(null);

  const chadScoreRef = useRef<BottomSheetRefProps>(null);
  const hasBledRef = useRef<BottomSheetRefProps>(null);
  const { hasbled } = useHistory();

  const [chad, setChad] = useState<any>(
    selectedPatient?.patient?.cha2ds2_vasc?.calculation
  );

  const [medicationKey, setMedicationKey] = useState<number | null>(null);
  const [medicationSelectedId, setMedicationSelectedId] = useState<
    string | null
  >(null);
  const { loader, error } = useLoaderAndError();
  const { loader: schedule_procedure_loader } =
    useProcedureDetailsLoaderAndError();
  const { scheduleLoader } = useLoadersState();
  const handleClose = () => {
    if (medicationBottomSheetRef.current) {
      medicationBottomSheetRef.current.stateCleanUp();
    }
  };
  const handleBottomSheetClose = (ref) => {
    ref.current?.close();
  };
  const handleMedOpen = (id?: string) => {
    setMedicationKey(Date.now());
    if (id) {
      setMedicationSelectedId(id);
    } else {
      setMedicationSelectedId(null);
    }
    bottomRef.current?.open();
  };

  const handleMedClose = () => {
    bottomRef.current?.close();
  };

  useFocusEffect(
    React.useCallback(() => {
      const fetchDetails = async () => {
        if (selectedPatient?.case_id) {
          await dispatch(fetchImplantingPhysicians());
          const res1 = await dispatch(
            fetchPreopDetails({
              case_id: selectedPatient?.case_id,
            })
          );

          const result = await dispatch(
            fetchProcedureDetails({
              case_id: selectedPatient?.case_id,
            })
          );
        }
      };
      fetchDetails();
    }, [
      dispatch,
      selectedPatient?.case_id,
      selectedPatient?.patient?.cha2ds2_vasc?.calculation,
    ])
  );

  const menuItems: Record<MenuOptions, React.ReactNode> = {
    PatientDemographics: <PatientDemographicsCard />,
    // SiteDetails: <SiteDetailsCard />,
    History: (
      <HistoryCard
        onMedOpen={handleMedOpen}
        onMedClose={handleMedClose}
        medicationBottomSheetRef={medicationBottomSheetRef}
        chadBottomSheetRef={chadScoreRef}
        hasBledBottomSheetRef={hasBledRef}
      />
    ),
    // ConsultVisit: <ConsultVisitCard />,
    // PreOpTesting: <PreOpTestingCard />,
    PreOpImaging: <PreOpImagingCard />,
    ScheduleProcedure: <ProcedureDateCard />,
  };

  // NEW END
  const [expandedMenu, setExpandedMenu] = useState<MenuOptions | null>(null);
  const animatedStates = useRef<Record<MenuOptions, AnimatedState>>({
    PatientDemographics: { open: false, animation: new Animated.Value(0) },
    // SiteDetails: { open: false, animation: new Animated.Value(0) },
    History: { open: false, animation: new Animated.Value(0) },
    ConsultVisit: { open: false, animation: new Animated.Value(0) },
    PreOpTesting: { open: false, animation: new Animated.Value(0) },
    PreOpImaging: { open: false, animation: new Animated.Value(0) },
    ScheduleProcedure: { open: false, animation: new Animated.Value(0) },
  }).current;

  const toggleMenu = (menu: MenuOptions) => {
    // Collapse all other menus
    if (expandedMenu && expandedMenu !== menu) {
      Animated.timing(animatedStates[expandedMenu].animation, {
        toValue: 0,
        duration: 400,
        useNativeDriver: true,
      }).start();
    }

    const animation = animatedStates[menu].animation;
    const isOpen = expandedMenu === menu;

    Animated.timing(animation, {
      toValue: isOpen ? 0 : 1,
      duration: 400,
      useNativeDriver: true,
    }).start();

    setExpandedMenu(isOpen ? null : menu);
  };

  const saveChadScoreDetails = async (updatedValues: any) => {
    handleBottomSheetClose(chadScoreRef);
    try {
      const res = await dispatch(
        putChadScoreDetails({
          case_id: selectedPatient?.case_id,
          payload: updatedValues,
        })
      );
      const result = await dispatch(
        fetchPreopDetails({
          case_id: selectedPatient?.case_id,
        })
      );

      setChad(result.payload.patient.cha2ds2_vasc.calculation);
    } catch (error) {
      console.error("Error updating CHAD Score:", error);
    }
  };

  const saveHasbledDetails = async (updatedValues: any) => {
    handleBottomSheetClose(hasBledRef);
    try {
      const res = await dispatch(
        putHasbledScoreDetails({
          case_id: selectedPatient?.case_id,
          payload: updatedValues,
        })
      );
      const result = await dispatch(
        fetchPreopDetails({
          case_id: selectedPatient?.case_id,
        })
      );
    } catch (error) {
      console.error("Error updating CHAD Score:", error);
    }
  };

  const handleRefresh = async () => {
    await dispatch(fetchImplantingPhysicians());

    if (!selectedPatient?.case_id) return;
    const refetchRes = await dispatch(
      fetchPreopDetails({
        case_id: selectedPatient?.case_id,
      })
    );
    await dispatch(
      fetchProcedureDetails({
        case_id: selectedPatient?.case_id,
      })
    );

    setChad(refetchRes.payload.patient.cha2ds2_vasc.calculation);
  };

  if (loader || schedule_procedure_loader) {
    return <Loader />;
  }

  return (
    <>
      <ScreenWrapper direction="column" onRefresh={handleRefresh}>
        {scheduleLoader ? (
          <View className="mt-64 flex-1 justify-center items-center">
            <Loader />
          </View>
        ) : (
          <CustomCard>
            <View className=" py-2">
              {Object.keys(menuItems).map((itemKey, index) => {
                const key = itemKey as MenuOptions;
                return (
                  <View key={index} className="">
                    <TouchableOpacity
                      onPress={() => toggleMenu(key)}
                      className=" flex-row justify-between items-center py-3 px-3"
                    >
                      <Text className="text-lg font-medium text-primaryBlack">
                        {key.replace(/([A-Z])/g, " $1").trim()}
                      </Text>
                      <Animated.View
                        style={{
                          transform: [
                            {
                              rotate: animatedStates[
                                key
                              ]?.animation.interpolate({
                                inputRange: [0, 1],
                                outputRange: ["0deg", "180deg"],
                              }),
                            },
                          ],
                        }}
                      >
                        <MaterialCommunityIcons
                          name="arrow-down-drop-circle-outline"
                          color="#8143d9"
                          size={25}
                        />
                      </Animated.View>
                    </TouchableOpacity>

                    {expandedMenu === key && (
                      <View style={{ overflow: "hidden" }}>
                        <Animated.View
                          style={{
                            transform: [
                              {
                                translateY: animatedStates[
                                  key
                                ]?.animation.interpolate({
                                  inputRange: [0, 1],
                                  outputRange: [-200, 0],
                                }),
                              },
                            ],
                            opacity: animatedStates[key]?.animation.interpolate(
                              {
                                inputRange: [0, 1],
                                outputRange: [0, 1],
                              }
                            ),

                            overflow: "hidden",
                          }}
                        >
                          {menuItems[key]}
                        </Animated.View>
                      </View>
                    )}

                    {Object.keys(menuItems).length - 1 !==
                    Object.keys(menuItems).indexOf(key) ? (
                      <LineSeperator extraStyle="my-3" />
                    ) : null}
                  </View>
                );
              })}
            </View>
          </CustomCard>
        )}
        <View className="mt-9"></View>
      </ScreenWrapper>
      <BottomSheetComponent
        ref={bottomRef}
        snapPoints={["75%", "85%"]}
        backgroundColor="white"
        onClose={() => handleClose()}
      >
        {
          <View className="flex-1">
            <MedicationBottomSheetComponent
              ref={medicationBottomSheetRef}
              medicineId={medicationSelectedId || ""}
              bottomSheetClose={handleMedClose}
              medKey={medicationKey}
              isPreOp={true}
            />
          </View>
        }
      </BottomSheetComponent>
      <BottomSheetComponent
        ref={chadScoreRef}
        snapPoints={["75%", "85%"]}
        backgroundColor="white"
        onClose={() => handleBottomSheetClose(chadScoreRef)}
      >
        <CHADCard chad={chad} onSave={saveChadScoreDetails} />
      </BottomSheetComponent>
      <BottomSheetComponent
        ref={hasBledRef}
        snapPoints={["75%", "85%"]}
        backgroundColor="white"
        onClose={handleBottomSheetClose(hasBledRef)}
      >
        {hasbled && (
          <HASBLEDCard hasbled={hasbled} onSave={saveHasbledDetails} />
        )}
      </BottomSheetComponent>
    </>
  );
};

export default PreOpScreen;
