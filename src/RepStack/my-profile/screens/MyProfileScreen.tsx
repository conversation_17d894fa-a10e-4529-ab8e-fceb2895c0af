import React from "react";
import {
  Text,
  TouchableOpacity,
  View,
  ActivityIndicator,
  Image,
} from "react-native";
import { useDispatch } from "react-redux";
import { logout } from "../../../store/auth";
import { AppDispatch } from "../../../store";
import { getAppVersion } from "../../../utils/appVersion";
import globalStyles from "../../../styles/GlobalStyles";
import profile from "../../../../assests/profile.png";
import CustomText from "../../../components/CustomText";
import {
  formatProfileDetails,
  useProfileDetails,
  useLoaderAndError,
} from "../hooks/profileHooks";
import { useFocusEffect } from "@react-navigation/native";
import { fetchProfile } from "../../../store/rep/MyProfileStack/MyProfileScreen/thunk";
import Loader from "../../../components/Loader";
import Error from "../../../components/Error";

interface IProfileProps {}

const MyProfileScreen: React.FunctionComponent<IProfileProps> = (props) => {
  const dispatch = useDispatch<AppDispatch>();
  const userDetails = useProfileDetails();

  const { user_id, name, role, email_id, site_id, site_name } =
    formatProfileDetails(userDetails);
  const { error, loader } = useLoaderAndError();

  useFocusEffect(
    React.useCallback(() => {
      const fetchDetails = async () => {
        const res = await dispatch(fetchProfile());
      };
      fetchDetails();
    }, [dispatch])
  );

  if (loader) {
    return <Loader />;
  }

  // if (error) {
  //   return <Error message={error} />;
  // }

  return (
    <View className="flex-1 bg-primaryBg items-center gap-4 p-4">
      <View className={`${globalStyles.containers.flex_center} p-6`}>
        <Image
          source={profile}
          className="w-44 h-44 rounded-full mr-5 border-4 border-[#8143d9] shadow-lg"
        />

        <View>
          <>
            <View className="mt-4 flex-row justify-start items-center">
              <CustomText value="Name:" className="font-bold text-md w-20" />
              <CustomText value={name} className="text-left text-md" />
            </View>

            <View className="mt-4 flex-row items-center">
              <CustomText value="Email:" className="font-bold text-md w-20" />
              <CustomText value={email_id} className="text-left text-md" />
            </View>

            <View className="mt-4 flex-row items-center">
              <CustomText value="User ID:" className="font-bold text-md w-20" />
              <CustomText value={user_id} className="text-left text-md" />
            </View>

            <View className="mt-4 flex-row items-center">
              <CustomText value="Role:" className="font-bold text-md w-20" />
              <CustomText value={role} className="text-left text-md" />
            </View>
          </>
        </View>
      </View>

      <TouchableOpacity
        className="px-4 py-2 w-[150px] bg-primaryPurple rounded-lg mt-6"
        onPress={() => dispatch(logout())}
      >
        <Text className="text-lg text-center text-primaryWhite font-bold">
          Logout
        </Text>
      </TouchableOpacity>

      <CustomText value={`App Version: ${getAppVersion()}`} />
    </View>
  );
};

export default MyProfileScreen;
