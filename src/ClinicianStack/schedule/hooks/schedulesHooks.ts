import { useSelector } from "react-redux";
import { RootState } from "../../../store";

const useClinicianSchedules = () => {
  const { schedules } = useSelector(
    (state: RootState) => state.clinician.schedules
  );
  return schedules;
};

const useLoadersState = () => {
  const scheduleLoader = useSelector(
    (state: RootState) => state.clinician.schedules.loaders.scheduleLoader
  );
  return { scheduleLoader };
};

const useClinicianSelectedPatient = () => {
  const { selectedPatient } = useSelector(
    (state: RootState) => state.clinician.schedules
  );
  return selectedPatient;
};



export {
  useClinicianSchedules,
  useLoadersState,
  useClinicianSelectedPatient,
};
