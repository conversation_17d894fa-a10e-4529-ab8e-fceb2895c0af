import * as React from "react";
import PatientTopBarStack from "../components/PatientTopBarStack";
import { useNavigation } from "@react-navigation/native";
import { resetTransseptalState } from "../../../store/rep/ScheduleStack/laaoProcedures/transseptalpuncture";
import { useDispatch } from "react-redux";
import { AppDispatch } from "../../../store";

interface IPatientDetailsScreenProps {}

const PatientDetailsScreen: React.FunctionComponent<
  IPatientDetailsScreenProps
> = (props) => {
  const dispatch = useDispatch<AppDispatch>();
  const navigation = useNavigation();

  // React.useEffect(() => {
  //   const unsubscribe = navigation.addListener("beforeRemove", () => {

  //     dispatch(resetTransseptalState());
  //   });

  //   return unsubscribe;
  // }, []);

  return <PatientTopBarStack />;
};

export default PatientDetailsScreen;
