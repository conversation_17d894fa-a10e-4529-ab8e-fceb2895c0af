import { createSlice } from "@reduxjs/toolkit";
import { fetchPostOpDetails, putPostOpDetails } from "./thunk";
import initialState from "./initialstate";
import { genUUID } from "../../../../utils";

const postOpSlice = createSlice({
  name: "postOp",
  initialState,
  reducers: {
    setPostOpDetails: (state, action) => {
      state.userDetails = {
        ...state.userDetails,
        ...action.payload,
      };
    },

    deleteMedicationById: (state, action) => {
      const medicineId = action.payload;
      state.medication = state.medication.filter(
        (med) => med.id !== medicineId
      );
    },
    addMedicationById: (state, action) => {
      const medicineId = action.payload;
      state.medication.push(medicineId);
    },
    updateMedicationById: (state, action) => {
      const { id, updatedMed } = action.payload;
      const index = state.medication.findIndex((med) => med.id === id);
      if (index !== -1) {
        state.medication[index] = {
          ...state.medication[index],
          ...updatedMed,
        };
      }
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchPostOpDetails.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchPostOpDetails.fulfilled, (state, action) => {
        state.loading = false;
        state.postOpDetails = action.payload;
        state.userDetails = action.payload;

        // Define the correct path consistently
        const anticoagulationPath =
          action.payload.patient?.anticoagulation ||
          action.payload.anticoagulation;

        // Check if anticoagulation data exists and is valid before mapping
        const selectedMeds = anticoagulationPath?.selected || [];

        // Filter out invalid medication objects
        const validMeds = selectedMeds.filter(
          (med) =>
            med &&
            med.id &&
            med.name &&
            med.dosing_frequency &&
            med.dosing_frequency.name &&
            med.period?.duration
        );

        // Map only valid medications
        state.medication = validMeds.map((med) => ({
          id: genUUID(),
          med_id: med.id,
          med_name: med.name,
          med_dose: med.quantity || "",
          dosing_frequency: med.dosing_frequency.name,
          dosing_frequency_id: med.dosing_frequency.id,
          period: med.period.duration.name,
          period_id: med.period.duration.id,
          period_count: med.period.count,
        }));

        state.apiMedication = [...state.medication];
      })

      .addCase(fetchPostOpDetails.rejected, (state, action) => {
        state.loading = false;
        state.postOpDetails = null;
        state.error = action.payload as string;
      });

    builder
      .addCase(putPostOpDetails.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(putPostOpDetails.fulfilled, (state, action) => {
        state.loading = false;
        state.postOpDetails = action.payload;
        state.userDetails = action.payload;
      })
      .addCase(putPostOpDetails.rejected, (state, action) => {
        state.loading = false;
        state.postOpDetails = null;
        state.error = action.payload as string;
      });
  },
});

export const {
  setPostOpDetails,
  deleteMedicationById,
  addMedicationById,
  updateMedicationById,
} = postOpSlice.actions;
export default postOpSlice.reducer;
