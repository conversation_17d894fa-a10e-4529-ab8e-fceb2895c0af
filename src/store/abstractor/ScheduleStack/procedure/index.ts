import { createSlice } from "@reduxjs/toolkit";
import initialState from "./initialstate"; 
import { fetchProcedureDetails, putProcedureDetails } from "./thunk";

const procedureDetailsSlice = createSlice({
  name: "procedureDetails",
  initialState,
  reducers: {
    setProcedureDetailsUserDetails: (state, action) => {
      state.userDetails = {
        ...state.userDetails,
        ...action.payload,
      };
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchProcedureDetails.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchProcedureDetails.fulfilled, (state, action) => {
        state.loading = false;
        state.procedureDetails = action.payload;
        state.userDetails = action.payload; 
      })
      .addCase(fetchProcedureDetails.rejected, (state, action) => {
        state.loading = false;
        state.procedureDetails = null;
        state.error = action.payload as string;
      });

    builder
      .addCase(putProcedureDetails.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(putProcedureDetails.fulfilled, (state, action) => {
        state.loading = false;
        state.procedureDetails = action.payload;
        state.userDetails = action.payload;  
      })
      .addCase(putProcedureDetails.rejected, (state, action) => {
        state.loading = false;
        state.procedureDetails = null;
        state.error = action.payload as string;
      });
  },
});

export const { setProcedureDetailsUserDetails } = procedureDetailsSlice.actions;
export default procedureDetailsSlice.reducer;
