// thunk.ts
import { createAsyncThunk } from "@reduxjs/toolkit";
import api from "../../../../../api/api";
import { IProcedureDetails } from "./types";

const fetchAfibAblationProedure = createAsyncThunk(
  "tasks/fetchAfibAblationProedure",
  async ({ case_id }: { case_id: string }, thunkAPI) => {
    try {
      const response = await api.private.get(
        api.routes.privateRoutes.rep.afib_procedure_get(case_id)
      );

      if (response.data && response.data.result) {
        return response.data.result;
      } else {
        return thunkAPI.rejectWithValue(
          'Invalid response format: Missing "result" field'
        );
      }
    } catch (error: any) {
      if (error.response) {
        if (error.response.status === 404) {
        }
        return thunkAPI.rejectWithValue(
          error.response.data.error_description ||
            "Failed to fetch Afib procedure details"
        );
      } else if (error.request) {
        console.error("No response received:", error.request);
        return thunkAPI.rejectWithValue("No response received from the server");
      } else {
        console.error("Error occurred:", error.message);
        return thunkAPI.rejectWithValue(
          error.message || "An unknown error occurred"
        );
      }
    }
  }
);

const putAfibAblationProcedure = createAsyncThunk(
  "tasks/putAfibAblationProcedure",
  async ({ case_id, payload }: { case_id: string; payload: any }, thunkAPI) => {
    try {
      const response = await api.private.put(
        api.routes.privateRoutes.rep.afib_procedure_put(case_id),
        payload
      );

      if (response.data && response.data.result) {
        return response.data.result;
      } else {
        return thunkAPI.rejectWithValue(
          'Invalid response format: Missing "result" field'
        );
      }
    } catch (error: any) {
      console.error("Error caught during API call:", error.response.data);
      if (error.response) {
        if (error.response.status === 404) {
        }
        return thunkAPI.rejectWithValue("Failed to PUT Afib procedure details");
      } else if (error.request) {
        console.error("No response received:", error.request);
        return thunkAPI.rejectWithValue("No response received from the server");
      } else {
        console.error("Error occurred:", error.message);
        return thunkAPI.rejectWithValue(
          error.message || "An unknown error occurred"
        );
      }
    }
  }
);

export { fetchAfibAblationProedure, putAfibAblationProcedure };
