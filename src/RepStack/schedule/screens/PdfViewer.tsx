import * as React from "react";
import { Text, View } from "react-native";
import Pdf from "react-native-pdf";
import Loader from "../../../components/Loader";

const PDFViewer: React.FunctionComponent = ({ route }) => {
  const [value, setValue] = React.useState(0);
  return (
    <View className="flex-1 bg-primaryBlack">
      <Pdf
        source={{
          uri: route.params?.url,
        }}
        style={{
          flex: 1,
        }}
        renderActivityIndicator={() => (
          <>
            <Text className="text-primaryWhite">loading {value} %</Text>
          </>
        )}
        trustAllCerts={false}
        onLoadProgress={(progress) => {
          setValue(Math.round(progress * 100));
        }}
      />
    </View>
  );
};

export default PDFViewer;
