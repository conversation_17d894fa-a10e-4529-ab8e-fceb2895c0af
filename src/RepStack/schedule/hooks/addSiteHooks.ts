import { useSelector } from "react-redux";
import { RootState } from "../../../store";
import { Site } from "../../../store/rep/ScheduleStack/addSite/types";

const useLoaderAndError = () => {
  const loader = useSelector(
    (state: RootState) => state.rep.settings.sites.loading
  );

  const error = useSelector(
    (state: RootState) => state.rep.settings.sites.error
  );
  return { loader, error };
};
const useSitesDetails = () => {
  const { siteDetails } = useSelector(
    (state: RootState) => state.rep.settings.sites
  );
  return siteDetails;
};

const useUserSitesDetails = () => {
  const { userDetails } = useSelector(
    (state: RootState) => state.rep.settings.sites
  );
  return userDetails;
};

const useActiveTab = (): "sites" | "implanting-physicians" => {
  return useSelector((state: RootState) => state.rep.settings.sites.activeTab);
};

const useAddSiteModalState = (): boolean => {
  return useSelector(
    (state: RootState) => state.rep.settings.sites.showAddSiteModal
  );
};

const useSelectedSite = (): Site => {
  return useSelector(
    (state: RootState) => state.rep.settings.sites.selectedSite
  );
};

const formatPostSite = (data: Site) => {
  const site_name = data?.name || "";
  const site_address = data?.address || "";
  const site_city = data?.city || "";
  const site_state = data?.state || "";
  const site_zipcode = data?.zip_code || "";
  const site_account_id = data?.account || "";
  return {
    site_name,
    site_account_id,
    site_address,
    site_city,
    site_state,
    site_zipcode,
  };
};

const findPostSiteDiff = () => {
  const userDetails = useUserSitesDetails();
  const siteDetails = useSitesDetails();

  const diff = JSON.stringify(userDetails) !== JSON.stringify(siteDetails);

  const currentValues = {
    name: userDetails?.name || null,
    address: userDetails?.address || "",
    city: userDetails?.city || "",
    state: userDetails?.state || "",
    zip_code: userDetails?.zip_code || "",
    account_id: userDetails?.account || "",
  };

  return {
    hasDiff: diff,
    currentValues: currentValues,
  };
};

export {
  useActiveTab,
  useLoaderAndError,
  useAddSiteModalState,
  useSitesDetails,
  useUserSitesDetails,
  useSelectedSite,
  formatPostSite,
  findPostSiteDiff,
};
