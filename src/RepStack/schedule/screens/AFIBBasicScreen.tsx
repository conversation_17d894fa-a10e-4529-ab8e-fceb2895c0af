import * as React from "react";
import Heading from "../../../components/Heading";
import LineSeperator from "../../../components/LineSeperator";
import ScreenWrapper from "../../../components/ScreenWrapper";
import { TouchableOpacity, View } from "react-native";
import CustomCard from "../../../components/CustomCard";
import CustomButton from "../../../components/CustomButton";
import DatePicker from "react-native-date-picker";
import CustomText from "../../../components/CustomText";
import moment from "moment";
import SaveActionButton from "../../../components/SaveActionButton";
import {
  useAfibBasicUserDetails,
  useAfibBAsicDetails,
  useLoaderAndError,
  findAfibBasicDiff,
} from "../hooks/afibAblationBasicHooks";
import Loader from "../../../components/Loader";
import Error from "../../../components/Error";
import { useDispatch } from "react-redux";
import { AppDispatch } from "../../../store";
import {
  fetchRepAfibAblationBasic,
  putAfibAblationBasic,
} from "../../../store/rep/ScheduleStack/afibAblation/basic/thunk";
import { useFocusEffect, useNavigation } from "@react-navigation/native";
import { useSelectedPatient } from "../hooks/PatientsHooks";
import SearchablePicker from "../../../components/SearchablePicker";
import { formatAfibBasicData } from "../../../utils";
import { setAfibBAsicUserDetails } from "../../../store/rep/ScheduleStack/afibAblation/basic";
import PopupModal from "../../../components/Popup";

interface ITransseptalPunctureScreenProps {}

const TransseptalPunctureScreen: React.FunctionComponent<
  ITransseptalPunctureScreenProps
> = (props) => {
  // NEW START

  const dispatch = useDispatch<AppDispatch>();
  const navigation = useNavigation();
  const selectedPatient = useSelectedPatient();
  const userDetails = useAfibBasicUserDetails();
  const basic = useAfibBAsicDetails();

  const [modalVisible, setModalVisible] = React.useState(false);

  const [popupMsg, setPopupMsg] = React.useState<string[]>([]);

  const validator = () => {
    setPopupMsg([]);

    var temp = false;

    if (
      procedure_date === null ||
      procedure_date === undefined ||
      procedure_date === ""
    ) {
      setPopupMsg((prev) => [...prev, "Please Select Procedure Date"]);
      temp = true;
    }

    if (
      physicianSelected === null ||
      physicianSelected === undefined ||
      physicianSelected === ""
    ) {
      setPopupMsg((prev) => [...prev, "Please Select Physician"]);
      temp = true;
    }

    if (
      groin_access_start_time === null ||
      groin_access_start_time === undefined ||
      groin_access_start_time === ""
    ) {
      setPopupMsg((prev) => [...prev, "Please Select Groin Access Start Time"]);
      temp = true;
    }

    if (
      sheath_removal_end_time === null ||
      sheath_removal_end_time === undefined ||
      sheath_removal_end_time === ""
    ) {
      setPopupMsg((prev) => [...prev, "Please Select Sheath Removal End Time"]);
      temp = true;
    }

    if (temp) {
      setModalVisible(true);
      return false;
    } else {
      return true;
    }
  };

  useFocusEffect(
    React.useCallback(() => {
      const fetchDetails = async () => {
        if (selectedPatient?.case_id) {
          const res = await dispatch(
            fetchRepAfibAblationBasic({
              case_id: selectedPatient?.case_id,
            })
          );
        }
      };
      fetchDetails();
    }, [dispatch, selectedPatient?.case_id])
  );

  const saveDetails = async () => {
    try {
      if (validator()) {
        const res = await dispatch(
          putAfibAblationBasic({
            case_id: selectedPatient?.case_id,
            payload: diff.currentValues,
          })
        );
        if (res.payload) {
          if (selectedPatient?.case_id) {
            const refetchRes = await dispatch(
              fetchRepAfibAblationBasic({
                case_id: selectedPatient.case_id,
              })
            );
          }
        }
      }
    } catch (err) {
      console.error(err);
    }
  };

  const handleCancel = () => {
    dispatch(
      setAfibBAsicUserDetails({
        ...userDetails,
        ...basic,
      })
    );
  };

  const { error, loader } = useLoaderAndError();

  const {
    groin_access_start_time,
    procedure_date,
    physicianOptions,
    physicianSelected,
    sheath_removal_end_time,
    hospitalSelected,
    hospitalOptions,
    study,
  } = formatAfibBasicData(userDetails);

  const diff = findAfibBasicDiff();

  // NEW END
  const getDate45DaysFromNow = () => {
    const currentDate = new Date();
    currentDate.setDate(currentDate.getDate() + 45);
    return currentDate;
  };

  const [dateModal, setDateModal] = React.useState(false);
  const [startTimeModal, setStartTimeModal] = React.useState(false);
  const [endTimeModal, setEndTimeModal] = React.useState(false);

  const handleRefresh = async () => {
    if (!selectedPatient?.case_id) return;
    await dispatch(
      fetchRepAfibAblationBasic({
        case_id: selectedPatient?.case_id,
      })
    );
  };
  if (loader) {
    return <Loader />;
  }

  if (error) {
    return <Error message={error} />;
  }

  return (
    <ScreenWrapper direction="column" onRefresh={handleRefresh}>
      <CustomCard extraStyle="mb-9">
        <SaveActionButton
          disabled={!diff.hasDiff}
          onPress={saveDetails}
          onCancel={handleCancel}
        />

        <Heading
          text="Date"
          size="sub-heading"
          color={"black"}
          extraStyle="pb-3"
          showSeperator={false}
        />

        <View className="flex-1">
          <TouchableOpacity
            onPress={() => {
              setDateModal(true);
            }}
            className="border border-primaryPurple p-4 rounded-lg"
          >
            <CustomText
              value={
                procedure_date
                  ? moment(procedure_date).format("MM-DD-YYYY")
                  : "Select date"
              }
              className="text-primaryBlack text-md"
            />
          </TouchableOpacity>
        </View>

        <LineSeperator extraStyle="mt-5 mb-3" />

        <View>
          <Heading
            text="Electrophysiologist"
            size="sub-heading"
            color={"black"}
            extraStyle="pb-3"
            showSeperator={false}
          />
          <SearchablePicker
            placeholder="Select MD"
            value={physicianSelected || ""}
            items={physicianOptions || []}
            onValueChange={(option) =>
              dispatch(
                setAfibBAsicUserDetails({
                  physician_details: {
                    ...userDetails.physician_details,
                    selected: {
                      id: option.value,
                      name: option.label,
                    },
                  },
                })
              )
            }
          />
        </View>
        <LineSeperator extraStyle="mt-5 mb-3" />

        {/* <View>
          <Heading
            text="Hospital"
            size="sub-heading"
            color={"black"}
            extraStyle="pb-3"
            showSeperator={false}
          />
          <SearchablePicker
            placeholder="Select Hospital"
            value={hospitalSelected || ""}
            items={hospitalOptions || []}
            onValueChange={(option) =>
              dispatch(
                setAfibBAsicUserDetails({
                  site_details: {
                    ...userDetails.site_details,
                    selected: {
                      id: option.value,
                      name: option.label,
                    },
                  },
                })
              )
            }
          />
        </View>
        <LineSeperator extraStyle="my-5" /> */}

        {study &&
          study.map((item: any, index: number) => {
            return (
              <>
                <CustomButton
                  className="bg-secondaryPurple border-none"
                  color="white"
                  value="CTA Images"
                  onPress={() => {
                    navigation.navigate("WebViewer", {
                      link: item.viewer_link,
                    });
                  }}
                />
                <LineSeperator extraStyle="mt-5 mb-3" />
              </>
            );
          })}

        <View>
          <Heading
            text="Start Time (groin access)"
            size="sub-heading"
            color={"black"}
            extraStyle="pb-3"
            showSeperator={false}
          />

          <TouchableOpacity
            onPress={() => {
              setStartTimeModal(true);
            }}
            className="border border-primaryPurple p-4 rounded-lg"
          >
            <CustomText
              value={
                groin_access_start_time
                  ? moment(groin_access_start_time, "HH:mm:ss").format("HH:mm")
                  : "Select Start Time"
              }
              className="text-primaryBlack text-md"
            />
          </TouchableOpacity>
        </View>

        <LineSeperator extraStyle="mt-5 mb-3" />

        <View className="mb-3">
          <Heading
            text="End Time (sheath removal)"
            size="sub-heading"
            color={"black"}
            extraStyle="pb-3"
            showSeperator={false}
          />
          <TouchableOpacity
            onPress={() => {
              setEndTimeModal(true);
            }}
            className="border border-primaryPurple p-4 rounded-lg"
          >
            <CustomText
              value={
                sheath_removal_end_time
                  ? moment(sheath_removal_end_time, "HH:mm:ss").format("HH:mm")
                  : "Select End Time"
              }
              className="text-primaryBlack text-md"
            />
          </TouchableOpacity>
        </View>
      </CustomCard>

      {/* DATE MODAL */}
      <DatePicker
        modal
        open={dateModal}
        date={
          procedure_date
            ? moment(procedure_date, "YYYY-MM-DD").toDate()
            : new Date()
        }
        onConfirm={(date) => {
          setDateModal(false);
          dispatch(
            setAfibBAsicUserDetails({
              procedure_date: moment(date).format("YYYY-MM-DD"),
            })
          );
        }}
        onCancel={() => {
          setDateModal(false);
        }}
        mode={"date"}
      />

      {/* START TIME */}
      <DatePicker
        modal
        open={startTimeModal}
        date={
          groin_access_start_time
            ? moment(groin_access_start_time, "HH:mm:ss").toDate()
            : new Date()
        }
        onConfirm={(date) => {
          setStartTimeModal(false);
          dispatch(
            setAfibBAsicUserDetails({
              groin_access_start_time: moment(date).format("HH:mm:ss"),
            })
          );
        }}
        onCancel={() => {
          setStartTimeModal(false);
        }}
        mode={"time"}
        maximumDate={
          sheath_removal_end_time
            ? moment(sheath_removal_end_time, "HH:mm:ss")
                .subtract(30, "minute")
                .toDate()
            : undefined
        }
      />

      {/* END TIME */}
      <DatePicker
        modal
        open={endTimeModal}
        date={
          sheath_removal_end_time
            ? moment(sheath_removal_end_time, "HH:mm:ss").toDate()
            : new Date()
        }
        onConfirm={(date) => {
          setEndTimeModal(false);
          dispatch(
            setAfibBAsicUserDetails({
              sheath_removal_end_time: moment(date).format("HH:mm:ss"),
            })
          );
        }}
        onCancel={() => {
          setEndTimeModal(false);
        }}
        mode={"time"}
        minimumDate={
          groin_access_start_time
            ? moment(groin_access_start_time, "HH:mm:ss")
                .add(30, "minute")
                .toDate()
            : undefined
        }
      />
      <PopupModal
        show={modalVisible}
        msg={popupMsg}
        status="warning"
        onClose={() => setModalVisible(false)}
      />
    </ScreenWrapper>
  );
};

export default TransseptalPunctureScreen;
