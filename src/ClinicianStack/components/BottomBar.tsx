import React from "react";
import { TouchableOpacity, View, ViewStyle } from "react-native";
import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";
import { BottomTabNavigationOptions } from "@react-navigation/bottom-tabs";
import TasksScreenStack from "../tasks";
import MyProfileScreenStack from "../my-profile";
import { MatIcon } from "../../utils";
import ChatBot from "../../components/ChatBot";
import ScheduleScreenStack from "../schedule";
import { useDispatch } from "react-redux";
import { AppDispatch } from "../../store";
import { setChatBotState } from "../../store/chatbot";
import { useChatBotState } from "./hooks";
// Screen Stack
import CoordinatorHomeStack from "../home";
const Tab = createBottomTabNavigator();

type BottomBarProps = {
  initialRoute?: string;
};

const BottomBar: React.FC<BottomBarProps> = ({
  initialRoute = "ScheduleTab",
}) => {
  const { chatBotIsOpened, chatBotScreenFocused, screenPath } =
    useChatBotState();

  const dispatch = useDispatch<AppDispatch>();

  const handleChatBotToggle = () => {
    // Toggle the chatbot state immediately
    dispatch(
      setChatBotState({
        screenPath: screenPath,
        currentScreen: chatBotScreenFocused,
        isOpened: !chatBotIsOpened,
      })
    );
  };
  return (
    <>
      <Tab.Navigator
        initialRouteName={initialRoute}
        screenOptions={{
          tabBarStyle: { backgroundColor: "#8143D9" },
          tabBarActiveTintColor: "#FFFFFF",
          tabBarInactiveTintColor: "#D2BEFE",
          headerShown: false,
          //   tabBarPosition: 'bottom',
        }}
      >
        <Tab.Screen
          name="DashboardTab"
          component={CoordinatorHomeStack}
          options={
            {
              tabBarLabel: "Dashboard",
              tabBarIcon: ({ color, size }) =>
                MatIcon("view-dashboard", color, size),
            } as BottomTabNavigationOptions
          }
        />
        {/* < <Tab.Screen
          name="TasksTab"
          component={TasksScreenStack}
          options={
            {
              tabBarLabel: "Tasks",
              tabBarIcon: ({ color, size }) =>
                MatIcon("clipboard-check-outline", color, size),
            } as BottomTabNavigationOptions
          }
        /> */}
        <Tab.Screen
          name="ScheduleTab"
          component={ScheduleScreenStack}
          options={
            {
              tabBarLabel: "Schedule",
              tabBarIcon: ({ color, size }) =>
                MatIcon("account-group", color, size),
            } as BottomTabNavigationOptions
          }
        />
        <Tab.Screen
          name="MyProfileTab"
          component={MyProfileScreenStack}
          options={
            {
              tabBarLabel: "My Profile",
              tabBarIcon: ({ color, size }) => MatIcon("account", color, size),
            } as BottomTabNavigationOptions
          }
        />
        <Tab.Screen
          name="Ask CM"
          options={{
            tabBarIcon: ({ color, size }) => MatIcon("robot", color, size),
            tabBarButton: (props) => (
              <TouchableOpacity
                {...props}
                onPress={handleChatBotToggle}
              ></TouchableOpacity>
            ),
          }}
        >
          {() => null}
        </Tab.Screen>
      </Tab.Navigator>
      {chatBotIsOpened ? <ChatBot /> : null}
    </>
  );
};

export default BottomBar;
