import { useSelector } from "react-redux";
import { RootState } from "../../../store";

/**
 * Function to generate a schedule with consolidated data per date in the requested format.
 *
 * @param {string} startDate - The start date of the range (YYYY-MM-DD).
 * @param {string} endDate - The end date of the range (YYYY-MM-DD).
 * @param {Array} inputData - An array of schedule data.
 * @returns {Array} - A structured array of objects with 'title' and 'data' for each date.
 */

const useSiteList = () => {
  const siteList = useSelector((state: RootState) => state.schedules.siteList);
  return siteList;
};

const useSelectedDate = () => {
  const selectedDate = useSelector(
    (state: RootState) => state.schedules.selectedDate
  );
  return selectedDate;
};

const useSchedules = () => {
  const schedules = useSelector(
    (state: RootState) => state.schedules.schedules
  );
  return schedules;
};

const useLoadersState = () => {
  const loaders = useSelector((state: RootState) => state.schedules.loaders);
  return loaders;
};

const usePatientsList = () => {
  const patientList = useSelector(
    (state: RootState) => state.schedules.patientList
  );
  return patientList;
};

const useAddCaseModal = () => {
  const addCaseModal = useSelector(
    (state: RootState) => state.schedules.showAddCaseModal
  );
  return addCaseModal;
};

const useAddPatientModal = () => {
  const addPatientModal = useSelector(
    (state: RootState) => state.schedules.showAddPatientModal
  );
  return addPatientModal;
};

const generateSchedule = (startDate, endDate, inputData) => {
  const convertTo12Hour = (time24) => {
    const [hours, minutes] = time24.split(":");
    const hour = parseInt(hours, 10);
    const period = hour >= 12 ? "PM" : "AM";
    const hour12 = hour % 12 || 12;
    return `${hour12}:${minutes} ${period}`;
  };

  const getDatesInRange = (startDate, endDate) => {
    const dateArray = [];
    let currentDate = new Date(startDate);

    while (currentDate <= new Date(endDate)) {
      dateArray.push(currentDate.toISOString().split("T")[0]);
      currentDate.setDate(currentDate.getDate() + 1);
    }

    return dateArray;
  };

  const dateRange = getDatesInRange(startDate, endDate);
  const result = [];

  // Initialize result with all dates in the range
  dateRange.forEach((date) => {
    const hospitalsData = {};

    // Process input data
    inputData.forEach((item, index) => {
      const itemDate = item.procedure_date;
      if (itemDate === date) {
        const hospitalName = item.site.name;
        const implantingMD = item.implanting_physician.name;
        const startTime12 = convertTo12Hour(item.start_time);

        // Assign hospital data dynamically to hospital1, hospital2, etc.
        hospitalsData[`hospital${index + 1}`] = {
          date: date, // Include the date in each hospital's data
          hospitalName,
          noOfCases: item.cases,
          startTime: startTime12,
          implantingMD,
          siteId: item.site.id,
          implantingPhysicianImage: item.implanting_physician.image_url,
          hospitalImage: item.site.image_url,
        };
      }
    });

    if (Object.keys(hospitalsData).length > 0) {
      result.push({
        title: date,
        data: [hospitalsData], // Group hospitals data under a single object
      });
    } else {
      // If no data for the day, add an empty indicator
      result.push({
        title: date,
        data: [{ isEmpty: true, date: date }],
      });
    }
  });

  return result;
};

export {
  useSiteList,
  useSelectedDate,
  useSchedules,
  useLoadersState,
  usePatientsList,
  useAddCaseModal,
  useAddPatientModal,
  generateSchedule,
};
