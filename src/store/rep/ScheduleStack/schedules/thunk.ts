import { createSlice, PayloadAction, createAsyncThunk } from "@reduxjs/toolkit";
import api from "../../../../api/api";
import { IfetchPatientsListParams, IfetchSchedulesParams } from "./types";

const fetchSchedules = createAsyncThunk(
  "schedules/fetchSchedules",
  async ({ from_date, to_date }: IfetchSchedulesParams, thunkAPI) => {
    try {
      const response = await api.private.get(
        api.routes.privateRoutes.rep.schedule,
        {
          params: {
            start_date: from_date,
            end_date: to_date,
          },
        }
      );

      if (response.data && response.data.result) {
        return response.data.result;
      } else {
        return thunkAPI.rejectWithValue(
          'Invalid response format: Missing "result" field'
        );
      }
    } catch (error) {
      console.error("Error caught during API call:", error);
      return thunkAPI.rejectWithValue("Failed to fetch schedule List");
    }
  }
);

const fetchSiteList = createAsyncThunk(
  "schedule/fetchSiteList",
  async (_, thunkAPI) => {
    try {
      const response = await api.private.get(
        api.routes.privateRoutes.rep.sites
      );

      if (response.data && response.data.result) {
        return response.data.result;
      } else {
        return thunkAPI.rejectWithValue(
          'Invalid response format: Missing "result" field'
        );
      }
    } catch (error) {
      console.error("Error caught during API call:", error);
      return thunkAPI.rejectWithValue("Failed to fetch sites list");
    }
  }
);

const fetchPatientsList = createAsyncThunk(
  "schedule/fetchPatientsList",
  async ({ site_id, case_date }: IfetchPatientsListParams, thunkAPI) => {
    try {
      const response = await api.private.get(
        api.routes.privateRoutes.rep.patientsListUnassigned(site_id, case_date)
      );

      if (response.data && response.data.result) {
        return response.data.result;
      } else {
        return thunkAPI.rejectWithValue(
          'Invalid response format: Missing "result" field'
        );
      }
    } catch (error) {
      console.error("Error caught during API call:", error);
      return thunkAPI.rejectWithValue("Failed to fetch patients list");
    }
  }
);

const putChadScoreDetails = createAsyncThunk(
  "tasks/putChadScoreDetails",
  async ({ case_id, payload }: { case_id: string; payload: any }, thunkAPI) => {
    try {
      const response = await api.private.put(
        api.routes.privateRoutes.coordinator.chad_score_put(case_id),
        payload
      );

      if (response.data && response.data.result) {
        return response.data.result;
      } else {
        return thunkAPI.rejectWithValue(
          'Invalid response format: Missing "result" field'
        );
      }
    } catch (error: any) {
      if (error.response) {
        console.error("Error updating CHAD Score:", error.response.data);
        return thunkAPI.rejectWithValue(
          error.response.data.error_description || "Failed to update CHAD Score"
        );
      } else if (error.request) {
        console.error("No response received:", error.request);
        return thunkAPI.rejectWithValue("No response received from the server");
      } else {
        console.error("Error occurred:", error.message);
        return thunkAPI.rejectWithValue(
          error.message || "An unknown error occurred"
        );
      }
    }
  }
);

const putHasbledScoreDetails = createAsyncThunk(
  "tasks/putHasbledScoreDetails",
  async ({ case_id, payload }: { case_id: string; payload: any }, thunkAPI) => {
    try {
      const response = await api.private.put(
        api.routes.privateRoutes.coordinator.hasbled_score_put(case_id),
        payload
      );

      if (response.data && response.data.result) {
        return response.data.result;
      } else {
        return thunkAPI.rejectWithValue(
          'Invalid response format: Missing "result" field'
        );
      }
    } catch (error: any) {
      if (error.response) {
        console.error("Error updating HASBLED Score:", error.response.data);
        return thunkAPI.rejectWithValue(
          error.response.data.error_description ||
            "Failed to update HASBLED Score"
        );
      } else if (error.request) {
        console.error("No response received:", error.request);
        return thunkAPI.rejectWithValue("No response received from the server");
      } else {
        console.error("Error occurred:", error.message);
        return thunkAPI.rejectWithValue(
          error.message || "An unknown error occurred"
        );
      }
    }
  }
);

const putRepCase = createAsyncThunk(
  "schedule/putRepCase",
  async ({ case_id }: { case_id: string }, thunkAPI) => {
    try {
      const response = await api.private.put(
        api.routes.privateRoutes.rep.case_put(case_id)
      );

      if (response.data && response.data.result) {
        return response.data.result;
      } else {
        return thunkAPI.rejectWithValue(
          'Invalid response format: Missing "result" field'
        );
      }
    } catch (error) {
      console.error("Error caught during API call:", error);
      return thunkAPI.rejectWithValue("Failed to PUTRepCase");
    }
  }
);

export {
  fetchSchedules,
  fetchSiteList,
  fetchPatientsList,
  putRepCase,
  putChadScoreDetails,
  putHasbledScoreDetails,
};
