import {
  IPostPatient,
  I<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ider,
  IRationale,
  IImplantingPhysician,
} from "./types";

const initialState = {
  postPatient: {
    patientDetails: null as IPostPatient | null,
    userDetails: null as IPostPatient | null,
    loading: false,
    error: null as string | null,
  },

  referringProviders: {
    referringProviders: null as IReferringProvider[] | null,
    loading: false,
    error: null as string | null,
  },

  implantingPhysicians: {
    implantingPhysicians: null as IImplantingPhysician[] | null,
    loading: false,
    error: null as string | null,
  },

  rationale: {
    rationale: null as IRationale[] | null,
    loading: false,
    error: null as string | null,
  },
};

export default initialState;
