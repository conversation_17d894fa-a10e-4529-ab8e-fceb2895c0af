import React, { useEffect } from "react";
import { Text, View } from "react-native";
import { useFocusEffect, useNavigation } from "@react-navigation/native"; //auto nav
import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons";
import SearchablePicker from "../../../components/SearchablePicker";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "../../../store";
import {
  findPostProviderDiff,
  useLoaderAndError,
  useUserProviderDetails,
  useProviderDetails,
  useSelectedProvider,
  useReferringProviderCredentialOptions,
  useReferringProvidersListData, // FIXED: Using the correct hook name
} from "../hooks/addReferringProvider";
import {
  fetchReferringProviderById,
  fetchReferringProviders,
  updateReferringProvider,
  fetchCredentialOptions,
} from "../../../store/common/addReferringProvider/thunk";
import CustomCard from "../../../components/CustomCard";
import Heading from "../../../components/Heading";
import LineSeperator from "../../../components/LineSeperator";
import SaveActionButton from "../../../components/SaveActionButton";
import Loader from "../../../components/Loader";
import CustomInput from "../../../components/CustomTextInput";
import ScreenWrapper from "../../../components/ScreenWrapper";
import { parseCharInput, parseIntInput } from "../../../utils";
import {
  setSelectedProvider,
  setProviderDetails,
  showAddProviderModal,
} from "../../../store/common/addReferringProvider";
import PopupModal from "../../../components/Popup";
import { simpleAutoSave } from "../../../services/simpleAutoSave";
import { setSaveStatus, setLastSavedData } from "../../../store/services";
import { colorScheme } from "nativewind";

interface ReferringProviderListProps {
  onAddPress: () => void;
  onProviderSelect?: (providerId: string) => void;
}

const ReferringProviderList: React.FC<ReferringProviderListProps> = ({
  onAddPress,
  onProviderSelect,
}) => {
  const dispatch = useDispatch<AppDispatch>();
  const navigation = useNavigation();
  const [popupMsg, setPopupMsg] = React.useState<string[]>([]);
  const [modalVisible, setModalVisible] = React.useState(false);
  const credentialOptionsData = useReferringProviderCredentialOptions();

  const providersListData = useReferringProvidersListData();

  const userDetails = useUserProviderDetails();
  const providerDetails = useProviderDetails();
  const selectedProvider = useSelectedProvider();
  const { loader, error } = useLoaderAndError();

  // Get the original properties from userDetails
  const {
    first_name,
    last_name,
    middle_name,
    credential,
    npi_number,
    email_id,
    phone_number,
    fax_number,
    address,
    city,
    state,
    zip_code,
    hospital_system,
  } = userDetails || {};

  const diff = findPostProviderDiff();

  useEffect(() => {
    // Fetch providers list and credential options
    dispatch(fetchReferringProviders());
    dispatch(fetchCredentialOptions());
  }, [dispatch]);

  // Create a ref to track if a save is in progress
  const saveInProgressRef = React.useRef(false);

  // Create a ref to track the last saved data hash
  const lastSavedDataHashRef = React.useRef("");

  const handleContextBasedAdd = (context: string) => {
    switch (context) {
      case "referring_provider":
        onAddPress(); // This calls your existing onAddPress prop
        break;
      case "credential":
        // dispatch(showAddCredentialModal(true)); // if you have such action
        break;
      default:
        console.log("Unknown context:", context);
    }
  };

  useFocusEffect(
    React.useCallback(() => {
      const fetchDetails = async () => {
        if (selectedProvider) {
          const res = await dispatch(
            fetchReferringProviderById(selectedProvider)
          );
          if (res.payload) {
            dispatch(setProviderDetails(res.payload));
          }
        }
      };

      fetchDetails();
    }, [dispatch, selectedProvider])
  );

  // IMPROVED: Better validation that doesn't interfere with autosave
  const validator = (dataToValidate = userDetails) => {
    setPopupMsg([]);
    var temp = false;

    if (!dataToValidate?.first_name) {
      setPopupMsg((prev) => [...prev, "Please Enter First Name"]);
      temp = true;
    }

    if (!dataToValidate?.last_name) {
      setPopupMsg((prev) => [...prev, "Please Enter Last Name"]);
      temp = true;
    }

    if (!dataToValidate?.npi_number) {
      setPopupMsg((prev) => [...prev, "Please Enter NPI Number"]);
      temp = true;
    }

    if (temp) {
      setModalVisible(true);
      return false;
    } else {
      return true;
    }
  };

  const credentialOptions = React.useMemo(() => {
    if (
      Array.isArray(credentialOptionsData) &&
      credentialOptionsData.length > 0
    ) {
      return credentialOptionsData.map((provider) => ({
        label: provider.name,
        value: provider.id,
      }));
    }
    return [];
  }, [credentialOptionsData]);

  // FIXED: Transform providers data for SearchablePicker using the correct hook
  const providerOptions = React.useMemo(() => {
    if (Array.isArray(providersListData) && providersListData.length > 0) {
      return providersListData.map((provider) => ({
        label: provider.name,
        value: provider.id,
      }));
    }
    return [];
  }, [providersListData]);

  const handleProviderChange = async (value: {
    value: string;
    label: string;
  }) => {
    dispatch(showAddProviderModal(false));
    dispatch(setSelectedProvider(value.value));
    onProviderSelect?.(value.value);

    if (value.value) {
      const res = await dispatch(fetchReferringProviderById(value.value));
      if (res.payload) {
        dispatch(setProviderDetails(res.payload));
      }
    }
  };

  const handleCredentialChange = (value: { value: string; label: string }) => {
    dispatch(
      setProviderDetails({
        ...userDetails,
        credential: value.value,
      })
    );
  };

  // FIXED: Check if data has meaningful changes before validating
  const hasValidDataForSave = (dataToCheck: any) => {
    if (!dataToCheck) return false;

    // Check if we have the minimum required fields with actual values (not just empty strings)
    const hasRequiredFields =
      dataToCheck.first_name &&
      dataToCheck.first_name.trim() !== "" &&
      dataToCheck.last_name &&
      dataToCheck.last_name.trim() !== "" &&
      dataToCheck.npi_number &&
      dataToCheck.npi_number.toString().trim() !== "";

    return hasRequiredFields;
  };

  const saveDetails = async (): Promise<void> => {
    try {
      const isValid = validator(userDetails);

      if (!isValid) {
        dispatch(
          setSaveStatus({
            screenId: "providerDetails",
            status: "validation_failed",
          })
        );
        setLocalSaveStatus("validation_failed");
        throw new Error("Validation failed");
      }

      if (saveInProgressRef.current) {
        return;
      }

      const dataCopy = { ...diff.currentValues };
      const currentDataHash = JSON.stringify(dataCopy);

      if (currentDataHash === lastSavedDataHashRef.current) {
        return;
      }

      saveInProgressRef.current = true;

      const res = await dispatch(
        updateReferringProvider({
          id: selectedProvider,
          payload: dataCopy,
        })
      );

      if (res?.payload) {
        await dispatch(fetchReferringProviderById(selectedProvider));
        await dispatch(fetchReferringProviders());
        lastSavedDataHashRef.current = currentDataHash;
      }

      saveInProgressRef.current = false;
    } catch (err) {
      console.error("Save error:", err);
      saveInProgressRef.current = false;
      throw err;
    }
  };

  const handleCancel = () => {
    dispatch(
      setProviderDetails({
        ...userDetails,
        ...providerDetails,
      })
    );
  };

  const saveStatus = useSelector(
    (state: any) => state.common?.autoSave?.["providerDetails"]?.status
  );

  const [localSaveStatus, setLocalSaveStatus] = React.useState<string>("");
  const effectiveSaveStatus = saveStatus || localSaveStatus;

  // Derive the save message and styling based on the save status
  const { saveMessage, messageStyle, iconName, iconColor } =
    React.useMemo(() => {
      let message = "";
      let style = "text-primaryPurple italic";
      let icon = "alert-circle-outline";
      let color = "#8143d9";

      switch (effectiveSaveStatus) {
        case "changes_detected":
          message = "Changes detected, saving soon...";
          style = "text-primaryBlack italic";
          icon = "alert-circle-outline";
          color = "#f5c542"; // yellow
          break;
        case "saving":
          message = "Saving... Please don't close the app";
          style = "text-primaryPurple font-bold italic";
          icon = "alert-circle-outline";
          color = "#8143d9"; // purple
          break;
        case "saved":
          message = "All changes saved";
          style = "text-green-3 italic";
          icon = "check-circle-outline";
          color = "#28a745"; // green
          break;
        case "validation_failed":
          message = "Validation failed. Please check your inputs.";
          style = "text-red-3 font-bold italic";
          icon = "alert-circle-outline";
          color = "#d93025"; // red
          break;
        case "error":
          message = "Error saving changes. Please try again.";
          style = "text-red-3 font-bold italic";
          icon = "alert-circle-outline";
          color = "#d93025"; // red
          break;
      }

      return {
        saveMessage: message,
        messageStyle: style,
        iconName: icon,
        iconColor: color,
      };
    }, [effectiveSaveStatus]);

  // Ref to track previous diff values to prevent unnecessary saves
  const prevDiffRef = React.useRef<string>("");

  // Ref to track if initial data has been loaded
  const initialDataLoadedRef = React.useRef<boolean>(false);

  // Effect to set initialDataLoaded when data is fetched
  React.useEffect(() => {
    const hasValidData = selectedProvider !== undefined && userDetails;

    if (hasValidData && !initialDataLoadedRef.current) {
      initialDataLoadedRef.current = true;
    }
  }, [userDetails, selectedProvider]);

  // FIXED: Improved autosave logic
  React.useEffect(() => {
    if (!initialDataLoadedRef.current) {
      return;
    }

    if (!selectedProvider) {
      return;
    }

    if (diff.hasDiff) {
      // FIXED: Better check for meaningful changes
      const hasEmptyOrUndefinedValues = Object.entries(
        diff.currentValues
      ).every(([key, value]) => {
        return (
          value === undefined ||
          value === null ||
          (typeof value === "string" && value.trim() === "") ||
          (typeof value === "number" && isNaN(value))
        );
      });

      // FIXED: Only proceed if we have valid data structure
      if (!hasValidDataForSave(userDetails)) {
        return;
      }

      const currentDiffString = JSON.stringify(diff.currentValues);

      if (currentDiffString !== prevDiffRef.current) {
        prevDiffRef.current = currentDiffString;

        const setStatusWithFallback = (payload: {
          screenId: string;
          status: string;
        }) => {
          setLocalSaveStatus(payload.status);
          return dispatch(setSaveStatus(payload));
        };

        simpleAutoSave(
          "providerDetails",
          diff.currentValues,
          saveDetails,
          4000,
          dispatch,
          setStatusWithFallback,
          setLastSavedData
        );
        dispatch(setSelectedProvider(selectedProvider));
      }
    }
  }, [
    diff.hasDiff,
    diff.currentValues,
    dispatch,
    selectedProvider,
    userDetails,
  ]);

  const handleRefresh = async () => {
    try {
      await dispatch(fetchReferringProviders());
      await dispatch(fetchCredentialOptions());
      if (selectedProvider) {
        const providerRes = await dispatch(
          fetchReferringProviderById(selectedProvider)
        );
        if (providerRes.payload) {
          dispatch(setProviderDetails(providerRes.payload));
        }
      }
    } catch (error) {
      console.error("Error refreshing provider list:", error);
    }
  };

  return (
    <>
      {loader ? (
        <View className="mt-64">
          <Loader />
        </View>
      ) : (
        <ScreenWrapper direction="column" onRefresh={handleRefresh}>
          <CustomCard>
            {saveMessage && (
              <View
                style={{
                  flexDirection: "row",
                  justifyContent: "flex-end",
                  alignItems: "center",
                  marginVertical: 4,
                }}
              >
                <MaterialCommunityIcons
                  name={iconName}
                  color={iconColor}
                  size={20}
                />
                <Text
                  className={`text-sm ${messageStyle}`}
                  style={{ marginLeft: 6 }}
                >
                  {saveMessage}
                </Text>
              </View>
            )}

            <View className="mt-2 gap-3">
              <View className="flex-row justify-between items-center">
                <Heading
                  text="Referring Provider"
                  size="sub-heading"
                  showSeperator={false}
                />
                <MaterialCommunityIcons
                  name="plus-circle-outline"
                  color="#8143d9"
                  size={25}
                  onPress={onAddPress}
                />
              </View>

              <View className="">
                <SearchablePicker
                  placeholder="Select Provider"
                  items={providerOptions}
                  value={selectedProvider}
                  onValueChange={handleProviderChange}
                  screenContext="referring_provider"
                  onAddPress={handleContextBasedAdd}
                />
              </View>
            </View>

            {selectedProvider && userDetails && (
              <>
                <LineSeperator extraStyle="mt-5 mb-3" />

                <View className="rounded-lg">
                  <View className="mb-3">
                    <Heading
                      text="First Name"
                      size="sub-heading"
                      showSeperator={false}
                    />
                    <View className="mt-2">
                      <CustomInput
                        inputValue={first_name}
                        placeholder="First Name"
                        onInputChange={(value) => {
                          parseCharInput(value, (parsedValue) => {
                            dispatch(
                              setProviderDetails({
                                ...userDetails,
                                first_name: parsedValue,
                              })
                            );
                          });
                        }}
                      />
                    </View>
                  </View>

                  <LineSeperator extraStyle="mt-2 mb-3" />

                  <View className="mb-3">
                    <Heading
                      text="Middle Name"
                      size="sub-heading"
                      showSeperator={false}
                    />
                    <View className="mt-2">
                      <CustomInput
                        inputValue={middle_name}
                        placeholder="Middle Name"
                        onInputChange={(value) => {
                          parseCharInput(value, (parsedValue) => {
                            dispatch(
                              setProviderDetails({
                                ...userDetails,
                                middle_name: parsedValue,
                              })
                            );
                          });
                        }}
                      />
                    </View>
                  </View>

                  <LineSeperator extraStyle="mt-2 mb-3" />

                  <View className="mb-3">
                    <Heading
                      text="Last Name"
                      size="sub-heading"
                      showSeperator={false}
                    />
                    <View className="mt-2">
                      <CustomInput
                        inputValue={last_name}
                        placeholder="Last Name"
                        onInputChange={(value) => {
                          parseCharInput(value, (parsedValue) => {
                            dispatch(
                              setProviderDetails({
                                ...userDetails,
                                last_name: parsedValue,
                              })
                            );
                          });
                        }}
                      />
                    </View>
                  </View>

                  <LineSeperator extraStyle="mt-2 mb-3" />

                  <View className="mb-3">
                    <Heading
                      text="Credential"
                      size="sub-heading"
                      showSeperator={false}
                    />
                    <View className="mt-2">
                      <SearchablePicker
                        value={credential}
                        placeholder="Select Credential"
                        items={credentialOptions.map((option) => {
                          return {
                            label: option.label,
                            value: option.value,
                          };
                        })}
                        onValueChange={(selectedItem) => {
                          dispatch(
                            setProviderDetails({
                              credential: selectedItem.value,
                            })
                          );
                        }}
                        screenContext="credential"
                        onAddPress={handleContextBasedAdd}
                        floatingLabel={false}
                        disable={true}
                        error={false}
                        disableError={false}
                      />
                    </View>
                  </View>

                  <LineSeperator extraStyle="mt-2 mb-3" />

                  <View className="mb-3">
                    <Heading
                      text="NPI Number"
                      size="sub-heading"
                      showSeperator={false}
                    />
                    <View className="mt-2">
                      <CustomInput
                        inputValue={npi_number}
                        placeholder="NPI Number"
                        onInputChange={(value) => {
                          parseIntInput(value, (parsedValue) => {
                            dispatch(
                              setProviderDetails({
                                ...userDetails,
                                npi_number: parsedValue.toString(),
                              })
                            );
                          });
                        }}
                        keyboardType="numeric"
                      />
                    </View>
                  </View>

                  <LineSeperator extraStyle="mt-2 mb-3" />

                  <View className="mb-3">
                    <Heading
                      text="Email"
                      size="sub-heading"
                      showSeperator={false}
                    />
                    <View className="mt-2">
                      <CustomInput
                        inputValue={email_id}
                        placeholder="Email"
                        onInputChange={(value) => {
                          dispatch(
                            setProviderDetails({
                              ...userDetails,
                              email_id: value,
                            })
                          );
                        }}
                        keyboardType="email-address"
                      />
                    </View>
                  </View>

                  <LineSeperator extraStyle="mt-2 mb-3" />

                  <View className="mb-3">
                    <Heading
                      text="Phone Number"
                      size="sub-heading"
                      showSeperator={false}
                    />
                    <View className="mt-2">
                      <CustomInput
                        inputValue={phone_number}
                        placeholder="Phone Number"
                        onInputChange={(value) => {
                          parseIntInput(value, (parsedValue) => {
                            dispatch(
                              setProviderDetails({
                                ...userDetails,
                                phone_number: parsedValue.toString(),
                              })
                            );
                          });
                        }}
                        keyboardType="phone-pad"
                      />
                    </View>
                  </View>

                  <LineSeperator extraStyle="mt-2 mb-3" />

                  <View className="mb-3">
                    <Heading
                      text="Fax Number"
                      size="sub-heading"
                      showSeperator={false}
                    />
                    <View className="mt-2">
                      <CustomInput
                        inputValue={fax_number}
                        placeholder="Fax Number"
                        onInputChange={(value) => {
                          parseIntInput(value, (parsedValue) => {
                            dispatch(
                              setProviderDetails({
                                ...userDetails,
                                fax_number: parsedValue.toString(),
                              })
                            );
                          });
                        }}
                        keyboardType="phone-pad"
                      />
                    </View>
                  </View>

                  <LineSeperator extraStyle="mt-2 mb-3" />

                  <View className="mb-3">
                    <Heading
                      text="Address"
                      size="sub-heading"
                      showSeperator={false}
                    />
                    <View className="mt-2">
                      <CustomInput
                        inputValue={address}
                        placeholder="Address"
                        onInputChange={(value) => {
                          dispatch(
                            setProviderDetails({
                              ...userDetails,
                              address: value,
                            })
                          );
                        }}
                      />
                    </View>
                  </View>

                  <LineSeperator extraStyle="mt-2 mb-3" />

                  <View className="mb-3">
                    <Heading
                      text="City"
                      size="sub-heading"
                      showSeperator={false}
                    />
                    <View className="mt-2">
                      <CustomInput
                        inputValue={city}
                        placeholder="City"
                        onInputChange={(value) => {
                          parseCharInput(value, (parsedValue) => {
                            dispatch(
                              setProviderDetails({
                                ...userDetails,
                                city: parsedValue,
                              })
                            );
                          });
                        }}
                      />
                    </View>
                  </View>

                  <LineSeperator extraStyle="mt-2 mb-3" />

                  <View className="mb-3">
                    <Heading
                      text="State"
                      size="sub-heading"
                      showSeperator={false}
                    />
                    <View className="mt-2">
                      <CustomInput
                        inputValue={state}
                        placeholder="State"
                        onInputChange={(value) => {
                          parseCharInput(value, (parsedValue) => {
                            dispatch(
                              setProviderDetails({
                                ...userDetails,
                                state: parsedValue,
                              })
                            );
                          });
                        }}
                      />
                    </View>
                  </View>

                  <LineSeperator extraStyle="mt-2 mb-3" />

                  <View className="mb-3">
                    <Heading
                      text="Zip Code"
                      size="sub-heading"
                      showSeperator={false}
                    />
                    <View className="mt-2">
                      <CustomInput
                        inputValue={zip_code}
                        placeholder="Zip Code"
                        onInputChange={(value) => {
                          parseIntInput(value, (parsedValue) => {
                            dispatch(
                              setProviderDetails({
                                ...userDetails,
                                zip_code: parsedValue.toString(),
                              })
                            );
                          });
                        }}
                        keyboardType="numeric"
                      />
                    </View>
                  </View>

                  <LineSeperator extraStyle="mt-2 mb-3" />

                  <View className="mb-3">
                    <Heading
                      text="Hospital System"
                      size="sub-heading"
                      showSeperator={false}
                    />
                    <View className="mt-2">
                      <CustomInput
                        inputValue={hospital_system}
                        placeholder="Hospital System"
                        onInputChange={(value) => {
                          dispatch(
                            setProviderDetails({
                              ...userDetails,
                              hospital_system: value,
                            })
                          );
                        }}
                      />
                    </View>
                  </View>
                </View>

                <LineSeperator extraStyle="my-5" />
              </>
            )}
            <View className="mb-3"></View>
          </CustomCard>
        </ScreenWrapper>
      )}
      <View className="mt-9"></View>

      <PopupModal
        show={modalVisible}
        msg={popupMsg}
        status="warning"
        onClose={() => setModalVisible(false)}
      />
    </>
  );
};

export default ReferringProviderList;

// import React, { useEffect } from "react";
// import { Text, View } from "react-native";
// import { useFocusEffect } from "@react-navigation/native";
// import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons";
// import SearchablePicker from "../../../components/SearchablePicker";
// import { useDispatch, useSelector } from "react-redux";
// import { AppDispatch, RootState } from "../../../store";
// import {
//   findPostProviderDiff,
//   useLoaderAndError,
//   useUserProviderDetails,
//   useProviderDetails,
//   useSelectedProvider,
//   useReferringProviderCredentialOptions,
// } from "../hooks/addReferringProvider";
// import {
//   fetchReferringProviderById,
//   fetchReferringProviders,
//   updateReferringProvider,
//   fetchCredentialOptions,
// } from "../../../store/common/addReferringProvider/thunk";
// import CustomCard from "../../../components/CustomCard";
// import Heading from "../../../components/Heading";
// import LineSeperator from "../../../components/LineSeperator";
// import SaveActionButton from "../../../components/SaveActionButton";
// import Loader from "../../../components/Loader";
// import CustomInput from "../../../components/CustomTextInput";
// import ScreenWrapper from "../../../components/ScreenWrapper";
// import { parseCharInput, parseIntInput } from "../../../utils";
// import {
//   setSelectedProvider,
//   setProviderDetails,
//   showAddProviderModal,
// } from "../../../store/common/addReferringProvider";
// import PopupModal from "../../../components/Popup";
// import { simpleAutoSave } from "../../../services/simpleAutoSave";
// import { setSaveStatus, setLastSavedData } from "../../../store/services";

// interface ReferringProviderListProps {
//   onAddPress: () => void;
//   onProviderSelect?: (providerId: string) => void;
// }

// const ReferringProviderList: React.FC<ReferringProviderListProps> = ({
//   onAddPress,
//   onProviderSelect,
// }) => {
//   const dispatch = useDispatch<AppDispatch>();
//   const [popupMsg, setPopupMsg] = React.useState<string[]>([]);
//   const [modalVisible, setModalVisible] = React.useState(false);
//   const credentialOptionsData = useReferringProviderCredentialOptions();

//   // FIXED: Get providers list from the correct Redux field
//   // const providersListData = useSelector(
//   //   (state: RootState) => state.common.referringProviders.providerListDetails
//   // );
//   const providersListData = useReferringProvidersListData();

//   const userDetails = useUserProviderDetails();
//   const providerDetails = useProviderDetails();
//   const selectedProvider = useSelectedProvider();
//   const { loader, error } = useLoaderAndError();

//   // Get the original properties from userDetails
//   const {
//     first_name,
//     last_name,
//     middle_name,
//     credential,
//     npi_number,
//     email_id,
//     phone_number,
//     fax_number,
//     address,
//     city,
//     state,
//     zip_code,
//     hospital_system,
//   } = userDetails || {};

//   const diff = findPostProviderDiff();

//   useEffect(() => {
//     // Fetch providers list and credential options
//     dispatch(fetchReferringProviders());
//     dispatch(fetchCredentialOptions());
//   }, [dispatch]);

//   // Create a ref to track if a save is in progress
//   const saveInProgressRef = React.useRef(false);

//   // Create a ref to track the last saved data hash
//   const lastSavedDataHashRef = React.useRef("");

//   const handleContextBasedAdd = (context: string) => {
//     switch (context) {
//       case "referring_provider":
//         onAddPress(); // This calls your existing onAddPress prop
//         break;
//       case "credential":
//         // Handle add credential - you might want to add this functionality
//         console.log("Add new credential");
//         // dispatch(showAddCredentialModal(true)); // if you have such action
//         break;
//       default:
//         console.log("Unknown context:", context);
//     }
//   };

//   useFocusEffect(
//     React.useCallback(() => {
//       const fetchDetails = async () => {
//         if (selectedProvider) {
//           const res = await dispatch(
//             fetchReferringProviderById(selectedProvider)
//           );
//           if (res.payload) {
//             dispatch(setProviderDetails(res.payload));
//           }
//         }
//       };

//       fetchDetails();
//     }, [dispatch, selectedProvider])
//   );

//   // IMPROVED: Better validation that doesn't interfere with autosave
//   const validator = (dataToValidate = userDetails) => {
//     setPopupMsg([]);
//     var temp = false;

//     if (!dataToValidate?.first_name) {
//       setPopupMsg((prev) => [...prev, "Please Enter First Name"]);
//       temp = true;
//     }

//     if (!dataToValidate?.last_name) {
//       setPopupMsg((prev) => [...prev, "Please Enter Last Name"]);
//       temp = true;
//     }

//     if (!dataToValidate?.npi_number) {
//       setPopupMsg((prev) => [...prev, "Please Enter NPI Number"]);
//       temp = true;
//     }

//     if (temp) {
//       setModalVisible(true);
//       return false;
//     } else {
//       return true;
//     }
//   };

//   const credentialOptions = React.useMemo(() => {
//     if (
//       Array.isArray(credentialOptionsData) &&
//       credentialOptionsData.length > 0
//     ) {
//       return credentialOptionsData.map((provider) => ({
//         label: provider.name,
//         value: provider.id,
//       }));
//     }
//     return [];
//   }, [credentialOptionsData]);

//   // FIXED: Transform providers data for SearchablePicker using the correct field
//   const providerOptions = React.useMemo(() => {
//     if (Array.isArray(providersListData) && providersListData.length > 0) {
//       return providersListData.map((provider) => ({
//         label: provider.name,
//         value: provider.id,
//       }));
//     }
//     return [];
//   }, [providersListData]);

//   const handleProviderChange = async (value: {
//     value: string;
//     label: string;
//   }) => {
//     dispatch(showAddProviderModal(false));
//     dispatch(setSelectedProvider(value.value));
//     onProviderSelect?.(value.value);

//     if (value.value) {
//       const res = await dispatch(fetchReferringProviderById(value.value));
//       if (res.payload) {
//         dispatch(setProviderDetails(res.payload));
//       }
//     }
//   };

//   const handleCredentialChange = (value: { value: string; label: string }) => {
//     dispatch(
//       setProviderDetails({
//         ...userDetails,
//         credential: value.value,
//       })
//     );
//   };

//   console.log("selectedProvider", selectedProvider);

//   // FIXED: Check if data has meaningful changes before validating
//   const hasValidDataForSave = (dataToCheck: any) => {
//     if (!dataToCheck) return false;

//     // Check if we have the minimum required fields with actual values (not just empty strings)
//     const hasRequiredFields =
//       dataToCheck.first_name &&
//       dataToCheck.first_name.trim() !== "" &&
//       dataToCheck.last_name &&
//       dataToCheck.last_name.trim() !== "" &&
//       dataToCheck.npi_number &&
//       dataToCheck.npi_number.toString().trim() !== "";

//     return hasRequiredFields;
//   };

//   const saveDetails = async (): Promise<void> => {
//     try {
//       // FIXED: Only validate if we have meaningful data to save
//       if (!hasValidDataForSave(userDetails)) {
//         console.log("Skipping save - insufficient data");
//         return;
//       }

//       const isValid = validator(userDetails);

//       if (!isValid) {
//         dispatch(
//           setSaveStatus({
//             screenId: "providerDetails",
//             status: "validation_failed",
//           })
//         );
//         setLocalSaveStatus("validation_failed");
//         throw new Error("Validation failed");
//       }

//       if (saveInProgressRef.current) {
//         return;
//       }

//       const dataCopy = { ...diff.currentValues };
//       const currentDataHash = JSON.stringify(dataCopy);

//       if (currentDataHash === lastSavedDataHashRef.current) {
//         return;
//       }

//       saveInProgressRef.current = true;

//       const res = await dispatch(
//         updateReferringProvider({
//           id: selectedProvider,
//           payload: dataCopy,
//         })
//       );

//       if (res?.payload) {
//         await dispatch(fetchReferringProviderById(selectedProvider));
//         await dispatch(fetchReferringProviders());
//         lastSavedDataHashRef.current = currentDataHash;
//       }

//       saveInProgressRef.current = false;
//     } catch (err) {
//       console.error("Save error:", err);
//       saveInProgressRef.current = false;
//       throw err;
//     }
//   };

//   const handleCancel = () => {
//     dispatch(
//       setProviderDetails({
//         ...userDetails,
//         ...providerDetails,
//       })
//     );
//   };

//   const saveStatus = useSelector(
//     (state: any) => state.common?.autoSave?.["providerDetails"]?.status
//   );

//   const [localSaveStatus, setLocalSaveStatus] = React.useState<string>("");
//   const effectiveSaveStatus = saveStatus || localSaveStatus;

//   // Derive the save message and styling based on the save status
//   const { saveMessage, messageStyle } = React.useMemo(() => {
//     let message = "";
//     let style = "text-primaryPurple italic";

//     switch (effectiveSaveStatus) {
//       case "changes_detected":
//         message = "Changes detected, saving soon...";
//         style = "text-primaryBlack italic";
//         break;
//       case "saving":
//         message = "⚠️ Saving... Please don't close the app";
//         style = "text-primaryPurple font-bold italic";
//         break;
//       case "saved":
//         message = "✅ All changes saved";
//         style = "text-green-3 italic";
//         break;
//       case "validation_failed":
//         message = "⚠️ Validation failed. Please check your inputs.";
//         style = "text-red-3 font-bold italic";
//         break;
//       case "error":
//         message = "❌ Error saving changes. Please try again.";
//         style = "text-red-3 font-bold italic";
//         break;
//     }

//     return { saveMessage: message, messageStyle: style };
//   }, [effectiveSaveStatus, saveStatus, localSaveStatus]);

//   // Ref to track previous diff values to prevent unnecessary saves
//   const prevDiffRef = React.useRef<string>("");

//   // Ref to track if initial data has been loaded
//   const initialDataLoadedRef = React.useRef<boolean>(false);

//   // Effect to set initialDataLoaded when data is fetched
//   React.useEffect(() => {
//     const hasValidData = selectedProvider !== undefined && userDetails;

//     if (hasValidData && !initialDataLoadedRef.current) {
//       initialDataLoadedRef.current = true;
//     }
//   }, [userDetails, selectedProvider]);

//   // FIXED: Improved autosave logic
//   React.useEffect(() => {
//     if (!initialDataLoadedRef.current) {
//       return;
//     }

//     if (!selectedProvider) {
//       return;
//     }

//     if (diff.hasDiff) {
//       // FIXED: Better check for meaningful changes
//       const hasEmptyOrUndefinedValues = Object.entries(
//         diff.currentValues
//       ).every(([key, value]) => {
//         return (
//           value === undefined ||
//           value === null ||
//           (typeof value === "string" && value.trim() === "") ||
//           (typeof value === "number" && isNaN(value))
//         );
//       });

//       if (hasEmptyOrUndefinedValues) {
//         console.log("Skipping autosave - no meaningful changes");
//         return;
//       }

//       // FIXED: Only proceed if we have valid data structure
//       if (!hasValidDataForSave(userDetails)) {
//         console.log("Skipping autosave - insufficient required fields");
//         return;
//       }

//       const currentDiffString = JSON.stringify(diff.currentValues);

//       if (currentDiffString !== prevDiffRef.current) {
//         prevDiffRef.current = currentDiffString;

//         const setStatusWithFallback = (payload: {
//           screenId: string;
//           status: string;
//         }) => {
//           setLocalSaveStatus(payload.status);
//           return dispatch(setSaveStatus(payload));
//         };

//         console.log("Triggering autosave with valid data");
//         simpleAutoSave(
//           "providerDetails",
//           diff.currentValues,
//           saveDetails,
//           4000,
//           dispatch,
//           setStatusWithFallback,
//           setLastSavedData
//         );
//         dispatch(setSelectedProvider(selectedProvider));
//       }
//     }
//   }, [
//     diff.hasDiff,
//     diff.currentValues,
//     dispatch,
//     selectedProvider,
//     userDetails,
//   ]);

//   const handleRefresh = async () => {
//     try {
//       await dispatch(fetchReferringProviders());
//       await dispatch(fetchCredentialOptions());
//       if (selectedProvider) {
//         const providerRes = await dispatch(
//           fetchReferringProviderById(selectedProvider)
//         );
//         if (providerRes.payload) {
//           dispatch(setProviderDetails(providerRes.payload));
//         }
//       }
//     } catch (error) {
//       console.error("Error refreshing provider list:", error);
//     }
//   };

//   return (
//     <>
//       {loader ? (
//         <View className="mt-64">
//           <Loader />
//         </View>
//       ) : (
//         <ScreenWrapper direction="column" onRefresh={handleRefresh}>
//           <CustomCard>
//             {saveMessage && (
//               <Text className={`text-sm text-right my-1 ${messageStyle}`}>
//                 {saveMessage}
//               </Text>
//             )}
//             <View className="mt-2 gap-3">
//               <View className="flex-row justify-between items-center">
//                 <Heading
//                   text="Referring Provider"
//                   size="sub-heading"
//                   showSeperator={false}
//                 />
//                 <MaterialCommunityIcons
//                   name="plus-circle-outline"
//                   color="#8143d9"
//                   size={25}
//                   onPress={onAddPress}
//                 />
//               </View>

//               <View className="">
//                 <SearchablePicker
//                   placeholder="Select Provider"
//                   items={providerOptions}
//                   value={selectedProvider}
//                   onValueChange={handleProviderChange}
//                   screenContext="referring_provider"
//                   onAddPress={handleContextBasedAdd}
//                 />
//               </View>
//             </View>

//             {selectedProvider && userDetails && (
//               <>
//                 <LineSeperator extraStyle="mt-5 mb-3" />

//                 <View className="rounded-lg">
//                   <View className="mb-3">
//                     <Heading
//                       text="First Name"
//                       size="sub-heading"
//                       showSeperator={false}
//                     />
//                     <View className="mt-2">
//                       <CustomInput
//                         inputValue={first_name}
//                         placeholder="First Name"
//                         onInputChange={(value) => {
//                           parseCharInput(value, (parsedValue) => {
//                             dispatch(
//                               setProviderDetails({
//                                 ...userDetails,
//                                 first_name: parsedValue,
//                               })
//                             );
//                           });
//                         }}
//                       />
//                     </View>
//                   </View>

//                   <LineSeperator extraStyle="mt-2 mb-3" />

//                   <View className="mb-3">
//                     <Heading
//                       text="Middle Name"
//                       size="sub-heading"
//                       showSeperator={false}
//                     />
//                     <View className="mt-2">
//                       <CustomInput
//                         inputValue={middle_name}
//                         placeholder="Middle Name"
//                         onInputChange={(value) => {
//                           parseCharInput(value, (parsedValue) => {
//                             dispatch(
//                               setProviderDetails({
//                                 ...userDetails,
//                                 middle_name: parsedValue,
//                               })
//                             );
//                           });
//                         }}
//                       />
//                     </View>
//                   </View>

//                   <LineSeperator extraStyle="mt-2 mb-3" />

//                   <View className="mb-3">
//                     <Heading
//                       text="Last Name"
//                       size="sub-heading"
//                       showSeperator={false}
//                     />
//                     <View className="mt-2">
//                       <CustomInput
//                         inputValue={last_name}
//                         placeholder="Last Name"
//                         onInputChange={(value) => {
//                           parseCharInput(value, (parsedValue) => {
//                             dispatch(
//                               setProviderDetails({
//                                 ...userDetails,
//                                 last_name: parsedValue,
//                               })
//                             );
//                           });
//                         }}
//                       />
//                     </View>
//                   </View>

//                   <LineSeperator extraStyle="mt-2 mb-3" />

//                   <View className="mb-3">
//                     <Heading
//                       text="Credential"
//                       size="sub-heading"
//                       showSeperator={false}
//                     />
//                     <View className="mt-2">
//                       <SearchablePicker
//                         value={credential}
//                         placeholder="Select Credential"
//                         items={credentialOptions.map((option) => {
//                           return {
//                             label: option.label,
//                             value: option.value,
//                           };
//                         })}
//                         onValueChange={(selectedItem) => {
//                           console.log("Selected Credential:", selectedItem);
//                           dispatch(
//                             setProviderDetails({
//                               credential: selectedItem.value,
//                             })
//                           );
//                         }}
//                         screenContext="credential"
//                         onAddPress={handleContextBasedAdd}
//                         floatingLabel={false}
//                         disable={true}
//                         error={false}
//                         disableError={false}
//                       />
//                     </View>
//                   </View>

//                   <LineSeperator extraStyle="mt-2 mb-3" />

//                   <View className="mb-3">
//                     <Heading
//                       text="NPI Number"
//                       size="sub-heading"
//                       showSeperator={false}
//                     />
//                     <View className="mt-2">
//                       <CustomInput
//                         inputValue={npi_number}
//                         placeholder="NPI Number"
//                         onInputChange={(value) => {
//                           parseIntInput(value, (parsedValue) => {
//                             dispatch(
//                               setProviderDetails({
//                                 ...userDetails,
//                                 npi_number: parsedValue.toString(),
//                               })
//                             );
//                           });
//                         }}
//                         keyboardType="numeric"
//                       />
//                     </View>
//                   </View>

//                   <LineSeperator extraStyle="mt-2 mb-3" />

//                   <View className="mb-3">
//                     <Heading
//                       text="Email"
//                       size="sub-heading"
//                       showSeperator={false}
//                     />
//                     <View className="mt-2">
//                       <CustomInput
//                         inputValue={email_id}
//                         placeholder="Email"
//                         onInputChange={(value) => {
//                           dispatch(
//                             setProviderDetails({
//                               ...userDetails,
//                               email_id: value,
//                             })
//                           );
//                         }}
//                         keyboardType="email-address"
//                       />
//                     </View>
//                   </View>

//                   <LineSeperator extraStyle="mt-2 mb-3" />

//                   <View className="mb-3">
//                     <Heading
//                       text="Phone Number"
//                       size="sub-heading"
//                       showSeperator={false}
//                     />
//                     <View className="mt-2">
//                       <CustomInput
//                         inputValue={phone_number}
//                         placeholder="Phone Number"
//                         onInputChange={(value) => {
//                           parseIntInput(value, (parsedValue) => {
//                             dispatch(
//                               setProviderDetails({
//                                 ...userDetails,
//                                 phone_number: parsedValue.toString(),
//                               })
//                             );
//                           });
//                         }}
//                         keyboardType="phone-pad"
//                       />
//                     </View>
//                   </View>

//                   <LineSeperator extraStyle="mt-2 mb-3" />

//                   <View className="mb-3">
//                     <Heading
//                       text="Fax Number"
//                       size="sub-heading"
//                       showSeperator={false}
//                     />
//                     <View className="mt-2">
//                       <CustomInput
//                         inputValue={fax_number}
//                         placeholder="Fax Number"
//                         onInputChange={(value) => {
//                           parseIntInput(value, (parsedValue) => {
//                             dispatch(
//                               setProviderDetails({
//                                 ...userDetails,
//                                 fax_number: parsedValue.toString(),
//                               })
//                             );
//                           });
//                         }}
//                         keyboardType="phone-pad"
//                       />
//                     </View>
//                   </View>

//                   <LineSeperator extraStyle="mt-2 mb-3" />

//                   <View className="mb-3">
//                     <Heading
//                       text="Address"
//                       size="sub-heading"
//                       showSeperator={false}
//                     />
//                     <View className="mt-2">
//                       <CustomInput
//                         inputValue={address}
//                         placeholder="Address"
//                         onInputChange={(value) => {
//                           dispatch(
//                             setProviderDetails({
//                               ...userDetails,
//                               address: value,
//                             })
//                           );
//                         }}
//                       />
//                     </View>
//                   </View>

//                   <LineSeperator extraStyle="mt-2 mb-3" />

//                   <View className="mb-3">
//                     <Heading
//                       text="City"
//                       size="sub-heading"
//                       showSeperator={false}
//                     />
//                     <View className="mt-2">
//                       <CustomInput
//                         inputValue={city}
//                         placeholder="City"
//                         onInputChange={(value) => {
//                           parseCharInput(value, (parsedValue) => {
//                             dispatch(
//                               setProviderDetails({
//                                 ...userDetails,
//                                 city: parsedValue,
//                               })
//                             );
//                           });
//                         }}
//                       />
//                     </View>
//                   </View>

//                   <LineSeperator extraStyle="mt-2 mb-3" />

//                   <View className="mb-3">
//                     <Heading
//                       text="State"
//                       size="sub-heading"
//                       showSeperator={false}
//                     />
//                     <View className="mt-2">
//                       <CustomInput
//                         inputValue={state}
//                         placeholder="State"
//                         onInputChange={(value) => {
//                           parseCharInput(value, (parsedValue) => {
//                             dispatch(
//                               setProviderDetails({
//                                 ...userDetails,
//                                 state: parsedValue,
//                               })
//                             );
//                           });
//                         }}
//                       />
//                     </View>
//                   </View>

//                   <LineSeperator extraStyle="mt-2 mb-3" />

//                   <View className="mb-3">
//                     <Heading
//                       text="Zip Code"
//                       size="sub-heading"
//                       showSeperator={false}
//                     />
//                     <View className="mt-2">
//                       <CustomInput
//                         inputValue={zip_code}
//                         placeholder="Zip Code"
//                         onInputChange={(value) => {
//                           parseIntInput(value, (parsedValue) => {
//                             dispatch(
//                               setProviderDetails({
//                                 ...userDetails,
//                                 zip_code: parsedValue.toString(),
//                               })
//                             );
//                           });
//                         }}
//                         keyboardType="numeric"
//                       />
//                     </View>
//                   </View>

//                   <LineSeperator extraStyle="mt-2 mb-3" />

//                   <View className="mb-3">
//                     <Heading
//                       text="Hospital System"
//                       size="sub-heading"
//                       showSeperator={false}
//                     />
//                     <View className="mt-2">
//                       <CustomInput
//                         inputValue={hospital_system}
//                         placeholder="Hospital System"
//                         onInputChange={(value) => {
//                           dispatch(
//                             setProviderDetails({
//                               ...userDetails,
//                               hospital_system: value,
//                             })
//                           );
//                         }}
//                       />
//                     </View>
//                   </View>
//                 </View>

//                 <LineSeperator extraStyle="my-5" />
//               </>
//             )}
//             <View className="mb-3"></View>
//           </CustomCard>
//         </ScreenWrapper>
//       )}
//       <View className="mt-9"></View>

//       <PopupModal
//         show={modalVisible}
//         msg={popupMsg}
//         status="warning"
//         onClose={() => setModalVisible(false)}
//       />
//     </>
//   );
// };

// export default ReferringProviderList;
