import React, { useCallback, useEffect, useState } from "react";
import { ScrollView, RefreshControl } from "react-native";
import { useDispatch } from "react-redux";
import { useFocusEffect } from "@react-navigation/native";
import { AppDispatch } from "../../../store";
import {
  fetchReferringProviders,
  fetchReferringProviderById,
} from "../../../store/common/addReferringProvider/thunk";
import {
  resetProviderDetails,
  showAddProviderModal,
  setSelectedProvider,
} from "../../../store/common/addReferringProvider";
import ReferringProviderList from "../components/ReferringProviderList";

const ReferringProvidersScreen: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const [refreshing, setRefreshing] = useState(false);

  const fetchData = async () => {
    await dispatch(fetchReferringProviders());
  };

  // 🔁 Fetch on first load
  useEffect(() => {
    fetchData();
  }, []);

  // 🔁 Auto reset and refetch on tab focus
  useFocusEffect(
    useCallback(() => {
      dispatch(resetProviderDetails());
      dispatch(setSelectedProvider(""));
      dispatch(showAddProviderModal(false));
      fetchData();
      return () => {
        // Optional cleanup
      };
    }, [dispatch])
  );

  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchData();
    setRefreshing(false);
  };

  const handleAddProvider = () => {
    dispatch(setSelectedProvider(""));
    dispatch(resetProviderDetails());
    dispatch(showAddProviderModal(true));
  };

  const handleProviderSelect = async (providerId: string) => {
    dispatch(setSelectedProvider(providerId));
    if (providerId) {
      await dispatch(fetchReferringProviderById(providerId));
    }
  };

  return (
    <ScrollView
      className="flex-1"
      refreshControl={
        <RefreshControl
          refreshing={refreshing}
          onRefresh={handleRefresh}
          colors={["#8143d9"]}
          tintColor={"#8143d9"}
        />
      }
    >
      <ReferringProviderList
        onAddPress={handleAddProvider}
        onProviderSelect={handleProviderSelect}
      />
    </ScrollView>
  );
};

export default ReferringProvidersScreen;
