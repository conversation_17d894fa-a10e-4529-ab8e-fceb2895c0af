import { useSelector } from "react-redux";
import { RootState } from "../../../store";

const useLoaderAndError = () => {
  const loader = useSelector(
    (state: RootState) => state.rep.laaanatomy.loading
  );
  const error = useSelector((state: RootState) => state.rep.laaanatomy.error);
  return { loader, error };
};

const useLaaAnatomyDetails = () => {
  const laaAnatomyDetails = useSelector(
    (state: RootState) => state.rep.laaanatomy.laaAnatomyDetails
  );
  return laaAnatomyDetails;
};

const useLaaAnatomyUserDetails = () => {
  const userDetails = useSelector(
    (state: RootState) => state.rep.laaanatomy.userDetails
  );
  return userDetails;
};

const findLaaoAnatomyDiff = () => {
  const laaAnatomyDetails = useLaaAnatomyDetails();
  const userDetails = useLaaAnatomyUserDetails();

  const diff = {
    // Compare morphology IDs
    laao_morphology:
      laaAnatomyDetails?.morphology?.selected?.id ===
      userDetails?.morphology?.selected?.id
        ? null
        : userDetails?.morphology?.selected?.id,

    // Compare tee_measurement arrays
    tee_measurement: userDetails?.tee_measurement?.map((userMeasurement) => {
      const laaMeasurement = laaAnatomyDetails?.tee_measurement?.find(
        (item) => item.angle === userMeasurement.angle
      );

      // Include all userMeasurement values, marking differences explicitly
      return {
        angle: userMeasurement.angle,
        width: userMeasurement.width,
        depth: userMeasurement.depth,
        isChanged:
          !laaMeasurement || // Missing in laaAnatomyDetails
          laaMeasurement.width !== userMeasurement.width ||
          laaMeasurement.depth !== userMeasurement.depth,
      };
    }),
  };

  // Filter out only unchanged entries if needed
  const filteredTeeMeasurement = diff.tee_measurement?.filter(
    (measurement) => measurement.isChanged
  );

  // Build a response that includes all existing values and marks differences

  const tempCurrentValues = {
    laao_morphology: diff.laao_morphology,
    tee_measurement: filteredTeeMeasurement || diff.tee_measurement,
  };
  const currentValues = {
    laao_morphology: diff.laao_morphology,
    tee_measurement: diff.tee_measurement,
  };

  const nonNullDiff = Object.entries(currentValues).reduce(
    (acc, [key, value]) => {
      if (value !== null && value !== undefined) {
        acc[key] = value;
      }
      return acc;
    },
    {}
  );

  const nonNullDiffTemp = Object.entries(tempCurrentValues).reduce(
    (acc, [key, value]) => {
      if (value !== null && value !== undefined) {
        acc[key] = value;
      }
      return acc;
    },
    {}
  );

  return {
    hasDiff: Object.values(nonNullDiffTemp).some(
      (value) => value && value.length > 0
    ),
    currentValues: nonNullDiff,
  };
};
export {
  useLoaderAndError,
  useLaaAnatomyDetails,
  useLaaAnatomyUserDetails,
  findLaaoAnatomyDiff,
};
