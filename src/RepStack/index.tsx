import * as React from "react";
import BottomBar from "./components/BottomBar";
import { View } from "react-native";
import { GestureHandlerRootView } from "react-native-gesture-handler";

interface IRepStackProps {
  role?: string;
}

const RepStack: React.FunctionComponent<IRepStackProps> = ({ role }) => {
  // chech the role if role rep schedular tab is not render
  return role === "REP" ? <BottomBar /> : <BottomBar adviser={true} />;
};

export default RepStack;
