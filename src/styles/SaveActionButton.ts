import globalStyles from "./GlobalStyles";

const nativewindStyles = {
  globalStyles,
  container: `${globalStyles.containers.flex_end} mb-2`,
  pressableSave: {
    container: (disabled: boolean) => {
      return `
    ${globalStyles.buttons.rounded.md} 
    ${disabled ? "bg-primaryGray" : "bg-primaryPurple"}
    ${globalStyles.containers.flex_between_row}
    `;
    },
    text: `${globalStyles.text.text_White_md} font-bold`,
  },
};

export default nativewindStyles;
