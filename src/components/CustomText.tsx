import { Text, useColorScheme } from "react-native";
import React from "react";

type TextProps = {
  className?: string;
  value: any;
  color?: string;
};

const CustomText: React.FC<TextProps> = ({
  className,
  value,
  color = "text-primaryBlack",
}) => {
  return (
    <Text className={`${className} ${color}`} allowFontScaling={false}>
      {value}
    </Text>
  );
};

export default CustomText;
