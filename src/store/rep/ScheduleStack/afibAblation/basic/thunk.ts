// thunk.ts
import { createAsyncThunk } from "@reduxjs/toolkit";
import api from "../../../../../api/api";
import { IAfibBasic } from "./types";

const fetchRepAfibAblationBasic = createAsyncThunk(
  "tasks/fetchRepAfibAblationBasic",
  async ({ case_id }: { case_id: string }, thunkAPI) => {
    try {
      const response = await api.private.get(
        api.routes.privateRoutes.rep.afib_basic_get(case_id)
      );

      if (response.data && response.data.result) {
        return response.data.result;
      } else {
        return thunkAPI.rejectWithValue(
          'Invalid response format: Missing "result" field'
        );
      }
    } catch (error: any) {
      if (error.response) {
        if (error.response.status === 404) {
        }
        return thunkAPI.rejectWithValue(
          error.response.data.error_description || "Failed to fetch Afib Basics"
        );
      } else if (error.request) {
        console.error("No response received:", error.request);
        return thunkAPI.rejectWithValue("No response received from the server");
      } else {
        console.error("Error occurred:", error.message);
        return thunkAPI.rejectWithValue(
          error.message || "An unknown error occurred"
        );
      }
    }
  }
);

const putAfibAblationBasic = createAsyncThunk(
  "tasks/putAfibAblationBasic",
  async ({ case_id, payload }: { case_id: string; payload: any }, thunkAPI) => {
    try {
      const response = await api.private.put(
        api.routes.privateRoutes.rep.afib_basic_put(case_id),
        payload
      );

      if (response.data && response.data.result) {
        return response.data.result;
      }
    } catch (error: any) {
      if (error.response) {
        if (error.response.status === 404) {
        }

        return thunkAPI.rejectWithValue("Failed to PUT Afib Basics");
      } else if (error.request) {
        console.error("No response received:", error.request);
        return thunkAPI.rejectWithValue("No response received from the server");
      } else {
        console.error("Error occurred:", error.message);
        return thunkAPI.rejectWithValue(
          error.message || "An unknown error occurred"
        );
      }
    }
  }
);

export { fetchRepAfibAblationBasic, putAfibAblationBasic };
