interface PatientCase {
  case_id: string;
  patient: {
    afib_ablation: boolean;
    age: number;
    anticoagulation: string | null;
    cha2ds2_vasc: {
      score: number;
      calculation: {
        age: number;
        congestive_heart_failure: {
          isChecked: boolean;
          value: number;
        };
        diabetes_mellitus: {
          isChecked: boolean;
          value: number;
        };
        hypertension: {
          isChecked: boolean;
          value: number;
        };
        lv_dysfunc: {
          isChecked: boolean;
          value: number;
        };
        sex: number;
        stroke: {
          isChecked: boolean;
          value: number;
        };
        thromboembolic_event: {
          isChecked: boolean;
          value: number;
        };
        tia: {
          isChecked: boolean;
          value: number;
        };
        vascular_disease: {
          isChecked: boolean;
          value: number;
        };
      };
    };
    cta: boolean;
    tee: boolean;
    id: string;
    name: string;
    rationale: string;
    referring_provider: any;
    sex: string;
  };
  procedure_date: string;
  procedure_time: string;
}

interface IinitialState {
  schedules: PatientCase[] | null;
  userDetails: PatientCase[] | null;
  selectedPatient: PatientCase | null;
  chadScore: {
    congestive_heart_fail: boolean;
    lv_dysfunc: boolean;
    hypertension: boolean;
    diabetes_mellitus: boolean;
    stroke: boolean;
    tia: boolean;
    thromboembolic_event: boolean;
    vascular_disease: boolean;
  } | null;
  loaders: {
    scheduleLoader: boolean;
    chadScoreLoader: boolean;
  };
  errors: {
    scheduleError: string | null;
    chadScoreError: string | null;
  };
}

export type { IinitialState };
