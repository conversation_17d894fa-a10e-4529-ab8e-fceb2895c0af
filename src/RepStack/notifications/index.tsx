import React from 'react';
import { createStackNavigator } from '@react-navigation/stack';
import Notifications from './screens/NotificationsScreen';


const NotificationsStack = createStackNavigator();

export default function NotificationsScreenStack(): React.JSX.Element {
  return (
    <NotificationsStack.Navigator>
      <NotificationsStack.Screen name="Notifications" component={Notifications} />
    </NotificationsStack.Navigator>
  );
}

