import { createSlice } from "@reduxjs/toolkit";
import initialState from "./initialstate";
import { fetchLaaoImplantDetails, putLaaoImplantDetails } from "./thunk";

const laaoimplantSlice = createSlice({
  name: "laaoimplant",
  initialState,
  reducers: {
    setImplantUserDetails: (state, action) => {
      state.userDetails = {
        ...state.userDetails,
        ...action.payload,
      };
    },

    resetImplantState: (state) => {
      state.laaoImplantDetails = null;
      state.userDetails = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchLaaoImplantDetails.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchLaaoImplantDetails.fulfilled, (state, action) => {
        state.loading = false;
        state.laaoImplantDetails = action.payload;
        state.error = null;
      })
      .addCase(fetchLaaoImplantDetails.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });

    // builder
    //   .addCase(putLaaoImplantDetails.pending, (state) => {
    //     state.loading = true;
    //   })
    //   .addCase(putLaaoImplantDetails.fulfilled, (state, action) => {
    //     state.loading = false;
    //     state.error = null;
    //   })
    //   .addCase(putLaaoImplantDetails.rejected, (state, action) => {
    //     state.loading = false;
    //     state.error = action.payload as string;
    //   });
  },
});

export const { setImplantUserDetails, resetImplantState } =
  laaoimplantSlice.actions;

export default laaoimplantSlice.reducer;
