interface Procedure {
  id: string;
  experience: number;
}

interface ImplantingPhysician {
  first_name: string;
  last_name: string;
  middle_name: string;
  credential: string;
  npi_number: string;
  email_id: string;
  phone_number: string;
  fax_number: string;
  address: string;
  city: string;
  state: string;
  zip_code: string;
  procedure: Procedure[];
  experience: string;
}

export type { ImplantingPhysician, Procedure };
