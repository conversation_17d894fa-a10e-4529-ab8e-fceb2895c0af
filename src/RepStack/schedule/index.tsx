import React, { useRef } from "react";
import { createStackNavigator } from "@react-navigation/stack";
import PatientsScreen from "./screens/PatientsScreen";
import ScheduleScreen from "./screens/ScheduleScreen";
import SettingsScreen from "./screens/SettingsScreen";
import PatientDetailsScreen from "./screens/PatientDetailsScreen";
import EmptyScreen from "./screens/EmptyScreen";
import ImageViewerScreen from "./screens/ImageViewer";
import AnesthesiaScreen from "./screens/AnesthesiaScreen";
import LAAAnatomyScreen from "./screens/LaaAnatomyScreen";
import CTAScreen from "./screens/CTAScreen";
import TEEScreen from "./screens/TEEScreen";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import WebViewer from "./screens/WebViewScreen";
import PDFViewer from "./screens/PdfViewer";
import { Text, TouchableOpacity, View } from "react-native";
import { MatIcon } from "../../utils";
import {
  showAddCaseModal,
  showAddPatientModal,
} from "../../store/rep/ScheduleStack/schedules";
import { useAddCaseModal } from "./hooks/schedulesHooks";
import { AppDispatch } from "../../store";
import { useDispatch } from "react-redux";
import BottomSheetComponent, {
  BottomSheetRefProps,
} from "../../components/BottomSheetComponent";
import CaseInfoCard from "../../components/CaseInfoCard";
import { useSelectedPatient } from "./hooks/PatientsHooks";
import { usePreopDetails } from "./hooks/patientDetailsHooks";
import { useChatBotState } from "../components/hooks";
import AddCaseBottomSheet from "./components/AddCaseBottomSheet";
import { useNavigation } from "@react-navigation/native";

const ScheduleStack = createStackNavigator();

export default function ScheduleScreenStack(): React.JSX.Element {
  const details = usePreopDetails();

  const navigation = useNavigation();

  const dispatch = useDispatch<AppDispatch>();

  const bottomRef = useRef<BottomSheetRefProps>(null);

  const [isBottomSheetOpen, setIsBottomSheetOpen] = React.useState(false);

  const handleBottomSheetOpen = () => {
    setIsBottomSheetOpen(true);
    bottomRef.current?.open();
  };

  const handleBottomSheetClose = () => {
    bottomRef.current?.close();
    setIsBottomSheetOpen(false);
  };

  // const handleBottomSheetChange = (index: number) => {
  //   // Assuming the first snap point (0 index) is the "collapsed" state
  //   setIsBottomSheetOpen(index !== 0);
  // };

  return (
    <>
      <ScheduleStack.Navigator>
        <ScheduleStack.Screen
          name="Schedule"
          component={ScheduleScreen}
          options={{
            headerShown: true,
            // headerRight: () => (
            //   <TouchableOpacity
            //     className="mr-2"
            //     onPress={() => {
            //       dispatch(showAddCaseModal(true));
            //     }}
            //   >
            //     {MatIcon("plus-circle", "#8143d9", 32)}
            //   </TouchableOpacity>
            // ),
            headerRight: () => (
              <View className="flex-row items-center mr-2">
                {/* <TouchableOpacity
                  className="mr-2"
                  onPress={() => {
                    dispatch(showAddCaseModal(true));
                  }}
                >
                  {MatIcon("domain-plus", "#8143d9", 27)}
                </TouchableOpacity> */}
                <TouchableOpacity
                  className="mr-2"
                  onPress={() => {
                    navigation.navigate("ScheduleTab", {
                      screen: "Settings",
                    });
                  }}
                >
                  {MatIcon("office-building-cog", "#8143d9", 27)}
                </TouchableOpacity>

                <TouchableOpacity
                  className="mr-2"
                  onPress={() => {
                    dispatch(showAddPatientModal(true));
                  }}
                >
                  {MatIcon("account-plus", "#8143d9", 27)}
                </TouchableOpacity>
              </View>
            ),
          }}
        />
        <ScheduleStack.Screen name="Patients" component={PatientsScreen} />
        <ScheduleStack.Screen
          name="Patient Details"
          component={PatientDetailsScreen}
          // options={{headerBackTitle: 'Homepage'}}
          options={{
            headerRight: () => (
              <TouchableOpacity
                style={{ marginRight: 16 }}
                onPress={() => handleBottomSheetOpen()}
              >
                {MatIcon("information", "#8143d9", 32)}
              </TouchableOpacity>
            ),
          }}
        />
        <ScheduleStack.Screen name="Settings" component={SettingsScreen} />
        {/* <ScheduleStack.Screen
          name="AddCaseScreen"
          component={AddCaseBottomSheet}
          options={{ title: "Add Case" }}
        /> */}
        <ScheduleStack.Screen
          name="CTA"
          component={CTAScreen}
          options={{ title: "PRE-OP CTA" }}
        />
        <ScheduleStack.Screen
          name="TEE"
          component={TEEScreen}
          options={{ title: "PRE-OP TEE" }}
        />
        <ScheduleStack.Screen name="Empty" component={EmptyScreen} />
        <ScheduleStack.Screen
          name="Image Viewer"
          component={ImageViewerScreen}
        />
        <ScheduleStack.Screen name="Anesthesia" component={AnesthesiaScreen} />
        <ScheduleStack.Screen
          name="LAAAnatomyScreen"
          component={LAAAnatomyScreen}
        />
        <ScheduleStack.Screen
          name="WebViewer"
          component={WebViewer}
          options={{ title: "" }}
        />

        <ScheduleStack.Screen
          name="PdfViewer"
          component={PDFViewer}
          options={{ title: "" }}
        />
      </ScheduleStack.Navigator>

      <BottomSheetComponent
        ref={bottomRef}
        snapPoints={["70%"]}
        backgroundColor="white"
        onClose={handleBottomSheetClose}
      >
        {isBottomSheetOpen && (
          <CaseInfoCard
            name={details?.patient?.name || ""}
            age={details?.patient?.age || 0}
            gender={details?.patient?.sex || ""}
            procedureDate={details?.procedure_date || ""}
            procedureTime={details?.procedure_time || ""}
            hospital={details?.site?.selected?.name || ""}
            implantingPhysician={
              details?.implanting_physician?.selected?.name || ""
            }
            case_id={details?.case_id || ""}
          />
        )}
      </BottomSheetComponent>
    </>
  );
}
