import * as React from "react";
import { Pressable, Text, View, TouchableOpacity } from "react-native";
import nativewindStyles from "../styles/SaveActionButton";
import { MatIcon } from "../utils";

interface ISaveActionButtonProps {
  disabled: boolean;
  onPress?: () => void;
  onCancel?: () => void;
}

const SaveActionButton: React.FunctionComponent<ISaveActionButtonProps> = ({
  disabled,
  onPress,
  onCancel,
}) => {
  return (
    <View className="flex-row gap-2 justify-end items-end">
      {!disabled && (
        <TouchableOpacity
          onPress={() => onCancel && onCancel()}
          className="border border-primaryPurple px-6 py-2 rounded-md bg-primaryBg"
        >
          <Text className="text-primaryPurple">Cancel</Text>
        </TouchableOpacity>
      )}

      <TouchableOpacity
        onPress={() => onPress && onPress()}
        disabled={disabled}
        className={`${nativewindStyles.pressableSave.container(disabled)}`}
      >
        {MatIcon("content-save-settings", "white", 20)}
        <Text className={`${nativewindStyles.pressableSave.text}`}>Save</Text>
      </TouchableOpacity>
    </View>
  );
};

export default SaveActionButton;
