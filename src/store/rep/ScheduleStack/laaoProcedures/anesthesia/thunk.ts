import { createAsyncThunk } from "@reduxjs/toolkit";
import api from "../../../../../api/api";

const fetchAnesthesiaDetails = createAsyncThunk(
  "schedule/fetchAnesthesiaDetails",
  async ({ case_id }: { case_id: string }, thunkAPI) => {
    try {
      const response = await api.private.get(
        api.routes.privateRoutes.rep.anesthesia_get(case_id)
      );

      if (response.data && response.data.result) {
        return response.data.result;
      } else {
        return thunkAPI.rejectWithValue(
          'Invalid response format: Missing "result" field'
        );
      }
    } catch (error: any) {
      console.error("Error caught during API call:", error.response.data);
      console.error("Error caught during API call:", error.message || error);
      return thunkAPI.rejectWithValue(
        error.response?.data?.message || "Failed to fetch anesthesia details"
      );
    }
  }
);

const putAnesthesiaDetails = createAsyncThunk(
  "schedule/putAnesthesiaDetails",
  async (
    { case_details_id, payload }: { case_details_id: number; payload: any },
    thunkAPI
  ) => {
    try {
      console.info("putAnesthesiaDetails", payload);
      const response = await api.private.put(
        api.routes.privateRoutes.rep.anesthesia_put(case_details_id),
        payload
      );
      if (response.data && response.data.result) {
        return response.data.result;
      } else {
        return thunkAPI.rejectWithValue(
          'Invalid response format: Missing "result" field'
        );
      }
    } catch (error: any) {
      console.error(
        "Error caught during API call:",
        error.response.data || error
      );
      return thunkAPI.rejectWithValue(
        error.response?.data?.message || "Failed to fetch anesthesia details"
      );
    }
  }
);
export { fetchAnesthesiaDetails, putAnesthesiaDetails };
