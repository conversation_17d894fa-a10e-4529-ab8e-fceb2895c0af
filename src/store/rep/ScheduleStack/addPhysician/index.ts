import { createSlice, PayloadAction } from "@reduxjs/toolkit";
import { ImplantingPhysician } from "./types";
import {
  fetchProcedureTypes,
  fetchProviderCredentials,
  fetchProviderExperience,
  fetchSpecificImplantingPhysician,
  putImplantingPhysician,
  postImplantingPhysician,
} from "./thunk";
import initialState from "./initialstate";

const implantingPhysiciansSlice = createSlice({
  name: "implantingPhysicians",
  initialState,
  reducers: {
    setImplantingPhysicianDetails: (state, action) => {
      state.userDetails = {
        ...state.userDetails,
        ...action.payload,
      };
    },

    resetImplantingPhysicianDetails: (state) => {
      state.userDetails = null;
    },
    setActiveTab: (
      state,
      action: PayloadAction<"sites" | "implanting-physicians">
    ) => {
      state.activeTab = action.payload;
    },
    showAddPhysicianModal: (state, action: PayloadAction<boolean>) => {
      state.showAddPhysicianModal = action.payload;
    },

    setImplantingPhysicianList: (state, action) => {
      state.implantingPhysicianList = action.payload;
    },

    setSelectedSite: (state, action: PayloadAction<string>) => {
      state.selectedSite = action.payload;
    },
    setSelectedPhysician: (state, action: PayloadAction<string>) => {
      state.selectedPhysician = action.payload;
    },
  },
  extraReducers: (builder) => {
    // Procedure Types
    builder.addCase(fetchProcedureTypes.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(fetchProcedureTypes.fulfilled, (state, action) => {
      state.loading = false;
      state.procedureTypes = action.payload;
    });
    builder.addCase(fetchProcedureTypes.rejected, (state, action) => {
      state.loading = false;
      state.error = action.error.message || "Failed to fetch procedure types";
      state.procedureTypes = [];
    });

    // Provider Credentials
    builder.addCase(fetchProviderCredentials.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(fetchProviderCredentials.fulfilled, (state, action) => {
      state.loading = false;
      state.providerCredentials = action.payload;
    });
    builder.addCase(fetchProviderCredentials.rejected, (state, action) => {
      state.loading = false;
      state.error =
        action.error.message || "Failed to fetch provider credentials";
      state.providerCredentials = [];
    });

    // Provider Experience
    builder.addCase(fetchProviderExperience.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(fetchProviderExperience.fulfilled, (state, action) => {
      state.loading = false;
      state.providerExperience = action.payload;
    });
    builder.addCase(fetchProviderExperience.rejected, (state, action) => {
      state.loading = false;
      state.error =
        action.error.message || "Failed to fetch provider experience";
      state.providerExperience = [];
    });

    // Fecth Specific Physician
    builder.addCase(fetchSpecificImplantingPhysician.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(
      fetchSpecificImplantingPhysician.fulfilled,
      (state, action: PayloadAction<ImplantingPhysician>) => {
        state.implantingPhysicianDetails = action.payload;
        state.userDetails = action.payload;
        state.loading = false;
      }
    );
    builder.addCase(
      fetchSpecificImplantingPhysician.rejected,
      (state, action) => {
        state.loading = false;
        state.error = action.error.message || "Failed to fetch site";
      }
    );

    //put implantingPhysician
    builder.addCase(putImplantingPhysician.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(
      putImplantingPhysician.fulfilled,
      (state, action: PayloadAction<ImplantingPhysician>) => {
        state.implantingPhysicianDetails = action.payload;
        state.userDetails = action.payload;
        state.loading = false;
      }
    );
    builder.addCase(putImplantingPhysician.rejected, (state, action) => {
      state.loading = false;
      state.error = action.error.message || "Failed to update site";
    });

    // Add Site
    builder.addCase(postImplantingPhysician.pending, (state) => {
      state.loading = true;
      state.error = null;
    });
    builder.addCase(
      postImplantingPhysician.fulfilled,
      (state, action: PayloadAction<ImplantingPhysician>) => {
        state.implantingPhysicianDetails = action.payload;
        state.userDetails = action.payload;
        state.loading = false;
      }
    );
    builder.addCase(postImplantingPhysician.rejected, (state, action) => {
      state.loading = false;
      state.error = action.error.message || "Failed to add site";
    });
  },
});

export const {
  setActiveTab,
  showAddPhysicianModal,
  setSelectedSite,
  setSelectedPhysician,
  resetImplantingPhysicianDetails,
  setImplantingPhysicianDetails,
  setImplantingPhysicianList,
} = implantingPhysiciansSlice.actions;
export default implantingPhysiciansSlice.reducer;
