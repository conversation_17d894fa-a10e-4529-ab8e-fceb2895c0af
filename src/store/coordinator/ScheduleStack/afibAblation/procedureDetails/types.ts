interface IProcedureDetails {
  sheath_utilized: {
    selected: {
      id: string | null;
      name: string | null;
    };
    options: {
      id: string;
      name: string;
    }[];
  };
  act_sec: number | null;
  pvi_lesioin_sets: {
    selected: {
      id: string | null;
      name: string | null;
      catheter_used: {
        id: string | null;
        name: string | null;
        csu_pvi_other_complications?: string | null;
      }[];
    };
    options: {
      id: string;
      name: string;
      catheter_used: {
        id: string | null;
        name: string | null;
      }[];
    }[];
  };
  add_lesioin_sets: {
    selected: {
      id: string | null;
      name: string | null;
    };
    options: {
      id: string;
      name: string;
    }[];
  };
  complication: {
    selected: {
      id: string;
      name: string;
      complication_other: string;
      complication_present: boolean;
    };
    options: {
      id: string;
      name: string;
    }[];
  };
  fluroscopy: {
    total_fluoro_time: number | null;
    total_fluoro: number | null;
    contrast: number | null;
  };
}

export type { IProcedureDetails };
