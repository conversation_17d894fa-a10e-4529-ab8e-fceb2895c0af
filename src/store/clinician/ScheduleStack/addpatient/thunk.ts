import { createAsyncThunk } from "@reduxjs/toolkit";
import api from "../../../../api/api";
import { IPostPatient } from "./types";

const fetchReferringProviders = createAsyncThunk(
  "addpatient/fetchReferringProviders",
  async (_, thunkAPI) => {
    try {
      const response = await api.private.get(
        api.routes.privateRoutes.clinician.referring_providers_get
      );

      if (response.data && response.data.result) {
        return response.data.result;
      } else {
        return thunkAPI.rejectWithValue(
          'Invalid response format: Missing "result" field'
        );
      }
    } catch (error: any) {
      if (error.response) {
        console.error("Error fetching referring providers:");
        return thunkAPI.rejectWithValue("Failed to fetch referring providers");
      } else if (error.request) {
        console.error("No response received:", error.request);
        return thunkAPI.rejectWithValue("No response received from the server");
      } else {
        console.error("Error occurred:", error.message);
        return thunkAPI.rejectWithValue("An unknown error occurred");
      }
    }
  }
);

const fetchImplantingPhysicians = createAsyncThunk(
  "addpatient/fetchImplantingPhysicians",
  async (_, thunkAPI) => {
    try {
      const response = await api.private.get(
        api.routes.privateRoutes.clinician.implanting_physicians_get
      );

      if (response.data && response.data.result) {
        return response.data.result;
      } else {
        return thunkAPI.rejectWithValue(
          'Invalid response format: Missing "result" field'
        );
      }
    } catch (error: any) {
      if (error.response) {
        console.error(
          "Error fetching implanting physicians:",
          error.response.data
        );
        return thunkAPI.rejectWithValue(
          "Failed to fetch implanting physicians"
        );
      } else if (error.request) {
        console.error("No response received:", error.request);
        return thunkAPI.rejectWithValue("No response received from the server");
      } else {
        console.error("Error occurred:", error.message);
        return thunkAPI.rejectWithValue("An unknown error occurred");
      }
    }
  }
);

const postPatientDetails = createAsyncThunk(
  "addpatient/putpostPatientDetails",
  async ({ payload }: { payload: IPostPatient }, thunkAPI) => {
    try {
      const response = await api.private.post(
        api.routes.privateRoutes.clinician.patient_post(),
        payload
      );

      if (response.data && response.data.result) {
        return response.data.result;
      }
    } catch (error: any) {
      console.error(error.response.data);
      if (error.response) {
        if (error.response.status === 400) {
        }
        return thunkAPI.rejectWithValue("Failed to post patient details");
      } else if (error.request) {
        console.error("No response received:", error.request);
        return thunkAPI.rejectWithValue("No response received from the server");
      } else {
        console.error("Error occurred:", error.message);
        return thunkAPI.rejectWithValue("An unknown error occurred");
      }
    }
  }
);

export {
  fetchReferringProviders,
  fetchImplantingPhysicians,
  postPatientDetails,
};
