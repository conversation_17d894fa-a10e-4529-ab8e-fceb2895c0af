import React, { useState, useEffect } from "react";
import WebView from "react-native-webview";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { useDispatch } from "react-redux";
import { AppDispatch } from "../../../store";
import { fetchProfile } from "../../../store/coordinator/MyProfile/MyProfileScreen/thunk";
import { StyleSheet } from "react-native";

const ChatbotTest: React.FC = () => {
  const [accessToken, setAccessToken] = useState("");
  const [userId, setUserId] = useState("");
  const dispatch = useDispatch<AppDispatch>();

  useEffect(() => {
    const fetchAccessToken = async () => {
      try {
        const token = await AsyncStorage.getItem("accessToken");
        if (token) {
          setAccessToken(token);
        } else {
          console.error("Access token not found");
        }
      } catch (error) {
        console.error("Error retrieving access token:", error);
      }
    };

    const fetchDetails = async () => {
      const res = await dispatch(fetchProfile());
      setUserId(res?.payload?.user_id);
    };

    fetchAccessToken();
    fetchDetails();
  }, []);

  return (
    <WebView
      source={{
        uri: `http://********:5500/chatbot/index.html?access_token=${accessToken}&user_id=${userId}&platform=mobile`,
      }}
      style={styles.webview}
      cacheEnabled={false}
      cacheMode={"LOAD_NO_CACHE"}
      javaScriptEnabled={true}
      domStorageEnabled={true}
      onMessage={(event) => {
        try {
          const navigationData = JSON.parse(event.nativeEvent.data);
        } catch (error) {
          console.error("Error parsing navigation message:", error.message);
        }
      }}
    />
  );
};

export default ChatbotTest;

const styles = StyleSheet.create({
  webview: {
    flex: 1,
    minHeight: 1000,
  },
});
