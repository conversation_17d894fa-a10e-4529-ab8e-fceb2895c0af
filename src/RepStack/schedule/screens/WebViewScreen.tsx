import * as React from "react";
import { View, TouchableOpacity, Text, StyleSheet } from "react-native";
import WebView from "react-native-webview";
import { useNavigation } from "@react-navigation/native";

interface IWebViewerProps {
  route: {
    params: {
      link: string; // Link passed through navigation
    };
  };
}

const WebViewer: React.FunctionComponent<IWebViewerProps> = ({ route }) => {
  const { link } = route.params; // Extract the link from navigation params
  const navigation = useNavigation();

  return (
    <View style={styles.container}>
      {/* <TouchableOpacity
        style={styles.backButton}
        onPress={() => navigation.goBack()}
      >
        <Text style={styles.backButtonText}>Back</Text>
      </TouchableOpacity> */}
      {/* WebView */}
      <WebView source={{ uri: link }} style={styles.webview} />
    </View>
  );
};

export default WebViewer;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  backButton: {
    padding: 10,
    backgroundColor: "#f8f8f8",
    alignItems: "center",
    borderBottomWidth: 1,
    borderBottomColor: "#ccc",
  },
  backButtonText: {
    color: "#007BFF",
    fontWeight: "bold",
  },
  webview: {
    flex: 1,
  },
});
