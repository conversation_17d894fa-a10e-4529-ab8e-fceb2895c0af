import * as React from "react";
import { View, Text, TouchableOpacity } from "react-native";
import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons";
import { genUUID } from "../utils";

interface IOptionSelectorProps {
  options: {
    label: string;
    value: any;
    icon?: string; // Icon name for MaterialCommunityIcons
  }[];
  selected: any;
  onSelect: (option: any) => void;
  width?: string;
  customSelectedColor?: string;
  customSelectedValue?: string;
  disabled?: boolean; // Add the disabled prop
}

const OptionSelector: React.FunctionComponent<IOptionSelectorProps> = (
  props
) => {
  return (
    <View className="flex-row flex-wrap justify-between items-center">
      {props.options?.map((option) => (
        <TouchableOpacity
          key={genUUID()}
          className={`
            px-8
            py-4
            mb-4
            rounded
            ${
              props.selected === option.value
                ? props.customSelectedColor
                  ? props.customSelectedValue === option.value
                    ? `${props.customSelectedColor}`
                    : "bg-primaryPurple"
                  : "bg-primaryPurple"
                : "bg-primaryBg"
            }
            ${
              props.width
                ? `w-[${props.width}]`
                : "w-[45%] min-w-[45%] max-w-[45%]"
            }
            
            `}
          onPress={() => !props.disabled && props.onSelect(option)}
          disabled={props.disabled} // Disable the button
        >
          <View className="flex-row items-center justify-center gap-2">
            {/* Render Icon if provided */}
            {option.icon && (
              <View className="p-2 bg-primaryWhite rounded-full">
                <MaterialCommunityIcons
                  name={option.icon}
                  size={22}
                  color={
                    props.selected === option.value ? "#8143d9" : "#000000"
                  }
                />
              </View>
            )}
            <Text
              className={`
                text-center
                text-md
                ${
                  props.selected === option.value
                    ? "text-primaryWhite font-bold"
                    : "text-primaryPurple"
                }
              `}
            >
              {option.label}
            </Text>
          </View>
        </TouchableOpacity>
      ))}
    </View>
  );
};

export default OptionSelector;
