import { createSlice } from "@reduxjs/toolkit";
import { fetchcaseSynopsis } from "./thunk";
import initialState from "./initialstate";

const reportSlice = createSlice({
  name: "report",
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchcaseSynopsis.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchcaseSynopsis.fulfilled, (state, action) => {
        state.loading = false;
        state.caseSynopsis = action.payload;
      })
      .addCase(fetchcaseSynopsis.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
        state.caseSynopsis = null;
      });
  },
});

export const {} = reportSlice.actions;
export default reportSlice.reducer;
