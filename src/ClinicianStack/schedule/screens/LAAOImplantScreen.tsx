import * as React from "react";
import ScreenWrapper from "../../../components/ScreenWrapper";
import { Text, TouchableOpacity, View } from "react-native";
import { useNavigation } from "@react-navigation/native";
import SearchablePicker from "../../../components/SearchablePicker";
import Heading from "../../../components/Heading";
import ToggleButton from "../../../components/ToggleButton";
import CustomInput from "../../../components/CustomTextInput";
import CustomCard from "../../../components/CustomCard";
import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons";

import LineSeperator from "../../../components/LineSeperator";
import {
  fetchLaaoImplantDetails,
  putLaaoImplantDetails,
} from "../../../store/clinician/ScheduleStack/laaoimplant/thunk";
import { setImplantUserDetails } from "../../../store/clinician/ScheduleStack/laaoimplant";
import { useFocusEffect } from "@react-navigation/native";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch } from "../../../store";
import Loader from "../../../components/Loader";
import { useClinicianSelectedPatient } from "../hooks/schedulesHooks";
import {
  useLoaderAndError,
  useLaaoImplantUserDetails,
  useLaaoImplantDetails,
} from "../hooks/laaoimplantHooks";
import {
  formatLaaoImplantOptions,
  formatPassCriteriaOptions,
} from "../../../utils";
import { findLaaoImplantDiff } from "../hooks/laaoimplantHooks";
import SaveActionButton from "../../../components/SaveActionButton";
import { parseIntInput, parseFloatInput } from "../../../utils";
import Error from "../../../components/Error";
import PopupModal from "../../../components/Popup";
import CustomCheckbox from "../../../components/CustomCheckboxComponent";
import RadioWithDropdown from "../../../components/RadioWithDropDown";
import CustomRadio from "../../../components/CustomRadio";
import {
  usePassCriteriaDetails,
  usePassCriteriaUserDetails,
  useLoaderAndError as usePassCriteriaLoaderAndError,
  findPassCriteriaDiff,
} from "../hooks/passcriteriaHooks";
import CompressionCalculator from "../components/CompressionCalculator";
import OptionSelector from "../../../components/OptionSelector";
import {
  setPassCriteriaUserDetails,
  setSelectedDeviceSize,
} from "../../../store/clinician/ScheduleStack/passcriteria";
import {
  fetchPassCriteriaDetails,
  putPassCriteriaDetails,
} from "../../../store/clinician/ScheduleStack/passcriteria/thunk";
import { setLastSavedData, setSaveStatus } from "../../../store/services";
import { simpleAutoSave } from "../../../services/simpleAutoSave";
import {
  fetchFluoroscopyDetails,
  putFluoroscopyDetails,
} from "../../../store/clinician/ScheduleStack/fluoroscopy/thunk";
import { setFluoroscopyUserDetails } from "../../../store/clinician/ScheduleStack/fluoroscopy";
import {
  useFluoroscopyUserDetails,
  useFluoroscopyDetails,
  useLoaderAndError as useFluoroscopyLoaderAndError,
  findFluoroscopyDiff,
} from "../hooks/fluoroscopyHooks";
import { formatFluoroscopyOptions } from "../../../utils";

interface ILAAOImplantScreenProps {}

const LAAOImplantScreen: React.FunctionComponent<
  ILAAOImplantScreenProps
> = () => {
  const complication_other_selected_id = "676916a32e31f611af713ee8";
  const dispatch = useDispatch<AppDispatch>();
  const selectedPatient = useClinicianSelectedPatient();
  const { loader, error } = useLoaderAndError();
  const { loader: passCriteriaLoader, error: passCriteriaError } =
    usePassCriteriaLoaderAndError();
  const { loader: fluoroscopyLoader, error: fluoroscopyError } =
    useFluoroscopyLoaderAndError();
  const userDetails = useLaaoImplantUserDetails();
  const laaoImplantDetails = useLaaoImplantDetails();
  const diff = findLaaoImplantDiff();

  const [modalVisible, setModalVisible] = React.useState(false);

  const [popupMsg, setPopupMsg] = React.useState<string[]>([]);
  const navigation = useNavigation();
  const [popupMsgType, setPopupMsgType] = React.useState("");
  const passCriteriaDiff = findPassCriteriaDiff();

  // Fluoroscopy state
  const fluoroscopyUserDetails = useFluoroscopyUserDetails();
  const fluoroscopyDetails = useFluoroscopyDetails();
  const fluoroscopyDiff = findFluoroscopyDiff();
  const [otherInput, setOtherInput] = React.useState("");

  const {
    accessSheathType,
    accessSheathTypeSelected,
    selectDeviceValue,
    suitabilityTug,
    partialRecaptures,
    number_partial_recaptures,
    manipulationType,
    deviceDeployed,
    deviceNotDeployedInput,
    caseAborted,
    caseAbortedInput,
    productChargable,
    productChargableInput,
    complications_type,
    complication_other,
    complication_selected_id,
    complications_toggler,
    selectedDevice,
    deviceType,
    complication_selected_name,
  } = formatLaaoImplantOptions(userDetails);

  const passCriteriaUserDetails = usePassCriteriaUserDetails();
  const passCriteriaDetails = usePassCriteriaDetails();

  const {
    device_sizes,
    size_selected,
    width0,
    width45,
    width90,
    width135,
    position,
    anchor,
    leak,
    leak_value,
  } = formatPassCriteriaOptions(passCriteriaUserDetails);

  // Fluoroscopy formatting
  const { creatinine_value, fluoro_time, fluoro_total, total_contrast } =
    formatFluoroscopyOptions(fluoroscopyUserDetails);

  const filteredOptions =
    deviceType?.find((item: any) => item.value === selectedDevice)?.value2 ||
    [];

  const validator = (isAutoSave = false) => {
    setPopupMsg([]);

    var temp = false;
    var messages = [];

    if (complications_toggler === "Yes" && !complication_selected_id) {
      messages.push("Please Select Complications");
      temp = true;
    }

    if (
      complications_toggler === "Yes" &&
      complication_selected_name?.toLowerCase() === "other" &&
      !complication_other
    ) {
      messages.push("Please Enter Other Complications");
      temp = true;
    }

    if (productChargable === "No" && !productChargableInput) {
      messages.push("Please Enter Product Not Chargeable Rationale");
      temp = true;
    }

    if (caseAborted === "Yes" && !caseAbortedInput) {
      messages.push("Please Enter Case Aborted Rationale");
      temp = true;
    }

    if (deviceDeployed === "No" && !deviceNotDeployedInput) {
      messages.push("Please Enter Device Not Deployed Rationale");
      temp = true;
    }

    if (partialRecaptures === "Yes" && !number_partial_recaptures) {
      messages.push("Please Enter Number of Partial Recaptures");
      temp = true;
    }

    if (partialRecaptures === "Yes" && !manipulationType) {
      messages.push("Please Enter Manipulation Type");
      temp = true;
    }

    // For autosave, we'll skip the width and leak validations
    // These are not critical for saving progress
    if (!isAutoSave) {
      // Enhanced width validations - check that ALL width values are filled
      const widthValues = [
        { value: width0, name: "Width 0°" },
        { value: width45, name: "Width 45°" },
        { value: width90, name: "Width 90°" },
        { value: width135, name: "Width 135°" },
      ];

      const emptyWidths = widthValues.filter(
        (width) =>
          width.value === null || width.value === undefined || width.value === 0
      );

      if (emptyWidths.length > 0) {
        emptyWidths.forEach((width) => {
          messages.push(`${width.name} cannot be empty`);
        });
        temp = true;
      }

      // Additional validation: Check if all width values are filled before allowing save
      const allWidthsFilled = widthValues.every(
        (width) =>
          width.value !== null && width.value !== undefined && width.value > 0
      );

      if (!allWidthsFilled) {
        messages.push(
          "All compression calculator width values (0°, 45°, 90°, 135°) must be filled before saving"
        );
        temp = true;
      }

      // Leak validation
      if (
        (leak && leak_value === null) ||
        leak_value === undefined ||
        leak_value === 0
      ) {
        messages.push("Leak Value cannot be empty");
        temp = true;
      }
    }

    // Set popup messages if there are any
    if (messages.length > 0) {
      setPopupMsg(messages);
    }

    if (temp) {
      setModalVisible(true);
      return false;
    } else {
      return true;
    }
  };

  const sealValues = [
    {
      label: "No Leak",
      value: "No Leak",
    },
    {
      label: "Leak",
      value: "Leak",
    },
  ];

  interface SizeSelectorProps {
    deviceSizes: number[];
    conditionArray: number[];
    selectedSize: number | null;
    onPress: (size: any) => void;
  }

  const SizeSelector: React.FC<SizeSelectorProps> = ({
    deviceSizes,
    conditionArray,
    selectedSize,
    onPress,
  }) => {
    return (
      <View className="flex-row flex-wrap justify-between bg-primaryBg">
        {deviceSizes?.length > 0 &&
          deviceSizes.map((item, index) => {
            const isInConditionArray = conditionArray.includes(item);
            const isSelected = selectedSize === item;

            return (
              <TouchableOpacity
                key={index}
                onPress={() => onPress(item)}
                className={`
                    
                    ${
                      isSelected
                        ? "border-2 border-primaryPurple bg-green-3"
                        : "border-2 border-primaryBg"
                    }
                    px-3
                    py-2
                    rounded-md
                    `}
              >
                <Text
                  // style={isSelected ? { textDecorationLine: "underline" } : {}}
                  className={`
                      text-center
                      ${
                        isSelected
                          ? "font-bold text-primaryWhite"
                          : "text-primaryBlack"
                      }
                     
                      text-lg
                   `}
                >
                  {item}
                </Text>
              </TouchableOpacity>
            );
          })}
      </View>
    );
  };

  // const saveDetails = async () => {
  //   try {
  //     if (validator()) {
  //       const promises = [];

  //       if (diff.hasDiff) {
  //         if (!diff.currentValues.partial_recaptures) {
  //           delete diff.currentValues.partial_recaptures_manipulation;
  //           delete diff.currentValues.no_partial_recaptures;
  //         }

  //         if (diff.currentValues.device_deployed) {
  //           delete diff.currentValues.device_not_deployed_rationale;
  //         }

  //         if (!diff.currentValues.case_aborted) {
  //           delete diff.currentValues.case_aborted_rationale;
  //         }

  //         if (diff.currentValues.product_chargeable) {
  //           delete diff.currentValues.product_not_chargeable_rationale;
  //         }

  //         if (
  //           diff.currentValues.complication_id != complication_other_selected_id
  //         ) {
  //           delete diff.currentValues.complication_other;
  //         }

  //         if (!diff.currentValues.complication_present) {
  //           delete diff.currentValues.complication_other;
  //           delete diff.currentValues.complication_id;
  //         }

  //         promises.push(
  //           dispatch(
  //             putLaaoImplantDetails({
  //               case_details_id: userDetails?.case_detail_id,
  //               payload: diff.currentValues,
  //             })
  //           )
  //         );
  //       }

  //       if (passCriteriaDiff && passCriteriaDiff.hasDiff) {
  //         promises.push(
  //           dispatch(
  //             putPassCriteriaDetails({
  //               case_details_id: userDetails?.case_detail_id,
  //               payload: passCriteriaDiff.currentValues,
  //             })
  //           )
  //         );
  //       }

  //       const results = await Promise.all(promises);

  //       if (selectedPatient?.case_id) {
  //         if (diff.hasDiff) {
  //           const refetchRes = await dispatch(
  //             fetchLaaoImplantDetails({ case_id: selectedPatient.case_id })
  //           );
  //           if (refetchRes.payload) {
  //             dispatch(setImplantUserDetails(refetchRes.payload));
  //           }
  //         }

  //         if (passCriteriaDiff && passCriteriaDiff.hasDiff) {
  //           const refetchRes = await dispatch(
  //             fetchPassCriteriaDetails({ case_id: selectedPatient.case_id })
  //           );
  //           if (refetchRes.payload) {
  //             dispatch(setPassCriteriaUserDetails(refetchRes.payload));
  //           }
  //         }
  //       }
  //     }
  //   } catch (err) {
  //     console.error(err);
  //   }
  // };

  // Wrapper function for simpleAutoSave that handles only implant data
  const saveDetailsWrapper = async (data: any): Promise<void> => {
    // For simpleAutoSave, we only get one data parameter
    // We'll use this for the implant data only, not pass criteria

    // Pass true for isAutoSave to use less strict validation
    await saveDetails(data, null, true);
  };

  // Main save function that handles implant data, pass criteria data, and fluoroscopy data
  const saveDetails = async (
    implantData: any,
    passCriteriaData: any,
    fluoroscopyData: any = null,
    isAutoSave = true
  ): Promise<void> => {
    try {
      // Use different validation rules for autosave vs manual save
      const isValid = validator(isAutoSave);

      if (!isValid) {
        // For autosave, we'll still save the data but mark it as validation_failed
        // For manual save, we'll throw an error and show the popup
        dispatch(
          setSaveStatus({
            screenId: "laaoImplant",
            status: "validation_failed",
          })
        );
        setLocalImplantSaveStatus("validation_failed");

        if (!isAutoSave) {
          throw new Error("Validation failed");
        }
        // For autosave, we'll continue with the save operation
      }

      if (!userDetails?.case_detail_id) {
        throw new Error("Missing case_detail_id");
      }

      if (implantData) {
        const isImplantEmpty = Object.values(implantData).every(
          (val) => val === undefined
        );

        if (!isImplantEmpty) {
          const implantCopy = { ...implantData };

          if (!implantCopy.partial_recaptures) {
            delete implantCopy.partial_recaptures_manipulation;
            delete implantCopy.no_partial_recaptures;
          }

          if (implantCopy.device_deployed) {
            delete implantCopy.device_not_deployed_rationale;
          }

          if (!implantCopy.case_aborted) {
            delete implantCopy.case_aborted_rationale;
          }

          if (implantCopy.product_chargeable) {
            delete implantCopy.product_not_chargeable_rationale;
          }

          if (implantCopy.complication_id !== complication_other_selected_id) {
            delete implantCopy.complication_other;
          }

          if (!implantCopy.complication_present) {
            delete implantCopy.complication_other;
            delete implantCopy.complication_id;
          }

          const res = await dispatch(
            putLaaoImplantDetails({
              case_details_id: userDetails.case_detail_id,
              payload: implantCopy,
            })
          );

          if (res) {
            const refetchRes = await dispatch(
              fetchLaaoImplantDetails({ case_id: selectedPatient?.case_id })
            );

            if (refetchRes.payload) {
              dispatch(setImplantUserDetails(refetchRes.payload));
            }

            const refetchResPassCriteria = await dispatch(
              fetchPassCriteriaDetails({ case_id: selectedPatient?.case_id })
            );

            if (refetchResPassCriteria.payload) {
              dispatch(
                setPassCriteriaUserDetails(refetchResPassCriteria.payload)
              );
            }
          }
        }
      }

      if (passCriteriaData) {
        const isPassCriteriaEmpty = Object.values(passCriteriaData).every(
          (val) => val === undefined
        );

        if (!isPassCriteriaEmpty) {
          await dispatch(
            putPassCriteriaDetails({
              case_details_id: userDetails.case_detail_id,
              payload: { ...passCriteriaData },
            })
          );

          if (selectedPatient?.case_id) {
            await dispatch(
              fetchPassCriteriaDetails({ case_id: selectedPatient.case_id })
            ).then((res) => {
              if (res.payload) {
                dispatch(setPassCriteriaUserDetails(res.payload));
              }
            });
          }
        }
      }

      // Handle fluoroscopy data save
      if (fluoroscopyData) {
        const isFluoroscopyEmpty = Object.values(fluoroscopyData).every(
          (val) => val === undefined
        );

        if (!isFluoroscopyEmpty) {
          await dispatch(
            putFluoroscopyDetails({
              case_details_id: userDetails.case_detail_id,
              payload: { ...fluoroscopyData },
            })
          );

          if (selectedPatient?.case_id) {
            await dispatch(
              fetchFluoroscopyDetails({ case_id: selectedPatient.case_id })
            ).then((res) => {
              if (res.payload) {
                dispatch(setFluoroscopyUserDetails(res.payload));
              }
            });
          }
        }
      }
    } catch (err) {
      throw err;
    }
  };

  const handleCancel = () => {
    dispatch(
      setImplantUserDetails({
        ...userDetails,
        ...laaoImplantDetails,
      })
    );

    dispatch(
      setPassCriteriaUserDetails({
        ...passCriteriaUserDetails,
        ...passCriteriaDetails,
      })
    );

    dispatch(
      setFluoroscopyUserDetails({
        ...fluoroscopyUserDetails,
        ...fluoroscopyDetails,
      })
    );
  };

  useFocusEffect(
    React.useCallback(() => {
      const fetchDetails = async () => {
        if (selectedPatient?.case_id) {
          const resLaao = await dispatch(
            fetchLaaoImplantDetails({ case_id: selectedPatient.case_id })
          );
          const resPassCriteria = await dispatch(
            fetchPassCriteriaDetails({ case_id: selectedPatient.case_id })
          );
          const resFluoroscopy = await dispatch(
            fetchFluoroscopyDetails({ case_id: selectedPatient.case_id })
          );

          if (resLaao.payload) {
            dispatch(setImplantUserDetails(resLaao.payload));
          }
          if (resPassCriteria.payload) {
            dispatch(setPassCriteriaUserDetails(resPassCriteria.payload));
          }
          if (resFluoroscopy.payload) {
            dispatch(setFluoroscopyUserDetails(resFluoroscopy.payload));
          }
        }
      };
      fetchDetails();
    }, [dispatch, selectedPatient?.case_id])
  );

  // Get save status for LAAO Implant
  const implantSaveStatus = useSelector(
    (state: any) => state.common?.autoSave?.["laaoImplant"]?.status
  );

  // Get save status for Pass Criteria
  const passCriteriaSaveStatus = useSelector(
    (state: any) => state.common?.autoSave?.["passCriteria"]?.status
  );

  // Get save status for Fluoroscopy
  const fluoroscopyStatus = useSelector(
    (state: any) => state.common?.autoSave?.["fluoroscopy"]?.status
  );

  // Local state for fallback
  const [localImplantSaveStatus, setLocalImplantSaveStatus] =
    React.useState<string>("");
  const [localPassCriteriaSaveStatus, setLocalPassCriteriaSaveStatus] =
    React.useState<string>("");
  const [localFluoroscopySaveStatus, setLocalFluoroscopySaveStatus] =
    React.useState<string>("");

  // Determine effective save status for each component
  const effectiveImplantSaveStatus =
    implantSaveStatus || localImplantSaveStatus;
  const effectivePassCriteriaSaveStatus =
    passCriteriaSaveStatus || localPassCriteriaSaveStatus;
  const effectiveFluoroscopySaveStatus =
    fluoroscopyStatus || localFluoroscopySaveStatus;

  // Track if user has made changes to prevent auto-navigation on initial load
  const [hasUserMadeChanges, setHasUserMadeChanges] = React.useState(false);

  // Track when user makes changes to any field
  React.useEffect(() => {
    if (
      (diff.hasDiff || passCriteriaDiff.hasDiff || fluoroscopyDiff.hasDiff) &&
      !hasUserMadeChanges
    ) {
      setHasUserMadeChanges(true);
    }
  }, [
    diff.hasDiff,
    passCriteriaDiff.hasDiff,
    fluoroscopyDiff.hasDiff,
    hasUserMadeChanges,
  ]);

  // Combine the save statuses to determine the overall status
  const combinedSaveStatus = React.useMemo(() => {
    // If any component is saving, show saving
    if (
      effectiveImplantSaveStatus === "saving" ||
      effectivePassCriteriaSaveStatus === "saving" ||
      effectiveFluoroscopySaveStatus === "saving"
    ) {
      return "saving";
    }

    // If any component has validation failed, show validation failed
    if (
      effectiveImplantSaveStatus === "validation_failed" ||
      effectivePassCriteriaSaveStatus === "validation_failed" ||
      effectiveFluoroscopySaveStatus === "validation_failed"
    ) {
      return "validation_failed";
    }

    // If any component has an error, show error
    if (
      effectiveImplantSaveStatus === "error" ||
      effectivePassCriteriaSaveStatus === "error" ||
      effectiveFluoroscopySaveStatus === "error"
    ) {
      return "error";
    }

    // If any component has changes detected, show changes detected
    if (
      effectiveImplantSaveStatus === "changes_detected" ||
      effectivePassCriteriaSaveStatus === "changes_detected" ||
      effectiveFluoroscopySaveStatus === "changes_detected"
    ) {
      return "changes_detected";
    }

    // Only show saved if at least one component has explicitly been saved
    // This prevents showing "All changes saved" when no changes have been made yet
    if (
      // If implant has been explicitly saved
      effectiveImplantSaveStatus === "saved" ||
      // Or if pass criteria has been explicitly saved
      effectivePassCriteriaSaveStatus === "saved" ||
      // Or if fluoroscopy has been explicitly saved
      effectiveFluoroscopySaveStatus === "saved"
    ) {
      return "saved";
    }

    // Default to empty string
    return "";
  }, [
    effectiveImplantSaveStatus,
    effectivePassCriteriaSaveStatus,
    effectiveFluoroscopySaveStatus,
    diff.hasDiff,
    passCriteriaDiff.hasDiff,
    fluoroscopyDiff.hasDiff,
  ]);

  // Add effect to navigate to Post Op screen when save is successful
  // React.useEffect(() => {
  //   if (combinedSaveStatus === "saved" && hasUserMadeChanges) {
  //     // Add a small delay to ensure the user sees the "saved" message briefly
  //     const timer = setTimeout(() => {
  //       (navigation as any).navigate("Post Op");
  //     }, 1000);

  //     return () => clearTimeout(timer);
  //   }
  // }, [combinedSaveStatus, navigation, hasUserMadeChanges]);

  /**
   * Derive the save message and styling based on the combined save status
   */

  const { saveMessage, messageStyle, iconName, iconColor } =
    React.useMemo(() => {
      let message = "";
      let style = "text-primaryPurple italic";
      let icon = "alert-circle-outline";
      let color = "#8143d9";

      switch (combinedSaveStatus) {
        case "changes_detected":
          message = "Changes detected, saving soon...";
          style = "text-primaryBlack italic";
          icon = "alert-circle-outline";
          color = "#f5c542"; // yellow
          break;
        case "saving":
          message = "Saving... Please don't close the app";
          style = "text-primaryPurple font-bold italic";
          icon = "alert-circle-outline";
          color = "#8143d9"; // purple
          break;
        case "saved":
          message = "All changes saved";
          style = "text-green-3 italic";
          icon = "check-circle-outline";
          color = "#28a745"; // green
          break;
        case "validation_failed":
          message = "Validation failed. Please check your inputs.";
          style = "text-red-3 font-bold italic";
          icon = "alert-circle-outline";
          color = "#d93025"; // red
          break;
        case "error":
          message = "Error saving changes. Please try again.";
          style = "text-red-3 font-bold italic";
          icon = "alert-circle-outline";
          color = "#d93025"; // red
          break;
      }

      return {
        saveMessage: message,
        messageStyle: style,
        iconName: icon,
        iconColor: color,
      };
    }, [
      combinedSaveStatus,
      effectiveImplantSaveStatus,
      effectivePassCriteriaSaveStatus,
    ]);
  /**
   * Refs to track previous diff values to prevent unnecessary saves
   */
  const prevImplantDiffRef = React.useRef<string>("");
  const prevPassCriteriaDiffRef = React.useRef<string>("");
  const prevFluoroscopyDiffRef = React.useRef<string>("");

  /**
   * Ref to track if initial data has been loaded
   * This prevents autosave from triggering before data is fetched
   */
  const initialDataLoadedRef = React.useRef<boolean>(false);

  /**
   * Effect to set initialDataLoaded when data is fetched
   */
  React.useEffect(() => {
    // Check if we have valid data
    const hasValidData = userDetails?.case_detail_id !== undefined;

    if (hasValidData && !initialDataLoadedRef.current) {
      initialDataLoadedRef.current = true;
    }
  }, [userDetails]);

  /**
   * Effect to trigger autosave when implant data changes
   * This uses our new simpleAutoSave service with local state fallback
   */
  React.useEffect(() => {
    if (!initialDataLoadedRef.current) return;

    if (!userDetails?.case_detail_id) return;

    if (diff.hasDiff) {
      const hasUndefinedValues = Object.values(diff.currentValues).every(
        (val) => val === undefined
      );
      if (hasUndefinedValues) return;

      const currentDiffString = JSON.stringify(diff.currentValues);

      if (currentDiffString !== prevImplantDiffRef.current) {
        prevImplantDiffRef.current = currentDiffString;

        const setStatusWithFallback = (payload: {
          screenId: string;
          status: string;
        }) => {
          if (payload.screenId === "laaoImplant") {
            setLocalImplantSaveStatus(payload.status);
          } else if (payload.screenId === "passCriteria") {
            setLocalPassCriteriaSaveStatus(payload.status);
          } else if (payload.screenId === "fluoroscopy") {
            setLocalFluoroscopySaveStatus(payload.status);
          }
          return dispatch(setSaveStatus(payload));
        };

        simpleAutoSave(
          "laaoImplant",
          diff.currentValues,
          saveDetailsWrapper,
          4000,
          dispatch,
          setStatusWithFallback,
          setLastSavedData
        );
      }
    }
  }, [
    diff.hasDiff,
    diff.currentValues,
    dispatch,
    setLocalImplantSaveStatus,
    setLocalPassCriteriaSaveStatus,
    setLocalFluoroscopySaveStatus,
    userDetails,
  ]);

  /**
   * Effect to trigger autosave when pass criteria data changes
   * This is a separate effect to handle pass criteria changes independently
   */
  React.useEffect(() => {
    if (!initialDataLoadedRef.current) return;

    if (!userDetails?.case_detail_id) return;

    if (passCriteriaDiff && passCriteriaDiff.hasDiff) {
      const hasUndefinedValues = Object.values(
        passCriteriaDiff.currentValues
      ).every((val) => val === undefined);
      if (hasUndefinedValues) return;

      const currentDiffString = JSON.stringify(passCriteriaDiff.currentValues);

      if (currentDiffString !== prevPassCriteriaDiffRef.current) {
        prevPassCriteriaDiffRef.current = currentDiffString;

        const setStatusWithFallback = (payload: {
          screenId: string;
          status: string;
        }) => {
          if (payload.screenId === "laaoImplant") {
            setLocalImplantSaveStatus(payload.status);
          } else if (payload.screenId === "passCriteria") {
            setLocalPassCriteriaSaveStatus(payload.status);
          } else if (payload.screenId === "fluoroscopy") {
            setLocalFluoroscopySaveStatus(payload.status);
          }
          return dispatch(setSaveStatus(payload));
        };

        const savePassCriteriaWrapper = async (data: any): Promise<void> => {
          try {
            if (!userDetails?.case_detail_id) {
              console.error("Missing case_detail_id");
              throw new Error("Missing case_detail_id");
            }

            // Save pass criteria data

            await dispatch(
              putPassCriteriaDetails({
                case_details_id: userDetails.case_detail_id,
                payload: data,
              })
            );
          } catch (err) {
            console.error("Error saving pass criteria data:", err);
            throw err;
          }
        };

        simpleAutoSave(
          "passCriteria",
          passCriteriaDiff.currentValues,
          savePassCriteriaWrapper,
          4000,
          dispatch,
          setStatusWithFallback,
          setLastSavedData
        );
      }
    }
  }, [
    passCriteriaDiff?.hasDiff,
    passCriteriaDiff?.currentValues,
    dispatch,
    setLocalImplantSaveStatus,
    setLocalPassCriteriaSaveStatus,
    setLocalFluoroscopySaveStatus,
    userDetails,
  ]);

  /**
   * Effect to trigger autosave when fluoroscopy data changes
   * This is a separate effect to handle fluoroscopy changes independently
   */
  React.useEffect(() => {
    if (!initialDataLoadedRef.current) return;

    if (!userDetails?.case_detail_id) return;

    if (fluoroscopyDiff && fluoroscopyDiff.hasDiff) {
      const hasUndefinedValues = Object.values(
        fluoroscopyDiff.currentValues
      ).every((val) => val === undefined);
      if (hasUndefinedValues) return;

      const currentDiffString = JSON.stringify(fluoroscopyDiff.currentValues);

      if (currentDiffString !== prevFluoroscopyDiffRef.current) {
        prevFluoroscopyDiffRef.current = currentDiffString;

        const setStatusWithFallback = (payload: {
          screenId: string;
          status: string;
        }) => {
          if (payload.screenId === "laaoImplant") {
            setLocalImplantSaveStatus(payload.status);
          } else if (payload.screenId === "passCriteria") {
            setLocalPassCriteriaSaveStatus(payload.status);
          } else if (payload.screenId === "fluoroscopy") {
            setLocalFluoroscopySaveStatus(payload.status);
          }
          return dispatch(setSaveStatus(payload));
        };

        const saveFluoroscopyWrapper = async (data: any): Promise<void> => {
          try {
            if (!userDetails?.case_detail_id) {
              console.error("Missing case_detail_id");
              throw new Error("Missing case_detail_id");
            }

            await dispatch(
              putFluoroscopyDetails({
                case_details_id: userDetails.case_detail_id,
                payload: data,
              })
            );
          } catch (err) {
            console.error("Error saving fluoroscopy data:", err);
            throw err;
          }
        };

        simpleAutoSave(
          "fluoroscopy",
          fluoroscopyDiff.currentValues,
          saveFluoroscopyWrapper,
          4000,
          dispatch,
          setStatusWithFallback,
          setLastSavedData
        );
      }
    }
  }, [
    fluoroscopyDiff?.hasDiff,
    fluoroscopyDiff?.currentValues,
    dispatch,
    setLocalImplantSaveStatus,
    setLocalPassCriteriaSaveStatus,
    setLocalFluoroscopySaveStatus,
    userDetails,
  ]);

  const handleRefresh = async () => {
    if (!selectedPatient?.case_id) return;
    const refetchResLaao = await dispatch(
      fetchLaaoImplantDetails({ case_id: selectedPatient.case_id })
    );
    const refetchResPassCriteria = await dispatch(
      fetchPassCriteriaDetails({ case_id: selectedPatient.case_id })
    );
    const refetchResFluoroscopy = await dispatch(
      fetchFluoroscopyDetails({ case_id: selectedPatient.case_id })
    );

    if (refetchResLaao.payload) {
      dispatch(setImplantUserDetails(refetchResLaao.payload));
    }
    if (refetchResPassCriteria.payload) {
      dispatch(setPassCriteriaUserDetails(refetchResPassCriteria.payload));
    }
    if (refetchResFluoroscopy.payload) {
      dispatch(setFluoroscopyUserDetails(refetchResFluoroscopy.payload));
    }
  };

  if (loader || passCriteriaLoader) {
    return <Loader />;
  }

  if (error) {
    return <Error />;
  }

  return (
    <ScreenWrapper direction="column" onRefresh={handleRefresh}>
      <CustomCard>
        {saveMessage && (
          <View
            style={{
              flexDirection: "row",
              justifyContent: "flex-end",
              alignItems: "center",
              marginVertical: 4,
            }}
          >
            <MaterialCommunityIcons
              name={iconName}
              color={iconColor}
              size={20}
            />
            <Text
              className={`text-sm ${messageStyle}`}
              style={{ marginLeft: 6 }}
            >
              {saveMessage}
            </Text>
          </View>
        )}
        {/* 
        <View>
          <Heading
            text="Access Sheath"
            size="sub-heading"
            showSeperator={false}
            extraStyle="pb-3"
          />
          <SearchablePicker
            items={accessSheathType}
            placeholder="Type"
            value={accessSheathTypeSelected}
            onValueChange={(option) => {
              dispatch(
                setImplantUserDetails({
                  implant_access_sheath: {
                    ...userDetails.implant_access_sheath,
                    selected: {
                      id: option.value,
                      name: option.label,
                    },
                  },
                })
              );
            }}
          />
        </View>

        <LineSeperator extraStyle="my-5" /> */}

        {/* <View className="mt-2 gap-2">
          <Heading text="Device" size="sub-heading" showSeperator={false} />
          <View className="bg-primaryBg rounded-md p-3 pb-5 flex-row justify-between gap-2">
            <View className=" flex-1">
              <SearchablePicker
                items={deviceType}
                placeholder="Select"
                value={selectedDevice}
                onValueChange={(option) => {
                  // Get the filtered options for the selected device
                  const newFilteredOptions =
                    deviceType?.find((item: any) => item.value === option.value)
                      ?.value2 || [];

                  // If there are options available, automatically select the first one
                  const firstSizeOption =
                    newFilteredOptions.length > 0
                      ? newFilteredOptions[0]
                      : null;

                  dispatch(
                    setImplantUserDetails({
                      device: {
                        ...userDetails.device,
                        selected: {
                          id: option.value,
                          name: option.label,
                          // If there's a first size option, set it as the device_size
                          ...(firstSizeOption && {
                            device_size: firstSizeOption.value,
                          }),
                        },
                      },
                    })
                  );
                }}
                floatingLabel
              />
            </View>
            <View className="flex-1">
              {filteredOptions && (
                <SearchablePicker
                  items={filteredOptions}
                  placeholder="Select"
                  value={Number(userDetails?.device?.selected?.device_size)}
                  onValueChange={(option) =>
                    dispatch(
                      setImplantUserDetails({
                        device: {
                          ...userDetails.device,
                          selected: {
                            ...userDetails.device?.selected,
                            device_size: option.value,
                          },
                        },
                      })
                    )
                  }
                  floatingLabel
                />
              )}
            </View>
          </View>
        </View> */}

        {/* <LineSeperator extraStyle="mt-4 mb-3" /> */}

        {/* <View className="flex-row justify-between items-center">
          <Heading
            text="Tug Test"
            color="black"
            size="sub-heading"
            showSeperator={false}
          />
          <ToggleButton
            messages={["Passed", "Failed"]}
            selected={suitabilityTug}
            setSelected={(value) =>
              dispatch(
                setImplantUserDetails({
                  suitability_tug: value === "Passed" ? true : false,
                })
              )
            }
            isPositive
          />

         
        </View> */}

        {/* <LineSeperator extraStyle="mt-4 mb-3" /> */}

        <View className="mt-2 flex-row justify-between items-center">
          <Heading
            text="Partial Recaptures"
            color="black"
            size="sub-heading"
            showSeperator={false}
          />
          <ToggleButton
            messages={["Yes", "No"]}
            selected={partialRecaptures}
            setSelected={(value) =>
              dispatch(
                setImplantUserDetails({
                  partial_recaptures: value === "Yes" ? true : false,
                })
              )
            }
            customToggler
            width="w-[55px]"
            customColors={["#fde047", "#16a34a"]}
          />
          {/* <CustomCheckbox
            value={partialRecaptures}
            onChange={(val) => {
              dispatch(
                setImplantUserDetails({
                  partial_recaptures: val,
                })
              );
            }}
            yesColor="yellow"
            noColor="green"
          /> */}
        </View>

        {partialRecaptures === "Yes" && (
          <View className="bg-primaryBg rounded-md p-3 pb-5 mt-3">
            <View>
              <Heading
                text="Number of partial recaptures"
                color="black"
                size="label"
                showSeperator={false}
                extraStyle="mb-2"
              />

              <CustomInput
                inputValue={number_partial_recaptures?.toString()}
                // error={!number_partial_recaptures}
                onInputChange={(value) =>
                  parseIntInput(value, (updatedValue) => {
                    dispatch(
                      setImplantUserDetails({
                        no_partial_recaptures: updatedValue,
                      })
                    );
                  })
                }
                placeholder="Number of partial recaptures"
                keyboardType="numeric"
              />
            </View>

            <View className="mt-3">
              <Heading
                text="Type of manipulations"
                color="black"
                size="label"
                showSeperator={false}
                extraStyle="mb-2"
              />
              <CustomInput
                inputValue={manipulationType?.toString()}
                // error={!manipulationType}
                onInputChange={(value) =>
                  dispatch(
                    setImplantUserDetails({
                      partial_recaptures_manipulation: value,
                    })
                  )
                }
                placeholder="Type of manipulations"
              />
            </View>
          </View>
        )}

        <LineSeperator extraStyle="mt-4 mb-3" />

        <View className="flex-row justify-between items-center">
          <Heading
            text="Device Deployed in Body"
            color="black"
            size="sub-heading"
            showSeperator={false}
          />
          <ToggleButton
            messages={["Yes", "No"]}
            selected={deviceDeployed}
            setSelected={(value) =>
              dispatch(
                setImplantUserDetails({
                  device_deployed: value === "Yes" ? true : false,
                })
              )
            }
            width="w-[55px]"
            isPositive
          />

          {/* <CustomCheckbox
            value={deviceDeployed}
            onChange={(val) => {
              dispatch(
                setImplantUserDetails({
                  device_deployed: val,
                })
              );
            }}
          /> */}
        </View>

        {deviceDeployed === "No" && (
          <View className="mt-3">
            <CustomInput
              inputValue={deviceNotDeployedInput?.toString()}
              // error={!deviceNotDeployedInput}
              onInputChange={(value) =>
                dispatch(
                  setImplantUserDetails({
                    device_not_deployed_rationale: value,
                  })
                )
              }
              placeholder="Rationale"
            />
          </View>
        )}

        <LineSeperator extraStyle="mt-4 mb-3" />

        <View className="flex-row justify-between items-center">
          <Heading
            text="Case Aborted"
            color="black"
            size="sub-heading"
            showSeperator={false}
          />
          <ToggleButton
            messages={["Yes", "No"]}
            selected={caseAborted}
            setSelected={(value) =>
              dispatch(
                setImplantUserDetails({
                  case_aborted: value === "Yes" ? true : false,
                })
              )
            }
            width="w-[55px]"
            invertColor
          />
          {/* 
          <CustomCheckbox
            value={caseAborted}
            onChange={(val) => {
              dispatch(
                setImplantUserDetails({
                  case_aborted: val,
                })
              );
            }}
            yesColor="red"
            noColor="green"
          /> */}
        </View>

        {caseAborted === "Yes" && (
          <View className="mt-3">
            <CustomInput
              inputValue={caseAbortedInput}
              // error={!caseAbortedInput}
              onInputChange={(value) =>
                dispatch(
                  setImplantUserDetails({
                    case_aborted_rationale: value === "" ? null : value,
                  })
                )
              }
              placeholder="Rationale"
            />
          </View>
        )}

        <LineSeperator extraStyle="mt-4 mb-3" />

        <View className="flex-row justify-between items-center">
          <Heading
            text="Product Chargeable"
            color="black"
            size="sub-heading"
            showSeperator={false}
          />
          <ToggleButton
            messages={["Yes", "No"]}
            selected={productChargable}
            setSelected={(value) =>
              dispatch(
                setImplantUserDetails({
                  product_chargeable: value === "Yes" ? true : false,
                })
              )
            }
            width="w-[55px]"
            isPositive
          />

          {/* <CustomCheckbox
            value={productChargable}
            onChange={(val) => {
              dispatch(
                setImplantUserDetails({
                  product_chargeable: val,
                })
              );
            }}
          /> */}
        </View>

        {productChargable === "No" && (
          <View className="mt-3">
            <CustomInput
              inputValue={productChargableInput}
              // error={!productChargableInput}
              onInputChange={(value) =>
                dispatch(
                  setImplantUserDetails({
                    product_not_chargeable_rationale:
                      value === "" ? null : value,
                  })
                )
              }
              placeholder="Rationale"
            />
          </View>
        )}

        <LineSeperator extraStyle="mt-4 mb-3" />

        <View className="flex-row justify-between items-center">
          <Heading
            text="Complications"
            color="black"
            size="sub-heading"
            showSeperator={false}
          />
          <ToggleButton
            messages={["Yes", "No"]}
            selected={complications_toggler}
            invertColor
            setSelected={(value) =>
              dispatch(
                setImplantUserDetails({
                  complication: {
                    ...userDetails.complication,
                    selected: {
                      ...userDetails.complication.selected,
                      complication_present: value === "Yes" ? true : false,
                    },
                  },
                })
              )
            }
            width="w-[55px]"
          />

          {/* <CustomCheckbox
              value={complications_toggler}
              onChange={(val) => {
                dispatch(
                  setImplantUserDetails({
                    complication: {
                      ...userDetails.complication,
                      selected: {
                        ...userDetails.complication.selected,
                        complication_present: val,
                      },
                    },
                  })
                );
              }}
              yesColor="red"
              noColor="green"
            /> */}
        </View>

        {complications_toggler === "Yes" && (
          <>
            <View className="mt-2">
              <SearchablePicker
                items={complications_type}
                placeholder="Select"
                value={complication_selected_id}
                onValueChange={(option) =>
                  dispatch(
                    setImplantUserDetails({
                      complication: {
                        ...userDetails.complication,
                        selected: {
                          ...userDetails.complication.selected,
                          id: option.value,
                          name: option.label,
                        },
                      },
                    })
                  )
                }
              />
            </View>

            {complication_selected_name?.toLowerCase() === "other" && (
              <View className="mt-3 bg-primaryBg">
                <CustomInput
                  inputValue={complication_other}
                  error={!complication_other}
                  onInputChange={(val: string) => {
                    dispatch(
                      setImplantUserDetails({
                        complication: {
                          ...userDetails.complication,
                          selected: {
                            ...userDetails.complication.selected,
                            complication_other: val,
                          },
                        },
                      })
                    );
                  }}
                  placeholder="Other"
                />
              </View>
            )}
          </>
        )}

        <LineSeperator extraStyle="mt-4 mb-3" />

        <View className="mt-2">
          <Heading text="Device" size="sub-heading" showSeperator={false} />
          {/* <View className="bg-primaryBg rounded-md p-3 pb-5 flex-row justify-between gap-2"> */}
          <View className=" flex-1">
            <SearchablePicker
              items={deviceType}
              // placeholder="Select"
              value={selectedDevice}
              onValueChange={(option) => {
                // Get the filtered options for the selected device
                const newFilteredOptions =
                  deviceType?.find((item: any) => item.value === option.value)
                    ?.value2 || [];

                // If there are options available, automatically select the first one
                const firstSizeOption =
                  newFilteredOptions.length > 0 ? newFilteredOptions[0] : null;

                dispatch(
                  setImplantUserDetails({
                    device: {
                      ...userDetails.device,
                      selected: {
                        id: option.value,
                        name: option.label,
                        // If there's a first size option, set it as the device_size
                        ...(firstSizeOption && {
                          device_size: firstSizeOption.value,
                        }),
                      },
                    },
                  })
                );
              }}
              floatingLabel
            />
          </View>
          {/* <View className="flex-1">
              {filteredOptions && (
                <SearchablePicker
                  items={filteredOptions}
                  placeholder="Select"
                  value={Number(userDetails?.device?.selected?.device_size)}
                  onValueChange={(option) =>
                    dispatch(
                      setImplantUserDetails({
                        device: {
                          ...userDetails.device,
                          selected: {
                            ...userDetails.device?.selected,
                            device_size: option.value,
                          },
                        },
                      })
                    )
                  }
                  floatingLabel
                />
              )}
            </View> */}
          {/* </View> */}
        </View>
        <LineSeperator extraStyle="mt-6 mb-1" />
        <Heading
          text="Size"
          color="black"
          size="sub-heading"
          showSeperator={false}
          extraStyle="mt-2 pb-2"
        />

        <View className="bg-primaryBg px-4 py-3 rounded-md">
          <SizeSelector
            deviceSizes={device_sizes}
            conditionArray={[]}
            selectedSize={size_selected}
            onPress={(value) => dispatch(setSelectedDeviceSize(value))}
          />
        </View>

        <LineSeperator extraStyle="mt-4 mb-3" />

        <View className="flex-row justify-between items-center">
          <Heading
            text="Position"
            color="black"
            size="sub-heading"
            showSeperator={false}
          />
          <ToggleButton
            messages={["Passed", "Failed"]}
            selected={position}
            setSelected={(value) =>
              dispatch(
                setPassCriteriaUserDetails({
                  position: value === "Passed" ? true : false,
                })
              )
            }
          />

          {/* <CustomCheckbox
            value={position}
            onChange={(val) => {
              dispatch(
                setPassCriteriaUserDetails({
                  position: val,
                })
              );
            }}
          /> */}
        </View>

        <LineSeperator extraStyle="mt-4 mb-3" />

        <View className="flex-row justify-between items-center">
          <Heading
            text="Anchor"
            color="black"
            size="sub-heading"
            showSeperator={false}
          />
          <ToggleButton
            messages={["Passed", "Failed"]}
            selected={anchor}
            setSelected={(value) =>
              dispatch(
                setPassCriteriaUserDetails({
                  anchor: value === "Passed" ? true : false,
                })
              )
            }
          />

          {/* <CustomCheckbox
            value={anchor}
            onChange={(val) => {
              dispatch(
                setPassCriteriaUserDetails({
                  anchor: val,
                })
              );
            }}
          /> */}
        </View>

        <LineSeperator extraStyle="mt-4 mb-3" />

        <View>
          <Heading
            text="Size"
            color="black"
            size="sub-heading"
            showSeperator={false}
            extraStyle="pb-1"
          />
          <View className="rounded-md py-1">
            <CompressionCalculator
              deviceSizes={device_sizes}
              selectedSize={size_selected}
              width0={width0}
              width45={width45}
              width90={width90}
              width135={width135}
            />
          </View>
        </View>

        <LineSeperator extraStyle="mt-4 mb-3" />

        <View>
          <Heading
            text="Seal"
            color="black"
            size="sub-heading"
            showSeperator={false}
            extraStyle="pb-2"
          />
          <View className="rounded-md p-3 bg-primaryBg">
            <OptionSelector
              options={sealValues}
              selected={leak ? "Leak" : "No Leak"}
              onSelect={(value) => {
                dispatch(
                  setPassCriteriaUserDetails({
                    leak: value.value === "Leak" ? true : false,
                  })
                );
              }}
              customSelectedColor="bg-orange-3"
              customSelectedValue="Leak"
            />
            {leak === true ? (
              <View className="flex-column items-center justify-center">
                <View className="mt-4 flex-row items-center pb-4">
                  <View className="mr-4">
                    <Heading
                      text="Leak Value (mm)"
                      size="label"
                      color="black"
                      showSeperator={false}
                    />
                  </View>
                  <CustomInput
                    inputValue={leak_value?.toString()}
                    error={leak_value === 0}
                    onInputChange={(value) =>
                      parseIntInput(value, (updatedValue) => {
                        dispatch(
                          setPassCriteriaUserDetails({
                            leak_value: updatedValue,
                          })
                        );
                      })
                    }
                    keyboardType="numeric"
                    placeholder="(mm)"
                    width="20%"
                    maxLength={2}
                  />
                </View>
                <View
                  className={`
                    ${leak_value >= 5 ? "bg-red-3" : "bg-yellow-3"} 
                    flex-row 
                    items-center 
                    justify-center
                    p-3 
                    rounded
                    w-full
                  `}
                >
                  <Text
                    className={`
                      text-primaryWhite
                    text-md
                    font-bold
                    `}
                  >
                    {leak_value >= 5 || leak_value === 0 ? "FAILED" : "PASSED"}
                  </Text>
                </View>
              </View>
            ) : (
              <View
                className={`
                bg-green-3
                flex-row 
                items-center 
                justify-center
                p-3 
                rounded
                w-full
              `}
              >
                <Text className="text-primaryWhite text-md font-bold">
                  PASSED
                </Text>
              </View>
            )}
          </View>
        </View>
        <LineSeperator extraStyle="my-5" />

        {/* Fluoroscopy & Contrast Section */}
        <View>
          <Heading
            text="Creatinine Value (mg/dL)"
            size="sub-heading"
            extraStyle="mt-2 pb-3"
            showSeperator={false}
          />
          <CustomInput
            inputValue={creatinine_value?.toString()}
            onInputChange={(value) =>
              parseFloatInput(
                value,
                (updatedValue) => {
                  dispatch(
                    setFluoroscopyUserDetails({
                      creatinine_value: updatedValue,
                    })
                  );
                },
                0,
                30,
                2
              )
            }
            placeholder="Creatinine"
            keyboardType="numeric"
          />
        </View>

        <LineSeperator extraStyle="mt-5 mb-3" />

        <View>
          <Heading
            text="Fluoro Time (min)"
            size="sub-heading"
            extraStyle="pb-3"
            showSeperator={false}
          />
          <CustomInput
            inputValue={fluoro_time?.toString()}
            onInputChange={(value) =>
              parseFloatInput(
                value,
                (updatedValue) => {
                  dispatch(
                    setFluoroscopyUserDetails({
                      fluoro_time: updatedValue,
                    })
                  );
                },
                0,
                100,
                2
              )
            }
            placeholder="Fluoro Time"
            keyboardType="numeric"
          />
        </View>

        <LineSeperator extraStyle="mt-5 mb-3" />

        <View>
          <Heading
            text="Total Contrast (mL)"
            size="sub-heading"
            extraStyle="pb-3"
            showSeperator={false}
          />
          <CustomInput
            inputValue={total_contrast?.toString()}
            onInputChange={(value) =>
              parseFloatInput(
                value,
                (updatedValue) => {
                  dispatch(
                    setFluoroscopyUserDetails({
                      total_contrast: updatedValue,
                    })
                  );
                },
                0,
                300,
                2
              )
            }
            placeholder="Contrast"
            keyboardType="numeric"
          />
        </View>

        <LineSeperator extraStyle="my-5" />

        {/* <View className="flex-row items-center justify-center">
          <SaveActionButton
            disabled={!(diff?.hasDiff || passCriteriaDiff?.hasDiff)}
            onPress={saveDetails}
            onCancel={handleCancel}
          />
        </View> */}
        <View className="mt-3"></View>
      </CustomCard>
      <View className="mt-9"></View>
      <PopupModal
        show={modalVisible}
        msg={popupMsg}
        status="warning"
        onClose={() => setModalVisible(false)}
      />
    </ScreenWrapper>
  );
};

export default LAAOImplantScreen;
