import * as React from "react";
import { View, TouchableOpacity, Text, StyleSheet } from "react-native";
import WebView from "react-native-webview";
import { useNavigation } from "@react-navigation/native";

interface IWebViewerProps {
  route: {
    params: {
      link: string;
      title: string;
    };
  };
}

const WebViewer: React.FunctionComponent<IWebViewerProps> = ({ route }) => {
  const { link, title } = route.params;
  const navigation = useNavigation();

  React.useEffect(() => {
    // Set the title for the header
    if (title) {
      navigation.setOptions({
        title: title,
      });
    }
  }, [navigation, title]);

  return (
    <View style={styles.container}>
      {/* WebView */}
      <WebView source={{ uri: link }} style={styles.webview} />
    </View>
  );
};

export default WebViewer;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  backButton: {
    padding: 10,
    backgroundColor: "#f8f8f8",
    alignItems: "center",
    borderBottomWidth: 1,
    borderBottomColor: "#ccc",
  },
  backButtonText: {
    color: "#007BFF",
    fontWeight: "bold",
  },
  webview: {
    flex: 1,
  },
});
