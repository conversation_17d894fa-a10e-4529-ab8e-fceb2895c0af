import { useSelector } from "react-redux";
import { RootState } from "../../../store";

const useTransseptalDetails = () => {
  const { transseptaDetails } = useSelector(
    (state: RootState) => state.clinician.laaoProcedure.transseptalpuncture
  );
  return transseptaDetails;
};

const useTransseptalUserDetails = () => {
  const { userDetails } = useSelector(
    (state: RootState) => state.clinician.laaoProcedure.transseptalpuncture
  );
  return userDetails;
};

const useLoaderAndError = () => {
  const loader = useSelector(
    (state: RootState) =>
      state.clinician.laaoProcedure.transseptalpuncture?.loading
  );
  const error = useSelector(
    (state: RootState) =>
      state.clinician.laaoProcedure.transseptalpuncture?.error
  );
  return { loader, error };
};

const findTransseptalDiff = () => {
  const transseptalDetails = useTransseptalDetails();
  const userDetails = useTransseptalUserDetails();

  const diff = {
    access_sheath:
      transseptalDetails?.tsp_access_sheath?.selected.id ===
      userDetails?.tsp_access_sheath?.selected.id,

    act:
      transseptalDetails?.activated_clotting_time ===
      userDetails?.activated_clotting_time,

    transseptal_access_system:
      transseptalDetails?.tsp_access_system?.selected.id ===
      userDetails?.tsp_access_system?.selected.id,

    tsp_recross: transseptalDetails?.tsp_recross === userDetails?.tsp_recross,

    atrial_septostomy:
      transseptalDetails?.atrial_septostomy === userDetails?.atrial_septostomy,

    tsp_imaging:
      transseptalDetails?.tsp_imaging?.selected.id ===
      userDetails?.tsp_imaging?.selected.id,

    final_tsp_location:
      transseptalDetails?.tsp_location?.selected.id ===
      userDetails?.tsp_location?.selected.id,

    heparin_administred:
      transseptalDetails?.heparin_administred ===
      userDetails?.heparin_administred,

    heparin_administred_units:
      transseptalDetails?.heparin_administred_units ===
      userDetails?.heparin_administred_units,
  };

  const currentValues = {
    tsp_access_sheath: userDetails?.tsp_access_sheath?.selected.id,
    activated_clotting_time: userDetails?.activated_clotting_time,
    tsp_access_system: userDetails?.tsp_access_system?.selected.id,
    tsp_recross: userDetails?.tsp_recross,
    atrial_septostomy: userDetails?.atrial_septostomy,
    tsp_imaging_type: userDetails?.tsp_imaging?.selected.id,
    tsp_location: userDetails?.tsp_location?.selected.id,
    heparin_administred: userDetails?.heparin_administred,
    heparin_administred_units: userDetails?.heparin_administred_units,
  };

  return {
    hasDiff: Object.values(diff).includes(false),
    currentValues: currentValues,
  };
};

export {
  useTransseptalDetails,
  useTransseptalUserDetails,
  useLoaderAndError,
  findTransseptalDiff,
};
