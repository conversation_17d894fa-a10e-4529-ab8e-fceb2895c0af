import * as React from "react";
import { RefreshControl, Platform } from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";

interface IScreenWrapperProps {
  children: React.ReactNode;
  direction?: "row" | "column";
  onRefresh?: () => Promise<void>;
}

const ScreenWrapper: React.FunctionComponent<IScreenWrapperProps> = ({
  children,
  direction = "column",
  onRefresh,
}) => {
  const insets = useSafeAreaInsets();
  const [refreshing, setRefreshing] = React.useState(false);

  const handleRefresh = async () => {
    if (!onRefresh) return;
    setRefreshing(true);
    await onRefresh();
    setRefreshing(false);
  };

  return (
    <KeyboardAwareScrollView
      showsVerticalScrollIndicator={false}
      showsHorizontalScrollIndicator={false}
      className={`flex-1 ${
        direction === "row" ? "flex-row" : "flex-col"
      } p-4 bg-primaryBg`}
      bounces={true}
      // Key properties for smooth keyboard handling
      enableOnAndroid={true}
      enableAutomaticScroll={true}
      keyboardOpeningTime={250}
      extraHeight={Platform.OS === "ios" ? 0 : 60}
      extraScrollHeight={Platform.OS === "ios" ? 0 : 60}
      enableResetScrollToCoords={false}
      keyboardShouldPersistTaps="handled"
      // Animation and behavior
      scrollEventThrottle={16}
      nestedScrollEnabled={true}
      refreshControl={
        onRefresh ? (
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={["#8143d9"]}
            tintColor={"#8143d9"}
          />
        ) : undefined
      }
    >
      {children}
    </KeyboardAwareScrollView>
  );
};

export default ScreenWrapper;

// import * as React from "react";
// import { RefreshControl, Platform } from "react-native";
// import { useSafeAreaInsets } from "react-native-safe-area-context";
// import { KeyboardAwareScrollView } from "react-native-keyboard-aware-scroll-view";

// interface IScreenWrapperProps {
//   children: React.ReactNode;
//   direction?: "row" | "column";
//   onRefresh?: () => Promise<void>;
// }

// const ScreenWrapper: React.FunctionComponent<IScreenWrapperProps> = ({
//   children,
//   direction = "column",
//   onRefresh,
// }) => {
//   const insets = useSafeAreaInsets();
//   const [refreshing, setRefreshing] = React.useState(false);

//   const handleRefresh = async () => {
//     if (!onRefresh) return;
//     setRefreshing(true);
//     await onRefresh();
//     setRefreshing(false);
//   };

//   return (
//     <KeyboardAwareScrollView
//       showsVerticalScrollIndicator={false}
//       showsHorizontalScrollIndicator={false}
//       className={`flex-1 ${
//         direction === "row" ? "flex-row" : "flex-col"
//       } p-4 bg-primaryBg`}
//       bounces={true}
//       // iOS-specific fixes for the jumping issue
//       enableOnAndroid={true}
//       enableAutomaticScroll={true}
//       extraHeight={Platform.OS === "ios" ? 0 : 75}
//       extraScrollHeight={Platform.OS === "ios" ? 0 : 75}
//       keyboardOpeningTime={250}
//       keyboardShouldPersistTaps="handled"
//       // Additional iOS fixes to show input above keyboard
//       automaticallyAdjustContentInsets={false}
//       contentInsetAdjustmentBehavior="automatic"
//       // Ensure input is visible above keyboard
//       getTextInputRefs={() => {
//         return [];
//       }}
//       scrollEventThrottle={16}
//       refreshControl={
//         onRefresh ? (
//           <RefreshControl
//             refreshing={refreshing}
//             onRefresh={handleRefresh}
//             colors={["#8143d9"]}
//             tintColor={"#8143d9"}
//           />
//         ) : undefined
//       }
//     >
//       {children}
//     </KeyboardAwareScrollView>
//   );
// };

// export default ScreenWrapper;
