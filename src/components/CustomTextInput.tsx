import React, {
  useState,
  forwardRef,
  useImperativeHandle,
  useRef,
} from "react";
import {
  TextInput,
  TouchableOpacity,
  View,
  StyleSheet,
  TextInputProps,
} from "react-native";
import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons";

interface CustomInputProps extends TextInputProps {
  inputValue: string;
  onInputChange: (text: string) => void;
  placeholder?: string;
  width?: number | string;
  height?: number;
  secureTextEntry?: boolean;
  disable?: boolean; // if true, input is editable
  searchIcon?: {
    icon: boolean;
    iconPress: () => void;
  };
  error?: boolean;
  maxLength?: number;
  multiline?: boolean;
  customBorderColor?: string; // Custom border color prop
  customBorderWidth?: number; // Custom border width prop
}

const CustomInput = forwardRef<TextInput, CustomInputProps>(
  (
    {
      inputValue,
      onInputChange,
      placeholder,
      width,
      height = 45,
      keyboardType = "default",
      secureTextEntry = false,
      disable = true,
      searchIcon,
      error = false,
      maxLength,
      multiline = false,
      customBorderColor,
      customBorderWidth,
      ...rest
    },
    ref
  ) => {
    // Create a ref to the underlying TextInput component.
    const innerRef = useRef<TextInput>(null);

    // Expose the focus() method and the TextInput instance to the parent via ref.
    useImperativeHandle(ref, () => ({
      focus: () => {
        if (innerRef.current) {
          innerRef.current.focus();
        }
      },
      blur: () => {
        if (innerRef.current) {
          innerRef.current.blur();
        }
      },
      // Forward the TextInput instance itself
      ...innerRef.current,
    }));

    const [isPasswordVisible, setPasswordVisible] = useState(!secureTextEntry);

    const togglePasswordVisibility = () => {
      setPasswordVisible((prev) => !prev);
    };

    return (
      <View style={[styles.container, { width: width || "100%" }]}>
        <TextInput
          ref={innerRef}
          placeholder={placeholder}
          placeholderTextColor="#999999"
          onChangeText={onInputChange}
          value={inputValue}
          style={[
            styles.input,
            { height },
            {
              borderColor: customBorderColor || (error ? "red" : "#8143d9"),
              borderWidth: customBorderWidth || 1,
            },
            secureTextEntry && { paddingRight: 40 },
          ]}
          keyboardType={keyboardType}
          secureTextEntry={!isPasswordVisible && secureTextEntry}
          editable={disable}
          maxLength={maxLength}
          multiline={multiline}
          {...rest}
        />

        {searchIcon?.icon && (
          <TouchableOpacity
            onPress={searchIcon.iconPress}
            style={styles.iconContainer}
          >
            <MaterialCommunityIcons name="magnify" size={24} color="#8143d9" />
          </TouchableOpacity>
        )}

        {secureTextEntry && (
          <TouchableOpacity
            onPress={togglePasswordVisibility}
            style={styles.iconContainer}
          >
            <MaterialCommunityIcons
              name={isPasswordVisible ? "eye-off-outline" : "eye-outline"}
              size={24}
              color="#8143d9"
            />
          </TouchableOpacity>
        )}
      </View>
    );
  }
);

CustomInput.displayName = "CustomInput";

const styles = StyleSheet.create({
  container: {
    position: "relative",
    backgroundColor: "white",
  },
  input: {
    padding: 5,
    borderRadius: 4,
    borderWidth: 1,
    color: "#000000",
  },
  iconContainer: {
    position: "absolute",
    right: 10,
    top: "50%",
    transform: [{ translateY: -12 }],
  },
});

export default CustomInput;
