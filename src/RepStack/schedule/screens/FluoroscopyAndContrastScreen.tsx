import * as React from "react";
import ScreenWrapper from "../../../components/ScreenWrapper";
import { Text, View } from "react-native";
import CustomInput from "../../../components/CustomTextInput";
import CustomCard from "../../../components/CustomCard";
import Heading from "../../../components/Heading";
import { parseFloatInput, parseIntInput } from "../../../utils";
import LineSeperator from "../../../components/LineSeperator";
import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons";
import {
  fetchFluoroscopyDetails,
  putFluoroscopyDetails,
} from "../../../store/rep/ScheduleStack/laaoProcedures/fluoroscopy/thunk";
import { useSelectedPatient } from "../hooks/PatientsHooks";
import { AppDispatch } from "../../../store";
import { useDispatch, useSelector } from "react-redux";
import { formatFluoroscopyOptions } from "../../../utils";
import {
  useFluoroscopyUserDetails,
  useFluoroscopyDetails,
  useLoaderAndError,
  findFluoroscopyDiff,
} from "../hooks/fluoroscopyHooks";
import { setFluoroscopyUserDetails } from "../../../store/rep/ScheduleStack/laaoProcedures/fluoroscopy";
import { useFocusEffect, useNavigation } from "@react-navigation/native";
import Loader from "../../../components/Loader";
import SaveActionButton from "../../../components/SaveActionButton";
import { useAutosave } from "../../../utils/AutoSave";
import { simpleAutoSave } from "../../../services/simpleAutoSave";
import { setSaveStatus, setLastSavedData } from "../../../store/services";

interface IFluoroscopyAndContrastScreenProps {}

const FluoroscopyAndContrastScreen: React.FunctionComponent<
  IFluoroscopyAndContrastScreenProps
> = (props) => {
  // NEW START

  const dispatch = useDispatch<AppDispatch>();
  const navigation = useNavigation();
  const selectedPatient = useSelectedPatient();
  const userDetails = useFluoroscopyUserDetails();
  const fluoroscopyDetails = useFluoroscopyDetails();
  const { loader, error } = useLoaderAndError();
  const diff = findFluoroscopyDiff();
  const saveInProgressRef = React.useRef(false);
  // Create a ref to track the last saved data hash
  const lastSavedDataHashRef = React.useRef("");
  const { creatinine_value, fluoro_time, fluoro_total, total_contrast } =
    formatFluoroscopyOptions(userDetails);

  const saveDetails = async (): Promise<void> => {
    try {
      // // Run validation
      // const isValid = validator();

      // // If validation fails, update status and throw error
      // if (!isValid) {
      //   dispatch(
      //     setSaveStatus({
      //       screenId: "fluoroscopy",
      //       status: "validation_failed",
      //     })
      //   );
      //   setLocalSaveStatus("validation_failed");
      //   throw new Error("Validation failed");
      // }

      // Avoid saving if already in progress
      if (saveInProgressRef.current) {
        return;
      }

      const dataCopy = { ...diff.currentValues };
      const currentDataHash = JSON.stringify(dataCopy);

      // If the data hasn't changed since the last save, skip saving
      if (currentDataHash === lastSavedDataHashRef.current) {
        return;
      }

      // Mark that a save is in progress
      saveInProgressRef.current = true;

      let result;

      if (diff.hasDiff) {
        result = await dispatch(
          putFluoroscopyDetails({
            case_details_id: userDetails?.case_detail_id,
            payload: dataCopy,
          })
        );

        // Update the last saved data hash
        lastSavedDataHashRef.current = currentDataHash;

        // Refetch if applicable
        if (selectedPatient?.case_id) {
          const refetchRes = await dispatch(
            fetchFluoroscopyDetails({ case_id: selectedPatient.case_id })
          );
          if (refetchRes.payload) {
            dispatch(setFluoroscopyUserDetails(refetchRes.payload));
          }
        }
      }

      // Reset the save in progress flag
      saveInProgressRef.current = false;
    } catch (err) {
      saveInProgressRef.current = false;
      throw err;
    }
  };

  const handleCancel = () => {
    dispatch(
      setFluoroscopyUserDetails({
        ...userDetails,
        ...fluoroscopyDetails,
      })
    );
  };

  useFocusEffect(
    React.useCallback(() => {
      const fetchDetails = async () => {
        if (selectedPatient?.case_id) {
          const res = await dispatch(
            fetchFluoroscopyDetails({ case_id: selectedPatient.case_id })
          );

          if (res.payload) {
            dispatch(setFluoroscopyUserDetails(res.payload));
          }
        }
      };
      fetchDetails();
    }, [dispatch, selectedPatient?.case_id])
  );

  const saveStatus = useSelector(
    (state: any) => state.common?.autoSave?.["fluoroscopy"]?.status
  );

  const [localSaveStatus, setLocalSaveStatus] = React.useState<string>("");

  const effectiveSaveStatus = saveStatus || localSaveStatus;

  // Add effect to navigate to Post Op tab when save is successful
  React.useEffect(() => {
    if (effectiveSaveStatus === "saved") {
      // Add a small delay to ensure the user sees the "saved" message briefly
      const timer = setTimeout(() => {
        // Navigate to the Post Op main tab
        navigation.navigate("Post Op");
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [effectiveSaveStatus, navigation]);

  /**
   * Derive the save message and styling based on the save status
   */
  const { saveMessage, messageStyle, iconName, iconColor } =
    React.useMemo(() => {
      let message = "";
      let style = "text-primaryPurple italic";
      let icon = "alert-circle-outline";
      let color = "#8143d9";

      switch (effectiveSaveStatus) {
        case "changes_detected":
          message = "Changes detected, saving soon...";
          style = "text-primaryBlack italic";
          icon = "alert-circle-outline";
          color = "#f5c542"; // yellow
          break;
        case "saving":
          message = "Saving... Please don't close the app";
          style = "text-primaryPurple font-bold italic";
          icon = "alert-circle-outline";
          color = "#8143d9"; // purple
          break;
        case "saved":
          message = "All changes saved";
          style = "text-green-3 italic";
          icon = "check-circle-outline";
          color = "#28a745"; // green
          break;
        case "validation_failed":
          message = "Validation failed. Please check your inputs.";
          style = "text-red-3 font-bold italic";
          icon = "alert-circle-outline";
          color = "#d93025"; // red
          break;
        case "error":
          message = "Error saving changes. Please try again.";
          style = "text-red-3 font-bold italic";
          icon = "alert-circle-outline";
          color = "#d93025"; // red
          break;
      }

      return {
        saveMessage: message,
        messageStyle: style,
        iconName: icon,
        iconColor: color,
      };
    }, [effectiveSaveStatus]);
  /**
   * Ref to track previous diff values to prevent unnecessary saves
   */
  const prevDiffRef = React.useRef<string>("");

  /**
   * Ref to track if initial data has been loaded
   * This prevents autosave from triggering before data is fetched
   */
  const initialDataLoadedRef = React.useRef<boolean>(false);

  /**
   * Effect to set initialDataLoaded when data is fetched
   */
  React.useEffect(() => {
    // Check if we have valid data
    const hasValidData = userDetails?.case_detail_id !== undefined;

    if (hasValidData && !initialDataLoadedRef.current) {
      initialDataLoadedRef.current = true;
    }
  }, [userDetails]);

  /**
   * Effect to trigger autosave when data changes
   * This uses our new simpleAutoSave service with local state fallback
   */
  React.useEffect(() => {
    // Only proceed if initial data has been loaded and there are actual differences
    if (!initialDataLoadedRef.current) {
      return;
    }

    // Check if case_detail_id exists to prevent API calls with undefined ID
    if (!userDetails?.case_detail_id) {
      return;
    }

    // Only proceed if there are actual differences
    if (diff.hasDiff) {
      // Check if we have any undefined values in critical fields
      const hasUndefinedValues = Object.values(diff.currentValues).every(
        (val) => val === undefined
      );
      if (hasUndefinedValues) {
        return;
      }

      // Convert current values to string for comparison
      const currentDiffString = JSON.stringify(diff.currentValues);

      // Only trigger save if the values have actually changed
      if (currentDiffString !== prevDiffRef.current) {
        // Update the ref with current values
        prevDiffRef.current = currentDiffString;

        // Create a custom wrapper for setSaveStatus that updates both Redux and local state
        const setStatusWithFallback = (payload: {
          screenId: string;
          status: string;
        }) => {
          // Update local state as a fallback
          setLocalSaveStatus(payload.status);
          // Also dispatch to Redux
          return dispatch(setSaveStatus(payload));
        };

        // Trigger the simple autosave with the current values
        simpleAutoSave(
          "fluoroscopy", // Screen ID
          diff.currentValues, // Data to save
          saveDetails, // Save function
          4000, // 4 second delay before saving
          dispatch, // Redux dispatch
          setStatusWithFallback, // Custom action to set save status with fallback
          setLastSavedData // Action to set last saved data
        );
      } else {
        console.log("No actual changes in diff values, skipping autosave");
      }
    }
  }, [
    diff.hasDiff,
    diff.currentValues,
    dispatch,
    setLocalSaveStatus,
    userDetails,
  ]);

  const handleRefresh = async () => {
    if (!selectedPatient?.case_id) return;
    const refetchRes = await dispatch(
      fetchFluoroscopyDetails({ case_id: selectedPatient.case_id })
    );
    if (refetchRes.payload) {
      dispatch(setFluoroscopyUserDetails(refetchRes.payload));
    }
  };

  // NEW END

  if (loader) {
    return <Loader />;
  }

  return (
    <ScreenWrapper direction="column" onRefresh={handleRefresh}>
      <CustomCard>
        {saveMessage && (
          <View
            style={{
              flexDirection: "row",
              justifyContent: "flex-end",
              alignItems: "center",
              marginVertical: 4,
            }}
          >
            <MaterialCommunityIcons
              name={iconName}
              color={iconColor}
              size={20}
            />
            <Text
              className={`text-sm ${messageStyle}`}
              style={{ marginLeft: 6 }}
            >
              {saveMessage}
            </Text>
          </View>
        )}
        <View className="mt-2">
          <Heading
            text="Creatinine Value (mg/dL)"
            size="sub-heading"
            extraStyle="mt-2 pb-3"
            showSeperator={false}
          />
          <CustomInput
            inputValue={creatinine_value?.toString()}
            onInputChange={(value) =>
              parseFloatInput(
                value,
                (updatedValue) => {
                  dispatch(
                    setFluoroscopyUserDetails({
                      creatinine_value: updatedValue,
                    })
                  );
                },
                0,
                30,
                2
              )
            }
            placeholder="Creatinine"
            keyboardType="numeric"
            // maxLength={2}
          />
        </View>

        <LineSeperator extraStyle="mt-5 mb-3" />

        <View>
          <Heading
            text="Fluoro Time (min)"
            size="sub-heading"
            extraStyle="pb-3"
            showSeperator={false}
          />
          <CustomInput
            inputValue={fluoro_time?.toString()}
            onInputChange={(value) =>
              parseFloatInput(
                value,
                (updatedValue) => {
                  dispatch(
                    setFluoroscopyUserDetails({
                      fluoro_time: updatedValue,
                    })
                  );
                },
                0,
                100,
                2
              )
            }
            placeholder="Fluoro Time"
            keyboardType="numeric"
            // maxLength={4}
          />
        </View>

        {/* <LineSeperator extraStyle="my-5" /> */}

        {/* <View>
          <Heading
            text="Fluoro total (mGy)"
            size="sub-heading"
            extraStyle="pb-3"
            showSeperator={false}
          />
          <CustomInput
            inputValue={fluoro_total?.toString()}
            onInputChange={(value) =>
              parseIntInput(value, (updatedValue) => {
                dispatch(
                  setFluoroscopyUserDetails({
                    fluoro_total: updatedValue,
                  })
                );
              })
            }
            placeholder="mGy"
            keyboardType="numeric"
          />
        </View> */}

        <LineSeperator extraStyle="mt-5 mb-3" />

        <View>
          <Heading
            text="Total Contrast (mL)"
            size="sub-heading"
            extraStyle="pb-3"
            showSeperator={false}
          />
          <CustomInput
            inputValue={total_contrast?.toString()}
            onInputChange={(value) =>
              parseIntInput(
                value,
                (updatedValue) => {
                  dispatch(
                    setFluoroscopyUserDetails({
                      total_contrast: updatedValue,
                    })
                  );
                },
                0,
                300
              )
            }
            placeholder="Contrast"
            keyboardType="numeric"
            // maxLength={2}
          />
        </View>
        <LineSeperator extraStyle="my-5" />

        {/* <View className="flex-row items-center justify-center">
          <SaveActionButton
            disabled={!diff.hasDiff}
            onPress={saveDetails}
            onCancel={handleCancel}
          />
        </View> */}

        <View className="mb-3 "></View>
      </CustomCard>
      <View className="mt-9"></View>
    </ScreenWrapper>
  );
};

export default FluoroscopyAndContrastScreen;
