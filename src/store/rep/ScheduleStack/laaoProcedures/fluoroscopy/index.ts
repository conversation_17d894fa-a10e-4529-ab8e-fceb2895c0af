import { createSlice } from "@reduxjs/toolkit";
import initialState from "./initialstate";
import { fetchFluoroscopyDetails, putFluoroscopyDetails } from "./thunk";

const fluoroscopySlice = createSlice({
  name: "fluoroscopy",
  initialState,
  reducers: {
    setFluoroscopyUserDetails: (state, action) => {
      state.userDetails = {
        ...state.userDetails,
        ...action.payload,
      };
    },

    resetFluoroscopyState: (state) => {
      state.fluoroscopyDetails = null;
      state.userDetails = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchFluoroscopyDetails.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchFluoroscopyDetails.fulfilled, (state, action) => {
        state.loading = false;
        state.fluoroscopyDetails = action.payload;
      })
      .addCase(fetchFluoroscopyDetails.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });

    // builder
    //   .addCase(putFluoroscopyDetails.pending, (state) => {
    //     state.loading = true;
    //   })
    //   .addCase(putFluoroscopyDetails.fulfilled, (state, action) => {
    //     state.loading = false;
    //     state.error = null;
    //   })
    //   .addCase(putFluoroscopyDetails.rejected, (state, action) => {
    //     state.loading = false;
    //     state.error = action.payload as string;
    //   });
  },
});

export const { setFluoroscopyUserDetails, resetFluoroscopyState } =
  fluoroscopySlice.actions;

export default fluoroscopySlice.reducer;
