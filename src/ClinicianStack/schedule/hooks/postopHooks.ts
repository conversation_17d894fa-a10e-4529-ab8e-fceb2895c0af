import { useSelector } from "react-redux";
import { RootState } from "../../../store";
import { IPostOpSchema } from "../../../store/rep/ScheduleStack/postop/types";

const useLoaderAndError = () => {
  const loader = useSelector(
    (state: RootState) => state.coordinator.schedule.postop.loading
  );
  const error = useSelector(
    (state: RootState) => state.coordinator.schedule.postop.error
  );
  return { loader, error };
};

const usePostOpUserDetails = () => {
  const { userDetails } = useSelector(
    (state: RootState) => state.clinician.postop
  );
  return userDetails;
};

const usePostOpDetails = () => {
  const { postOpDetails } = useSelector(
    (state: RootState) => state.clinician.postop
  );
  return postOpDetails;
};

const useMedData = () => {
  const med = useSelector(
    (state: RootState) => state.clinician.postop.medication
  );

  return med;
};

const useMedicationData = (medicineId?: string) => {
  const userDetails = usePostOpUserDetails();

  const anticoagulationOptions = userDetails?.anticoagulation?.options?.map(
    (item) => ({
      label: item.name,
      value: item.id,
    })
  );

  const med_data = useMedData();

  const med_selected_to_edit = med_data?.find((med) => med.id === medicineId);

  const dosageOptions = userDetails?.anticoagulation?.options
    ?.find((item) => item.id === med_selected_to_edit?.med_id)
    ?.quantity?.map((quantity) => ({
      label: quantity?.toString(),
      value: quantity?.toString(),
    }));

  const findDosageOptions = (medicationId: string) => {
    return userDetails?.anticoagulation?.options
      ?.find((item) => item.id === medicationId)
      ?.quantity?.map((quantity) => ({
        label: quantity?.toString(),
        value: quantity?.toString(),
      }));
  };

  const frequency_options =
    userDetails?.anticoagulation?.frequency_options?.map((item) => ({
      label: item.name,
      value: item.id,
    }));

  const duration_options = userDetails?.anticoagulation?.duration_options?.map(
    (item) => ({
      label: item.name,
      value: item.id,
    })
  );

  return {
    anticoagulationOptions,
    med_data,
    med_selected_to_edit,
    dosageOptions,
    frequency_options,
    duration_options,
    findDosageOptions,
  };
};

const formatPostOpData = (data: IPostOpSchema | null) => {
  const anticoagulation_selected = data?.anticoagulation?.selected.map(
    (option: any) => ({
      quantity: option?.quantity?.toString(),
      name: option.name,
      value: option.id?.toString(),
    })
  );

  const anticoagulation_options = data?.anticoagulation?.options.map(
    (option: any) => ({
      name: option.name,
      value: option.id,
      quantity: option.quantity,
    })
  );

  const discharge_plan_selected = data?.discharge_plan?.selected?.id;
  const discharge_plan_options = data?.discharge_plan?.options?.map(
    (option: any) => ({
      label: option.name,
      value: option.id,
    })
  );
  const anticipated_45_days_follow_up_date =
    data?.anticipated_45_days_follow_up_date;

  const follow_ups_45_days = {
    completed: data?.follow_ups_45_days?.completed === true ? "Yes" : "No",
    date: data?.follow_ups_45_days?.date,
    peri_device_leak:
      data?.follow_ups_45_days?.peri_device_leak === true ? "Yes" : "No",
    width: data?.follow_ups_45_days?.width,
    thrombus: data?.follow_ups_45_days?.thrombus === true ? "Yes" : "No",
    cta_link: data?.follow_ups_45_days?.cta_link?.viewer_link,
    tee_link: data?.follow_ups_45_days?.tee_link?.viewer_link,
  };

  const follow_ups_6_months = {
    completed: data?.follow_ups_6_months?.completed === true ? "Yes" : "No",
    date: data?.follow_ups_6_months?.date,
    peri_device_leak:
      data?.follow_ups_6_months?.peri_device_leak === true ? "Yes" : "No",
    width: data?.follow_ups_6_months?.width,
    thrombus: data?.follow_ups_6_months?.thrombus === true ? "Yes" : "No",
    cta_link: data?.follow_ups_6_months?.cta_link?.viewer_link,
    tee_link: data?.follow_ups_6_months?.tee_link?.viewer_link,
  };

  return {
    anticoagulation_selected,
    anticoagulation_options,
    discharge_plan_selected,
    discharge_plan_options,
    anticipated_45_days_follow_up_date,
    follow_ups_45_days,
    follow_ups_6_months,
  };
};

const useApiMedData = () => {
  const data = useSelector(
    (state: RootState) => state.coordinator.schedule.postop.apiMedication
  );
  return data;
};

const findPostOpDiff = () => {
  const userDetails = usePostOpUserDetails();
  const postOpDetails = usePostOpDetails();
  const med = useMedData();
  const apiMed = useApiMedData();

  // Compare discharge plan ID
  const isDischargePlanDifferent =
    userDetails?.discharge_plan?.selected?.id !==
    postOpDetails?.discharge_plan?.selected?.id;

  // Compare anticipated follow-up date
  const isFollowUpDateDifferent =
    userDetails?.anticipated_45_days_follow_up_date !==
    postOpDetails?.anticipated_45_days_follow_up_date;

  // Compare 45-day follow-up fields
  const isFollowUp45Different =
    userDetails?.follow_ups_45_days?.completed !==
      postOpDetails?.follow_ups_45_days?.completed ||
    userDetails?.follow_ups_45_days?.date !==
      postOpDetails?.follow_ups_45_days?.date ||
    userDetails?.follow_ups_45_days?.peri_device_leak !==
      postOpDetails?.follow_ups_45_days?.peri_device_leak ||
    userDetails?.follow_ups_45_days?.thrombus !==
      postOpDetails?.follow_ups_45_days?.thrombus ||
    userDetails?.follow_ups_45_days?.width !==
      postOpDetails?.follow_ups_45_days?.width;

  // Compare 6-month follow-up fields
  const isFollowUp6Different =
    userDetails?.follow_ups_6_months?.completed !==
      postOpDetails?.follow_ups_6_months?.completed ||
    userDetails?.follow_ups_6_months?.date !==
      postOpDetails?.follow_ups_6_months?.date ||
    userDetails?.follow_ups_6_months?.peri_device_leak !==
      postOpDetails?.follow_ups_6_months?.peri_device_leak ||
    userDetails?.follow_ups_6_months?.thrombus !==
      postOpDetails?.follow_ups_6_months?.thrombus ||
    userDetails?.follow_ups_6_months?.width !==
      postOpDetails?.follow_ups_6_months?.width;

  // Compare anticoagulation medications
  const isAnticoagulationDifferent = med.some((item, index) => {
    return (
      item.med_id !== apiMed[index]?.med_id ||
      item.med_dose !== apiMed[index]?.med_dose ||
      item.dosing_frequency_id !== apiMed[index]?.dosing_frequency_id ||
      item.period_id !== apiMed[index]?.period_id ||
      item.period_count !== apiMed[index]?.period_count
    );
  });

  const hasDiff =
    isDischargePlanDifferent ||
    isFollowUpDateDifferent ||
    isFollowUp45Different ||
    isFollowUp6Different ||
    isAnticoagulationDifferent;

  // Prepare current values to return
  const currentValues = {
    anticoagulation: med.map((item) => ({
      medicine: item.med_id,
      quantity: item.med_dose,
      dosing_frequency: item.dosing_frequency_id,
      period: {
        duration: item.period_id,
        count: item.period_count ? item.period_count : null,
      },
    })),
    discharge_plan: userDetails?.discharge_plan?.selected?.id,
    anticipated_45_days_follow_up_date:
      userDetails?.anticipated_45_days_follow_up_date,
    follow_ups_45_days: {
      completed: userDetails?.follow_ups_45_days?.completed ?? false,
      date: userDetails?.follow_ups_45_days?.completed
        ? userDetails?.follow_ups_45_days?.date
        : null,
      peri_device_leak:
        userDetails?.follow_ups_45_days?.peri_device_leak ?? false,
      width: userDetails?.follow_ups_45_days?.width ?? null,
      thrombus: userDetails?.follow_ups_45_days?.thrombus ?? false,
    },
    follow_ups_6_months: {
      completed: userDetails?.follow_ups_6_months?.completed ?? false,
      date: userDetails?.follow_ups_6_months?.completed
        ? userDetails?.follow_ups_6_months?.date
        : null,
      peri_device_leak:
        userDetails?.follow_ups_6_months?.peri_device_leak ?? false,
      width: userDetails?.follow_ups_6_months?.width ?? null,
      thrombus: userDetails?.follow_ups_6_months?.thrombus ?? false,
    },
  };

  return {
    hasDiff,
    currentValues,
  };
};

export {
  useLoaderAndError,
  usePostOpUserDetails,
  usePostOpDetails,
  findPostOpDiff,
  formatPostOpData,
  useMedicationData,
};
