import { useSelector } from "react-redux";
import { RootState } from "../../../store";
import { CaseData } from "../../../store/rep/ScheduleStack/postop/types";

const useLoaderAndError = () => {
  const loader = useSelector((state: RootState) => state.rep.postop.loading);
  const error = useSelector((state: RootState) => state.rep.postop.error);
  return { loader, error };
};

const usePostOpUserDetails = (): CaseData | null => {
  const { userDetails } = useSelector((state: RootState) => state.rep.postop);
  return userDetails;
};

const usePostOpDetails = () => {
  const { postOpDetails } = useSelector((state: RootState) => state.rep.postop);
  return postOpDetails;
};

const formatPostOpData = (data: CaseData | null) => {
  const anticoagulation_selected = data?.anticoagulation?.selected.map(
    (option: any) => ({
      quantity: option?.quantity?.toString(),
      name: option.name,
      value: option.id?.toString(),
    })
  );

  const anticoagulation_options = data?.anticoagulation?.options.map(
    (option: any) => ({
      name: option.name,
      value: option.id,
      quantity: option.quantity,
    })
  );

  const discharge_plan_selected = data?.discharge_plan?.selected?.id;
  const discharge_plan_options = data?.discharge_plan?.options?.map(
    (option: any) => ({
      label: option.name,
      value: option.id,
    })
  );
  const anticipated_45_days_follow_up_date =
    data?.anticipated_45_days_follow_up_date;

  const follow_ups_45_days = {
    completed: data?.follow_ups_45_days?.completed === true ? "Yes" : "No",
    date:
      data?.follow_ups_45_days?.date || new Date().toISOString().split("T")[0],
    peri_device_leak:
      data?.follow_ups_45_days?.peri_device_leak === true ? "Yes" : "No",
    width: data?.follow_ups_45_days?.width,
    thrombus: data?.follow_ups_45_days?.thrombus === true ? "Yes" : "No",
    cta_link: data?.follow_ups_45_days?.cta_link?.viewer_link,
    tee_link: data?.follow_ups_45_days?.tee_link?.viewer_link,
  };

  const follow_ups_6_months = {
    completed: data?.follow_ups_6_months?.completed === true ? "Yes" : "No",
    date:
      data?.follow_ups_6_months?.date || new Date().toISOString().split("T")[0],
    peri_device_leak:
      data?.follow_ups_6_months?.peri_device_leak === true ? "Yes" : "No",
    width: data?.follow_ups_6_months?.width,
    thrombus: data?.follow_ups_6_months?.thrombus === true ? "Yes" : "No",
    cta_link: data?.follow_ups_6_months?.cta_link?.viewer_link,
    tee_link: data?.follow_ups_6_months?.tee_link?.viewer_link,
  };

  const follow_ups_1_yr = {
    completed: data?.follow_ups_1_yr?.completed === true ? "Yes" : "No",
    date: data?.follow_ups_1_yr?.date,
    peri_device_leak:
      data?.follow_ups_1_yr?.peri_device_leak === true ? "Yes" : "No",
    width: data?.follow_ups_1_yr?.width,
    thrombus: data?.follow_ups_1_yr?.thrombus === true ? "Yes" : "No",
    cta_link: data?.follow_ups_1_yr?.cta_link?.viewer_link,
    tee_link: data?.follow_ups_1_yr?.tee_link?.viewer_link,
  };

  return {
    anticoagulation_selected,
    anticoagulation_options,
    discharge_plan_selected,
    discharge_plan_options,
    anticipated_45_days_follow_up_date,
    follow_ups_45_days,
    follow_ups_6_months,
    follow_ups_1_yr,
  };
};

const useApiMedData = () => {
  const data = useSelector(
    (state: RootState) => state.rep.postop.apiMedication
  );
  return data;
};

const findPostOpDiff = () => {
  const userDetails = usePostOpUserDetails();
  const postOpDetails = usePostOpDetails();
  const med = useMedData();
  const apiMed = useApiMedData();

  const diff =
    JSON.stringify(userDetails) !== JSON.stringify(postOpDetails) ||
    JSON.stringify(med) !== JSON.stringify(apiMed);

  const currentValues = {
    discharge_plan: userDetails?.discharge_plan?.selected?.id,
    anticoagulation: med
      ? med?.map((item) => ({
          medicine: item.med_id,
          quantity: item.med_dose,
          dosing_frequency: item.dosing_frequency_id,
          period: {
            duration: item.period_id,
            count: item.period_count ? item.period_count : null,
          },
        }))
      : [],
  };

  return {
    hasDiff: diff,
    currentValues: currentValues,
  };
};

const useMedData = () => {
  const med = useSelector((state: RootState) => state.rep.postop.medication);

  return med;
};

const useMedicationData = (medicineId?: string) => {
  const userDetails = usePostOpUserDetails();

  const anticoagulationOptions = userDetails?.anticoagulation?.options?.map(
    (item) => ({
      label: item.name,
      value: item.id,
    })
  );

  const med_data = useMedData();

  const api_Med_data = useApiMedData();

  const med_selected_to_edit = med_data?.find((med) => med.id === medicineId);

  const dosageOptions = userDetails?.anticoagulation?.options
    ?.find((item) => item.id === med_selected_to_edit?.med_id)
    ?.quantity?.map((quantity) => ({
      label: quantity?.toString(),
      value: quantity?.toString(),
    }));

  const findDosageOptions = (medicationId: string) => {
    return userDetails?.anticoagulation?.options
      ?.find((item) => item.id === medicationId)
      ?.quantity?.map((quantity) => ({
        label: quantity?.toString(),
        value: quantity?.toString(),
      }));
  };

  const frequency_options =
    userDetails?.anticoagulation?.frequency_options?.map((item) => ({
      label: item.name,
      value: item.id,
    }));

  const duration_options = userDetails?.anticoagulation?.duration_options?.map(
    (item) => ({
      label: item.name,
      value: item.id,
    })
  );

  return {
    anticoagulationOptions,
    med_data,
    api_Med_data,
    med_selected_to_edit,
    dosageOptions,
    frequency_options,
    duration_options,
    findDosageOptions,
  };
};

const useMedicationDiff = () => {
  const med = useMedData();
  const userDetails = usePostOpUserDetails();
  const apiMed = useApiMedData();
  const diff = JSON.stringify(med) !== JSON.stringify(apiMed);

  const currentValues = {
    discharge_plan: userDetails?.discharge_plan?.selected?.id,
    anticoagulation: med
      ? med?.map((item) => ({
          medicine: item.med_id,
          quantity: item.med_dose,
          dosing_frequency: item.dosing_frequency_id,
          period: {
            duration: item.period_id,
            count: item.period_count ? item.period_count : null,
          },
        }))
      : [],
  };

  return {
    hasDiff: diff,
    currentValues: currentValues,
  };
};

export {
  useLoaderAndError,
  usePostOpUserDetails,
  usePostOpDetails,
  findPostOpDiff,
  formatPostOpData,
  useMedicationData,
  useMedicationDiff,
};
