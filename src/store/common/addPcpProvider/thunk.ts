import { createAsyncThunk } from "@reduxjs/toolkit";
import api from "../../../api/api";
import { PcpProvider } from "./types";

// Fetch all PCP providers
export const fetchPcpProviders = createAsyncThunk(
  "addpcpprovider/fetchPcpProviders",
  async (_, thunkAPI) => {
    try {
      const response = await api.private.get(
        api.routes.privateRoutes.common.pcp_providers_get
      );

      if (response.data?.result) {
        return response.data.result;
      }

      throw new Error('Missing "result" in API response');
    } catch (error: any) {
      console.error("Error fetching PCP providers:", error.response || error);
      return thunkAPI.rejectWithValue(
        error.response?.data?.message || "Failed to fetch PCP providers"
      );
    }
  }
);

// Fetch specific PCP provider by ID
export const fetchPcpProviderById = createAsyncThunk(
  "addpcpprovider/fetchPcpProviderById",
  async (id: string, thunkAPI) => {
    try {
      const response = await api.private.get(
        api.routes.privateRoutes.common.pcp_providers_id_get(id)
      );

      if (response.data?.result) return response.data.result;

      throw new Error('Missing "result" in API response');
    } catch (error: any) {
      console.error(
        "Error fetching PCP provider by ID:",
        error.response || error
      );
      return thunkAPI.rejectWithValue(
        error.response?.data?.message || "Failed to fetch PCP provider"
      );
    }
  }
);

// Update (PUT) PCP provider by ID
export const updatePcpProvider = createAsyncThunk(
  "addpcpprovider/putPcpProvider",
  async ({ id, payload }: { id: string; payload: PcpProvider }, thunkAPI) => {
    try {
      const response = await api.private.put(
        api.routes.privateRoutes.common.pcp_providers_put(id),
        payload
      );

      if (response.data?.result) return response.data.result;

      throw new Error('Missing "result" in API response');
    } catch (error: any) {
      console.error("Error updating PCP provider:", error.response || error);
      return thunkAPI.rejectWithValue(
        error.response?.data?.message || "Failed to update PCP provider"
      );
    }
  }
);

// Create (POST) a new PCP provider
export const createPcpProvider = createAsyncThunk(
  "addpcpprovider/postPcpProvider",
  async (payload: Omit<PcpProvider, "id">, thunkAPI) => {
    try {
      const response = await api.private.post(
        api.routes.privateRoutes.common.pcp_providers_post,
        payload
      );

      if (response.data?.result) return response.data.result;

      throw new Error('Missing "result" in API response');
    } catch (error: any) {
      console.error("Error creating PCP provider:", error.response || error);
      return thunkAPI.rejectWithValue(
        error.response?.data?.message || "Failed to create PCP provider"
      );
    }
  }
);

// credential-options
export const fetchCredentialOptions = createAsyncThunk(
  "addpcpprovider/fetchCredentialOptions",
  async (_, thunkAPI) => {
    try {
      const response = await api.private.get(
        "/referring-providers/credential-options"
      );
      if (response.data?.result) {
        return response.data.result;
      }
      throw new Error('Missing "result" in API response');
    } catch (error: any) {
      console.error(
        "Error fetching credential options:",
        error.response || error
      );
      return thunkAPI.rejectWithValue(
        error.response?.data?.message || "Failed to fetch credential options"
      );
    }
  }
);

// NPI provider lookup
export const lookupProviderByNPI = createAsyncThunk(
  "addpcpprovider/lookupProviderByNPI",
  async (npi_number: string, thunkAPI) => {
    try {
      const response = await api.private.get(
        api.routes.privateRoutes.common.npi_provider_lookup(npi_number)
      );

      // If result is null, return the response message for UI display
      if (response.data?.result === null) {
        return {
          isNotFound: true,
          message:
            response.data?.message ||
            response.data?.error_description ||
            "No provider found",
        };
      }

      if (response.data?.result) {
        return {
          isNotFound: false,
          data: response.data.result,
        };
      }

      throw new Error("Unexpected API response structure");
    } catch (error: any) {
      console.error(
        "Error looking up provider by NPI:",
        error.response || error
      );
      return thunkAPI.rejectWithValue(
        error.response?.data?.message || "Failed to lookup provider by NPI"
      );
    }
  }
);
