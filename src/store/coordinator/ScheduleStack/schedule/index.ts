import { createSlice } from "@reduxjs/toolkit";
import initialState from "./initialstate";
import { fetchCoordinatorSchedules, putChadScoreDetails } from "./thunk";

// Updated reducer to allow partial update for selectedPatient
const scheduleSlice = createSlice({
  name: "schedules",
  initialState,
  reducers: {
    setSelectedPatient: (state, action) => {
      if (state.selectedPatient) {
        state.selectedPatient = {
          ...state.selectedPatient,
          ...action.payload,
        };
      } else {
        state.selectedPatient = action.payload;
      }
    },
    // Optionally update chadScore as before.
    setChadScore: (state, action) => {
      state.chadScore = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchCoordinatorSchedules.pending, (state) => {
        state.loaders.scheduleLoader = true;
        state.errors.scheduleError = null;
      })
      .addCase(fetchCoordinatorSchedules.fulfilled, (state, action) => {
        state.loaders.scheduleLoader = false;
        state.schedules = action.payload;
        state.userDetails = action.payload;
      })
      .addCase(fetchCoordinatorSchedules.rejected, (state, action) => {
        state.loaders.scheduleLoader = false;
        state.errors.scheduleError = action.payload as string;
        state.schedules = initialState?.schedules;
      })
      .addCase(putChadScoreDetails.pending, (state) => {
        state.loaders.scheduleLoader = true;
        state.errors.scheduleError = null;
      })
      .addCase(putChadScoreDetails.fulfilled, (state, action) => {
        state.loaders.scheduleLoader = false;
        state.chadScore = action.payload;
      })
      .addCase(putChadScoreDetails.rejected, (state, action) => {
        state.loaders.scheduleLoader = false;
        state.errors.scheduleError = action.payload as string;
        state.chadScore = initialState?.chadScore;
      });
  },
});

export const { setSelectedPatient, setChadScore } = scheduleSlice.actions;

export default scheduleSlice.reducer;
