import { createSlice } from "@reduxjs/toolkit";
import initialState from "./initialstate";
import {
  fetchPreopDetails,
  putPreopHistoryDetails,
  putPreopTesting,
  putPreopDetails,
  putConsultVisit,
} from "./thunk";
import { genUUID } from "../../../../utils";

const preopSlice = createSlice({
  name: "preop",
  initialState,
  reducers: {
    setPreopUserDetails: (state, action) => {
      state.userDetails = {
        ...state.userDetails,
        ...action.payload,
      };
    },
    deleteMedicationById: (state, action) => {
      const medicineId = action.payload;
      state.medication = state.medication.filter(
        (med) => med.id !== medicineId
      );
    },
    addMedicationById: (state, action) => {
      const medicineId = action.payload;
      state.medication.push(medicineId);
    },
    updateMedicationById: (state, action) => {
      const { id, updatedMed } = action.payload;
      const index = state.medication.findIndex((med) => med.id === id);
      if (index !== -1) {
        state.medication[index] = {
          ...state.medication[index],
          ...updatedMed,
        };
      }
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchPreopDetails.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchPreopDetails.fulfilled, (state, action) => {
        state.loading = false;
        state.preopDetails = action.payload;
        state.userDetails = action.payload;

        // Check if anticoagulation data exists and is valid before mapping
        const selectedMeds =
          action.payload.patient.anticoagulation?.selected || [];
        const validMeds = selectedMeds.filter(
          (med) =>
            med &&
            med.id &&
            med.name &&
            med.dosing_frequency &&
            med.dosing_frequency.name
        );

        state.medication =
          validMeds.length > 0
            ? validMeds.map((med) => ({
                id: genUUID(),
                med_id: med.id,
                med_name: med.name,
                med_dose: med.quantity || "",
                dosing_frequency: med.dosing_frequency.name,
                dosing_frequency_id: med.dosing_frequency.id,
                // period: med.period.duration.name,
                // period_id: med.period.duration.id,
                // period_count: med.period.count,
              }))
            : [];
        state.apiMedication = [...state.medication];
      })

      .addCase(fetchPreopDetails.rejected, (state, action) => {
        state.loading = false;
        state.preopDetails = null;
        state.error = action.payload as string;
      });
    // builder
    //   .addCase(putPreopDetails.pending, (state) => {
    //     state.loading = true;
    //     state.error = null;
    //   })
    //   .addCase(putPreopDetails.fulfilled, (state, action) => {
    //     state.loading = false;
    //   })
    //   .addCase(putPreopDetails.rejected, (state, action) => {
    //     state.loading = false;
    //     state.userDetails = null;
    //     state.error = action.payload as string;
    //   });

    // builder
    //   .addCase(putPreopHistoryDetails.pending, (state) => {
    //     state.loading = true;
    //     state.error = null;
    //   })
    //   .addCase(putPreopHistoryDetails.fulfilled, (state, action) => {
    //     state.loading = false;
    //   })
    //   .addCase(putPreopHistoryDetails.rejected, (state, action) => {
    //     state.loading = false;
    //     state.userDetails = null;
    //     state.error = action.payload as string;
    //   });

    // builder
    //   .addCase(putConsultVisit.pending, (state) => {
    //     state.loading = true;
    //     state.error = null;
    //   })
    //   .addCase(putConsultVisit.fulfilled, (state, action) => {
    //     state.loading = false;
    //   })
    //   .addCase(putConsultVisit.rejected, (state, action) => {
    //     state.loading = false;
    //     state.userDetails = null;
    //     state.error = action.payload as string;
    //   });

    // builder
    //   .addCase(putPreopTesting.pending, (state) => {
    //     state.loading = true;
    //     state.error = null;
    //   })
    //   .addCase(putPreopTesting.fulfilled, (state, action) => {
    //     state.loading = false;
    //   })
    //   .addCase(putPreopTesting.rejected, (state, action) => {
    //     state.loading = false;
    //     state.userDetails = null;
    //     state.error = action.payload as string;
    //   });
  },
});

export const {
  setPreopUserDetails,
  deleteMedicationById,
  addMedicationById,
  updateMedicationById,
} = preopSlice.actions;
export default preopSlice.reducer;
