import React, { useRef } from "react";
import { View } from "react-native";
import { useFocusEffect } from "@react-navigation/native";

import { useDispatch } from "react-redux";
import { AppDispatch } from "../../../store";
import { useSiteDetails, usePatientCases } from "../hooks/PatientsHooks";
import { fetchCases } from "../../../store/rep/ScheduleStack/patients/thunk";
import { putChadScoreDetails } from "../../../store/rep/ScheduleStack/schedules/thunk";
import SiteCard from "../components/SiteCard";
import PatientList from "../components/PatientList";
import BottomSheetComponent, {
  BottomSheetRefProps,
} from "../../../components/BottomSheetComponent";
import ScreenWrapper from "../../../components/ScreenWrapper";
import CHADCard from "../../../components/CHADCard";
import { useLoadersState } from "../hooks/schedulesHooks";
import Loader from "../../../components/Loader";

const PatientsScreen: React.FunctionComponent = () => {
  const [chad, setChad] = React.useState();
  const [selectedPatientId, setSelectedPatientId] = React.useState(null);
  const bottomSheetRef = useRef<BottomSheetRefProps>(null);
  const dispatch = useDispatch<AppDispatch>();
  const { agendaLoader } = useLoadersState();
  const {
    implantingPhysicianName,
    selectedDate,
    selectedHospitalId,
    selectedHospitalName,
    implantingPhysicianImage,
    hospitalImage,
  } = useSiteDetails();

  const data = usePatientCases();

  const handleOpen = () => {
    bottomSheetRef.current?.open();
  };

  const handleClose = () => {
    bottomSheetRef.current?.close();
  };

  const handleChadPress = (patient: any) => {
    setSelectedPatientId(patient?.case_id);
    setChad(patient?.patient?.cha2ds2_vasc?.calculation);
    handleOpen();
  };

  const saveDetails = async (updatedValues: any) => {
    handleClose();
    try {
      const res = await dispatch(
        putChadScoreDetails({
          case_id: selectedPatientId,
          payload: updatedValues,
        })
      );

      if (res) {
        handleClose();
        await handleRefresh();
      }
    } catch (error) {
      console.error("Error updating CHAD Score:", error);
    }
  };

  useFocusEffect(
    React.useCallback(() => {
      const refreshData = async () => {
        try {
          await handleRefresh();
        } catch (err) {
          console.error("Error in handleRefresh:", err);
        }
      };

      refreshData();
    }, [selectedDate, selectedHospitalId])
  );

  const handleRefresh = async () => {
    try {
      await dispatch(
        fetchCases({
          siteId: selectedHospitalId,
          date: selectedDate,
        })
      );
    } catch (error) {
      console.error("Error fetching cases:", error);
    }
  };

  return (
    <View className="flex-1 bg-primaryBg">
      <View>
        <SiteCard
          details={{
            date: selectedDate,
            hospitalName: selectedHospitalName,
            siteId: selectedHospitalId,
            noOfCases: data.length,
            implantingMD: implantingPhysicianName,
            hospitalImage: hospitalImage,
          }}
          isPatients={true}
          showDate={true}
        />
      </View>

      {agendaLoader ? (
        <View className="flex-1 justify-center items-center">
          <Loader />
        </View>
      ) : (
        <ScreenWrapper direction="column" onRefresh={handleRefresh}>
          <View className="py-1">
            <PatientList onChadPress={handleChadPress} />
          </View>
        </ScreenWrapper>
      )}

      <BottomSheetComponent
        ref={bottomSheetRef}
        snapPoints={["50%", "65%", "80%"]}
      >
        {chad && <CHADCard chad={chad} onSave={saveDetails} />}
      </BottomSheetComponent>
    </View>
  );
};

export default PatientsScreen;
