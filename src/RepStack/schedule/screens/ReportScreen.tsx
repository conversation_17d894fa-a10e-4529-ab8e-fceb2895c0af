import * as React from "react";
import ScreenWrapper from "../../../components/ScreenWrapper";
import { Image, Text, View, TouchableOpacity } from "react-native";
import BostonLogo from "../../../../assests/bs-logo.png";
import {
  usecaseSynopsisDetails,
  useLoaderAndError,
} from "../hooks/reportHooks";
import { fetchcaseSynopsis } from "../../../store/rep/ScheduleStack/report/thunk";
import { useDispatch } from "react-redux";
import { AppDispatch } from "../../../store";
import { useSelectedPatient } from "../hooks/PatientsHooks";
import { useFocusEffect } from "@react-navigation/native";
import { capitalizeName, formatReportData } from "../../../utils";
import Loader from "../../../components/Loader";
import moment from "moment";
import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons";

interface IReportScreenProps {}

const ReportScreen: React.FunctionComponent<IReportScreenProps> = (props) => {
  // NEW START

  const dispatch = useDispatch<AppDispatch>();
  const selectedPatient = useSelectedPatient();
  const details = usecaseSynopsisDetails();
  const reportDetails = formatReportData(details.caseSynopsis);

  const { loader, error } = useLoaderAndError();

  // Helper function to find the largest W value in baseline measurements
  const findLargestW = (baselineTable: any) => {
    if (!baselineTable) return null;
    let largestW = 0;
    let largestWKey = null;

    Object.entries(baselineTable).forEach(([key, values]: [string, any]) => {
      if (!isNaN(Number(key)) && values?.value1) {
        const wValue = parseFloat(values.value1);
        if (wValue > largestW) {
          largestW = wValue;
          largestWKey = key;
        }
      }
    });

    return largestWKey;
  };

  // Helper function to find the lowest compression percentage in final measurements
  const findLowestCompression = (finalMeasurements: any) => {
    if (!finalMeasurements) return null;
    let lowestCompression = Infinity;
    let lowestCompressionKey = null;

    Object.entries(finalMeasurements).forEach(
      ([key, values]: [string, any]) => {
        if (!isNaN(Number(key)) && values?.per) {
          const compressionValue = parseFloat(values.per);
          if (compressionValue < lowestCompression) {
            lowestCompression = compressionValue;
            lowestCompressionKey = key;
          }
        }
      }
    );

    return lowestCompressionKey;
  };

  const largestWKey = findLargestW(reportDetails?.baselineTable);
  const lowestCompressionKey = findLowestCompression(
    reportDetails?.finalMeasurements
  );

  // Screenshot functionality
  const handleScreenshot = () => {
    // Add screenshot functionality here
    console.log("Screenshot button pressed");
  };
  useFocusEffect(
    React.useCallback(() => {
      const fetchDetails = async () => {
        if (selectedPatient?.case_id) {
          const response = await dispatch(
            fetchcaseSynopsis({
              case_id: selectedPatient.case_id,
            })
          );
        }
      };

      fetchDetails();
    }, [selectedPatient?.case_id, dispatch])
  );

  // NEW END

  const uiSection1 = {
    e1: {
      title: "Pt Name",
      value: reportDetails.ptName,
    },
    e2: {
      title: "DOB",
      value: reportDetails?.dob,
    },
  };

  const uiSection2 = {
    e1: {
      title: "Procedure Date",
      value: reportDetails?.date,
    },
  };

  const uiSection3 = {
    e0: {
      title: "Hospital",
      value: reportDetails?.hospital,
    },
    e1: {
      title: "Implanting MD",
      value: reportDetails?.implantingMD,
    },
    e2: {
      title: "Watchman case specialist",
      value: reportDetails?.caseSpecialist,
    },
  };

  const PatientDetails = () => {
    const entries = Object.values(uiSection3);

    return (
      <View className="rounded-lg mt-2">
        <View className="gap-4 py-2">
          <View className="flex-1 flex-row justify-start">
            <View>
              <Text className="font-semibold text-primaryBlack">
                {uiSection1.e1.title?.toUpperCase()}:
              </Text>
            </View>
            <View className="ml-2">
              <Text className="text-primaryBlack text-left">
                {uiSection1.e1.value
                  ? capitalizeName(uiSection1.e1.value)
                  : "N/A"}
              </Text>
            </View>
          </View>
          <View className="flex-1 flex-row justify-start">
            <View className="">
              <Text className="font-semibold text-primaryBlack">
                {uiSection1.e2.title?.toUpperCase()}:
              </Text>
            </View>
            <View className="ml-2">
              <Text className="text-primaryBlack text-center">
                {uiSection1.e2.value
                  ? moment(uiSection1.e2.value).format("MM/DD/YYYY")
                  : "N/A"}
              </Text>
            </View>
          </View>
        </View>

        <View className="flex-row justify-between py-2">
          <View className="flex-1 flex-row justify-start">
            <View>
              <Text className="font-semibold text-primaryBlack">
                {uiSection2.e1.title?.toUpperCase()}:
              </Text>
            </View>
            <View className="ml-2">
              <Text className="text-primaryBlack text-left">
                {uiSection2.e1.value
                  ? moment(uiSection2.e1.value).format("MM/DD/YYYY")
                  : "None"}
              </Text>
            </View>
          </View>
        </View>

        <View>
          {entries.map((data, index) => (
            <View className="flex-row py-2" key={index}>
              <View>
                <Text className="text-primaryBlack font-semibold text-md">
                  {data.title?.toUpperCase()}:
                </Text>
              </View>
              <View className="ml-2">
                <Text className="text-primaryBlack text-md text-center">
                  {data.value ? data.value : "N/A"}
                </Text>
              </View>
            </View>
          ))}
        </View>
      </View>
    );
  };

  const elementSection2 = reportDetails?.baselineTable;

  const elementSection3 = {
    e1: {
      title: "Device",
      value: reportDetails?.device + " " + reportDetails?.deviceName,
    },
  };

  const elementSection4 = {
    e1: {
      title: "Leak",
      value: `${reportDetails?.leak} mm`,
    },
    // e2: {
    //   title: "complications",
    //   value: reportDetails?.complications,
    // },
    // e3: {
    //   title: "Post drug Rx",
    //   value: reportDetails?.postDrugRx?.join(", "),
    // },

    e4: {
      title: "LAP",
      value: `${reportDetails?.lap} mmHg`,
    },
    e5: {
      title: "ACT",
      value: `${reportDetails?.act} sec`,
    },
    e6: {
      title: "Creatinine",
      value: `${reportDetails?.creatinine} mg/dL`,
    },
    e7: {
      title: "Fluoro time",
      value: `${reportDetails?.fluoroTime} min`,
    },
    // e8: {
    //   title: "Fluoro",
    //   value: `${reportDetails?.fluoro} gy/cm2`,
    // },
  };

  const table2 = reportDetails?.finalMeasurements;

  const elementSection6 = {
    e1: {
      title: "PT Rationale",
      value: reportDetails?.ptRationale,
    },
    e2: {
      title: "CHA2DS2-Vasc",
      value: reportDetails?.CHAD,
    },
    e3: {
      title: "Referring Provider",
      value: reportDetails?.referringProvider,
    },
    e4: {
      title: "PCP",
      value: reportDetails?.pcp,
    },
  };

  const elementSection7 = {
    heading: "Final Measurements",
    0: {
      w: 21,
      per: 22,
    },
    45: {
      w: 21,
      per: 22,
    },
    90: {
      w: 21,
      per: 22,
    },
    135: {
      w: 20,
      per: 26,
    },
  };

  const elementSection8 = {
    e1: {
      title: "LAA Type",
      value: reportDetails?.laaType,
    },
    e3: {
      title: "Final TSP Location",
      value: reportDetails?.finalTspLocation,
    },
  };

  const handleRefresh = async () => {
    if (!selectedPatient?.case_id) return;
    await dispatch(
      fetchcaseSynopsis({
        case_id: selectedPatient.case_id,
      })
    );
  };

  if (loader) {
    return <Loader />;
  }

  return (
    <ScreenWrapper direction="column" onRefresh={handleRefresh}>
      {/* Header with Screenshot Button */}
      <View className="flex-row justify-between items-center border-b border-primaryGray pb-3 mb-4">
        <Text className="text-primaryPurple text-xl font-bold">
          Case Report
        </Text>
        <TouchableOpacity
          onPress={handleScreenshot}
          className="bg-primaryPurple p-2 rounded-lg"
        >
          <MaterialCommunityIcons name="camera" color="#FFFFFF" size={20} />
        </TouchableOpacity>
      </View>

      {/* Patient Information Section */}
      <View className="border border-primaryBlack rounded-lg p-4 mb-4">
        <View className="flex-row justify-between mb-2">
          <View className="flex-1">
            <Text className="font-semibold text-primaryBlack">
              PT NAME:{" "}
              {reportDetails.ptName
                ? capitalizeName(reportDetails.ptName)
                : "N/A"}
            </Text>
          </View>
          <View className="flex-1">
            <Text className="font-semibold text-primaryBlack text-right">
              DOB:{" "}
              {reportDetails?.dob
                ? moment(reportDetails.dob).format("MM/DD/YYYY")
                : "N/A"}
            </Text>
          </View>
        </View>

        <Text className="font-semibold text-primaryBlack mb-1">
          CHA2DS2-VASC: {reportDetails?.CHAD || "N/A"}
        </Text>
        <Text className="font-semibold text-primaryBlack mb-1">
          PT RATIONALE: {reportDetails?.ptRationale || "N/A"}
        </Text>
        <Text className="font-semibold text-primaryBlack mb-1">
          PROCEDURE DATE:{" "}
          {reportDetails?.date
            ? moment(reportDetails.date).format("MM/DD/YYYY")
            : "N/A"}
        </Text>
        <Text className="font-semibold text-primaryBlack mb-1">
          SITE: {reportDetails?.hospital || "N/A"}
        </Text>
        <Text className="font-semibold text-primaryBlack mb-1">
          IMPLANTING PROVIDER: {reportDetails?.implantingMD || "N/A"}
        </Text>
        <Text className="font-semibold text-primaryBlack mb-1">
          CASE SPECIALIST: {reportDetails?.caseSpecialist || "N/A"}
        </Text>
        <Text className="font-semibold text-primaryBlack mb-1">
          REFERRING PROVIDER: {reportDetails?.referringProvider || "N/A"}
        </Text>
        <Text className="font-semibold text-primaryBlack">
          PCP: {reportDetails?.pcp || "N/A"}
        </Text>
      </View>

      {/* Device Section */}
      <View className="border border-primaryBlack rounded-lg p-4 mb-4">
        <Text className="font-semibold text-primaryBlack">
          DEVICE:{" "}
          {reportDetails?.device
            ? `${reportDetails.device} ${reportDetails.deviceName || ""}`.trim()
            : "N/A"}
        </Text>
      </View>

      {/* Tables Section */}
      <View className="flex-row justify-between mb-4">
        {/* Baseline Measurements Table */}
        {elementSection2 && (
          <View className="flex-1 mr-2">
            <View className="border border-primaryBlack rounded-lg p-3">
              <Text className="font-semibold text-center text-primaryBlack mb-2">
                {elementSection2?.tableHeading?.toUpperCase() ||
                  "BASELINE MEASUREMENTS"}
              </Text>
              <View className="flex-row border-b border-primaryBlack py-1">
                <Text className="flex-1 font-semibold text-center text-primaryPurple text-xs">
                  Angle
                </Text>
                <Text className="flex-1 font-semibold text-center text-primaryPurple text-xs">
                  W (mm)
                </Text>
                <Text className="flex-1 font-semibold text-center text-primaryPurple text-xs">
                  L (mm)
                </Text>
              </View>

              {Object.entries(elementSection2).map(
                ([key, values]: [string, any], index) => {
                  if (isNaN(Number(key))) return null;
                  const isLargestW = key === largestWKey;

                  return (
                    <View
                      key={index}
                      className={`flex-row py-1 ${
                        index ===
                        Object.entries(elementSection2).filter(
                          ([k]) => !isNaN(Number(k))
                        ).length -
                          1
                          ? ""
                          : "border-b border-primaryBlack"
                      }`}
                    >
                      <Text className="flex-1 text-center text-primaryBlack text-xs">
                        {key}°
                      </Text>
                      <View className="flex-1 items-center">
                        <View
                          className={`${
                            isLargestW
                              ? "bg-green-3 rounded-full px-2 py-1"
                              : ""
                          }`}
                        >
                          <Text
                            className={`text-center text-xs ${
                              isLargestW
                                ? "text-white font-bold"
                                : "text-primaryBlack"
                            }`}
                          >
                            {values?.value1 || "N/A"}
                          </Text>
                        </View>
                      </View>
                      <Text className="flex-1 text-center text-primaryBlack text-xs">
                        {values?.value2 || "N/A"}
                      </Text>
                    </View>
                  );
                }
              )}
            </View>
          </View>
        )}

        {/* Final Measurements Table */}
        {table2 && (
          <View className="flex-1 ml-2">
            <View className="border border-primaryBlack rounded-lg p-3">
              <Text className="font-semibold text-center text-primaryBlack mb-2">
                {table2?.heading?.toUpperCase() || "FINAL MEASUREMENTS"}
              </Text>
              <View className="flex-row border-b border-primaryBlack py-1">
                <Text className="flex-1 font-semibold text-center text-primaryPurple text-xs">
                  Angle
                </Text>
                <Text className="flex-1 font-semibold text-center text-primaryPurple text-xs">
                  W (mm)
                </Text>
                <Text className="flex-1 font-semibold text-center text-primaryPurple text-xs">
                  C (%)
                </Text>
              </View>

              {Object.entries(table2)?.map(
                ([key, values]: [string, any], index) => {
                  if (isNaN(Number(key))) return null;
                  const isLowestCompression = key === lowestCompressionKey;

                  return (
                    <View
                      key={index}
                      className={`flex-row py-1 ${
                        index ===
                        Object.entries(table2).filter(
                          ([k]) => !isNaN(Number(k))
                        ).length -
                          1
                          ? ""
                          : "border-b border-primaryBlack"
                      }`}
                    >
                      <Text className="flex-1 text-center text-primaryBlack text-xs">
                        {key}°
                      </Text>
                      <Text className="flex-1 text-center text-primaryBlack text-xs">
                        {values?.w || "N/A"}
                      </Text>
                      <View className="flex-1 items-center">
                        <View
                          className={`${
                            isLowestCompression
                              ? "bg-green-3 rounded-full px-2 py-1"
                              : ""
                          }`}
                        >
                          <Text
                            className={`text-center text-xs ${
                              isLowestCompression
                                ? "text-white font-bold"
                                : "text-primaryBlack"
                            }`}
                          >
                            {values?.per || "N/A"}
                          </Text>
                        </View>
                      </View>
                    </View>
                  );
                }
              )}
            </View>
          </View>
        )}
      </View>

      {/* Additional Information Section */}
      <View className="border border-primaryBlack rounded-lg p-4 mb-4">
        <Text className="font-semibold text-primaryBlack mb-2">
          LAA TYPE: {reportDetails?.laaType || "N/A"}
        </Text>
        <Text className="font-semibold text-primaryBlack mb-2">
          FINAL TSP LOCATION: {reportDetails?.finalTspLocation || "N/A"}
        </Text>
        <Text className="font-semibold text-primaryBlack mb-2">
          COMPLICATIONS: {reportDetails?.complications || "N/A"}
        </Text>
        <Text className="font-semibold text-primaryBlack mb-2">
          LEAK: {reportDetails?.leak ? `${reportDetails.leak} mm` : "None"}
        </Text>
        <Text className="font-semibold text-primaryBlack mb-2">
          LAP: {reportDetails?.lap ? `${reportDetails.lap} mmHg` : "N/A"} ACT:{" "}
          {reportDetails?.act ? `${reportDetails.act} sec` : "N/A"}
        </Text>
        <Text className="font-semibold text-primaryBlack mb-2">
          FLUORO TIME:{" "}
          {reportDetails?.fluoroTime
            ? `${reportDetails.fluoroTime} min`
            : "N/A"}
        </Text>
        <Text className="font-semibold text-primaryBlack">
          POST DRUG RX:{" "}
          {reportDetails?.postDrugRx
            ? reportDetails.postDrugRx
                .map((item: string) =>
                  item.includes("Indefinitely")
                    ? item.replace("x 0", "-").trim()
                    : item
                )
                .join(", ")
            : "N/A"}
        </Text>
      </View>
    </ScreenWrapper>
  );
};

export default ReportScreen;
