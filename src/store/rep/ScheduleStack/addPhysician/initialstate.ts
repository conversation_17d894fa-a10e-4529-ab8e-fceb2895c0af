import { Provider } from "react-redux";
import { ImplantingPhysician, Procedure } from "./types";

const initialState = {
  implantingPhysicianDetails: null as ImplantingPhysician | null,
  userDetails: null as ImplantingPhysician | null,
  providerCredentials: [],
  providerExperience: [],
  activeTab: "sites" as "sites" | "implanting-physicians",
  showAddPhysicianModal: false,
  selectedSite: "",
  selectedPhysician: "",
  implantingPhysicianList: [],
  procedureTypes: [] as Procedure[],
  loading: false,
  error: null,
};

export default initialState;
