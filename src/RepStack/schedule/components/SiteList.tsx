import React, { useEffect } from "react";
import { Text, View } from "react-native";
import { useFocusEffect, useNavigation } from "@react-navigation/native";

import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons";
import SearchablePicker from "../../../components/SearchablePicker";
import { useSiteList } from "../hooks/schedulesHooks";
import { fetchSiteList } from "../../../store/rep/ScheduleStack/schedules/thunk";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch } from "../../../store";
import {
  findPostSiteDiff,
  formatPostSite,
  useLoaderAndError,
  useUserSitesDetails,
  useSitesDetails,
  useSelectedSite,
} from "../hooks/addSiteHooks";
import {
  fetchSpecificSite,
  putSite,
} from "../../../store/rep/ScheduleStack/addSite/thunk";
import CustomCard from "../../../components/CustomCard";
import Heading from "../../../components/Heading";
import OptionSelector from "../../../components/OptionSelector";
import LineSeperator from "../../../components/LineSeperator";
import SaveActionButton from "../../../components/SaveActionButton";
import Loader from "../../../components/Loader";
import CustomInput from "../../../components/CustomTextInput";
import ScreenWrapper from "../../../components/ScreenWrapper";
import { parseCharInput, parseIntInput } from "../../../utils";
import {
  setSelectedSite,
  setSiteDetails,
  showAddSiteModal,
} from "../../../store/rep/ScheduleStack/addSite";
import PopupModal from "../../../components/Popup";
import { simpleAutoSave } from "../../../services/simpleAutoSave";
import { setSaveStatus, setLastSavedData } from "../../../store/services";

interface SiteListProps {
  onAddPress: () => void;
  onSiteSelect?: (siteId: string) => void;
}

const SiteList: React.FC<SiteListProps> = ({ onAddPress, onSiteSelect }) => {
  const dispatch = useDispatch<AppDispatch>();
  const navigation = useNavigation();
  const [popupMsg, setPopupMsg] = React.useState<string[]>([]);
  const [modalVisible, setModalVisible] = React.useState(false);
  const siteListData = useSiteList();
  const userDetails = useUserSitesDetails();
  const siteDetails = useSitesDetails();
  const selectedSite = useSelectedSite();
  const { loader, error } = useLoaderAndError();

  const {
    site_name,
    site_account_id,
    site_address,
    site_city,
    site_state,
    site_zipcode,
  } = formatPostSite(userDetails);
  const diff = findPostSiteDiff();

  useEffect(() => {
    dispatch(fetchSiteList());
  }, [dispatch]);
  // Create a ref to track if a save is in progress
  const saveInProgressRef = React.useRef(false);

  // Create a ref to track the last saved data hash
  const lastSavedDataHashRef = React.useRef("");
  useFocusEffect(
    React.useCallback(() => {
      const fetchDetails = async () => {
        if (selectedSite) {
          const res = await dispatch(fetchSpecificSite(selectedSite));
          if (res.payload) {
            dispatch(setSiteDetails(res.payload));
          }
        }
      };

      fetchDetails();
    }, [dispatch, selectedSite])
  );

  const validator = () => {
    if (!initialDataLoadedRef.current) {
      return false;
    }

    setPopupMsg([]);

    let hasError = false;

    if (!site_name || site_name.trim() === "") {
      setPopupMsg((prev) => [...prev, "Please Enter Site Name"]);
      hasError = true;
    }

    if (!site_city || site_city.trim() === "") {
      setPopupMsg((prev) => [...prev, "Please Enter Site City"]);
      hasError = true;
    }

    if (!site_state || site_state.trim() === "") {
      setPopupMsg((prev) => [...prev, "Please Enter Site State"]);
      hasError = true;
    }

    if (hasError) {
      setModalVisible(true);
      return false;
    }

    return true;
  };

  // const validator = () => {
  //   setPopupMsg([]);

  //   var temp = false;
  //   if (site_name === "" || site_name === null || site_name === undefined) {
  //     setPopupMsg((prev) => [...prev, "Please Enter Site Name"]);
  //     temp = true;
  //   }

  //   if (site_city === "" || site_city === null || site_city === undefined) {
  //     setPopupMsg((prev) => [...prev, "Please Enter Site City"]);
  //     temp = true;
  //   }

  //   if (site_state === "" || site_state === null || site_state === undefined) {
  //     setPopupMsg((prev) => [...prev, "Please Enter Site State"]);
  //     temp = true;
  //   }

  //   if (temp) {
  //     setModalVisible(true);
  //     return false;
  //   } else {
  //     return true;
  //   }
  // };

  const siteOptions = React.useMemo(() => {
    return (
      siteListData?.map((site) => ({
        label: site.name,
        value: site.id,
      })) || []
    );
  }, [siteListData]);

  const handleSiteChange = async (value: { value: string; label: string }) => {
    dispatch(showAddSiteModal(false));
    dispatch(setSelectedSite(value.value));
    onSiteSelect?.(value.value);

    if (value.value) {
      const res = await dispatch(fetchSpecificSite(value.value));
      if (res.payload) {
        dispatch(setSiteDetails(res.payload));
      }
    }
  };

  const saveDetails = async (): Promise<void> => {
    try {
      const isValid = validator();

      if (!isValid) {
        dispatch(
          setSaveStatus({
            screenId: "siteDetails", // update if needed
            status: "validation_failed",
          })
        );
        setLocalSaveStatus("validation_failed");
        throw new Error("Validation failed");
      }

      if (saveInProgressRef.current) {
        return;
      }

      const dataCopy = { ...diff.currentValues };
      const currentDataHash = JSON.stringify(dataCopy);

      if (currentDataHash === lastSavedDataHashRef.current) {
        return;
      }

      saveInProgressRef.current = true;

      const res = await dispatch(
        putSite({
          site_id: selectedSite,
          payload: dataCopy,
        })
      );

      if (res?.payload) {
        await dispatch(fetchSpecificSite(selectedSite));
        await dispatch(fetchSiteList());
        lastSavedDataHashRef.current = currentDataHash;
      }

      saveInProgressRef.current = false;
    } catch (err) {
      console.error("Save error:", err);
      saveInProgressRef.current = false;
      throw err;
    }
  };

  const handleCancel = () => {
    // setSelectedSite("");
    dispatch(
      setSiteDetails({
        ...userDetails,
        ...siteDetails,
      })
    );
  };

  const handleSiteContextAdd = (context: string) => {
    switch (context) {
      case "site":
        dispatch(showAddSiteModal(true));
        break;
      default:
        console.log("Unknown context:", context);
    }
  };
  const saveStatus = useSelector(
    (state: any) => state.common?.autoSave?.["siteDetails"]?.status
  );

  const [localSaveStatus, setLocalSaveStatus] = React.useState<string>("");

  const effectiveSaveStatus = saveStatus || localSaveStatus;

  /**
   * Derive the save message and styling based on the save status
   */
  const { saveMessage, messageStyle, iconName, iconColor } =
    React.useMemo(() => {
      let message = "";
      let style = "text-primaryPurple italic";
      let icon = "alert-circle-outline";
      let color = "#8143d9";

      switch (effectiveSaveStatus) {
        case "changes_detected":
          message = "Changes detected, saving soon...";
          style = "text-primaryBlack italic";
          icon = "alert-circle-outline";
          color = "#f5c542"; // yellow
          break;
        case "saving":
          message = "Saving... Please don't close the app";
          style = "text-primaryPurple font-bold italic";
          icon = "alert-circle-outline";
          color = "#8143d9"; // purple
          break;
        case "saved":
          message = "All changes saved";
          style = "text-green-3 italic";
          icon = "check-circle-outline";
          color = "#28a745"; // green
          break;
        case "validation_failed":
          message = "Validation failed. Please check your inputs.";
          style = "text-red-3 font-bold italic";
          icon = "alert-circle-outline";
          color = "#d93025"; // red
          break;
        case "error":
          message = "Error saving changes. Please try again.";
          style = "text-red-3 font-bold italic";
          icon = "alert-circle-outline";
          color = "#d93025"; // red
          break;
      }

      return {
        saveMessage: message,
        messageStyle: style,
        iconName: icon,
        iconColor: color,
      };
    }, [effectiveSaveStatus]);

  /**
   * Ref to track previous diff values to prevent unnecessary saves
   */
  const prevDiffRef = React.useRef<string>("");

  /**
   * Ref to track if initial data has been loaded
   * This prevents autosave from triggering before data is fetched
   */
  const initialDataLoadedRef = React.useRef<boolean>(false);

  /**
   * Effect to set initialDataLoaded when data is fetched
   */
  // React.useEffect(() => {
  //   // Check if we have valid data
  //   const hasValidData = selectedSite !== undefined;

  //   if (hasValidData && !initialDataLoadedRef.current) {
  //     initialDataLoadedRef.current = true;
  //   }
  // }, [userDetails]);

  React.useEffect(() => {
    if (selectedSite && userDetails?.name) {
      initialDataLoadedRef.current = true;
    }
  }, [selectedSite, userDetails]);

  /**
   * Effect to trigger autosave when data changes
   * This uses our new simpleAutoSave service with local state fallback
   */
  React.useEffect(() => {
    // Only proceed if initial data has been loaded and there are actual differences
    if (!initialDataLoadedRef.current) {
      return;
    }

    // Check if selectedSite exists to prevent API calls with undefined ID
    if (!selectedSite) {
      return;
    }

    // Only proceed if there are actual differences
    if (diff.hasDiff) {
      // Check if we have any undefined values in critical fields
      const hasUndefinedValues = Object.values(diff.currentValues).every(
        (val) => val === undefined
      );
      if (hasUndefinedValues) {
        return;
      }

      // Convert current values to string for comparison
      const currentDiffString = JSON.stringify(diff.currentValues);

      // Only trigger save if the values have actually changed
      if (currentDiffString !== prevDiffRef.current) {
        // Update the ref with current values
        prevDiffRef.current = currentDiffString;

        // Create a custom wrapper for setSaveStatus that updates both Redux and local state
        const setStatusWithFallback = (payload: {
          screenId: string;
          status: string;
        }) => {
          // Update local state as a fallback
          setLocalSaveStatus(payload.status);
          // Also dispatch to Redux
          return dispatch(setSaveStatus(payload));
        };

        // Trigger the simple autosave with the current values
        simpleAutoSave(
          "siteDetails", // Screen ID
          diff.currentValues, // Data to save
          saveDetails, // Save function
          4000, // 4 second delay before saving
          dispatch, // Redux dispatch
          setStatusWithFallback, // Custom action to set save status with fallback
          setLastSavedData // Action to set last saved data
        );
        dispatch(setSelectedSite(selectedSite));
      } else {
        console.log("No actual changes in diff values, skipping autosave");
      }
    }
  }, [
    diff.hasDiff,
    diff.currentValues,
    dispatch,
    setLocalSaveStatus,
    userDetails,
  ]);
  // console.log(siteOptions, "siteOptions");
  const handleRefresh = async () => {
    try {
      await dispatch(fetchSiteList());

      if (selectedSite) {
        const siteRes = await dispatch(fetchSpecificSite(selectedSite));
        if (siteRes.payload) {
          dispatch(setSiteDetails(siteRes.payload));
        }
      }
    } catch (error) {
      console.error("Error refreshing site list:", error);
    }
  };

  return (
    <>
      {loader ? (
        <View className="mt-64">
          <Loader />
        </View>
      ) : (
        <ScreenWrapper direction="column" onRefresh={handleRefresh}>
          <CustomCard extraStyle="flex-1">
            {saveMessage && (
              <View
                style={{
                  flexDirection: "row",
                  justifyContent: "flex-end",
                  alignItems: "center",
                  marginVertical: 4,
                }}
              >
                <MaterialCommunityIcons
                  name={iconName}
                  color={iconColor}
                  size={20}
                />
                <Text
                  className={`text-sm ${messageStyle}`}
                  style={{ marginLeft: 6 }}
                >
                  {saveMessage}
                </Text>
              </View>
            )}
            <View className="mt-2 gap-3">
              <View className="flex-row justify-between items-center">
                <Heading
                  text="Hospital Site"
                  size="sub-heading"
                  showSeperator={false}
                />
                <MaterialCommunityIcons
                  name="plus-circle-outline"
                  color="#8143d9"
                  size={25}
                  onPress={onAddPress}
                />
              </View>

              <View className="">
                <SearchablePicker
                  placeholder="Select"
                  items={siteOptions}
                  value={selectedSite}
                  onValueChange={handleSiteChange}
                  screenContext="site"
                  onAddPress={handleSiteContextAdd}
                  showAddInSearch={true}
                />
              </View>
            </View>

            {selectedSite && userDetails && (
              <>
                <LineSeperator extraStyle="mt-5 mb-3" />

                <View className="rounded-lg">
                  <View className="mb-3">
                    <Heading
                      text="Hospital Name"
                      size="sub-heading"
                      showSeperator={false}
                    />
                    <View className="mt-2">
                      <CustomInput
                        inputValue={site_name}
                        // disabled={true}
                        placeholder="Hospital Name"
                        onInputChange={(value) => {
                          parseCharInput(value, (parsedValue) => {
                            dispatch(
                              setSiteDetails({
                                ...userDetails,
                                name: parsedValue,
                              })
                            );
                          });
                        }}
                      />
                    </View>
                  </View>

                  <LineSeperator extraStyle="mt-2 mb-3" />

                  <View className="mb-3">
                    <Heading
                      text="Account ID"
                      size="sub-heading"
                      showSeperator={false}
                    />
                    <View className="mt-2">
                      <CustomInput
                        inputValue={site_account_id?.toString()}
                        disabled={true}
                        placeholder="Account ID"
                        onInputChange={(value) => {
                          parseIntInput(value, (parsedValue) => {
                            dispatch(
                              setSiteDetails({
                                ...userDetails,
                                account: parsedValue.toString(),
                              })
                            );
                          });
                        }}
                        keyboardType="numeric"
                      />
                    </View>
                  </View>

                  <LineSeperator extraStyle="mt-2 mb-3" />

                  <View className="mb-3">
                    <Heading
                      text="Address"
                      size="sub-heading"
                      showSeperator={false}
                    />
                    <View className="mt-2">
                      <CustomInput
                        inputValue={site_address}
                        disabled={true}
                        placeholder="Address"
                        onInputChange={(value) => {
                          dispatch(
                            setSiteDetails({
                              ...userDetails,
                              address: value,
                            })
                          );
                        }}
                      />
                    </View>
                  </View>

                  <LineSeperator extraStyle="mt-2 mb-3" />

                  <View className="mb-3">
                    <Heading
                      text="City"
                      size="sub-heading"
                      showSeperator={false}
                    />
                    <View className="mt-2">
                      <CustomInput
                        inputValue={site_city}
                        disabled={true}
                        placeholder="City"
                        onInputChange={(value) => {
                          parseCharInput(value, (parsedValue) => {
                            dispatch(
                              setSiteDetails({
                                ...userDetails,
                                city: parsedValue,
                              })
                            );
                          });
                        }}
                      />
                    </View>
                  </View>

                  <LineSeperator extraStyle="mt-2 mb-3" />

                  <View className="mb-3">
                    <Heading
                      text="State"
                      size="sub-heading"
                      showSeperator={false}
                    />
                    <View className="mt-2">
                      <CustomInput
                        inputValue={site_state}
                        disabled={true}
                        placeholder="State"
                        onInputChange={(value) => {
                          parseCharInput(value, (parsedValue) => {
                            dispatch(
                              setSiteDetails({
                                ...userDetails,
                                state: parsedValue,
                              })
                            );
                          });
                        }}
                      />
                    </View>
                  </View>

                  <LineSeperator extraStyle="mt-2 mb-3" />

                  <View className="mb-3">
                    <Heading
                      text="Zipcode"
                      size="sub-heading"
                      showSeperator={false}
                    />
                    <View className="mt-2">
                      <CustomInput
                        inputValue={site_zipcode?.toString()}
                        disabled={true}
                        placeholder="Zipcode"
                        onInputChange={(value) => {
                          parseIntInput(value, (parsedValue) => {
                            dispatch(
                              setSiteDetails({
                                zip_code: parsedValue.toString(),
                              })
                            );
                          });
                        }}
                        keyboardType="numeric"
                      />
                    </View>
                  </View>
                </View>

                <LineSeperator extraStyle="my-5" />

                {/* <View className="flex-row justify-center gap-4">
                  <SaveActionButton
                    disabled={!diff.hasDiff}
                    onPress={saveDetails}
                    onCancel={handleCancel}
                  />
                </View> */}
              </>
            )}
            <View className="mb-3"></View>
          </CustomCard>
        </ScreenWrapper>
      )}
      <View className="mt-9"></View>

      <PopupModal
        show={modalVisible}
        msg={popupMsg}
        status="warning"
        onClose={() => setModalVisible(false)}
      />
    </>
  );
};

export default SiteList;
