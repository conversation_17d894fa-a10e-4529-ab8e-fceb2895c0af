import { createSlice } from "@reduxjs/toolkit";
import initialState from "./initialstate";
import { fetchAbstractorSchedules } from "./thunk";

const abstractorScheduleSlice = createSlice({
  name: "abstractorSchedules",
  initialState,
  reducers: {
    setSelectedPatient: (state, action) => {
      state.selectedPatient = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder

      .addCase(fetchAbstractorSchedules.pending, (state) => {
        state.loaders.scheduleLoader = true;
        state.errors.scheduleError = null;
      })
      .addCase(fetchAbstractorSchedules.fulfilled, (state, action) => {
        state.loaders.scheduleLoader = false;
        state.schedules = action.payload;
      })
      .addCase(fetchAbstractorSchedules.rejected, (state, action) => {
        state.loaders.scheduleLoader = false;
        state.errors.scheduleError = action.payload as string;
        state.schedules = initialState?.schedules;
      });
  },
});

export const { setSelectedPatient } = abstractorScheduleSlice.actions;

export default abstractorScheduleSlice.reducer;
