import { configureStore, combineReducers } from "@reduxjs/toolkit";
import authReducer from "./auth";
import patientListReducer from "./rep/ScheduleStack/patients";
import scheduleReducer from "./rep/ScheduleStack/schedules";
import patientDetailsReducer from "./rep/ScheduleStack/patientDetails";
import asyncActionLogger from "./middleware";
import coordinatorSchedule from "./coordinator/ScheduleStack/schedule";
import abstractorSchedule from "./abstractor/ScheduleStack/schedule";
import anesthesiaReducer from "./rep/ScheduleStack/laaoProcedures/anesthesia";
import transseptalpunctureReducer from "./rep/ScheduleStack/laaoProcedures/transseptalpuncture";
import laaoImplantReducer from "./rep/ScheduleStack/laaoProcedures/laaoimplant";
import fluoroscopyReducer from "./rep/ScheduleStack/laaoProcedures/fluoroscopy";
import passcriteriaReducer from "./rep/ScheduleStack/laaoProcedures/passcriteria";
import laaanatomyReducer from "./rep/ScheduleStack/laaoProcedures/laaanatomy";
import profileReducer from "./rep/MyProfileStack/MyProfileScreen";
import preopCTAReducer from "./rep/ScheduleStack/preop-cta";
import caseSynopcisReducer from "./rep/ScheduleStack/report";
import ToasReducer from "./toaster";
import ErrorHandler from "./ErrorHandlerMiddleWare";
import ChatBotReducer from "./chatbot";
import repTasksReducer from "./rep/Tasks/tasks";
import coordinatorTasksReducer from "./coordinator/Tasks/tasks";
import afibAblationBasicReducer from "./rep/ScheduleStack/afibAblation/basic";
import afibAblationProcedureDetailsReducer from "./rep/ScheduleStack/afibAblation/procedureDetails";
import reppostPatientSlices from "./rep/ScheduleStack/addpatient";
import postopReducer from "./rep/ScheduleStack/postop";
import afibCoordinatorBasicReducer from "./coordinator/ScheduleStack/afibAblation/basic";
import afibCoordinatorProcedureDetailsReducer from "./coordinator/ScheduleStack/afibAblation/procedureDetails";
import coordinatorCaseSynopsisReducer from "./coordinator/ScheduleStack/report";
import procedureDetailsReducer from "./coordinator/ScheduleStack/procedure";
import postPatientSlices from "./coordinator/ScheduleStack/addpatient";
import coordinatorPreopReducer from "./coordinator/ScheduleStack/preop";
import coordinatorPreopCtaReducer from "./coordinator/ScheduleStack/preop-cta";
import coordinatorPreopTeeReducer from "./coordinator/ScheduleStack/tee";
import coordinatorProfileReducer from "./coordinator/MyProfile/MyProfileScreen";
import coordinatorPostOpReducer from "./coordinator/ScheduleStack/postop";
import clinicainScheduleReducer from "./clinician/ScheduleStack/schedule";
import clinicianLaaoAnatomyReducer from "./clinician/ScheduleStack/laaanatomy";
import coordinatorAnesthesiaReducer from "./clinician/ScheduleStack/anesthesia";
import clinicianTransseptalpunctureReducer from "./clinician/ScheduleStack/transseptalpuncture";
import clinicianLaaoImplantReducer from "./clinician/ScheduleStack/laaoimplant";
import clinicianPasscriteriaReducer from "./clinician/ScheduleStack/passcriteria";
import clinicianFluoroscopyReducer from "./clinician/ScheduleStack/fluoroscopy";
import clinicianReportReducer from "./clinician/ScheduleStack/report";
import clinicianPostopReducer from "./clinician/ScheduleStack/postop";
import clinicinaPreopReducer from "./clinician/ScheduleStack/preop";
import clinicianPostopSlices from "./clinician/ScheduleStack/addpatient";
import clinicianProocedureDetailsReducer from "./clinician/ScheduleStack/procedure";
import clinicianProfileReducer from "./clinician/MyProfile/MyProfileScreen";
import commonDashboardReducer from "./common/dashboard";
import jiraReducer from "./issuefeedback";
import siteReducer from "./rep/ScheduleStack/addSite";
import referringProvider from "./common/addReferringProvider";
import PcpProvider from "./common/addPcpProvider";
import postPhysicianReducer from "./rep/ScheduleStack/addPhysician";
import autoSaveReducer from "./services";

const clinicianReducer = combineReducers({
  preop: clinicinaPreopReducer,
  schedules: clinicainScheduleReducer,
  laaoProcedure: combineReducers({
    laaanatomy: clinicianLaaoAnatomyReducer,
    anesthesia: coordinatorAnesthesiaReducer,
    transseptalpuncture: clinicianTransseptalpunctureReducer,
    laaoimplant: clinicianLaaoImplantReducer,
    passcriteria: clinicianPasscriteriaReducer,
    fluoroscopy: clinicianFluoroscopyReducer,
  }),
  report: clinicianReportReducer,
  postop: clinicianPostopReducer,
  postPatient: clinicianPostopSlices.postPatientSlice.reducer,
  // referringProviders: clinicianPostopSlices.referringProvidersSlice.reducer,

  implantingPhysicians: clinicianPostopSlices.implantingPhysiciansSlice.reducer,
  procedureDetails: clinicianProocedureDetailsReducer,
  profile: clinicianProfileReducer,
});

const coordinatorReducer = combineReducers({
  schedule: combineReducers({
    schedules: coordinatorSchedule,
    cta: coordinatorPreopCtaReducer,
    tee: coordinatorPreopTeeReducer,
    postop: coordinatorPostOpReducer,
    afib: combineReducers({
      basic: afibCoordinatorBasicReducer,
      procedureDetails: afibCoordinatorProcedureDetailsReducer,
    }),
    preop: coordinatorPreopReducer,
    procedureDetails: procedureDetailsReducer,
    report: coordinatorCaseSynopsisReducer,
    postPatient: postPatientSlices.postPatientSlice.reducer,
    // referringProviders: postPatientSlices.referringProvidersSlice.reducer,

    implantingPhysicians: postPatientSlices.implantingPhysiciansSlice.reducer,
    rationaleOptions: postPatientSlices.rationaleSlice.reducer,
  }),
  profile: coordinatorProfileReducer,
  tasks: coordinatorTasksReducer,
});

const repReducer = combineReducers({
  anesthesia: anesthesiaReducer,
  transseptalpuncture: transseptalpunctureReducer,
  laaoimplant: laaoImplantReducer,
  fluoroscopy: fluoroscopyReducer,
  passcriteria: passcriteriaReducer,
  laaanatomy: laaanatomyReducer,
  cta: preopCTAReducer,
  tasks: repTasksReducer,
  postop: postopReducer,
  postPatient: reppostPatientSlices.postPatientSlice.reducer,
  // referringProviders: reppostPatientSlices.referringProvidersSlice.reducer,

  implantingPhysicians: reppostPatientSlices.implantingPhysiciansSlice.reducer,
  afib: combineReducers({
    basic: afibAblationBasicReducer,
    procedureDetails: afibAblationProcedureDetailsReducer,
  }),
  settings: combineReducers({
    sites: siteReducer,
    implantingPhysicians: postPhysicianReducer,
  }),
});

const abstractor = combineReducers({
  schedule: combineReducers({
    schedules: abstractorSchedule,
  }),
});

const common = combineReducers({
  dashboard: commonDashboardReducer,
  autoSave: autoSaveReducer,
  referringProviders: referringProvider,
  pcpProviders: PcpProvider,
});

const store = configureStore({
  reducer: {
    auth: authReducer,
    // REP REDUCERS
    rep: repReducer,
    patients: patientListReducer,
    schedules: scheduleReducer,
    preop: patientDetailsReducer,
    profile: profileReducer,
    report: caseSynopcisReducer,
    // COORDINATOR REDUCERS
    coordinator: coordinatorReducer,
    // CLINICIAN REDUCERS
    clinician: clinicianReducer,

    abstractor: abstractor,
    // COMMON REDUCERS
    common: common,
    // TOASTER
    toaster: ToasReducer,
    // CHATBOT
    chatbot: ChatBotReducer,

    jira: jiraReducer,
  },

  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware().concat(asyncActionLogger).concat(ErrorHandler),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
export default store;
