import React, { useState, useRef } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  Animated,
  Dimensions,
} from "react-native";

interface TabOption {
  label: string;
  value: string;
}

interface CustomTabViewProps {
  options: TabOption[];
  onSelect: (value: string) => void;
  defaultIndex?: number;
}

const CustomTabView: React.FC<CustomTabViewProps> = ({
  options,
  onSelect,
  defaultIndex = 1, // Use passed defaultIndex prop or default to 1
}) => {
  const [currentIndex, setCurrentIndex] = useState(defaultIndex);
  const windowWidth = Dimensions.get("window").width;
  const horizontalPadding = 1;
  const tabWidth = (windowWidth - horizontalPadding * 50) / options.length;

  // Initialize translateX based on the defaultIndex prop
  const translateX = useRef(
    new Animated.Value(defaultIndex * tabWidth)
  ).current;

  const handleTabPress = (index: number) => {
    setCurrentIndex(index);
    Animated.timing(translateX, {
      toValue: index * tabWidth,
      duration: 300,
      useNativeDriver: true,
    }).start();

    onSelect(options[index].value);
  };

  return (
    <View style={{ paddingHorizontal: horizontalPadding }}>
      <View className="py-2 rounded-lg flex-row justify-center items-center bg-primaryPurple mb-4">
        <View
          className="relative flex-row overflow-hidden bg-gray-300"
          style={{ width: tabWidth * options.length }}
        >
          <Animated.View
            style={[
              {
                width: tabWidth,
                transform: [{ translateX }],
              },
            ]}
            className="rounded-md absolute h-full bg-primaryBg"
          />
          {/* Tabs */}
          {options.map((option, index) => (
            <TouchableOpacity
              key={option.value}
              onPress={() => handleTabPress(index)}
              className="flex-1 justify-center items-center py-2"
            >
              <Text
                className={`text-md font-bold ${
                  currentIndex === index
                    ? "text-primaryPurple"
                    : "font-normal text-primaryWhite"
                }`}
              >
                {option.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    </View>
  );
};

export default CustomTabView;
