import * as React from "react";
import { Button, TouchableOpacity, View } from "react-native";
import CustomText from "../../../components/CustomText";
import LineSeperator from "../../../components/LineSeperator";
import Heading from "../../../components/Heading";
import globalStyles from "../../../styles/GlobalStyles";
import { ITask } from "../../../store/coordinator/Tasks/tasks/types";

interface IRepTaskCardProps {
  info: ITask;
}

const RepTaskCard: React.FunctionComponent<IRepTaskCardProps> = ({ info }) => {
  const {
    assigner,
    auto_complete,
    due_date,
    due_time,
    assigned_date,
    assigned_time,
    patient,
    task_id,
    task_status,
    task_type,
  } = info;

  return (
    <View className="p-4 bg-primaryWhite rounded-lg shadow-sm">
      <View className={`${globalStyles.containers.flex_between_row}`}>
        {/* <View className="bg-primaryBg text-center p-2 rounded-md border-primaryPurple border-2"> */}
        <CustomText
          value={task_type?.sub_type?.name}
          color="text-primaryPurple"
        />
        {/* </View> */}

        {task_status?.toLocaleLowerCase() === "pending" && (
          <View className="bg-red-1 text-center p-1 rounded-md border-red-3 border-2">
            <CustomText value={"Pending"} color="text-red-3" />
          </View>
        )}

        {task_status?.toLocaleLowerCase() === "in_progress" && (
          <View className="bg-yellow-1 text-center p-1 rounded-md border-yellow-3 border-2">
            <CustomText value={"In Progress"} color="text-yellow-3" />
          </View>
        )}

        {task_status?.toLocaleLowerCase() === "completed" && (
          <View className="bg-green-1 text-center p-1 rounded-md border-green-3 border-2">
            <CustomText value={"Completed"} color="text-green-3" />
          </View>
        )}
      </View>

      <LineSeperator extraStyle="my-4" />

      <View className="flex-row items-end">
        {/*  */}
        <View className="flex-1 gap-y-3">
          <View className={`flex-row`}>
            <CustomText value={"Name:"} className="flex-1 text-start" />
            <CustomText value={patient?.name} className="flex-1 text-start" />
          </View>
          <View className={`flex-row`}>
            <CustomText value={"Age:"} className="flex-1 text-start" />
            <CustomText value={patient?.age} className="flex-1 text-start" />
          </View>
          <View className={`flex-row`}>
            <CustomText value={"Assigned by:"} className="flex-1 text-start" />
            <CustomText value={assigner?.name} className="flex-1 text-start" />
          </View>
          <View className={`flex-row`}>
            <CustomText
              value={"Assigned Date:"}
              className="flex-1 text-start"
            />
            <CustomText value={assigned_date} className="flex-1 text-start" />
          </View>
          <View className={`flex-row`}>
            <CustomText value={"Due Date:"} className="flex-1 text-start" />
            <CustomText value={due_date} className="flex-1 text-start" />
          </View>
          {/* <View className={`flex-row`}>
            <CustomText value={"Due Time:"} className="flex-1 text-start" />
            <CustomText value={due_time} className="flex-1 text-start" />
          </View> */}
          {/* <View className={`flex-row`}>
            <CustomText
              value={"Auto Completed:"}
              className="flex-1 text-start"
            />
            <CustomText
              value={auto_complete ? "Yes" : "No"}
              className="flex-1 text-start"
            />
          </View> */}
        </View>
        {/*  */}
        {/* <View className="">
          {auto_complete ? (
            <TouchableOpacity className="bg-primaryPurple text-center p-2 rounded-md">
              <CustomText
                value={"Complete"}
                color="text-primaryWhite"
                className="font-bold"
              />
            </TouchableOpacity>
          ) : (
            <></>
          )}
        </View> */}
      </View>
    </View>
  );
};

export default RepTaskCard;
