import React from "react";
import { TouchableOpacity, Text, View } from "react-native";

interface ISaveActionButtonPropsV2 {
  disabled: boolean;
  onPress?: (action: "save" | "cancel") => void;
}

const SaveActionV2: React.FunctionComponent<ISaveActionButtonPropsV2> = ({
  disabled,
  onPress,
}) => {
  return (
    <View className="flex-row justify-center gap-4 mt-6">
      <TouchableOpacity
        onPress={() => onPress && onPress("save")}
        disabled={disabled}
        className={`border border-primaryPurple px-6 py-3 rounded-full ${
          disabled ? "bg-gray-300" : "bg-primaryPurple"
        }`}
      >
        <Text
          className={`font-semibold ${
            disabled ? "text-gray-500" : "text-primaryWhite"
          }`}
        >
          Save
        </Text>
      </TouchableOpacity>
      <TouchableOpacity
        onPress={() => onPress && onPress("cancel")}
        className="border border-primaryPurple px-6 py-3 rounded-full bg-primaryBg"
      >
        <Text className="text-primaryPurple">Cancel</Text>
      </TouchableOpacity>
    </View>
  );
};

export default SaveActionV2;
