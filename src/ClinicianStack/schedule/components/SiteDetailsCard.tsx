import React, { useState } from "react";
import { View, Text, TouchableOpacity } from "react-native";
import CustomCard from "../../../components/CustomCard";
import SearchablePicker from "../../../components/SearchablePicker";

interface ISiteDetailsCardProps {}

const SiteDetailsCard: React.FunctionComponent<ISiteDetailsCardProps> = () => {
  const initialData = {
    hospital: "OHSU",
    state: "Portland, OR",
    physician: "<PERSON>, MD",
  };

  const [selectedHospital, setSelectedHospital] = useState<string | null>(
    initialData.hospital
  );
  const [selectedState, setSelectedState] = useState<string | null>(
    initialData.state
  );
  const [selectedPhysician, setSelectedPhysician] = useState<string | null>(
    initialData.physician
  );

  const handleSave = () => {
    // Logic for saving data
  };

  const handleCancel = () => {
    // Reset the fields to the initial data
    setSelectedHospital(initialData.hospital);
    setSelectedState(initialData.state);
    setSelectedPhysician(initialData.physician);
  };

  return (
    <CustomCard>
      <View className="bg-primaryBg rounded-md p-3">
        <View>
          <View className="flex-row justify-between items-center mb-3">
            <Text className="text-primaryBlack font-medium w-[40%]">
              Hospital
            </Text>
            <View className="flex-1">
              <SearchablePicker
                placeholder="Select Hospital"
                items={[
                  { label: "Legacy Emanuel", value: "Legacy Emanuel" },
                  { label: "OHSU", value: "OHSU" },
                  {
                    label: "Providence St. Vincent",
                    value: "Providence St. Vincent",
                  },
                ]}
                value={selectedHospital}
                onValueChange={(item) => setSelectedHospital(item.value)}
              />
            </View>
          </View>

          {/* State Picker */}
          <View className="flex-row justify-between items-center mb-3">
            <Text className="text-primaryBlack font-medium w-[40%]">State</Text>
            <View className="flex-1">
              <SearchablePicker
                placeholder="Select State"
                items={[
                  { label: "Vancouver, WA", value: "Vancouver, WA" },
                  { label: "Portland, OR", value: "Portland, OR" },
                  { label: "Seattle, WA", value: "Seattle, WA" },
                ]}
                value={selectedState}
                onValueChange={(item) => setSelectedState(item.value)}
              />
            </View>
          </View>

          {/* <View className="flex-row justify-between items-center mb-3">
            <Text className="text-primaryBlack font-medium w-[40%]">
              Implanting Physician
            </Text>
            <View className="flex-1">
              <SearchablePicker
                placeholder="Select Physician"
                items={[
                  { label: "Ronald Smith, MD", value: "Ronald Smith, MD" },
                  { label: "Susan Johnson, MD", value: "Susan Johnson, MD" },
                  { label: "James Lee, MD", value: "James Lee, MD" },
                ]}
                value={selectedPhysician}
                onValueChange={(item) => setSelectedPhysician(item.value)}
              />
            </View>
          </View> */}
        </View>

        {/* Action Buttons */}
        <View className="flex-row justify-center gap-4 mt-6">
          <TouchableOpacity
            onPress={handleSave}
            className="border border-primaryPurple px-6 py-3 rounded-full bg-primaryPurple"
          >
            <Text className="text-primaryWhite font-semibold">Save</Text>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={handleCancel}
            className="border border-primaryPurple px-6 py-3 rounded-full bg-primaryBg"
          >
            <Text className="text-primaryPurple">Cancel</Text>
          </TouchableOpacity>
        </View>
      </View>
    </CustomCard>
  );
};

export default SiteDetailsCard;
