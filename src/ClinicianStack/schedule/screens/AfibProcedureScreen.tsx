import * as React from "react";
import { createMaterialTopTabNavigator } from "@react-navigation/material-top-tabs";
import AFIBBasicScreen from "./AFIBBasicScreen";
import AFIBProcedureDetails from "./AFIBProcedureDetails";

const morphData = [
  "https://th.bing.com/th/id/OIP.3MPIj0FEeyyL5a54zm7wfwHaEB?rs=1&pid=ImgDetMain",
  "https://th.bing.com/th/id/R.********************************?rik=JH97KZq0GyNZ2w&riu=http%3a%2f%2fwww.melbourneheartcare.com.au%2fwp-content%2fuploads%2f2011%2f07%2fCardiac-CT-3D-image.jpg&ehk=pwj1q9beViWiR94ByoGIpHgTKt6YEe5JinpgXWFFayA%3d&risl=&pid=ImgRaw&r=0",
  "https://pkductin.vn/contents_ductin/uploads/images/ctca-page_image-1-(3d-heart).png",
];

interface IAfibProcedureScreenProps {}

const AfibProcedureScreen: React.FunctionComponent<
  IAfibProcedureScreenProps
> = (props) => {
  const Tab = createMaterialTopTabNavigator();

  return (
    <Tab.Navigator
      screenOptions={{
        tabBarScrollEnabled: false,
        tabBarIndicatorStyle: {
          backgroundColor: "#8143d9",
          height: "10%",
          width: "50%",
        },
        tabBarActiveTintColor: "#8143d9",
        tabBarInactiveTintColor: "black",
        tabBarLabelStyle: {
          fontSize: 10,
          fontWeight: "bold",
        },
      }}
      sceneContainerStyle={{ backgroundColor: "white" }}
    >
      <Tab.Screen
        name="Case Info"
        component={AFIBBasicScreen}
        initialParams={{
          ostium: "23 mm x 29 mm",
          morphology: morphData,
          clotData: morphData,
          angles: ["0", "90", "45", "135"],
          depths: ["37 mm", "30 mm", "35 mm", "26 mm"],
          diameters: ["26 mm", "19 mm", "19 mm", "18 mm"],
        }}
      />
      <Tab.Screen name="Procedure Details" component={AFIBProcedureDetails} />
    </Tab.Navigator>
  );
};

export default AfibProcedureScreen;
